<template>
  <div class="calendar-example">
    <h2>CalendarViewPreset 使用示例</h2>
    
    <!-- 基础用法 -->
    <div class="example-section">
      <h3>基础用法</h3>
      <CalendarViewPreset
        title="宠物健康记录日历"
        :records="healthRecords"
        :record-types="recordTypes"
        :color-mapping="colorMapping"
        :label-mapping="labelMapping"
        @date-click="handleDateClick"
        @view-change="handleViewChange"
        @date-change="handleDateChange"
      />
    </div>

    <!-- 自定义字段映射 -->
    <div class="example-section">
      <h3>自定义字段映射</h3>
      <CalendarViewPreset
        title="事件记录日历"
        :records="eventRecords"
        record-id-field="event_id"
        record-type-field="event_type"
        record-date-field="event_date"
        record-desc-field="event_description"
        :color-mapping="eventColorMapping"
        :label-mapping="eventLabelMapping"
        initial-view="week"
        @date-click="handleEventDateClick"
      />
    </div>

    <!-- 提醒记录示例 -->
    <div class="example-section">
      <h3>提醒记录日历</h3>
      <CalendarViewPreset
        title="提醒日历"
        :records="reminderRecords"
        record-id-field="reminder_id"
        record-type-field="reminder_type"
        record-date-field="reminder_date"
        record-desc-field="reminder_content"
        :color-mapping="reminderColorMapping"
        :label-mapping="reminderLabelMapping"
        initial-view="year"
        @date-click="handleReminderDateClick"
      />
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <h3>事件日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import CalendarViewPreset from './CalendarViewPreset.vue'

// 事件日志
const eventLogs = ref([])

const addLog = (event, data) => {
  eventLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    event,
    data: JSON.stringify(data)
  })
  if (eventLogs.value.length > 10) {
    eventLogs.value.pop()
  }
}

// 健康记录示例数据
const healthRecords = ref([
  {
    id: 1,
    record_type: 'vaccination',
    date: '2024-06-15',
    description: '狂犬病疫苗接种',
    created_at: '2024-06-15T10:00:00Z'
  },
  {
    id: 2,
    record_type: 'checkup',
    date: '2024-06-10',
    description: '常规体检',
    created_at: '2024-06-10T14:30:00Z'
  },
  {
    id: 3,
    record_type: 'deworming',
    date: '2024-06-05',
    description: '体内驱虫',
    created_at: '2024-06-05T09:15:00Z'
  },
  {
    id: 4,
    record_type: 'illness',
    date: '2024-06-01',
    description: '轻微感冒',
    created_at: '2024-06-01T16:45:00Z'
  }
])

// 记录类型配置
const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病', color: '#F56C6C' },
  { value: 'medication', label: '用药', color: '#909399' },
  { value: 'surgery', label: '手术', color: '#F56C6C' },
  { value: 'other', label: '其他', color: '#C0C4CC' }
])

// 颜色映射
const colorMapping = reactive({
  vaccination: 'vaccination',
  deworming: 'deworming',
  checkup: 'checkup',
  illness: 'illness',
  medication: 'medication',
  surgery: 'surgery',
  other: 'other'
})

// 标签映射
const labelMapping = reactive({
  vaccination: '疫苗接种',
  deworming: '驱虫',
  checkup: '体检',
  illness: '疾病',
  medication: '用药',
  surgery: '手术',
  other: '其他'
})

// 事件记录示例数据（自定义字段）
const eventRecords = ref([
  {
    event_id: 'evt_1',
    event_type: 'meeting',
    event_date: '2024-06-20',
    event_description: '团队会议',
    created_at: '2024-06-20T09:00:00Z'
  },
  {
    event_id: 'evt_2',
    event_type: 'training',
    event_date: '2024-06-18',
    event_description: '技能培训',
    created_at: '2024-06-18T14:00:00Z'
  },
  {
    event_id: 'evt_3',
    event_type: 'review',
    event_date: '2024-06-16',
    event_description: '项目评审',
    created_at: '2024-06-16T10:30:00Z'
  }
])

// 事件颜色映射
const eventColorMapping = reactive({
  meeting: 'checkup',
  training: 'vaccination',
  review: 'deworming',
  deadline: 'illness'
})

// 事件标签映射
const eventLabelMapping = reactive({
  meeting: '会议',
  training: '培训',
  review: '评审',
  deadline: '截止日期'
})

// 提醒记录示例数据
const reminderRecords = ref([
  {
    reminder_id: 'rem_1',
    reminder_type: 'daily',
    reminder_date: '2024-06-25',
    reminder_content: '每日提醒事项',
    created_at: '2024-06-25T08:00:00Z'
  },
  {
    reminder_id: 'rem_2',
    reminder_type: 'weekly',
    reminder_date: '2024-06-22',
    reminder_content: '周报提交',
    created_at: '2024-06-22T17:00:00Z'
  },
  {
    reminder_id: 'rem_3',
    reminder_type: 'monthly',
    reminder_date: '2024-06-30',
    reminder_content: '月度总结',
    created_at: '2024-06-30T18:00:00Z'
  }
])

// 提醒颜色映射
const reminderColorMapping = reactive({
  daily: 'vaccination',
  weekly: 'checkup',
  monthly: 'deworming',
  urgent: 'illness'
})

// 提醒标签映射
const reminderLabelMapping = reactive({
  daily: '每日提醒',
  weekly: '每周提醒',
  monthly: '每月提醒',
  urgent: '紧急提醒'
})

// 事件处理函数
const handleDateClick = (dateStr, records) => {
  addLog('健康记录日期点击', { date: dateStr, recordCount: records.length })
}

const handleViewChange = (newView) => {
  addLog('健康记录视图切换', { view: newView })
}

const handleDateChange = (newDate) => {
  addLog('健康记录日期变化', { date: newDate.toISOString().split('T')[0] })
}

const handleEventDateClick = (dateStr, records) => {
  addLog('事件记录日期点击', { date: dateStr, recordCount: records.length })
}

const handleReminderDateClick = (dateStr, records) => {
  addLog('提醒记录日期点击', { date: dateStr, recordCount: records.length })
}
</script>

<style scoped>
.calendar-example {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.calendar-example h2 {
  color: #303133;
  margin-bottom: 32px;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
}

.example-section {
  margin-bottom: 48px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.example-section h3 {
  color: #409EFF;
  margin-bottom: 24px;
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.event-log {
  margin-top: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.event-log h3 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #909399;
  font-weight: 500;
  min-width: 80px;
}

.log-event {
  color: #409EFF;
  font-weight: 600;
  min-width: 120px;
}

.log-data {
  color: #606266;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  flex: 1;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calendar-example {
    padding: 16px;
  }

  .example-section {
    padding: 16px;
    margin-bottom: 32px;
  }

  .log-item {
    flex-direction: column;
    gap: 4px;
  }

  .log-time,
  .log-event {
    min-width: auto;
  }
}
</style>
