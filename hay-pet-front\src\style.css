/* 引入设计令牌和组件预设 */
@import './styles/design-tokens.css';
@import './styles/tag-presets.css';
@import './styles/color-picker-presets.css';
@import './styles/view-toggle-presets.css';
@import './styles/stats-card-presets.css';
@import './styles/type-management-dialog.css';
@import './styles/drag-sort-presets.css';

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  background-attachment: fixed;
}

#app {
  min-height: 100vh;
}

/* 全局动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 全局Element Plus样式覆盖 */
.el-card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.el-button {
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-button:hover {
  transform: translateY(-2px);
}

.el-input__wrapper {
  border-radius: 10px;
  transition: all 0.3s ease;
}

.el-input__wrapper:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 工具类 */
.text-center {
  text-align: center;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }
  
  .el-table .el-table__cell {
    padding: 8px 0;
  }
  
  .pet-info {
    flex-direction: column;
    text-align: center;
  }
  
  .avatar-section {
    align-self: center;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state .empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state .empty-text {
  font-size: 16px;
  margin-bottom: 20px;
}

/* vue3-photo-preview 全局z-index修复 */
.PhotoView-Portal {
  z-index: 3000 !important;
}

.PhotoView {
  z-index: 3000 !important;
}

.PhotoView-Slider {
  z-index: 3000 !important;
}

.PhotoView-Backdrop {
  z-index: 3000 !important;
}

.PhotoView-PhotoWrap {
  z-index: 3000 !important;
}

/* 确保所有可能的vue3-photo-preview相关元素都有足够高的z-index */
[class*="PhotoView"] {
  z-index: 3000 !important;
}

[class*="photo-slider"] {
  z-index: 3000 !important;
}

[class*="PhotoSlider"] {
  z-index: 3000 !important;
}

/* 确保预览组件在所有对话框之上 */
body > div[class*="PhotoView"],
body > div[class*="photo-slider"],
body > div[class*="PhotoSlider"] {
  z-index: 3000 !important;
}