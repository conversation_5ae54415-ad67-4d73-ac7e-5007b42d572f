<template>
  <div class="reminders-view">
    <div v-if="!currentPetId" class="no-pet-selected">
      <el-empty description="请先在左侧选择一个宠物以查看或管理提醒事项" />
    </div>
    <div v-else>
      <!-- 页面标题栏 -->
      <PageHeaderBar
        title="提醒事项"
        subtitle="管理您的宠物提醒事项"
        add-button-text="添加提醒"
        @add-click="openAddDialog"
      />

      <!-- 统计卡片面板 -->
      <div v-if="reminders.length > 0" class="stats-section">
        <StandardStatsGrid
          :stats-items="statsItems"
          :clickable="false"
        />

        <!-- 详细统计分析面板 -->
        <el-card class="stats-analysis-panel" shadow="hover" v-if="reminders.length > 0">
          <template #header>
            <div class="analysis-header">
              <span class="analysis-title">统计分析</span>
              <el-button
                type="primary"
                link
                @click="showDetailedStats = !showDetailedStats"
                class="toggle-stats-btn"
              >
                <el-icon>
                  <component :is="showDetailedStats ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
                {{ showDetailedStats ? '收起' : '展开' }}详细分析
              </el-button>
            </div>
          </template>

          <div v-show="showDetailedStats" class="analysis-content">
            <!-- 分类统计 -->
            <div class="analysis-section">
              <h4 class="section-title">分类分布</h4>
              <div class="category-stats-grid">
                <div
                  v-for="(stat, categoryValue) in categoryStats"
                  :key="categoryValue"
                  class="category-stat-item"
                >
                  <div class="category-info">
                    <div class="category-color" :style="{ backgroundColor: stat.color }"></div>
                    <span class="category-name">{{ stat.label }}</span>
                  </div>
                  <div class="category-numbers">
                    <span class="total-count">{{ stat.total }}</span>
                    <span class="completion-rate">{{ stat.completionRate }}%</span>
                  </div>
                  <div class="category-progress">
                    <el-progress
                      :percentage="stat.completionRate"
                      :color="stat.color"
                      :stroke-width="6"
                      :show-text="false"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 优先级统计 -->
            <div class="analysis-section">
              <h4 class="section-title">优先级分布</h4>
              <div class="priority-stats-grid">
                <div class="priority-stat-item high">
                  <div class="priority-label">高优先级</div>
                  <div class="priority-count">{{ priorityStats['高'] }}</div>
                </div>
                <div class="priority-stat-item medium">
                  <div class="priority-label">中优先级</div>
                  <div class="priority-count">{{ priorityStats['中'] }}</div>
                </div>
                <div class="priority-stat-item low">
                  <div class="priority-label">低优先级</div>
                  <div class="priority-count">{{ priorityStats['低'] }}</div>
                </div>
              </div>
            </div>

            <!-- 趋势分析 -->
            <div class="analysis-section">
              <h4 class="section-title">7天趋势</h4>
              <div class="trend-chart">
                <div class="trend-bars">
                  <div
                    v-for="week in weeklyTrend"
                    :key="week.week"
                    class="trend-bar-container"
                  >
                    <div class="trend-bar">
                      <div
                        class="trend-bar-fill"
                        :style="{
                          height: `${Math.max(week.total * 10, 5)}px`,
                          backgroundColor: week.completionRate > 80 ? '#67C23A' : week.completionRate > 50 ? '#E6A23C' : '#F56C6C'
                        }"
                      ></div>
                    </div>
                    <div class="trend-label">{{ week.week }}</div>
                    <div class="trend-value">{{ week.total }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 日历面板 -->
      <div class="calendar-section">
        <CalendarViewPreset
          title="提醒日历"
          :records="reminders"
          :record-types="reminderCategories"
          :color-mapping="reminderColorMapping"
          :label-mapping="reminderLabelMapping"
          :show-stats="true"
          record-id-field="id"
          record-type-field="category"
          record-date-field="due_date"
          record-desc-field="title"
          @date-click="handleCalendarDateClick"
          @date-dblclick="handleCalendarDateDoubleClick"
          @view-change="handleCalendarViewChange"
          @date-change="handleCalendarDateChange"
          @record-click="handleReminderClick"
          @record-edit="handleReminderEdit"
        />
      </div>

      <!-- 分类筛选和视图切换 -->
      <div class="filter-section">
        <el-card shadow="hover">
          <div class="filter-content">

            <div class="filter-left">
              <div class="filter-group">
                <div class="filter-header">
                  <div class="filter-label-section">
                    <span class="filter-label">提醒分类：</span>
                    <span v-if="selectedCategories.length > 0" class="selected-count">
                      已选择 {{ selectedCategories.length }} 个分类
                    </span>
                  </div>
                  <EnhancedViewToggle
                    v-model="viewMode"
                    :options="viewModeOptions"
                    size="small"
                    @change="handleViewModeChange"
                  />
                </div>
                <div class="category-tags-container">
                  <!-- 全部标签 -->
                  <StandardTag
                    text="全部"
                    variant="primary"
                    :active="selectedCategories.length === 0"
                    :background-color="getAllTagBackground(selectedCategories.length === 0)"
                    :color="getAllTagColor(selectedCategories.length === 0)"
                    :border-color="getAllTagBorderColor(selectedCategories.length === 0)"
                    :editable="false"
                    :deletable="false"
                    :draggable="false"
                    :show-actions="false"
                    class="all-tag-standard"
                    @click="handleCategoryFilter('')"
                  />

                  <!-- 可拖拽的分类标签 -->
                  <draggable
                    v-model="reminderCategories"
                    v-bind="dragOptions"
                    @start="onDragStart"
                    @end="onDragEnd"
                    item-key="id"
                    :key="reminderCategories.length"
                    class="draggable-container"
                    tag="div"
                  >
                    <template #item="{ element: category }">
                      <div class="draggable-item">
                        <StandardTag
                          :text="category.label"
                          :variant="getTagVariant(category.color)"
                          :active="selectedCategories.includes(category.value)"
                          :editable="true"
                          :deletable="true"
                          :color="getTagTextColor(category.color, selectedCategories.includes(category.value))"
                          :background-color="getTagBackground(category.color, selectedCategories.includes(category.value))"
                          :border-color="getTagBorderColor(category.color, selectedCategories.includes(category.value))"
                          :class="{
                            'is-dragging': isDragging,
                            'multi-selected': selectedCategories.includes(category.value)
                          }"
                          @click="handleCategoryFilter(category.value)"
                          @edit="editCategory(category)"
                          @delete="deleteCategory(category.value)"
                        />
                      </div>
                    </template>
                  </draggable>

                  <!-- 添加分类按钮 -->
                  <AddButton
                    @click="showAddCategoryDialog = true"
                    tooltip="添加新分类"
                    variant="primary"
                    size="md"
                    animation="rotate"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 提醒事项展示区域 -->
      <div class="reminders-section">
        <!-- 使用 RecordViewsPreset 组件 -->
        <RecordViewsPreset
          :records="filteredReminders"
          :view-mode="viewMode"
          :selected-records="selectedReminders"
          :color-mapping="reminderColorMapping"
          :label-mapping="reminderLabelMapping"
          date-field="due_date"
          category-field="category"
          description-field="title"
          completed-field="is_completed"
          @edit="editReminder"
          @delete="handleDeleteReminder"
          @toggle-completion="toggleReminderStatus"
          @batch-complete="batchCompleteReminders"
          @batch-delete="batchDeleteReminders"
          @selection-change="handleSelectionChange"
          @view-mode-change="handleViewModeChange"
        >
          <template #empty-action>
            <el-button type="primary" @click="openAddDialog">
              <el-icon><Plus /></el-icon>
              添加第一条提醒
            </el-button>
          </template>
        </RecordViewsPreset>


      </div>

      <!-- 悬浮提示框 -->
      <div
        v-if="showTooltip && tooltipReminders.length > 0"
        class="reminders-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-header">
          <el-icon><Calendar /></el-icon>
          {{ formatTooltipDate(tooltipDate) }}
        </div>
        <div class="tooltip-content">
          <div
            v-for="(reminder, index) in tooltipReminders.slice(0, 5)"
            :key="reminder.id"
            class="tooltip-reminder"
          >
            <StandardTag
              :text="getCategoryLabel(reminder.category)"
              :variant="getCategoryVariant(reminder.category)"
              size="xs"
              :editable="false"
              :deletable="false"
              :show-actions="false"
            />
            <span class="reminder-desc">
              {{ truncateDescription(reminder.title) }}
            </span>
          </div>
          <div
            v-if="tooltipReminders.length > 5"
            class="more-reminders-hint"
          >
            还有 {{ tooltipReminders.length - 5 }} 条提醒...
          </div>
        </div>
      </div>

      <!-- 添加/编辑提醒对话框 -->
      <el-dialog
        v-model="showAddDialog"
        :title="isEditMode ? '编辑提醒' : '添加提醒'"
        width="600px"
        @closed="resetCurrentReminder"
      >
        <el-form :model="currentReminder" label-width="100px" ref="reminderFormRef">
          <el-form-item label="标题" prop="title" :rules="[{ required: true, message: '请输入标题', trigger: 'blur' }]">
            <el-input v-model="currentReminder.title" placeholder="请输入提醒标题"></el-input>
          </el-form-item>

          <el-form-item label="分类" prop="category">
            <el-select v-model="currentReminder.category" placeholder="选择分类" style="width: 100%;">
              <el-option
                v-for="category in reminderCategories"
                :key="category.value"
                :label="category.label"
                :value="category.value"
              >
                <div class="category-option">
                  <StandardTag
                    :text="category.label"
                    :variant="getCategoryVariant(category.color)"
                    size="xs"
                    :editable="false"
                    :deletable="false"
                    :show-actions="false"
                  />
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="截止日期" prop="due_date">
            <el-date-picker
              v-model="currentReminder.due_date"
              type="datetime"
              placeholder="选择日期时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              style="width: 100%;"
            ></el-date-picker>
          </el-form-item>

          <el-form-item label="优先级" prop="priority">
            <el-select v-model="currentReminder.priority" placeholder="选择优先级" style="width: 100%;">
              <el-option label="高" value="高"></el-option>
              <el-option label="中" value="中"></el-option>
              <el-option label="低" value="低"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="预计耗时" prop="estimated_duration">
            <el-input-number
              v-model="currentReminder.estimated_duration"
              :min="0"
              :max="1440"
              placeholder="分钟"
              style="width: 100%;"
            />
            <span class="form-help-text">预计完成此提醒需要的时间（分钟）</span>
          </el-form-item>

          <el-form-item label="提前通知" prop="advance_notice">
            <el-select v-model="currentReminder.advance_notice" placeholder="选择提前通知时间" style="width: 100%;">
              <el-option label="不提前通知" :value="0"></el-option>
              <el-option label="提前5分钟" :value="5"></el-option>
              <el-option label="提前15分钟" :value="15"></el-option>
              <el-option label="提前30分钟" :value="30"></el-option>
              <el-option label="提前1小时" :value="60"></el-option>
              <el-option label="提前2小时" :value="120"></el-option>
              <el-option label="提前1天" :value="1440"></el-option>
              <el-option label="提前1周" :value="10080"></el-option>
            </el-select>
          </el-form-item>

          <!-- 重复设置 -->
          <el-form-item label="重复设置" prop="repeat_type">
            <el-select v-model="currentReminder.repeat_type" placeholder="选择重复类型" style="width: 100%;" @change="handleRepeatTypeChange">
              <el-option label="不重复" value="none"></el-option>
              <el-option label="每日重复" value="daily"></el-option>
              <el-option label="每周重复" value="weekly"></el-option>
              <el-option label="每月重复" value="monthly"></el-option>
              <el-option label="每年重复" value="yearly"></el-option>
              <el-option label="自定义间隔" value="custom"></el-option>
            </el-select>
          </el-form-item>

          <!-- 自定义重复间隔 -->
          <el-form-item
            v-if="currentReminder.repeat_type === 'custom'"
            label="重复间隔"
            prop="repeat_interval"
          >
            <div style="display: flex; align-items: center; gap: 8px;">
              <el-input-number
                v-model="currentReminder.repeat_interval"
                :min="1"
                :max="365"
                placeholder="间隔"
                style="width: 120px;"
              />
              <el-select v-model="currentReminder.repeat_unit" placeholder="单位" style="width: 100px;">
                <el-option label="天" value="days"></el-option>
                <el-option label="周" value="weeks"></el-option>
                <el-option label="月" value="months"></el-option>
              </el-select>
              <span class="form-help-text">每 {{ currentReminder.repeat_interval || 1 }} {{ getRepeatUnitLabel(currentReminder.repeat_unit) }}重复一次</span>
            </div>
          </el-form-item>

          <!-- 重复结束设置 -->
          <el-form-item
            v-if="currentReminder.repeat_type !== 'none'"
            label="重复结束"
            prop="repeat_end_type"
          >
            <el-radio-group v-model="currentReminder.repeat_end_type">
              <el-radio value="never">永不结束</el-radio>
              <el-radio value="count">重复次数</el-radio>
              <el-radio value="date">结束日期</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 重复次数设置 -->
          <el-form-item
            v-if="currentReminder.repeat_type !== 'none' && currentReminder.repeat_end_type === 'count'"
            label="重复次数"
            prop="repeat_count"
          >
            <el-input-number
              v-model="currentReminder.repeat_count"
              :min="1"
              :max="100"
              placeholder="次数"
              style="width: 100%;"
            />
            <span class="form-help-text">总共重复 {{ currentReminder.repeat_count || 1 }} 次</span>
          </el-form-item>

          <!-- 重复结束日期 -->
          <el-form-item
            v-if="currentReminder.repeat_type !== 'none' && currentReminder.repeat_end_type === 'date'"
            label="结束日期"
            prop="repeat_end_date"
          >
            <el-date-picker
              v-model="currentReminder.repeat_end_date"
              type="date"
              placeholder="选择结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%;"
            />
          </el-form-item>

          <el-form-item label="备注" prop="notes">
            <el-input
              type="textarea"
              :rows="3"
              v-model="currentReminder.notes"
              placeholder="请输入备注信息"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showAddDialog = false">取消</el-button>
            <el-button type="primary" @click="saveReminder">保存</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加/编辑分类对话框 -->
      <TypeManagementDialog
        v-model="showAddCategoryDialog"
        :is-edit-mode="isEditCategoryMode"
        type-label="分类"
        :data="newCategory"
        :predefined-colors="predefinedCategoryColors"
        @save="handleCategorySave"
        @cancel="handleCategoryCancel"
        @closed="resetCategoryDialog"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, computed, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Plus, Delete, Edit, Calendar, Clock, Check, RefreshLeft,
  Document, Bell, TrendCharts, ArrowLeft, ArrowRight
} from '@element-plus/icons-vue';
import draggable from 'vuedraggable';
import StandardTag from '@/components/common/StandardTag.vue';
import StandardStatsGrid from '@/components/common/StandardStatsGrid.vue';
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue';
import AddButton from '@/components/common/AddButton.vue';
import TypeManagementDialog from '@/components/common/TypeManagementDialog.vue';
import PageHeaderBar from '@/components/common/PageHeaderBar.vue';
import CalendarViewPreset from '@/components/presets/CalendarViewPreset.vue';
import RecordViewsPreset from '@/components/presets/RecordViewsPreset.vue';

const supabase = inject('supabase');
const reminders = ref([]);
const selectedReminders = ref([]);
const showAddDialog = ref(false);
const isEditMode = ref(false);
const viewMode = ref('cards');
const selectedCategories = ref([]);
const reminderCategories = ref([]);
const showAddCategoryDialog = ref(false);
const isEditCategoryMode = ref(false);
const showDetailedStats = ref(false);

// 搜索和筛选相关
const searchKeyword = ref('');
const selectedPriorities = ref([]);
const dateRangeFilter = ref([]);
const statusFilter = ref('all'); // all, pending, completed, overdue
const showAdvancedFilters = ref(false);

// 日历视图相关
const selectedDate = ref(new Date());
const calendarView = ref('month');
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const calendarRef = ref(null);

// 悬浮提示相关
const showTooltip = ref(false);
const tooltipDate = ref('');
const tooltipReminders = ref([]);
const tooltipStyle = ref({});
const tooltipTimer = ref(null);

// 拖拽排序相关
const isDragging = ref(false);
const dragOptions = {
  animation: 300,
  group: 'record-types',
  disabled: false,
  ghostClass: 'ghost'
};

const currentReminder = ref({
  id: null,
  title: '',
  due_date: '',
  priority: '中',
  notes: '',
  category: 'other',
  estimated_duration: 0,
  advance_notice: 0,
  is_completed: false,
  repeat_type: 'none',
  repeat_interval: 1,
  repeat_unit: 'days',
  repeat_end_type: 'never',
  repeat_count: 1,
  repeat_end_date: '',
  parent_reminder_id: null
});

const newCategory = ref({
  label: '',
  color: '#409EFF',
  icon: 'Bell',
  description: ''
});

// 预定义颜色
const predefinedCategoryColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
  '#909399', '#9C27B0', '#FF9800', '#4CAF50',
  '#2196F3', '#FF5722', '#795548', '#607D8B'
];

// 视图模式选项
const viewModeOptions = [
  { value: 'cards', label: '卡片视图', icon: 'Grid' },
  { value: 'timeline', label: '时间线视图', icon: 'Clock' },
  { value: 'table', label: '表格视图', icon: 'List' }
];

// 为 CalendarViewPreset 组件准备的映射
const reminderColorMapping = computed(() => {
  const mapping = {};
  reminderCategories.value.forEach(category => {
    mapping[category.value] = category.color;
  });
  return mapping;
});

const reminderLabelMapping = computed(() => {
  const mapping = {};
  reminderCategories.value.forEach(category => {
    mapping[category.value] = category.label;
  });
  return mapping;
});

// 计算属性
const isAllSelected = computed(() => {
  return reminders.value.length > 0 && selectedReminders.value.length === reminders.value.length;
});

const filteredReminders = computed(() => {
  let filtered = reminders.value;

  // 按分类筛选
  if (selectedCategories.value.length > 0) {
    filtered = filtered.filter(reminder =>
      selectedCategories.value.includes(reminder.category)
    );
  }

  // 关键词搜索
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase();
    filtered = filtered.filter(reminder =>
      reminder.title.toLowerCase().includes(keyword) ||
      (reminder.notes && reminder.notes.toLowerCase().includes(keyword))
    );
  }

  // 优先级筛选
  if (selectedPriorities.value.length > 0) {
    filtered = filtered.filter(reminder =>
      selectedPriorities.value.includes(reminder.priority)
    );
  }

  // 状态筛选
  if (statusFilter.value !== 'all') {
    switch (statusFilter.value) {
      case 'pending':
        filtered = filtered.filter(reminder => !reminder.is_completed);
        break;
      case 'completed':
        filtered = filtered.filter(reminder => reminder.is_completed);
        break;
      case 'overdue':
        filtered = filtered.filter(reminder => !reminder.is_completed && isOverdue(reminder));
        break;
      case 'urgent':
        filtered = filtered.filter(reminder => !reminder.is_completed && isUrgent(reminder));
        break;
    }
  }

  // 日期范围筛选
  if (dateRangeFilter.value && dateRangeFilter.value.length === 2) {
    const [startDate, endDate] = dateRangeFilter.value;
    filtered = filtered.filter(reminder => {
      const reminderDate = new Date(reminder.due_date);
      return reminderDate >= new Date(startDate) && reminderDate <= new Date(endDate);
    });
  }

  return filtered.sort((a, b) => {
    // 未完成的排在前面
    if (a.is_completed !== b.is_completed) {
      return a.is_completed ? 1 : -1;
    }
    // 按到期日期排序
    return new Date(a.due_date) - new Date(b.due_date);
  });
});

const statsItems = computed(() => {
  const total = reminders.value.length;
  const completed = reminders.value.filter(r => r.is_completed).length;
  const pending = total - completed;
  const overdue = reminders.value.filter(r => !r.is_completed && isOverdue(r)).length;
  const urgent = reminders.value.filter(r => !r.is_completed && isUrgent(r)).length;

  // 计算完成率
  const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

  // 计算本周新增
  const thisWeekStart = getWeekStart(new Date());
  const thisWeekReminders = reminders.value.filter(r => {
    const createdDate = new Date(r.created_at);
    return createdDate >= thisWeekStart;
  }).length;

  return [
    {
      key: 'total-reminders',
      type: 'total-reminders',
      icon: Document,
      label: '总提醒数',
      value: total,
      date: '全部提醒',
      variant: 'total-records'
    },
    {
      key: 'pending-reminders',
      type: 'pending-reminders',
      icon: Bell,
      label: '待处理',
      value: pending,
      date: '需要关注',
      variant: 'pending-reminders'
    },
    {
      key: 'completed-reminders',
      type: 'completed-reminders',
      icon: Check,
      label: '已完成',
      value: completed,
      date: `完成率 ${completionRate}%`,
      variant: 'success'
    },
    {
      key: 'urgent-reminders',
      type: 'urgent-reminders',
      icon: TrendCharts,
      label: '紧急提醒',
      value: urgent,
      date: `逾期 ${overdue} 条`,
      variant: 'danger'
    }
  ];
});

// 新增统计分析计算属性
const categoryStats = computed(() => {
  const stats = {};
  reminderCategories.value.forEach(category => {
    const categoryReminders = reminders.value.filter(r => r.category === category.value);
    const completed = categoryReminders.filter(r => r.is_completed).length;
    const total = categoryReminders.length;

    stats[category.value] = {
      label: category.label,
      color: category.color,
      total,
      completed,
      pending: total - completed,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
    };
  });
  return stats;
});

const priorityStats = computed(() => {
  const stats = { '高': 0, '中': 0, '低': 0 };
  reminders.value.forEach(reminder => {
    if (!reminder.is_completed) {
      stats[reminder.priority] = (stats[reminder.priority] || 0) + 1;
    }
  });
  return stats;
});

const weeklyTrend = computed(() => {
  const weeks = [];
  const now = new Date();

  for (let i = 6; i >= 0; i--) {
    const weekStart = new Date(now);
    weekStart.setDate(weekStart.getDate() - (i * 7));
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);

    const weekReminders = reminders.value.filter(reminder => {
      const createdDate = new Date(reminder.created_at);
      return createdDate >= weekStart && createdDate <= weekEnd;
    });

    const completed = weekReminders.filter(r => r.is_completed).length;

    weeks.push({
      week: `${weekStart.getMonth() + 1}/${weekStart.getDate()}`,
      total: weekReminders.length,
      completed,
      completionRate: weekReminders.length > 0 ? Math.round((completed / weekReminders.length) * 100) : 0
    });
  }

  return weeks;
});

// 工具函数
const getWeekStart = (date) => {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
  return new Date(d.setDate(diff));
};

const isOverdue = (reminder) => {
  if (reminder.is_completed) return false;
  return new Date(reminder.due_date) < new Date();
};

const isUrgent = (reminder) => {
  if (reminder.is_completed) return false;
  const now = new Date();
  const dueDate = new Date(reminder.due_date);
  const diffHours = (dueDate - now) / (1000 * 60 * 60);
  return diffHours <= 24 && diffHours >= 0;
};

const getCategoryLabel = (categoryValue) => {
  const category = reminderCategories.value.find(c => c.value === categoryValue);
  return category ? category.label : '其他';
};

const getCategoryVariant = (categoryColor) => {
  // 根据颜色返回对应的变体
  const colorMap = {
    '#F56C6C': 'danger',
    '#E6A23C': 'warning',
    '#67C23A': 'success',
    '#409EFF': 'primary',
    '#9C27B0': 'info'
  };
  return colorMap[categoryColor] || 'info';
};

const getTagVariant = (color) => {
  return getCategoryVariant(color);
};

const getTagTextColor = (color, isActive) => {
  return isActive ? '#ffffff' : color;
};

const getTagBackground = (color, isActive) => {
  return isActive ? color : `${color}15`;
};

const getTagBorderColor = (color, isActive) => {
  return isActive ? color : `${color}30`;
};

const getAllTagBackground = (isActive) => {
  return isActive ? '#409EFF' : '#409EFF15';
};

const getAllTagColor = (isActive) => {
  return isActive ? '#ffffff' : '#409EFF';
};

const getAllTagBorderColor = (isActive) => {
  return isActive ? '#409EFF' : '#409EFF30';
};

// 新时间线相关方法
const groupedReminders = computed(() => {
  const groups = {};
  sortedReminders.value.forEach(reminder => {
    const date = reminder.due_date.split('T')[0]; // 只取日期部分
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(reminder);
  });
  return groups;
});

const sortedReminders = computed(() => {
  return [...filteredReminders.value].sort((a, b) => {
    return new Date(a.due_date) - new Date(b.due_date);
  });
});

const getReminderDateRange = () => {
  if (filteredReminders.value.length === 0) return '';
  const dates = filteredReminders.value.map(r => new Date(r.due_date));
  const minDate = new Date(Math.min(...dates));
  const maxDate = new Date(Math.max(...dates));

  if (minDate.toDateString() === maxDate.toDateString()) {
    return minDate.toLocaleDateString('zh-CN');
  }

  return `${minDate.toLocaleDateString('zh-CN')} - ${maxDate.toLocaleDateString('zh-CN')}`;
};

const getActiveCategories = () => {
  const activeCategories = new Set(filteredReminders.value.map(r => r.category));
  return reminderCategories.value.filter(category => activeCategories.has(category.value));
};

const getCategoryCount = (categoryValue) => {
  return filteredReminders.value.filter(r => r.category === categoryValue).length;
};

const formatReminderGroupDate = (dateStr) => {
  const date = new Date(dateStr);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  if (date.toDateString() === today.toDateString()) {
    return '今天';
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天';
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return '明天';
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' });
  }
};

const formatReminderGroupDay = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', { weekday: 'long' });
};

const formatReminderTime = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const getCategoryColor = (categoryValue) => {
  const category = reminderCategories.value.find(c => c.value === categoryValue);
  return category ? category.color : '#909399';
};

const getReminderIcon = (reminder) => {
  if (reminder.is_completed) return 'Check';
  if (isOverdue(reminder)) return 'Warning';
  if (isUrgent(reminder)) return 'Bell';

  // 根据分类返回图标
  const iconMap = {
    'health': 'Monitor',
    'medication': 'Plus',
    'appointment': 'Calendar',
    'exercise': 'TrendCharts',
    'feeding': 'Document',
    'grooming': 'Star',
    'other': 'Document'
  };
  return iconMap[reminder.category] || 'Bell';
};

const viewReminderDetail = (reminder) => {
  // 这里可以添加查看详情的逻辑，暂时使用编辑功能
  editReminder(reminder);
};

// 事件处理
const handleSelectionChange = (selection) => {
  selectedReminders.value = selection;
};

const selectReminder = (reminder) => {
  const index = selectedReminders.value.findIndex(r => r.id === reminder.id);
  if (index > -1) {
    selectedReminders.value.splice(index, 1);
  } else {
    selectedReminders.value.push(reminder);
  }
};

const handleCategoryFilter = (categoryValue) => {
  if (categoryValue === '') {
    selectedCategories.value = [];
  } else {
    const index = selectedCategories.value.indexOf(categoryValue);
    if (index > -1) {
      selectedCategories.value.splice(index, 1);
    } else {
      selectedCategories.value.push(categoryValue);
    }
  }
};

const handleViewModeChange = (newMode) => {
  viewMode.value = newMode;
};

// 筛选相关方法
const clearAllFilters = () => {
  searchKeyword.value = '';
  selectedCategories.value = [];
  selectedPriorities.value = [];
  dateRangeFilter.value = [];
  statusFilter.value = 'all';
};

const setTodayFilter = () => {
  const today = new Date().toISOString().split('T')[0];
  dateRangeFilter.value = [today, today];
};

const setThisWeekFilter = () => {
  const today = new Date();
  const weekStart = getWeekStart(today);
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekEnd.getDate() + 6);

  dateRangeFilter.value = [
    weekStart.toISOString().split('T')[0],
    weekEnd.toISOString().split('T')[0]
  ];
};

const setThisMonthFilter = () => {
  const today = new Date();
  const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
  const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  dateRangeFilter.value = [
    monthStart.toISOString().split('T')[0],
    monthEnd.toISOString().split('T')[0]
  ];
};

// 重复设置相关方法
const handleRepeatTypeChange = (type) => {
  if (type === 'none') {
    currentReminder.value.repeat_interval = 1;
    currentReminder.value.repeat_unit = 'days';
    currentReminder.value.repeat_end_type = 'never';
    currentReminder.value.repeat_count = 1;
    currentReminder.value.repeat_end_date = '';
  } else if (type === 'daily') {
    currentReminder.value.repeat_interval = 1;
    currentReminder.value.repeat_unit = 'days';
  } else if (type === 'weekly') {
    currentReminder.value.repeat_interval = 1;
    currentReminder.value.repeat_unit = 'weeks';
  } else if (type === 'monthly') {
    currentReminder.value.repeat_interval = 1;
    currentReminder.value.repeat_unit = 'months';
  } else if (type === 'yearly') {
    currentReminder.value.repeat_interval = 1;
    currentReminder.value.repeat_unit = 'months';
    currentReminder.value.repeat_interval = 12;
  }
};

const getRepeatUnitLabel = (unit) => {
  const unitMap = {
    'days': '天',
    'weeks': '周',
    'months': '月'
  };
  return unitMap[unit] || '天';
};

// 获取重复描述
const getRepeatDescription = (reminder) => {
  if (!reminder.repeat_type || reminder.repeat_type === 'none') {
    return '';
  }

  const typeMap = {
    'daily': '每日重复',
    'weekly': '每周重复',
    'monthly': '每月重复',
    'yearly': '每年重复',
    'custom': `每${reminder.repeat_interval || 1}${getRepeatUnitLabel(reminder.repeat_unit || 'days')}重复`
  };

  return typeMap[reminder.repeat_type] || '重复提醒';
};

// 创建重复提醒实例
const createRepeatInstances = async (parentReminder) => {
  const instances = [];
  const startDate = new Date(parentReminder.due_date);
  let currentDate = new Date(startDate);

  // 根据重复结束类型确定创建多少个实例
  let maxInstances = 10; // 默认最多创建10个实例

  if (currentReminder.value.repeat_end_type === 'count') {
    maxInstances = Math.min(currentReminder.value.repeat_count - 1, 50); // 减1因为主提醒已经存在
  } else if (currentReminder.value.repeat_end_type === 'date') {
    const endDate = new Date(currentReminder.value.repeat_end_date);
    maxInstances = Math.min(calculateInstancesUntilDate(startDate, endDate, parentReminder.repeat_type, parentReminder.repeat_interval), 50);
  }

  for (let i = 0; i < maxInstances; i++) {
    // 计算下一个日期
    currentDate = getNextRepeatDate(currentDate, parentReminder.repeat_type, parentReminder.repeat_interval);

    // 如果有结束日期限制，检查是否超出
    if (currentReminder.value.repeat_end_type === 'date') {
      const endDate = new Date(currentReminder.value.repeat_end_date);
      if (currentDate > endDate) break;
    }

    // 创建重复实例
    const instance = {
      ...parentReminder,
      id: undefined, // 让数据库生成新ID
      due_date: currentDate.toISOString(),
      parent_reminder_id: parentReminder.id,
      created_at: undefined,
      updated_at: undefined
    };

    instances.push(instance);
  }

  // 批量插入重复实例
  if (instances.length > 0) {
    const { error } = await supabase
      .from('reminders')
      .insert(instances);

    if (error) {
      console.error('创建重复实例失败:', error);
      ElMessage.warning('主提醒创建成功，但部分重复实例创建失败');
    }
  }
};

// 计算下一个重复日期
const getNextRepeatDate = (currentDate, repeatType, repeatInterval) => {
  const nextDate = new Date(currentDate);

  switch (repeatType) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + repeatInterval);
      break;
    case 'weekly':
      nextDate.setDate(nextDate.getDate() + (repeatInterval * 7));
      break;
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + repeatInterval);
      break;
    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + repeatInterval);
      break;
    case 'custom':
      if (currentReminder.value.repeat_unit === 'days') {
        nextDate.setDate(nextDate.getDate() + repeatInterval);
      } else if (currentReminder.value.repeat_unit === 'weeks') {
        nextDate.setDate(nextDate.getDate() + (repeatInterval * 7));
      } else if (currentReminder.value.repeat_unit === 'months') {
        nextDate.setMonth(nextDate.getMonth() + repeatInterval);
      }
      break;
  }

  return nextDate;
};

// 计算到指定日期的实例数量
const calculateInstancesUntilDate = (startDate, endDate, repeatType, repeatInterval) => {
  let count = 0;
  let currentDate = new Date(startDate);

  while (currentDate <= endDate && count < 50) { // 最多50个实例
    currentDate = getNextRepeatDate(currentDate, repeatType, repeatInterval);
    if (currentDate <= endDate) {
      count++;
    }
  }

  return count;
};

// 日历视图相关方法
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear - 5; i <= currentYear + 5; i++) {
    years.push(i);
  }
  return years;
});

const monthOptions = computed(() => {
  return [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ];
});

const formatCurrentDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long'
  });
};

const formatTooltipDate = (dateStr) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const getRemindersForDate = (dateStr) => {
  if (!dateStr) return [];
  const targetDate = new Date(dateStr).toDateString();
  return reminders.value.filter(reminder => {
    const reminderDate = new Date(reminder.due_date).toDateString();
    return reminderDate === targetDate;
  });
};

const getReminderCategoryClass = (record) => {
  const category = record.category || record.record_type;
  const categoryObj = reminderCategories.value.find(c => c.value === category);
  if (!categoryObj) return 'reminder-dot-default';

  const colorMap = {
    '#F56C6C': 'reminder-dot-danger',
    '#E6A23C': 'reminder-dot-warning',
    '#67C23A': 'reminder-dot-success',
    '#409EFF': 'reminder-dot-primary',
    '#9C27B0': 'reminder-dot-info'
  };

  return colorMap[categoryObj.color] || 'reminder-dot-default';
};

// CalendarViewPreset 组件需要的方法
const getReminderCategoryTag = (record) => {
  const category = record.category || record.record_type;
  const categoryObj = reminderCategories.value.find(c => c.value === category);
  if (!categoryObj) return 'info';

  const tagMap = {
    '#F56C6C': 'danger',
    '#E6A23C': 'warning',
    '#67C23A': 'success',
    '#409EFF': 'primary',
    '#9C27B0': 'info'
  };

  return tagMap[categoryObj.color] || 'info';
};

const getReminderCategoryLabel = (record) => {
  const category = record.category || record.record_type;
  const categoryObj = reminderCategories.value.find(c => c.value === category);
  return categoryObj ? categoryObj.label : '未分类';
};

const truncateReminderDescription = (reminder) => {
  const desc = reminder.title || reminder.notes || '';
  if (!desc) return '无描述';
  return desc.length > 20 ? desc.substring(0, 20) + '...' : desc;
};

// 日历事件处理方法
const handleCalendarDateClick = (data) => {
  selectedDate.value = new Date(data.date);
  // 可以在这里添加筛选当日提醒的逻辑
  if (data.records && data.records.length > 0) {
    console.log('当日提醒:', data.records);
  }
};

// 处理日历日期双击事件 - 新建提醒
const handleCalendarDateDoubleClick = (dateInfo) => {
  if (!currentPetId.value) {
    ElMessage.warning('请先选择一个宠物');
    return;
  }

  // 重置当前提醒并预设截止日期
  resetCurrentReminder();

  // 设置截止日期为双击的日期，时间设为当天的18:00
  const targetDate = new Date(dateInfo.date);
  targetDate.setHours(18, 0, 0, 0);
  currentReminder.value.due_date = targetDate;

  // 打开新建对话框
  isEditMode.value = false;
  showAddDialog.value = true;
};

const handleCalendarViewChange = (newView) => {
  console.log('日历视图切换:', newView);
};

const handleCalendarDateChange = (newDate) => {
  selectedDate.value = newDate;
};

// 处理悬浮面板中的提醒点击事件
const handleReminderClick = (reminder) => {
  // 滚动到对应提醒的位置
  nextTick(() => {
    // 首先确保提醒在当前筛选结果中可见
    // 根据提醒的截止日期进行筛选
    const reminderDate = new Date(reminder.due_date);
    selectedDate.value = reminderDate;

    // 等待DOM更新后再滚动
    setTimeout(() => {
      const reminderElement = document.querySelector(`[data-reminder-id="${reminder.id}"]`);
      if (reminderElement) {
        reminderElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        // 添加高亮效果
        reminderElement.classList.add('highlight-reminder');
        setTimeout(() => {
          reminderElement.classList.remove('highlight-reminder');
        }, 2000);
      } else {
        // 如果找不到具体提醒元素，滚动到提醒区域
        const remindersSection = document.querySelector('.reminders-section');
        if (remindersSection) {
          remindersSection.scrollIntoView({ behavior: 'smooth' });
        }
      }
    }, 100);
  });
};

// 处理悬浮面板中的提醒双击编辑事件
const handleReminderEdit = (reminder) => {
  editReminder(reminder);
};

// 时间线视图相关方法

const getTimelineType = (reminder) => {
  if (reminder.is_completed) return 'success';
  if (isOverdue(reminder)) return 'danger';
  if (isUrgent(reminder)) return 'warning';
  return 'primary';
};

const getTimelineColor = (reminder) => {
  if (reminder.is_completed) return '#67C23A';
  if (isOverdue(reminder)) return '#F56C6C';
  if (isUrgent(reminder)) return '#E6A23C';
  return '#409EFF';
};

// 表格视图相关方法
const handleTableSelectionChange = (selection) => {
  selectedReminders.value = selection;
};

const getTableRowClassName = ({ row }) => {
  const classes = [];
  if (row.is_completed) classes.push('completed-row');
  if (isOverdue(row)) classes.push('overdue-row');
  if (isUrgent(row)) classes.push('urgent-row');
  return classes.join(' ');
};

const isSelectedDate = (dateStr) => {
  if (!dateStr || !selectedDate.value) return false;
  const targetDate = new Date(dateStr).toDateString();
  const currentDate = new Date(selectedDate.value).toDateString();
  return targetDate === currentDate;
};

const handleDateClick = (dateStr) => {
  selectedDate.value = new Date(dateStr);
  const remindersForDate = getRemindersForDate(dateStr);
  if (remindersForDate.length > 0) {
    // 可以在这里添加显示当日提醒的逻辑
    console.log('当日提醒:', remindersForDate);
  }
};

const handleDateHover = (dateStr, event) => {
  const remindersForDate = getRemindersForDate(dateStr);
  if (remindersForDate.length === 0) return;

  tooltipDate.value = dateStr;
  tooltipReminders.value = remindersForDate;

  // 计算提示框位置
  const rect = event.target.getBoundingClientRect();
  tooltipStyle.value = {
    position: 'fixed',
    left: `${rect.left + rect.width / 2}px`,
    top: `${rect.top - 10}px`,
    transform: 'translateX(-50%) translateY(-100%)',
    zIndex: 9999
  };

  showTooltip.value = true;
};

const handleDateLeave = () => {
  showTooltip.value = false;
  tooltipReminders.value = [];
};

const previousPeriod = () => {
  const newDate = new Date(selectedDate.value);
  newDate.setMonth(newDate.getMonth() - 1);
  selectedDate.value = newDate;
  currentYear.value = newDate.getFullYear();
  currentMonth.value = newDate.getMonth();
};

const nextPeriod = () => {
  const newDate = new Date(selectedDate.value);
  newDate.setMonth(newDate.getMonth() + 1);
  selectedDate.value = newDate;
  currentYear.value = newDate.getFullYear();
  currentMonth.value = newDate.getMonth();
};

const goToToday = () => {
  const today = new Date();
  selectedDate.value = today;
  currentYear.value = today.getFullYear();
  currentMonth.value = today.getMonth();
};

const handleYearChange = (year) => {
  const newDate = new Date(selectedDate.value);
  newDate.setFullYear(year);
  selectedDate.value = newDate;
};

const handleMonthChange = (month) => {
  const newDate = new Date(selectedDate.value);
  newDate.setMonth(month);
  selectedDate.value = newDate;
};

const truncateDescription = (text) => {
  if (!text) return '';
  return text.length > 20 ? text.substring(0, 20) + '...' : text;
};

// 数据加载方法
const loadReminderCategories = async () => {
  try {
    const { data, error } = await supabase
      .from('reminder_categories')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) throw error;
    reminderCategories.value = data || [];
  } catch (error) {
    console.error('加载分类失败:', error);
    ElMessage.error('加载分类失败');
  }
};

const batchCompleteReminders = async () => {
  if (selectedReminders.value.length === 0) return;

  try {
    const ids = selectedReminders.value.map(r => r.id);
    const { error } = await supabase
      .from('reminders')
      .update({ is_completed: true })
      .in('id', ids);

    if (error) throw error;

    ElMessage.success(`成功完成 ${selectedReminders.value.length} 个提醒！`);
    selectedReminders.value = [];
    fetchReminders();
  } catch (error) {
    console.error('批量完成提醒失败:', error.message);
    ElMessage.error('批量完成提醒失败: ' + error.message);
  }
};

// 处理分类保存
const handleCategorySave = async (formData) => {
  if (!formData.label) {
    ElMessage.error('请输入分类名称');
    return;
  }

  try {
    if (isEditCategoryMode.value) {
      // 编辑模式
      const { error } = await supabase
        .from('reminder_categories')
        .update({
          label: formData.label,
          color: formData.color,
          icon: formData.icon || 'Bell',
          description: formData.description
        })
        .eq('value', newCategory.value.value);

      if (error) throw error;
      ElMessage.success('分类更新成功！');
    } else {
      // 新增模式 - 获取当前最大的sort_order
      const { data: maxOrderData } = await supabase
        .from('reminder_categories')
        .select('sort_order')
        .order('sort_order', { ascending: false })
        .limit(1);

      const nextSortOrder = maxOrderData && maxOrderData.length > 0
        ? (maxOrderData[0].sort_order || 0) + 1
        : 1;

      const categoryData = {
        value: formData.label.toLowerCase().replace(/\s+/g, '_'),
        label: formData.label,
        color: formData.color,
        icon: formData.icon || 'Bell',
        description: formData.description,
        is_system: false,
        sort_order: nextSortOrder
      };

      const { error } = await supabase
        .from('reminder_categories')
        .insert(categoryData);

      if (error) throw error;
      ElMessage.success('分类添加成功！');
    }

    showAddCategoryDialog.value = false;
    isEditCategoryMode.value = false;
    resetCategoryDialog();
    loadReminderCategories();
  } catch (error) {
    console.error('保存分类失败:', error.message);
    ElMessage.error('保存分类失败: ' + error.message);
  }
};

// 处理分类取消
const handleCategoryCancel = () => {
  showAddCategoryDialog.value = false;
  resetCategoryDialog();
};

// 编辑分类
const editCategory = (category) => {
  newCategory.value = {
    label: category.label,
    color: category.color,
    icon: category.icon || 'Bell',
    description: category.description || '',
    value: category.value // 保留原始value用于更新
  };
  isEditCategoryMode.value = true;
  showAddCategoryDialog.value = true;
};

// 删除分类
const deleteCategory = async (categoryValue) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    const { error } = await supabase
      .from('reminder_categories')
      .delete()
      .eq('value', categoryValue);

    if (error) throw error;

    ElMessage.success('分类删除成功！');

    // 如果当前选中的分类被删除，清除选择
    if (selectedCategories.value.includes(categoryValue)) {
      selectedCategories.value = selectedCategories.value.filter(val => val !== categoryValue);
    }

    loadReminderCategories();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error.message);
      ElMessage.error('删除分类失败: ' + error.message);
    }
  }
};

// 颜色选择相关方法
// 选择预设颜色
const selectPresetCategoryColor = (color) => {
  newCategory.value.color = color;
};

// 处理颜色选择器变化
const handleCategoryColorPickerChange = (color) => {
  if (color) {
    newCategory.value.color = color;
  }
};

// 获取颜色名称（用于提示）
const getCategoryColorName = (color) => {
  const colorNames = {
    '#409EFF': '蓝色',
    '#67C23A': '绿色',
    '#E6A23C': '橙色',
    '#F56C6C': '红色',
    '#909399': '灰色',
    '#9C27B0': '紫色',
    '#FF9800': '琥珀色',
    '#4CAF50': '青绿色',
    '#2196F3': '天蓝色',
    '#FF5722': '深橙色',
    '#795548': '棕色',
    '#607D8B': '蓝灰色'
  };
  return colorNames[color] || '自定义颜色';
};

// 取消对话框
const cancelCategoryDialog = () => {
  showAddCategoryDialog.value = false;
  resetCategoryDialog();
};

// 重置分类对话框
const resetCategoryDialog = () => {
  isEditCategoryMode.value = false;
  newCategory.value = {
    label: '',
    color: '#409EFF',
    icon: 'Bell',
    description: ''
  };
};

const batchDeleteReminders = async () => {
  if (selectedReminders.value.length === 0) return;
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedReminders.value.length} 个提醒吗？`, '批量删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    
    const ids = selectedReminders.value.map(r => r.id);
    const { error } = await supabase
      .from('reminders')
      .delete()
      .in('id', ids);
    
    if (error) throw error;
    
    ElMessage.success(`成功删除 ${selectedReminders.value.length} 个提醒！`);
    selectedReminders.value = [];
    fetchReminders();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除提醒失败:', error.message);
      ElMessage.error('批量删除提醒失败: ' + error.message);
    }
  }
};

const formatDisplayDate = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  const now = new Date();
  const diffTime = date - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    return '明天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === -1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays > 0 && diffDays <= 7) {
    return `${diffDays}天后 ` + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays < 0 && diffDays >= -7) {
    return `${Math.abs(diffDays)}天前 ` + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else {
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }
};

const formatFullDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  return new Date(dateTimeStr).toLocaleString('zh-CN');
};

const currentPetId = ref(localStorage.getItem('currentPetId'));

const formatDateForDisplay = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  return new Date(dateTimeStr).toLocaleString();
};

const formatDateForInput = (dateTimeStr) => {
  if (!dateTimeStr) return null;
  // ElDatePicker 需要 Date 对象或 ISO 字符串
  return new Date(dateTimeStr);
};

const fetchReminders = async () => {
  if (!currentPetId.value) {
    reminders.value = [];
    return;
  }
  try {
    const { data, error } = await supabase
      .from('reminders')
      .select('*')
      .eq('pet_id', currentPetId.value)
      .order('due_date', { ascending: true });
    if (error) throw error;
    reminders.value = data.map(r => ({ 
        ...r, 
        // due_date for display is handled in template or computed if needed
      }));
  } catch (error) {
    console.error('获取提醒事项失败:', error.message);
    ElMessage.error('获取提醒事项失败: ' + error.message);
    reminders.value = [];
  }
};

const priorityTagType = (priority) => {
  if (priority === '高') return 'danger';
  if (priority === '中') return 'warning';
  return 'info'; // 低
};

const openAddDialog = () => {
  resetCurrentReminder();
  isEditMode.value = false;
  showAddDialog.value = true;
};

const saveReminder = async () => {
  if (!currentPetId.value) {
    ElMessage.error('请先选择一个宠物');
    return;
  }
  if (!currentReminder.value.title) {
    ElMessage.error('请输入提醒标题');
    return;
  }

  const reminderData = {
    ...currentReminder.value,
    pet_id: currentPetId.value,
    due_date: currentReminder.value.due_date ? new Date(currentReminder.value.due_date).toISOString() : null,
    category: currentReminder.value.category || 'other',
    estimated_duration: currentReminder.value.estimated_duration || 0,
    advance_notice: currentReminder.value.advance_notice || 0,
    repeat_type: currentReminder.value.repeat_type || 'none',
    repeat_interval: currentReminder.value.repeat_interval || 1,
    parent_reminder_id: currentReminder.value.parent_reminder_id || null
  };
  // Remove id if it's null (for insert)
  if (reminderData.id === null) {
    delete reminderData.id;
  }

  try {
    if (isEditMode.value) {
      const { error } = await supabase
        .from('reminders')
        .update(reminderData)
        .eq('id', currentReminder.value.id);
      if (error) throw error;
      ElMessage.success('提醒更新成功！');
    } else {
      // 保存主提醒
      const { data: insertedReminder, error } = await supabase
        .from('reminders')
        .insert(reminderData)
        .select()
        .single();

      if (error) throw error;

      // 如果是重复提醒，创建后续的重复实例
      if (reminderData.repeat_type !== 'none') {
        await createRepeatInstances(insertedReminder);
      }

      ElMessage.success('提醒添加成功！');
    }
    showAddDialog.value = false;
    fetchReminders();
  } catch (error) {
    console.error('保存提醒失败:', error.message);
    ElMessage.error('保存提醒失败: ' + error.message);
  }
};

const editReminder = (reminder) => {
  isEditMode.value = true;
  currentReminder.value = { 
      ...reminder,
      due_date: formatDateForInput(reminder.due_date) // Ensure date picker gets correct format
    };
  showAddDialog.value = true;
};

const deleteReminder = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除此提醒吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    const { error } = await supabase
      .from('reminders')
      .delete()
      .eq('id', id);
    if (error) throw error;
    ElMessage.success('提醒删除成功！');
    fetchReminders();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除提醒失败:', error.message);
      ElMessage.error('删除提醒失败: ' + error.message);
    }
  }
};

// 适配器方法：处理来自 RecordViewsPreset 组件的删除事件
const handleDeleteReminder = async (reminder) => {
  await deleteReminder(reminder.id);
};

const toggleReminderStatus = async (reminder) => {
  try {
    const newStatus = !reminder.is_completed;
    const { error } = await supabase
      .from('reminders')
      .update({ is_completed: newStatus })
      .eq('id', reminder.id);
    if (error) throw error;
    
    // 更新本地数据
    reminder.is_completed = newStatus;
    ElMessage.success(`提醒状态已更新为: ${newStatus ? '已完成' : '未完成'}`);
    
    // 触发首页数据刷新
    window.dispatchEvent(new CustomEvent('reminderStatusChanged'));
  } catch (error) {
    console.error('更新提醒状态失败:', error.message);
    ElMessage.error('更新提醒状态失败: ' + error.message);
  }
};

const resetCurrentReminder = () => {
  isEditMode.value = false;
  currentReminder.value = {
    id: null,
    title: '',
    due_date: '',
    priority: '中',
    notes: '',
    category: 'other',
    estimated_duration: 0,
    advance_notice: 0,
    is_completed: false,
    repeat_type: 'none',
    repeat_interval: 1,
    repeat_unit: 'days',
    repeat_end_type: 'never',
    repeat_count: 1,
    repeat_end_date: '',
    parent_reminder_id: null
  };
};

// 拖拽事件处理
const onDragStart = () => {
  isDragging.value = true;
};

const onDragEnd = async (evt) => {
  isDragging.value = false;

  // 如果位置没有改变，不需要更新数据库
  if (evt.oldIndex === evt.newIndex) {
    return;
  }

  try {
    // 获取当前用户ID
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      ElMessage.error('用户未登录');
      return;
    }

    // 更新所有分类的排序
    const updates = reminderCategories.value.map((category, index) => ({
      id: category.id,
      sort_order: index + 1
    }));

    // 批量更新数据库
    for (const update of updates) {
      const { error } = await supabase
        .from('reminder_categories')
        .update({ sort_order: update.sort_order })
        .eq('id', update.id);

      if (error) throw error;
    }

    ElMessage.success('分类排序已保存');
  } catch (error) {
    console.error('保存分类排序失败:', error.message);
    ElMessage.error('保存分类排序失败: ' + error.message);
    // 重新获取数据以恢复原始顺序
    await loadReminderCategories();
  }
};

const handleCurrentPetChanged = (event) => {
  currentPetId.value = event.detail.petId;
  fetchReminders();
};

onMounted(() => {
  fetchReminders();
  loadReminderCategories();
  window.addEventListener('currentPetChanged', handleCurrentPetChanged, { passive: true });
});

import { onBeforeUnmount } from 'vue';
onBeforeUnmount(() => {
  window.removeEventListener('currentPetChanged', handleCurrentPetChanged);
});

</script>

<style scoped>
/* 引入类型管理对话框样式 */
@import '@/styles/type-management-dialog.css';
.reminders-view {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.no-pet-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* 页面头部样式现在由 PageHeaderBar 预设组件提供 */

.add-icon {
  font-size: 16px;
}

.btn-text {
  font-size: 14px;
}

/* 统计面板样式已移至预设样式系统 (stats-card-presets.css) */
.stats-section {
  margin-bottom: 24px;
}

/* 统计分析面板样式 */
.stats-analysis-panel {
  margin-top: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.analysis-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.toggle-stats-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409EFF;
  font-size: 14px;
  transition: all 0.3s ease;
}

.toggle-stats-btn:hover {
  color: #66b3ff;
}

.analysis-content {
  padding: 20px 0;
}

.analysis-section {
  margin-bottom: 32px;
}

.analysis-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f2ff;
}

/* 分类统计样式 */
.category-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.category-stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.category-stat-item:hover {
  background: #f0f9ff;
  border-color: #409EFF;
  transform: translateY(-2px);
}

.category-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.category-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.category-numbers {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-width: 60px;
}

.total-count {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.completion-rate {
  font-size: 12px;
  color: #909399;
}

.category-progress {
  width: 80px;
  flex-shrink: 0;
}

/* 优先级统计样式 */
.priority-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.priority-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.priority-stat-item.high {
  background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
  border: 1px solid #fecaca;
}

.priority-stat-item.medium {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  border: 1px solid #fed7aa;
}

.priority-stat-item.low {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #bbf7d0;
}

.priority-stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.priority-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.priority-count {
  font-size: 24px;
  font-weight: 700;
}

.priority-stat-item.high .priority-count {
  color: #F56C6C;
}

.priority-stat-item.medium .priority-count {
  color: #E6A23C;
}

.priority-stat-item.low .priority-count {
  color: #67C23A;
}

/* 趋势图样式 */
.trend-chart {
  background: #fafbfc;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.trend-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 8px;
  height: 120px;
  padding: 0 16px;
}

.trend-bar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.trend-bar {
  width: 100%;
  max-width: 40px;
  height: 80px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.trend-bar-fill {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  min-height: 5px;
}

.trend-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.trend-value {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-content {
  padding: 20px;
}

.filter-left {
  width: 100%;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-label-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.selected-count {
  font-size: 14px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.category-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.all-tag-standard {
  cursor: pointer;
  transition: all 0.3s ease;
}

.all-tag-standard:hover {
  transform: translateY(-1px);
}

/* 批量操作面板 */
.batch-actions-panel {
  margin-bottom: 20px;
}

.batch-actions-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}

.selected-info {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.batch-actions {
  display: flex;
  gap: 12px;
}

.complete-batch-btn,
.delete-batch-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.complete-batch-btn:hover,
.delete-batch-btn:hover {
  transform: translateY(-1px);
}

/* 视图切换动画 */
.view-fade-enter-active,
.view-fade-leave-active {
  transition: all 0.3s ease;
}

.view-fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.view-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 卡片视图 */
.cards-view {
  width: 100%;
}

.reminders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.reminder-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid;
}

.reminder-card:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.reminder-card.selected {
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.2);
}

.reminder-card.is-completed {
  background: #f8f9fa;
  opacity: 0.7;
}

.reminder-card.is-completed .reminder-title,
.reminder-card.is-completed .reminder-description {
  color: #999999;
}

.reminder-card.overdue {
  background: linear-gradient(135deg, #fff 0%, #fef0f0 100%);
}

.reminder-card.urgent {
  background: linear-gradient(135deg, #fff 0%, #fdf6ec 100%);
}

.reminder-content {
  padding: 20px;
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.reminder-category-badge {
  flex-shrink: 0;
}

.reminder-priority {
  flex-shrink: 0;
}

.reminder-body {
  margin-bottom: 16px;
}

.reminder-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.reminder-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.reminder-meta {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.reminder-date,
.reminder-duration,
.reminder-repeat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #909399;
}

.reminder-repeat {
  color: #67C23A;
  font-weight: 500;
}

.date-text {
  font-weight: 500;
}

.reminder-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.reminder-actions .completion-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.reminder-actions .completion-button:hover {
  border-color: #10b981;
  background: #f0fdf4;
  transform: scale(1.1);
}

.reminder-actions .completion-button.is-completed {
  border-color: #10b981;
  background: #ffffff;
}

.reminder-actions .completion-button.is-completed:hover {
  background: #f0fdf4;
  transform: scale(1.1);
}

.reminder-actions .check-icon {
  font-size: 14px;
  font-weight: bold;
  color: #10b981;
  animation: checkmark-appear 0.3s ease-in-out;
}

.reminder-actions .delete-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: 2px solid #d1d5db;
  color: #6b7280;
  cursor: pointer;
}

.reminder-actions .delete-button:hover {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
}

/* 列表视图 */
.list-view {
  width: 100%;
}

.list-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.reminders-list {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.reminder-list-item {
  padding: 16px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
  background: #fafafa;
}

.reminder-list-item:hover {
  background: #f0f9ff;
  border-color: #409EFF;
}

.reminder-list-item.selected {
  background: #e6f7ff;
  border-color: #409EFF;
}

.reminder-list-item.completed {
  opacity: 0.7;
}

.reminder-list-item.overdue {
  background: #fef0f0;
  border-color: #F56C6C;
}

.list-item-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 20px;
}

.list-item-main {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0;
  gap: 12px;
}

.list-item-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-item-badges {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
}

.list-item-meta {
  display: flex;
  gap: 16px;
  align-items: center;
  font-size: 13px;
  color: #909399;
}

.list-item-date,
.list-item-duration {
  display: flex;
  align-items: center;
  gap: 4px;
}

.list-item-actions {
  display: flex;
  gap: 8px;
  align-items: flex-start;
  flex-shrink: 0;
  padding-top: 4px;
}

/* 对话框样式 */
.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 拖拽相关样式 - 与HealthRecordsView.vue保持完全一致 */
.draggable-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.draggable-tag {
  cursor: move;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.draggable-tag.is-dragging {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.draggable-tag.is-dragging .tag-actions {
  display: none !important;
}

/* 拖拽状态样式 */
.is-dragging {
  transform: rotate(5deg) scale(1.05) !important;
  z-index: 1000 !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  cursor: grabbing !important;
}

/* 拖拽时的幽灵元素样式 */
.ghost {
  opacity: 0.5;
  background: linear-gradient(135deg, #c8ebfb 0%, #e3f2fd 100%) !important;
  border: 2px dashed #409EFF !important;
  transform: scale(0.95);
  color: #409EFF !important;
}

/* 拖拽时的占位符样式 */
.sortable-chosen {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  z-index: 999;
}

.sortable-drag {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

/* 拖拽项目包装器 */
.draggable-item {
  display: inline-block;
  cursor: move;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.draggable-item:hover {
  cursor: grab;
}

.draggable-item:active {
  cursor: grabbing;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reminders-view {
    padding: 16px;
  }

  /* 页面头部响应式样式现在由 PageHeaderBar 预设组件提供 */

  .filter-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .reminders-grid {
    grid-template-columns: 1fr;
  }



  .batch-actions-content {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .list-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .list-item-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .list-item-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .reminders-view {
    padding: 12px;
  }

  .category-tags-container {
    gap: 8px;
  }

  .reminder-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 日历视图样式 */
.calendar-view {
  width: 100%;
}

.calendar-card {
  border-radius: 12px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-bottom: 1px solid #e5e7eb;
}

.calendar-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.calendar-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.current-date-info {
  font-size: 14px;
  color: #909399;
}

.calendar-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.navigation-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.today-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  background: linear-gradient(135deg, #67C23A, #85CE61);
  border: none;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.today-btn:hover {
  background: #85CE61;
  border-color: #85CE61;
}

.today-text {
  margin-left: 4px;
}

.date-selectors {
  display: flex;
  align-items: center;
  gap: 8px;
}

.year-selector,
.month-selector {
  width: 80px;
}

.calendar-content {
  padding: 20px;
}

.reminders-calendar {
  margin-top: 16px;
}

/* 隐藏Element Plus日历的默认导航按钮 */
.reminders-calendar :deep(.el-calendar__header) {
  display: none;
}

.reminders-calendar :deep(.el-calendar__body) {
  padding: 0;
}

.calendar-cell {
  position: relative;
  width: 100%;
  height: 80px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  border-radius: 6px;
}

.calendar-cell:hover {
  background: #f0f9ff;
  transform: scale(1.02);
}

.calendar-cell.has-reminders {
  background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
  border: 1px solid #e1ecff;
}

.calendar-cell.selected-date {
  background: linear-gradient(135deg, #409EFF, #66b3ff);
  color: white;
}

.date-number {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.calendar-cell.selected-date .date-number {
  color: white;
}

.reminder-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  justify-content: center;
  max-width: 100%;
}

.reminder-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.reminder-dot-primary {
  background: #409EFF;
}

.reminder-dot-success {
  background: #67C23A;
}

.reminder-dot-warning {
  background: #E6A23C;
}

.reminder-dot-danger {
  background: #F56C6C;
}

.reminder-dot-info {
  background: #9C27B0;
}

.reminder-dot-default {
  background: #909399;
}

.more-indicator {
  font-size: 10px;
  color: #909399;
  font-weight: 500;
  margin-left: 2px;
}

/* 提示框样式 */
.reminders-tooltip {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
  padding: 0;
  min-width: 280px;
  max-width: 400px;
  z-index: 9999;
  animation: tooltipFadeIn 0.3s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) scale(1);
  }
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f5f7fa;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 12px 12px 0 0;
}

.tooltip-content {
  padding: 12px 20px 16px;
  max-height: 300px;
  overflow-y: auto;
}

.tooltip-reminder {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
}

.tooltip-reminder:last-child {
  border-bottom: none;
}

.reminder-desc {
  font-size: 14px;
  color: #606266;
  flex: 1;
}

.more-reminders-hint {
  text-align: center;
  color: #909399;
  font-size: 12px;
  font-style: italic;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f5f7fa;
}

/* 时间线视图样式 */
.timeline-view {
  width: 100%;
}

.timeline-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.timeline-card {
  margin-bottom: 0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.timeline-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.timeline-content {
  padding: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  flex-wrap: wrap;
  gap: 8px;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  flex: 1;
}

.timeline-badges {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.timeline-body {
  margin-bottom: 16px;
}

.timeline-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.timeline-meta {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  font-size: 13px;
  color: #909399;
}

.timeline-duration,
.timeline-repeat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.timeline-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 表格视图样式 */
.table-view {
  width: 100%;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.table-title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.completed-text {
  text-decoration: line-through;
  color: #909399;
}

.status-tag {
  margin-left: 8px;
}

.table-date-cell {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
}

.table-notes {
  color: #606266;
  font-size: 13px;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.table-actions {
  display: flex;
  gap: 4px;
}

.text-muted {
  color: #c0c4cc;
  font-style: italic;
}

/* 表格行样式 */
:deep(.completed-row) {
  background-color: #f0f9ff;
}

:deep(.overdue-row) {
  background-color: #fef0f0;
}

:deep(.urgent-row) {
  background-color: #fdf6ec;
}

:deep(.completed-row:hover) {
  background-color: #e1f3ff !important;
}

:deep(.overdue-row:hover) {
  background-color: #fde2e2 !important;
}

:deep(.urgent-row:hover) {
  background-color: #faecd8 !important;
}

/* 简约时间线样式 */
.simple-timeline {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.timeline-container {
  padding: 24px;
}

/* 日期分组 */
.timeline-group {
  margin-bottom: 32px;
}

.timeline-group:last-child {
  margin-bottom: 0;
}

.group-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.date-label {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.date-text {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.date-sub {
  font-size: 13px;
  color: #6b7280;
}

/* 时间线项目 */
.group-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fafbfc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 60px;
}

.timeline-item:hover {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.timeline-item.is-completed {
  opacity: 0.6;
  background: #f0f9ff;
}

.timeline-item.is-overdue {
  border-left: 3px solid #ef4444;
  background: #fef2f2;
}

.timeline-item.is-urgent {
  border-left: 3px solid #f59e0b;
  background: #fffbeb;
}

.timeline-item.is-completed {
  background: #f8f9fa;
  opacity: 0.7;
}

.timeline-item.is-completed .item-description,
.timeline-item.is-completed .category-name {
  color: #999999;
}

/* 左侧完成状态按钮 */
.item-completion-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 12px;
}

/* 主要内容 */
.item-content {
  flex: 1;
  min-width: 0;
  padding: 0 8px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-time {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.item-category {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.category-name {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.item-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
}

.completion-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.completion-button:hover {
  border-color: #10b981;
  background: #f0fdf4;
  transform: scale(1.1);
}

.completion-button.is-completed {
  border-color: #10b981;
  background: #ffffff;
}

.completion-button.is-completed:hover {
  background: #f0fdf4;
  transform: scale(1.1);
}

.check-icon {
  font-size: 14px;
  font-weight: bold;
  color: #10b981;
  animation: checkmark-appear 0.3s ease-in-out;
}

@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 右侧删除按钮 */
.item-actions-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: 12px;
}

.delete-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: 2px solid #d1d5db;
  color: #6b7280;
}

.delete-button:hover {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-overdue,
.status-urgent {
  position: relative;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-overdue .pulse-dot {
  background: #ef4444;
}

.status-urgent .pulse-dot {
  background: #f59e0b;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* 简化空状态 */
.simple-empty {
  padding: 60px 24px;
  text-align: center;
  background: #fafbfc;
}

.empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-text h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-text p {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .simple-timeline {
    border-radius: 8px;
    margin: 0 -8px;
  }

  .timeline-container {
    padding: 16px;
  }

  .timeline-group {
    margin-bottom: 24px;
  }

  .group-header {
    margin-bottom: 12px;
  }

  .date-text {
    font-size: 15px;
  }

  .date-sub {
    font-size: 12px;
  }

  .group-items {
    gap: 8px;
  }

  .timeline-item {
    padding: 12px;
    gap: 12px;
  }

  .item-time {
    min-width: 50px;
  }

  .time-text {
    font-size: 12px;
  }

  .time-dot {
    width: 6px;
    height: 6px;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-title {
    font-size: 14px;
  }

  .item-actions {
    opacity: 1;
    width: 100%;
    justify-content: flex-end;
  }

  .item-description {
    font-size: 13px;
  }

  .item-meta {
    gap: 12px;
    font-size: 11px;
  }

  .item-completion {
    margin-left: 8px;
    gap: 6px;
  }

  .completion-button {
    width: 20px;
    height: 20px;
  }

  .check-icon {
    font-size: 10px;
  }

  .simple-empty {
    padding: 40px 16px;
  }

  .empty-icon {
    font-size: 40px;
  }

  .empty-text h3 {
    font-size: 16px;
  }

  .empty-text p {
    font-size: 13px;
  }

  /* 旧时间线响应式 */
  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .timeline-badges {
    width: 100%;
  }

  .timeline-meta {
    flex-direction: column;
    gap: 8px;
  }

  .timeline-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .table-actions {
    flex-direction: column;
    gap: 2px;
  }
}
</style>