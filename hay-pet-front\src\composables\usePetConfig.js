import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { generatePetConfig, validateConfig } from '@/utils/petConfigScanner'

// 全局状态管理
const globalState = {
  config: ref(null),
  isLoading: ref(false),
  error: ref(null),
  lastScanTime: ref(null),
  cacheKey: ref(null),
  scanCount: ref(0)
}

// 缓存管理
const CACHE_PREFIX = 'hay_pet_config_'
const CACHE_DURATION = 24 * 60 * 60 * 1000 // 24小时
const MAX_CACHE_SIZE = 5 // 最多保留5个缓存版本

/**
 * 高级宠物配置管理组合式函数
 * @param {Object} options - 配置选项
 * @returns {Object} 配置管理对象
 */
export const usePetConfig = (options = {}) => {
  const {
    enableCache = true,
    autoRefresh = true,
    refreshInterval = 30000, // 30秒检查一次
    enableHotReload = true,
    onConfigChange = null,
    onError = null
  } = options

  // 本地状态
  const localError = ref(null)
  const isRefreshing = ref(false)
  const refreshTimer = ref(null)

  // 计算属性
  const config = computed(() => globalState.config.value)
  const isLoading = computed(() => globalState.isLoading.value || isRefreshing.value)
  const error = computed(() => globalState.error.value || localError.value)
  const lastScanTime = computed(() => globalState.lastScanTime.value)
  const scanCount = computed(() => globalState.scanCount.value)

  // 宠物类型列表
  const petTypes = computed(() => {
    return config.value?.petTypes || []
  })

  // 扫描信息
  const scanInfo = computed(() => {
    return config.value?.scanInfo || {
      totalTypes: 0,
      totalBreeds: 0,
      supportedExtensions: [],
      typeFiles: 0,
      breedFiles: 0
    }
  })

  // 配置统计
  const stats = computed(() => {
    const types = petTypes.value
    const totalBreeds = types.reduce((sum, type) => sum + (type.breeds?.length || 0), 0)
    
    return {
      totalTypes: types.length,
      totalBreeds,
      averageBreedsPerType: types.length > 0 ? (totalBreeds / types.length).toFixed(1) : 0,
      typesWithBreeds: types.filter(t => t.breeds?.length > 0).length,
      typesWithoutBreeds: types.filter(t => !t.breeds?.length).length,
      mostPopularType: getMostPopularType(types),
      supportedExtensions: config.value?.scanInfo?.supportedFormats || []
    }
  })

  // 是否有效配置
  const isValidConfig = computed(() => {
    if (!config.value) return false
    const validation = validateConfig(config.value)
    return validation.isValid && petTypes.value.length > 0
  })

  // 缓存状态
  const cacheStatus = computed(() => {
    const cacheKey = globalState.cacheKey.value
    if (!cacheKey || !enableCache) return null
    
    const cached = getCachedConfig(cacheKey)
    return {
      exists: !!cached,
      key: cacheKey,
      timestamp: cached?.timestamp,
      isExpired: cached ? isExpired(cached.timestamp) : true,
      size: cached ? JSON.stringify(cached).length : 0
    }
  })

  /**
   * 加载配置
   * @param {boolean} forceRefresh - 强制刷新
   * @returns {Promise<Object>} 配置对象
   */
  const loadConfig = async (forceRefresh = false) => {
    try {
      globalState.isLoading.value = true
      globalState.error.value = null
      localError.value = null

      // 尝试从缓存加载
      if (enableCache && !forceRefresh) {
        const cached = loadFromCache()
        if (cached) {
          globalState.config.value = cached
          globalState.lastScanTime.value = cached.lastScanTime
          globalState.cacheKey.value = cached.metadata?.cacheKey
          return cached
        }
      }

      // 生成新配置
      const newConfig = await generatePetConfig()
      
      // 验证配置
      const validation = validateConfig(newConfig)
      
      if (!validation.isValid) {
        throw new Error(`配置验证失败: ${validation.errors?.join(', ')}`)
      }

      // 添加元数据
      newConfig.metadata = {
        generatedBy: 'usePetConfig',
        scanPath: '/src/assets/images/pets/',
        cacheKey: `${CACHE_PREFIX}${Date.now()}`,
        validUntil: Date.now() + CACHE_DURATION
      }

      // 更新全局状态
      globalState.config.value = newConfig
      globalState.lastScanTime.value = newConfig.lastScanTime
      globalState.cacheKey.value = newConfig.metadata.cacheKey
      globalState.scanCount.value += 1

      // 保存到缓存
      if (enableCache) {
        saveToCache(newConfig)
      }

      // 触发配置变更回调
      if (onConfigChange) {
        await nextTick()
        onConfigChange(newConfig)
      }

      ElMessage.success(`配置加载成功！发现 ${newConfig.totalTypes} 个类型，${newConfig.totalBreeds} 个品种`)
      return newConfig
    } catch (err) {
      const errorMsg = `加载宠物配置失败: ${err.message}`
      globalState.error.value = errorMsg
      
      if (onError) {
        onError(err)
      }
      
      ElMessage.error(errorMsg)
      console.error(errorMsg, err)
      throw err
    } finally {
      globalState.isLoading.value = false
    }
  }

  /**
   * 刷新配置
   * @returns {Promise<Object>} 配置对象
   */
  const refreshConfig = async () => {
    try {
      isRefreshing.value = true
      return await loadConfig(true)
    } finally {
      isRefreshing.value = false
    }
  }

  /**
   * 搜索宠物
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Object} 搜索结果
   */
  const searchPets = (query, options = {}) => {
    const {
      searchTypes = true,
      searchBreeds = true,
      fuzzyMatch = true,
      maxResults = 50
    } = options

    if (!query || !config.value) {
      return { types: [], breeds: [], total: 0 }
    }

    const normalizedQuery = query.toLowerCase().trim()
    const results = { types: [], breeds: [], total: 0 }

    petTypes.value.forEach(type => {
      // 搜索类型
      if (searchTypes && matchesQuery(type, normalizedQuery, fuzzyMatch)) {
        results.types.push({
          ...type,
          matchScore: calculateMatchScore(type, normalizedQuery)
        })
      }

      // 搜索品种
      if (searchBreeds && type.breeds) {
        type.breeds.forEach(breed => {
          if (matchesQuery(breed, normalizedQuery, fuzzyMatch)) {
            results.breeds.push({
              ...breed,
              typeName: type.name,
              typeId: type.id,
              matchScore: calculateMatchScore(breed, normalizedQuery)
            })
          }
        })
      }
    })

    // 排序结果
    results.types.sort((a, b) => b.matchScore - a.matchScore)
    results.breeds.sort((a, b) => b.matchScore - a.matchScore)

    // 限制结果数量
    if (maxResults > 0) {
      results.types = results.types.slice(0, maxResults)
      results.breeds = results.breeds.slice(0, maxResults)
    }

    results.total = results.types.length + results.breeds.length
    return results
  }

  /**
   * 根据ID获取类型
   * @param {string} typeId - 类型ID
   * @returns {Object|null} 类型对象
   */
  const getTypeById = (typeId) => {
    return petTypes.value.find(type => type.id === typeId) || null
  }

  /**
   * 根据ID获取品种
   * @param {string} breedId - 品种ID
   * @returns {Object|null} 品种对象
   */
  const getBreedById = (breedId) => {
    for (const type of petTypes.value) {
      if (type.breeds) {
        const breed = type.breeds.find(b => b.id === breedId)
        if (breed) {
          return { ...breed, typeName: type.name, typeId: type.id }
        }
      }
    }
    return null
  }

  /**
   * 获取类型的品种列表
   * @param {string} typeId - 类型ID
   * @returns {Array} 品种列表
   */
  const getBreedsByType = (typeId) => {
    const type = getTypeById(typeId)
    return type?.breeds || []
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    if (!enableCache) return
    
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(CACHE_PREFIX))
      keys.forEach(key => localStorage.removeItem(key))
      ElMessage.success(`已清除 ${keys.length} 个缓存项`)

    } catch (error) {
      console.warn('清除缓存失败:', error)
      ElMessage.warning('清除缓存失败')
    }
  }

  /**
   * 获取缓存信息
   * @returns {Object} 缓存信息
   */
  const getCacheInfo = () => {
    if (!enableCache) return null
    
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(CACHE_PREFIX))
      const caches = keys.map(key => {
        try {
          const data = JSON.parse(localStorage.getItem(key))
          return {
            key,
            timestamp: data.timestamp,
            size: JSON.stringify(data).length,
            isExpired: data ? isExpired(data.timestamp) : true,
            version: data.config?.version
          }
        } catch {
          return null
        }
      }).filter(Boolean)
      
      return {
        total: caches.length,
        totalSize: caches.reduce((sum, cache) => sum + cache.size, 0),
        expired: caches.filter(cache => cache.isExpired).length,
        caches: caches.sort((a, b) => b.timestamp - a.timestamp)
      }
    } catch (error) {
      console.warn('获取缓存信息失败:', error)
      return null
    }
  }

  // 内部辅助函数
  const loadFromCache = () => {
    if (!enableCache) return null
    
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(CACHE_PREFIX))
      
      for (const key of keys) {
        try {
          const cached = JSON.parse(localStorage.getItem(key))
          if (cached && !isExpired(cached.timestamp)) {
            return cached.config
          }
        } catch {
          // 删除损坏的缓存
          localStorage.removeItem(key)
        }
      }
    } catch (error) {
      console.warn('从缓存加载失败:', error)
    }
    return null
  }

  const saveToCache = (config) => {
    if (!enableCache) return
    
    try {
      const cacheKey = config.metadata?.cacheKey || `${CACHE_PREFIX}${Date.now()}`
      const cacheData = {
        config,
        timestamp: Date.now(),
        version: '2.0.0'
      }
      
      localStorage.setItem(cacheKey, JSON.stringify(cacheData))
      
      // 清理过期缓存
      cleanupCache()
    } catch (error) {
      console.warn('保存到缓存失败:', error)
    }
  }

  const getCachedConfig = (cacheKey) => {
    if (!enableCache || !cacheKey) return null
    
    try {
      const cached = localStorage.getItem(cacheKey)
      return cached ? JSON.parse(cached) : null
    } catch {
      return null
    }
  }

  const isExpired = (timestamp) => {
    return Date.now() - timestamp > CACHE_DURATION
  }

  const cleanupCache = () => {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(CACHE_PREFIX))
      const caches = keys.map(key => {
        try {
          const data = JSON.parse(localStorage.getItem(key))
          return { key, timestamp: data.timestamp }
        } catch {
          return { key, timestamp: 0 } // 损坏的缓存，视为过期
        }
      })
      
      // 删除过期缓存
      caches.forEach(cache => {
        if (isExpired(cache.timestamp)) {
          localStorage.removeItem(cache.key)
        }
      })
      
      // 保留最新的几个缓存
      const validCaches = caches.filter(cache => !isExpired(cache.timestamp))
      if (validCaches.length > MAX_CACHE_SIZE) {
        validCaches
          .sort((a, b) => a.timestamp - b.timestamp)
          .slice(0, validCaches.length - MAX_CACHE_SIZE)
          .forEach(cache => localStorage.removeItem(cache.key))
      }
    } catch (error) {
      console.warn('清理缓存失败:', error)
    }
  }

  const matchesQuery = (item, query, fuzzyMatch) => {
    const searchFields = [item.name, item.id, item.englishName, item.displayName]
      .filter(Boolean)
      .map(field => field.toLowerCase())
    
    if (fuzzyMatch) {
      return searchFields.some(field => field.includes(query))
    } else {
      return searchFields.some(field => field === query)
    }
  }

  const calculateMatchScore = (item, query) => {
    let score = 0
    const name = item.name?.toLowerCase() || ''
    const id = item.id?.toLowerCase() || ''
    const englishName = item.englishName?.toLowerCase() || ''
    const displayName = item.displayName?.toLowerCase() || ''
    
    // 名称完全匹配
    if (name === query) score += 100
    else if (name.startsWith(query)) score += 50
    else if (name.includes(query)) score += 20

    // 英文名称完全匹配
    if (englishName === query) score += 90
    else if (englishName.startsWith(query)) score += 45
    else if (englishName.includes(query)) score += 18

    // 显示名称完全匹配
    if (displayName === query) score += 80
    else if (displayName.startsWith(query)) score += 40
    else if (displayName.includes(query)) score += 16

    // ID 匹配
    if (id.includes(query)) score += 10
    
    return score
  }

  const getMostPopularType = (types) => {
    if (!types.length) return null
    
    return types.reduce((most, current) => {
      const currentBreeds = current.breeds?.length || 0
      const mostBreeds = most.breeds?.length || 0
      return currentBreeds > mostBreeds ? current : most
    })
  }

  // 设置自动刷新
  const setupAutoRefresh = () => {
    if (!autoRefresh || refreshTimer.value) return
    
    refreshTimer.value = setInterval(async () => {
      try {
        // 检查是否需要刷新
        const lastScan = globalState.lastScanTime.value
        if (lastScan && Date.now() - new Date(lastScan).getTime() > refreshInterval) {
          await refreshConfig()
        }
      } catch (error) {
        console.warn('自动刷新失败:', error)
      }
    }, refreshInterval)
  }

  // 清理定时器
  const cleanup = () => {
    if (refreshTimer.value) {
      clearInterval(refreshTimer.value)
      refreshTimer.value = null
    }
  }

  // 监听配置变化
  watch(
    () => config.value,
    (newConfig, oldConfig) => {
      if (newConfig && newConfig !== oldConfig) {

      }
    },
    { deep: true }
  )

  // 组件挂载时初始化
  onMounted(() => {
    // 如果没有配置，自动加载
    if (!config.value) {
      loadConfig()
    }
    
    // 设置自动刷新
    setupAutoRefresh()
  })

  // 返回API
  return {
    // 状态
    config,
    isLoading,
    error,
    lastScanTime,
    scanCount,
    petTypes,
    scanInfo,
    stats,
    isValidConfig,
    cacheStatus,
    
    // 方法
    loadConfig,
    refreshConfig,
    searchPets,
    getTypeById,
    getBreedById,
    getBreedsByType,
    clearCache,
    getCacheInfo,
    cleanup
  }
}

/**
 * 全局单例宠物配置
 * @returns {Object} 全局配置对象
 */
let globalPetConfigInstance = null

export const useGlobalPetConfig = () => {
  if (!globalPetConfigInstance) {
    globalPetConfigInstance = usePetConfig({
      enableCache: true,
      autoRefresh: true,
      refreshInterval: 60000, // 1分钟
      enableHotReload: true
    })
    // 首次加载配置
    globalPetConfigInstance.loadConfig()
  }
  return globalPetConfigInstance
}