-- 为照片表添加文件时间字段
-- 请在Supabase SQL编辑器中执行此脚本

-- 添加file_time字段
ALTER TABLE public.photos ADD COLUMN IF NOT EXISTS file_time TIMESTAMPTZ;

-- 添加索引以优化按文件时间排序的查询性能
CREATE INDEX IF NOT EXISTS idx_photos_file_time ON public.photos(file_time);

-- 添加复合索引以优化按宠物ID和文件时间的查询
CREATE INDEX IF NOT EXISTS idx_photos_pet_file_time ON public.photos(pet_id, file_time DESC);

-- 验证字段是否添加成功
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'photos' AND column_name = 'file_time';

-- 完成提示
-- 执行完成后，photos表将包含以下新字段：
-- - file_time: TIMESTAMPTZ - 存储从文件名提取的时间戳或文件的lastModified时间
-- 
-- 索引说明：
-- - idx_photos_file_time: 优化按文件时间排序
-- - idx_photos_pet_file_time: 优化按宠物ID筛选并按文件时间排序的查询
