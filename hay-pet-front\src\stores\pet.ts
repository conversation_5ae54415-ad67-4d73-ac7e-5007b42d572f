import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/utils/supabase';

export interface Pet {
  id: string;
  name: string;
  type: string; // 宠物类型，例如：猫、狗
  species?: string; // 宠物品种
  birth_date?: string;
  gender?: string;
  weight?: number;
  latest_weight_kg?: number;
  avatar_url?: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export const usePetStore = defineStore('pet', () => {
  // 状态
  const pets = ref<Pet[]>([]);
  const currentPetId = ref<string | null>(localStorage.getItem('currentPetId'));
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性
  const currentPet = computed(() => {
    if (!currentPetId.value) return null;
    return pets.value.find(pet => pet.id === currentPetId.value) || null;
  });

  const hasPets = computed(() => pets.value.length > 0);

  // 操作方法
  const fetchPets = async () => {
    try {
      loading.value = true;
      error.value = null;
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('用户未登录');
      }

      // 获取宠物信息并关联最新体重记录
      const { data, error: fetchError } = await supabase
        .from('pets')
        .select(`
          *,
          weight_records(
            weight,
            date
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: true });

      if (fetchError) throw fetchError;

      // 处理数据，添加最新体重信息
      const processedPets = (data || []).map(pet => {
        const weightRecords = pet.weight_records || [];
        // 按日期排序，获取最新的体重记录
        const latestWeightRecord = weightRecords
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
        
        return {
          ...pet,
          latest_weight_kg: latestWeightRecord?.weight || null, // 存储的是克，字段名保持兼容性
          weight_records: undefined // 移除临时的weight_records字段
        };
      });

      pets.value = processedPets;
      
      // 如果没有当前宠物ID，设置第一个宠物为当前宠物
      if (!currentPetId.value && pets.value.length > 0) {
        currentPetId.value = pets.value[0].id;
        localStorage.setItem('currentPetId', pets.value[0].id);
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取宠物列表失败';
      console.error('获取宠物列表失败:', err);
    } finally {
      loading.value = false;
    }
  };

  const setCurrentPet = (petId: string) => {
    if (pets.value.find(pet => pet.id === petId)) {
      currentPetId.value = petId;
      localStorage.setItem('currentPetId', petId);
      // 触发全局事件，通知其他组件宠物已切换
      window.dispatchEvent(new CustomEvent('currentPetChanged', { detail: petId }));
    }
  };

  const addPet = (pet: Pet) => {
    pets.value.push(pet);
    if (!currentPetId.value) {
      currentPetId.value = pet.id;
      localStorage.setItem('currentPetId', pet.id);
    }
  };

  const updatePet = (petId: string, updates: Partial<Pet>) => {
    const index = pets.value.findIndex(pet => pet.id === petId);
    if (index !== -1) {
      pets.value[index] = { ...pets.value[index], ...updates };
    }
  };

  const removePet = (petId: string) => {
    const index = pets.value.findIndex(pet => pet.id === petId);
    if (index !== -1) {
      pets.value.splice(index, 1);
      // 如果删除的是当前宠物，切换到第一个宠物
      if (currentPetId.value === petId) {
        currentPetId.value = pets.value.length > 0 ? pets.value[0].id : null;
        if (currentPetId.value) {
          localStorage.setItem('currentPetId', currentPetId.value);
        } else {
          localStorage.removeItem('currentPetId');
        }
      }
    }
  };

  const clearPets = () => {
    pets.value = [];
    currentPetId.value = null;
    localStorage.removeItem('currentPetId');
  };

  return {
    // 状态
    pets,
    currentPetId,
    loading,
    error,
    
    // 计算属性
    currentPet,
    hasPets,
    
    // 方法
    fetchPets,
    setCurrentPet,
    addPet,
    updatePet,
    removePet,
    clearPets
  };
});