/**
 * 颜色选择器组件预设样式系统
 * 基于优化后的颜色选择器设计，提供统一的颜色选择交互体验
 */

/* ========== 基础颜色选择器样式 ========== */

.color-picker-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.color-selection-area {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-card);
  padding: var(--spacing-5);
  border: 2px solid var(--color-border-light);
  margin-top: var(--spacing-3);
}

.unified-color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: var(--spacing-3);
  max-width: 300px;
}

/* ========== 颜色按钮基础样式 ========== */

.color-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all var(--duration-base) ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  background: none;
  outline: none;
}

.color-button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* ========== 颜色按钮交互状态 ========== */

.color-button:hover {
  transform: scale(1.1);
  box-shadow: 
    0 6px 20px rgba(0, 0, 0, 0.25),
    0 0 0 3px rgba(64, 158, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  filter: brightness(1.1) saturate(1.1);
}

.color-button:active {
  transform: scale(1.05);
  transition: transform 0.1s ease;
}

.color-button.selected {
  /* 移除外部蓝色描边，只保留基础样式 */
  box-shadow: var(--shadow-sm);
}

/* ========== 选中状态指示器 ========== */

.selected-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation: selectedPulse var(--duration-base) ease-out;
}

.selected-indicator .check-icon {
  color: white;
  font-size: 22px;
  font-weight: bold;
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.6),
    0 0 8px rgba(255, 255, 255, 0.4);
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5));
  animation: checkIconBounce 0.4s ease-out 0.1s both;
}

/* ========== 选中状态动画 ========== */

@keyframes selectedPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkIconBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  60% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ========== 颜色按钮尺寸变体 ========== */

.color-button-sm {
  width: 32px;
  height: 32px;
}

.color-button-sm .selected-indicator .check-icon {
  font-size: 16px;
}

.color-button-md {
  /* 默认尺寸，使用基础样式 */
}

.color-button-lg {
  width: 48px;
  height: 48px;
}

.color-button-lg .selected-indicator .check-icon {
  font-size: 26px;
}

.color-button-xl {
  width: 56px;
  height: 56px;
}

.color-button-xl .selected-indicator .check-icon {
  font-size: 30px;
}

/* ========== 颜色网格布局变体 ========== */

.color-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.color-grid-5 {
  grid-template-columns: repeat(5, 1fr);
}

.color-grid-6 {
  /* 默认布局，使用基础样式 */
}

.color-grid-8 {
  grid-template-columns: repeat(8, 1fr);
}

/* ========== 颜色选择器主题变体 ========== */

.color-picker-compact .color-selection-area {
  padding: var(--spacing-3);
  border: 1px solid var(--color-border-base);
}

.color-picker-compact .unified-color-grid {
  gap: var(--spacing-2);
}

.color-picker-minimal .color-selection-area {
  background: transparent;
  border: none;
  padding: var(--spacing-2);
}

/* ========== 响应式设计 ========== */

/* 平板设备 */
@media (max-width: 992px) {
  .unified-color-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: var(--spacing-2);
  }
  
  .color-button {
    width: 36px;
    height: 36px;
  }
  
  .color-button-lg {
    width: 44px;
    height: 44px;
  }
}

/* 手机设备 */
@media (max-width: 768px) {
  .unified-color-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-2);
  }
  
  .color-button {
    width: 32px;
    height: 32px;
  }
  
  .color-button-lg {
    width: 40px;
    height: 40px;
  }
  
  .selected-indicator {
    border: 1.5px solid rgba(255, 255, 255, 0.8);
    box-shadow: 
      0 3px 8px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }
  
  .selected-indicator .check-icon {
    font-size: 18px;
  }
  
  .color-selection-area {
    padding: var(--spacing-3);
  }
}

/* ========== 工具类 ========== */

/* 快速应用颜色选择器样式的工具类 */
.color-picker { @apply color-picker-container; }
.color-grid { @apply unified-color-grid color-grid-6; }
.color-btn { @apply color-button color-button-md; }
.color-btn-sm { @apply color-button color-button-sm; }
.color-btn-lg { @apply color-button color-button-lg; }

/* 组合类 */
.color-picker-standard { @apply color-picker-container; }
.color-picker-standard .color-selection-area { @apply color-selection-area; }
.color-picker-standard .unified-color-grid { @apply unified-color-grid; }
