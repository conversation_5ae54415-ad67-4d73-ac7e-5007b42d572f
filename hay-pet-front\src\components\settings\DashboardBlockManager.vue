<template>
  <div class="dashboard-block-manager">
    <div class="manager-header">
      <h3 class="manager-title">首页板块设置</h3>
      <p class="manager-description">拖拽调整板块顺序，点击开关控制显示/隐藏</p>
    </div>
    
    <div class="block-list">
      <draggable 
        v-model="localBlocks" 
        item-key="id"
        handle=".block-drag-handle"
        @end="onDragEnd"
        :animation="200"
        ghost-class="ghost-item"
        chosen-class="chosen-item"
        drag-class="drag-item"
      >
        <template #item="{ element: block, index }">
          <div 
            class="block-item"
            :class="{ 'block-disabled': !block.visible }"
          >
            <div class="block-drag-handle">
              <el-icon><Rank /></el-icon>
            </div>
            
            <div class="block-info">
              <div class="block-header">
                <span class="block-title">{{ block.name }}</span>
                <el-switch 
                  v-model="block.visible"
                  @change="updateBlockVisibility(block.id, $event)"
                  :disabled="block.required"
                />
              </div>
              <p class="block-description">{{ block.description }}</p>
              <div class="block-meta">
                <el-tag v-if="block.required" type="info" size="small">必需板块</el-tag>
                <span class="block-order">排序: {{ index + 1 }}</span>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>
    
    <div class="manager-actions">
      <el-button @click="resetToDefault" :loading="resetting">
        <el-icon><RefreshLeft /></el-icon>
        恢复默认设置
      </el-button>
      <el-button type="primary" @click="saveSettings" :loading="saving">
        <el-icon><Check /></el-icon>
        保存设置
      </el-button>
    </div>
    
    <div class="preview-section">
      <el-divider>
        <span class="preview-title">预览效果</span>
      </el-divider>
      <div class="preview-blocks">
        <div 
          v-for="block in visibleBlocks" 
          :key="block.id"
          class="preview-block"
        >
          <div class="preview-block-header">
            <span class="preview-block-title">{{ block.name }}</span>
            <el-tag type="success" size="small">显示</el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { Rank, RefreshLeft, Check } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useDashboardStore } from '@/stores/dashboard';
import type { DashboardBlock } from '@/types/dashboard';
import draggable from 'vuedraggable';

const dashboardStore = useDashboardStore();

const localBlocks = ref<DashboardBlock[]>([]);
const saving = ref(false);
const resetting = ref(false);

const visibleBlocks = computed(() => {
  return localBlocks.value.filter(block => block.visible);
});

// 初始化本地数据
function initializeLocalBlocks() {
  localBlocks.value = JSON.parse(JSON.stringify(dashboardStore.blocks));
}

// 更新板块可见性
function updateBlockVisibility(blockId: string, visible: boolean) {
  const block = localBlocks.value.find(b => b.id === blockId);
  if (block) {
    block.visible = visible;
  }
}

// 拖拽结束处理
function onDragEnd() {
  // 更新每个板块的order属性
  localBlocks.value.forEach((block, index) => {
    block.order = index + 1;
  });
  
  // 显示提示信息
  ElMessage.success('排序已更新，请点击保存设置按钮保存更改');
}

// 保存设置
async function saveSettings() {
  saving.value = true;
  try {
    // 更新 store 中的配置
    dashboardStore.updateBlocksOrder(localBlocks.value);
    
    // 保存到 localStorage
    await dashboardStore.saveConfig();
    
    ElMessage.success('设置已保存');
  } catch (error) {
    console.error('保存设置失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    saving.value = false;
  }
}

// 重置为默认设置
async function resetToDefault() {
  resetting.value = true;
  try {
    const result = await dashboardStore.resetToDefault();
    if (result.success) {
      initializeLocalBlocks();
      ElMessage.success('已恢复默认设置');
    } else {
      console.error('重置设置失败:', result.error);
      ElMessage.error(`重置失败: ${result.error || '请重试'}`);
    }
  } catch (error) {
    console.error('重置设置失败:', error);
    ElMessage.error('重置失败，请重试');
  } finally {
    resetting.value = false;
  }
}

// 监听 store 变化
watch(
  () => dashboardStore.blocks,
  () => {
    initializeLocalBlocks();
  },
  { deep: true }
);

onMounted(async () => {
  // 异步加载配置，优先从数据库加载
  await dashboardStore.loadConfigAsync();
  initializeLocalBlocks();
});
</script>

<style scoped>
.dashboard-block-manager {
  max-width: 800px;
  margin: 0 auto;
}

.manager-header {
  margin-bottom: 24px;
  text-align: center;
}

.manager-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.manager-description {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.block-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.block-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: move;
}

.block-item:hover {
  border-color: var(--el-color-primary-light-7);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.block-item.block-disabled {
  opacity: 0.6;
  background: var(--el-bg-color-page);
}

.block-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  color: #909399;
  cursor: grab;
  margin-right: 12px;
  transition: color 0.3s ease;
}

.block-drag-handle:hover {
  color: #409eff;
}

.block-drag-handle:active {
  cursor: grabbing;
}

/* 拖拽状态样式 */
.ghost-item {
  opacity: 0.5;
  background: #f5f7fa;
  border: 2px dashed #409eff;
}

.chosen-item {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.drag-item {
  transform: rotate(5deg);
  opacity: 0.8;
}

.block-info {
  flex: 1;
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.block-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.block-description {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.block-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.block-order {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.manager-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 32px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
}

.preview-section {
  margin-top: 32px;
}

.preview-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.preview-blocks {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-block {
  padding: 12px 16px;
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 6px;
}

.preview-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-block-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

@media (max-width: 768px) {
  .dashboard-block-manager {
    padding: 0 16px;
  }
  
  .block-item {
    padding: 12px;
  }
  
  .block-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .block-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .manager-actions {
    flex-direction: column;
  }
}
</style>