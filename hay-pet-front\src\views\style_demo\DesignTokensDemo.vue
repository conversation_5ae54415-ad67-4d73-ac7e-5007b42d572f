<template>
  <div class="design-tokens-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🎯 Design Tokens 设计令牌</h1>
        <p class="page-description">
          统一的设计变量系统，包含颜色、间距、字体、阴影等设计元素，确保整个项目的视觉一致性
        </p>
        <router-link to="/style_demo" class="back-link">
          ← 返回样式系统首页
        </router-link>
      </div>

      <!-- 颜色系统 -->
      <div class="demo-section">
        <h2>🎨 颜色系统</h2>
        
        <!-- 主色调 -->
        <div class="color-group">
          <h3>主色调</h3>
          <div class="color-palette">
            <div class="color-item" v-for="color in primaryColors" :key="color.name">
              <div class="color-swatch" :style="{ backgroundColor: `var(${color.var})` }"></div>
              <div class="color-info">
                <div class="color-name">{{ color.name }}</div>
                <div class="color-var">{{ color.var }}</div>
                <div class="color-value">{{ color.value }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 辅助色彩 -->
        <div class="color-group">
          <h3>辅助色彩</h3>
          <div class="color-palette">
            <div class="color-item" v-for="color in secondaryColors" :key="color.name">
              <div class="color-swatch" :style="{ backgroundColor: `var(${color.var})` }"></div>
              <div class="color-info">
                <div class="color-name">{{ color.name }}</div>
                <div class="color-var">{{ color.var }}</div>
                <div class="color-value">{{ color.value }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中性色彩 -->
        <div class="color-group">
          <h3>中性色彩</h3>
          <div class="color-palette">
            <div class="color-item" v-for="color in neutralColors" :key="color.name">
              <div class="color-swatch" :style="{ backgroundColor: `var(${color.var})` }"></div>
              <div class="color-info">
                <div class="color-name">{{ color.name }}</div>
                <div class="color-var">{{ color.var }}</div>
                <div class="color-value">{{ color.value }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 渐变色彩 -->
        <div class="color-group">
          <h3>渐变色彩</h3>
          <div class="gradient-palette">
            <div class="gradient-item" v-for="gradient in gradients" :key="gradient.name">
              <div class="gradient-swatch" :style="{ background: `var(${gradient.var})` }"></div>
              <div class="gradient-info">
                <div class="gradient-name">{{ gradient.name }}</div>
                <div class="gradient-var">{{ gradient.var }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 间距系统 -->
      <div class="demo-section">
        <h2>📏 间距系统</h2>
        <div class="spacing-demo">
          <div class="spacing-item" v-for="spacing in spacings" :key="spacing.name">
            <div class="spacing-label">{{ spacing.name }}</div>
            <div class="spacing-var">{{ spacing.var }}</div>
            <div class="spacing-value">{{ spacing.value }}</div>
            <div class="spacing-visual">
              <div class="spacing-box" :style="{ width: `var(${spacing.var})`, height: `var(${spacing.var})` }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 字体系统 -->
      <div class="demo-section">
        <h2>📝 字体系统</h2>
        
        <!-- 字体大小 -->
        <div class="typography-group">
          <h3>字体大小</h3>
          <div class="font-size-demo">
            <div class="font-item" v-for="font in fontSizes" :key="font.name">
              <div class="font-example" :style="{ fontSize: `var(${font.var})` }">
                {{ font.name }}
              </div>
              <div class="font-info">
                <div class="font-var">{{ font.var }}</div>
                <div class="font-value">{{ font.value }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 字体粗细 -->
        <div class="typography-group">
          <h3>字体粗细</h3>
          <div class="font-weight-demo">
            <div class="weight-item" v-for="weight in fontWeights" :key="weight.name">
              <div class="weight-example" :style="{ fontWeight: `var(${weight.var})` }">
                {{ weight.name }}
              </div>
              <div class="weight-info">
                <div class="weight-var">{{ weight.var }}</div>
                <div class="weight-value">{{ weight.value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 圆角系统 */
      <div class="demo-section">
        <h2>🔘 圆角系统</h2>
        <div class="radius-demo">
          <div class="radius-item" v-for="radius in radiuses" :key="radius.name">
            <div class="radius-label">{{ radius.name }}</div>
            <div class="radius-var">{{ radius.var }}</div>
            <div class="radius-value">{{ radius.value }}</div>
            <div class="radius-visual">
              <div class="radius-box" :style="{ borderRadius: `var(${radius.var})` }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 阴影系统 -->
      <div class="demo-section">
        <h2>🌫️ 阴影系统</h2>
        <div class="shadow-demo">
          <div class="shadow-item" v-for="shadow in shadows" :key="shadow.name">
            <div class="shadow-label">{{ shadow.name }}</div>
            <div class="shadow-var">{{ shadow.var }}</div>
            <div class="shadow-visual">
              <div class="shadow-box" :style="{ boxShadow: `var(${shadow.var})` }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 动画系统 -->
      <div class="demo-section">
        <h2>⚡ 动画系统</h2>
        
        <!-- 过渡时间 */
        <div class="animation-group">
          <h3>过渡时间</h3>
          <div class="duration-demo">
            <div class="duration-item" v-for="duration in durations" :key="duration.name">
              <div class="duration-label">{{ duration.name }}</div>
              <div class="duration-var">{{ duration.var }}</div>
              <div class="duration-value">{{ duration.value }}</div>
              <div class="duration-visual">
                <div 
                  class="duration-box" 
                  :style="{ 
                    transition: `transform var(${duration.var}) ease`,
                    transform: hoveredDuration === duration.name ? 'translateX(50px)' : 'translateX(0)'
                  }"
                  @mouseenter="hoveredDuration = duration.name"
                  @mouseleave="hoveredDuration = null"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 缓动函数 -->
        <div class="animation-group">
          <h3>缓动函数</h3>
          <div class="easing-demo">
            <div class="easing-item" v-for="easing in easings" :key="easing.name">
              <div class="easing-label">{{ easing.name }}</div>
              <div class="easing-var">{{ easing.var }}</div>
              <div class="easing-value">{{ easing.value }}</div>
              <div class="easing-visual">
                <div 
                  class="easing-box" 
                  :style="{ 
                    transition: `transform 0.5s var(${easing.var})`,
                    transform: hoveredEasing === easing.name ? 'translateX(50px)' : 'translateX(0)'
                  }"
                  @mouseenter="hoveredEasing = easing.name"
                  @mouseleave="hoveredEasing = null"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用指南 -->
      <div class="demo-section">
        <h2>📖 使用指南</h2>
        <div class="usage-guide">
          <div class="guide-item">
            <h3>1. 引入设计令牌</h3>
            <pre><code>@import '@/styles/design-tokens.css';</code></pre>
          </div>
          
          <div class="guide-item">
            <h3>2. 在CSS中使用</h3>
            <pre><code>.my-component {
  color: var(--color-primary);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  transition: all var(--duration-base) var(--ease-out);
}</code></pre>
          </div>
          
          <div class="guide-item">
            <h3>3. 在Vue组件中使用</h3>
            <pre><code>&lt;div :style="{
  backgroundColor: 'var(--color-primary)',
  padding: 'var(--spacing-4)'
}"&gt;
  内容
&lt;/div&gt;</code></pre>
          </div>
          
          <div class="guide-item">
            <h3>4. 响应式使用</h3>
            <pre><code>@media (max-width: var(--breakpoint-sm)) {
  .my-component {
    padding: var(--spacing-2);
    font-size: var(--font-size-sm);
  }
}</code></pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 响应式数据
const hoveredDuration = ref(null)
const hoveredEasing = ref(null)

// 颜色数据
const primaryColors = [
  { name: '主色调', var: '--color-primary', value: '#409EFF' },
  { name: '主色调浅', var: '--color-primary-light', value: '#79BBFF' },
  { name: '主色调深', var: '--color-primary-dark', value: '#337ECC' },
  { name: '主色调更浅', var: '--color-primary-lighter', value: '#A0CFFF' },
  { name: '主色调更深', var: '--color-primary-darker', value: '#2B6CB0' }
]

const secondaryColors = [
  { name: '成功色', var: '--color-success', value: '#67C23A' },
  { name: '警告色', var: '--color-warning', value: '#E6A23C' },
  { name: '危险色', var: '--color-danger', value: '#F56C6C' },
  { name: '信息色', var: '--color-info', value: '#909399' }
]

const neutralColors = [
  { name: '主要文字', var: '--color-text-primary', value: '#303133' },
  { name: '次要文字', var: '--color-text-secondary', value: '#606266' },
  { name: '占位文字', var: '--color-text-placeholder', value: '#C0C4CC' },
  { name: '主要背景', var: '--color-bg-primary', value: '#FFFFFF' },
  { name: '次要背景', var: '--color-bg-secondary', value: '#F8F9FA' },
  { name: '三级背景', var: '--color-bg-tertiary', value: '#F5F7FA' }
]

const gradients = [
  { name: '主要渐变', var: '--gradient-primary' },
  { name: '成功渐变', var: '--gradient-success' },
  { name: '警告渐变', var: '--gradient-warning' },
  { name: '中性渐变', var: '--gradient-neutral' }
]

// 间距数据
const spacings = [
  { name: 'XS', var: '--spacing-1', value: '4px' },
  { name: 'SM', var: '--spacing-2', value: '8px' },
  { name: 'MD', var: '--spacing-3', value: '12px' },
  { name: 'LG', var: '--spacing-4', value: '16px' },
  { name: 'XL', var: '--spacing-5', value: '20px' },
  { name: '2XL', var: '--spacing-6', value: '24px' },
  { name: '3XL', var: '--spacing-8', value: '32px' }
]

// 字体数据
const fontSizes = [
  { name: '超小', var: '--font-size-xs', value: '12px' },
  { name: '小号', var: '--font-size-sm', value: '14px' },
  { name: '基础', var: '--font-size-base', value: '16px' },
  { name: '中号', var: '--font-size-md', value: '18px' },
  { name: '大号', var: '--font-size-lg', value: '20px' },
  { name: '超大', var: '--font-size-xl', value: '24px' },
  { name: '2倍大', var: '--font-size-2xl', value: '30px' },
  { name: '3倍大', var: '--font-size-3xl', value: '36px' }
]

const fontWeights = [
  { name: '细体', var: '--font-weight-light', value: '300' },
  { name: '正常', var: '--font-weight-normal', value: '400' },
  { name: '中等', var: '--font-weight-medium', value: '500' },
  { name: '半粗', var: '--font-weight-semibold', value: '600' },
  { name: '粗体', var: '--font-weight-bold', value: '700' }
]

// 圆角数据
const radiuses = [
  { name: '小圆角', var: '--radius-sm', value: '4px' },
  { name: '基础圆角', var: '--radius-base', value: '6px' },
  { name: '中圆角', var: '--radius-md', value: '8px' },
  { name: '大圆角', var: '--radius-lg', value: '12px' },
  { name: '卡片圆角', var: '--radius-card', value: '12px' },
  { name: '标签圆角', var: '--radius-tag', value: '20px' },
  { name: '完全圆角', var: '--radius-full', value: '9999px' }
]

// 阴影数据
const shadows = [
  { name: '小阴影', var: '--shadow-sm' },
  { name: '基础阴影', var: '--shadow-base' },
  { name: '中阴影', var: '--shadow-md' },
  { name: '大阴影', var: '--shadow-lg' },
  { name: '超大阴影', var: '--shadow-xl' },
  { name: '主要阴影', var: '--shadow-primary' }
]

// 动画数据
const durations = [
  { name: '快速', var: '--duration-fast', value: '0.15s' },
  { name: '基础', var: '--duration-base', value: '0.3s' },
  { name: '慢速', var: '--duration-slow', value: '0.5s' },
  { name: '更慢', var: '--duration-slower', value: '0.8s' }
]

const easings = [
  { name: '线性', var: '--ease-linear', value: 'linear' },
  { name: '缓入', var: '--ease-in', value: 'cubic-bezier(0.4, 0, 1, 1)' },
  { name: '缓出', var: '--ease-out', value: 'cubic-bezier(0, 0, 0.2, 1)' },
  { name: '缓入缓出', var: '--ease-in-out', value: 'cubic-bezier(0.4, 0, 0.2, 1)' },
  { name: '回弹', var: '--ease-back', value: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)' }
]
</script>

<style scoped>
/* 引入设计令牌 */
@import '@/styles/design-tokens.css';

.design-tokens-demo {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.page-header h1 {
  font-size: var(--font-size-3xl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 800px;
  margin: 0 auto var(--spacing-4);
  line-height: 1.6;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--duration-base) ease;
}

.back-link:hover {
  color: var(--color-primary-dark);
}

/* 演示区块 */
.demo-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
}

.demo-section h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

/* 颜色组 */
.color-group {
  margin-bottom: var(--spacing-6);
}

.color-group h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.color-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3);
  text-align: center;
}

.color-swatch {
  width: 100%;
  height: 60px;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-2);
  border: 1px solid var(--color-border-light);
}

.color-info {
  font-size: var(--font-size-sm);
}

.color-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.color-var {
  font-family: monospace;
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

.color-value {
  color: var(--color-text-secondary);
}

/* 渐变色板 */
.gradient-palette {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.gradient-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3);
  text-align: center;
}

.gradient-swatch {
  width: 100%;
  height: 60px;
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-2);
  border: 1px solid var(--color-border-light);
}

.gradient-info {
  font-size: var(--font-size-sm);
}

.gradient-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.gradient-var {
  font-family: monospace;
  color: var(--color-primary);
}

/* 间距演示 */
.spacing-demo {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: var(--spacing-4);
}

.spacing-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3);
  text-align: center;
}

.spacing-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.spacing-var {
  font-family: monospace;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-1);
}

.spacing-value {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
}

.spacing-visual {
  display: flex;
  justify-content: center;
}

.spacing-box {
  background: var(--color-primary);
  border-radius: var(--radius-sm);
}

/* 字体演示 */
.typography-group {
  margin-bottom: var(--spacing-6);
}

.typography-group h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
}

.font-size-demo,
.font-weight-demo {
  display: grid;
  gap: var(--spacing-4);
}

.font-item,
.weight-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.font-example,
.weight-example {
  color: var(--color-text-primary);
}

.font-info,
.weight-info {
  text-align: right;
  font-size: var(--font-size-sm);
}

.font-var,
.weight-var {
  font-family: monospace;
  color: var(--color-primary);
  margin-bottom: var(--spacing-1);
}

.font-value,
.weight-value {
  color: var(--color-text-secondary);
}

/* 圆角演示 */
.radius-demo {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: var(--spacing-4);
}

.radius-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3);
  text-align: center;
}

.radius-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.radius-var {
  font-family: monospace;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-1);
}

.radius-value {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
}

.radius-visual {
  display: flex;
  justify-content: center;
}

.radius-box {
  width: 40px;
  height: 40px;
  background: var(--color-primary);
}

/* 阴影演示 */
.shadow-demo {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.shadow-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  text-align: center;
}

.shadow-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
}

.shadow-var {
  font-family: monospace;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-3);
}

.shadow-visual {
  display: flex;
  justify-content: center;
}

.shadow-box {
  width: 60px;
  height: 60px;
  background: var(--color-bg-primary);
  border-radius: var(--radius-md);
}

/* 动画演示 */
.animation-group {
  margin-bottom: var(--spacing-6);
}

.animation-group h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
}

.duration-demo,
.easing-demo {
  display: grid;
  gap: var(--spacing-4);
}

.duration-item,
.easing-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.duration-label,
.easing-label {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  min-width: 80px;
}

.duration-var,
.easing-var {
  font-family: monospace;
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  min-width: 150px;
}

.duration-value,
.easing-value {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  min-width: 100px;
}

.duration-visual,
.easing-visual {
  min-width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
}

.duration-box,
.easing-box {
  width: 20px;
  height: 20px;
  background: var(--color-primary);
  border-radius: var(--radius-sm);
  cursor: pointer;
}

/* 使用指南 */
.usage-guide {
  display: grid;
  gap: var(--spacing-6);
}

.guide-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.guide-item h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
}

.guide-item pre {
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-3);
  overflow-x: auto;
  font-size: var(--font-size-sm);
}

.guide-item code {
  color: var(--color-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .design-tokens-demo {
    padding: var(--spacing-4);
  }
  
  .color-palette {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .spacing-demo {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
  
  .font-item,
  .weight-item,
  .duration-item,
  .easing-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }
}
</style>
