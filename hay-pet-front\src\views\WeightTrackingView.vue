<template>
  <div class="weight-tracking-view">
    <div v-if="!currentPetId" class="no-pet-selected">
      <el-empty description="请先在左侧选择一个宠物以查看或记录体重"></el-empty>
    </div>
    <div v-else>
      <!-- 页面标题和操作按钮 -->
      <div class="page-header">
        <div class="header-content">
          <h2 class="page-title">体重追踪</h2>
          <div class="header-actions">
            <el-button 
              class="add-weight-btn" 
              type="primary" 
              size="large"
              @click="openAddDialog"
            >
              <el-icon class="add-icon"><Plus /></el-icon>
              <span class="btn-text">添加体重记录</span>
            </el-button>

          </div>
        </div>
      </div>

      <!-- 时间控制面板 -->
      <div class="time-controls-section">
        <el-card class="time-controls-card" shadow="hover">
          <template #header>
            <div class="time-controls-header">
              <div class="controls-title-section">
                <span class="controls-title">体重变化趋势</span>
                <div class="current-period-info">
                  {{ currentPeriodTitle }} · 共 {{ filteredWeightRecords.length }} 条记录
                </div>
              </div>
              <div class="controls-actions">
                <!-- 时间维度切换 -->
                <EnhancedViewToggle
                  v-model="timeDimension"
                  :options="timeDimensionOptions"
                  @change="handleTimeDimensionChange"
                />
              </div>
            </div>
          </template>


        </el-card>
      </div>

      <!-- 体重概览卡片 -->
      <div class="weight-overview-section">
        <el-card class="weight-chart-card" shadow="hover">
          <template #header>
            <div class="chart-card-header">
              <div class="chart-title-section">
                <span class="chart-title">图表展示</span>
                <span class="period-title">{{ currentPeriodTitle }}</span>
              </div>
            </div>
          </template>
          <div class="chart-content">
            <!-- 图表加载状态 -->
            <div v-if="loading" class="chart-skeleton">
              <el-skeleton animated>
                <template #template>
                  <div class="skeleton-chart">
                    <el-skeleton-item variant="text" style="width: 30%; margin: 0 auto 20px;" />
                    <div class="skeleton-chart-area">
                      <el-skeleton-item variant="rect" style="width: 100%; height: 300px;" />
                    </div>
                  </div>
                </template>
              </el-skeleton>
            </div>
            <!-- 实际图表 -->
            <v-chart
              v-else
              class="weight-chart"
              :option="chartOption"
              :key="`chart-${timeDimension}-${currentPeriodTitle}-${filteredWeightRecords.length}`"
              autoresize
            />
          </div>
        </el-card>

        <!-- 统计信息卡片 -->
        <div class="stats-grid">
          <!-- 最新体重卡片 -->
          <el-card class="stat-card latest-weight" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><ScaleToOriginal /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">最新体重</div>
                <div v-if="loading" class="stat-skeleton">
                  <el-skeleton animated>
                    <template #template>
                      <el-skeleton-item variant="text" style="width: 60%; height: 24px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="text" style="width: 40%; height: 16px;" />
                    </template>
                  </el-skeleton>
                </div>
                <template v-else>
                  <div class="stat-value">{{ latestWeight }}</div>
                  <div class="stat-date">{{ formatDate(latestWeightRecord?.date || latestWeightRecord?.created_at) }}</div>
                </template>
              </div>
            </div>
          </el-card>

          <!-- 变化趋势卡片 -->
          <el-card class="stat-card weight-trend" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">变化趋势</div>
                <div v-if="loading" class="stat-skeleton">
                  <el-skeleton animated>
                    <template #template>
                      <el-skeleton-item variant="text" style="width: 50%; height: 24px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="text" style="width: 70%; height: 16px;" />
                    </template>
                  </el-skeleton>
                </div>
                <template v-else>
                  <div class="stat-value" :class="weightTrendClass">{{ weightTrend }}</div>
                  <div class="stat-date">{{ weightTrendDescription }}</div>
                </template>
              </div>
            </div>
          </el-card>

          <!-- 体重范围卡片 -->
          <el-card class="stat-card weight-range" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">体重范围</div>
                <div v-if="loading" class="stat-skeleton">
                  <el-skeleton animated>
                    <template #template>
                      <el-skeleton-item variant="text" style="width: 80%; height: 24px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="text" style="width: 60%; height: 16px;" />
                    </template>
                  </el-skeleton>
                </div>
                <template v-else>
                  <div class="stat-value">{{ weightRange }}</div>
                  <div class="stat-date">最高 - 最低</div>
                </template>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 体重记录表格 -->
      <div class="weight-records-section">
        <el-card shadow="hover">
          <template #header>
            <div class="table-header">
              <span class="table-title">历史记录</span>
              <el-button
                v-if="selectedRecords.length > 0"
                class="delete-batch-btn"
                type="danger"
                size="default"
                @click="batchDeleteRecords"
              >
                <el-icon class="delete-icon"><Delete /></el-icon>
                <span class="btn-text">删除选中 ({{ selectedRecords.length }})</span>
              </el-button>
            </div>
          </template>

          <!-- 表格加载状态 -->
          <div v-if="loading" class="table-skeleton">
            <el-skeleton animated>
              <template #template>
                <div class="skeleton-table">
                  <div class="skeleton-table-header">
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 50%;" />
                    <el-skeleton-item variant="text" style="width: 20%;" />
                  </div>
                  <div v-for="i in 5" :key="i" class="skeleton-table-row">
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 50%;" />
                    <el-skeleton-item variant="text" style="width: 20%;" />
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>

          <!-- 实际表格 -->
          <el-table
            v-else-if="weightRecords.length > 0"
            :data="sortedWeightRecords"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            :row-class-name="tableRowClassName"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="日期" width="180">
              <template #header>
                <div class="column-header">
                  <span>日期</span>
                  <div class="sort-buttons">
                    <el-button 
                      link 
                      size="small" 
                      :type="sortConfig.field === 'date' && sortConfig.order === 'asc' ? 'primary' : 'info'"
                      @click="handleSort('date', 'asc')"
                      title="按日期升序排列（旧到新）"
                    >
                      <el-icon><ArrowUp /></el-icon>
                    </el-button>
                    <el-button 
                      link 
                      size="small" 
                      :type="sortConfig.field === 'date' && sortConfig.order === 'desc' ? 'primary' : 'info'"
                      @click="handleSort('date', 'desc')"
                      title="按日期降序排列（新到旧）"
                    >
                      <el-icon><ArrowDown /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <div class="date-cell">
                  <el-icon class="date-icon"><Calendar /></el-icon>
                  <span :title="formatFullDateTime(row.date || row.created_at)">{{ formatDate(row.date || row.created_at) }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="体重" width="180">
              <template #header>
                <div class="column-header">
                  <span>体重</span>
                  <div class="sort-buttons">
                    <el-button 
                      link 
                      size="small" 
                      :type="sortConfig.field === 'weight' && sortConfig.order === 'asc' ? 'primary' : 'info'"
                      @click="handleSort('weight', 'asc')"
                      title="按体重升序排列（轻到重）"
                    >
                      <el-icon><ArrowUp /></el-icon>
                    </el-button>
                    <el-button 
                      link 
                      size="small" 
                      :type="sortConfig.field === 'weight' && sortConfig.order === 'desc' ? 'primary' : 'info'"
                      @click="handleSort('weight', 'desc')"
                      title="按体重降序排列（重到轻）"
                    >
                      <el-icon><ArrowDown /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <div class="weight-cell">
                  <el-tag type="primary" size="large" class="weight-tag">{{ formatWeight(row.weight) }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template #default="{ row }">
                <span class="notes-text">{{ row.notes || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button type="primary" link @click="openEditDialog(row)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button type="danger" link @click="deleteRecord(row)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-else-if="!loading && weightRecords.length === 0" description="还没有体重记录，快去添加吧！">
            <el-button type="primary" @click="openAddDialog">
              <el-icon><Plus /></el-icon>
              添加第一条记录
            </el-button>
          </el-empty>
        </el-card>
      </div>

      <!-- 添加/编辑体重记录对话框 -->
      <el-dialog v-model="showDialog" :title="isEditing ? '编辑体重记录' : '添加体重记录'" width="500px" @closed="resetForm">
        <el-form :model="weightForm" label-width="80px" :rules="rules" ref="formRef">
          <el-form-item label="体重" prop="weight">
            <div class="weight-input-group">
              <el-input-number v-model="weightForm.weight" :precision="1" :step="0.1" :min="0" style="width: 180px" />
              <span class="unit-label">{{ weightUnitDisplay }}</span>
            </div>
          </el-form-item>
          <el-form-item label="日期" prop="date">
            <el-date-picker
              v-model="weightForm.date"
              type="datetime"
              placeholder="选择日期和时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="备注" prop="notes">
            <el-input v-model="weightForm.notes" type="textarea" :rows="3" placeholder="输入备注信息（可选）" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Edit, ScaleToOriginal, TrendCharts, DataAnalysis, Calendar, ArrowUp, ArrowDown, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import { usePetStore } from '@/stores/pet';
import { supabase } from '@/utils/supabase';
import { formatWeight as utilFormatWeight, globalSettings, convertWeight } from '@/utils/settings';

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
]);

const petStore = usePetStore();

const weightRecords = ref<any[]>([]);
const selectedRecords = ref<any[]>([]);
const showDialog = ref(false);
const isEditing = ref(false);
const loading = ref(true); // 初始状态为加载中
const submitting = ref(false);
const formRef = ref();

// 时间维度切换相关状态
const timeDimension = ref<'month' | 'quarter' | 'year'>('month');
const currentDate = ref(new Date());

// 时间维度选项
const timeDimensionOptions = [
  { value: 'month', label: '月视图' },
  { value: 'quarter', label: '季度视图' },
  { value: 'year', label: '年视图' }
];



// 排序配置
const sortConfig = ref({
  field: 'date' as 'date' | 'weight',
  order: 'desc' as 'asc' | 'desc'
});

const weightForm = ref({
  weight: null as number | null,
  date: '',
  notes: ''
});

const editingRecord = ref<any>(null);

const currentPetId = computed(() => {
  // 优先使用petStore中的currentPetId，如果没有则从localStorage获取
  return petStore.currentPetId || localStorage.getItem('currentPetId');
});

// 当前时间段标题
const currentPeriodTitle = computed(() => {
  switch (timeDimension.value) {
    case 'month':
      return '月视图';
    case 'quarter':
      return '季度视图';
    case 'year':
      return '年视图';
    default:
      return '数据视图';
  }
});



// 计算时间范围 - 显示所有记录
const currentPeriodRange = computed(() => {
  const now = new Date();

  // 如果没有记录，返回当前时间
  if (weightRecords.value.length === 0) {
    return {
      start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 默认最近7天
      end: now
    };
  }

  // 获取所有记录的时间范围
  const allDates = weightRecords.value.map(record =>
    new Date(record.date || record.created_at)
  ).sort((a, b) => a.getTime() - b.getTime());

  const earliestDate = allDates[0];
  const latestDate = allDates[allDates.length - 1];

  // 返回全部记录的时间范围
  return {
    start: earliestDate,
    end: latestDate
  };
});

// 筛选当前时间段的数据
const filteredWeightRecords = computed(() => {
  const { start, end } = currentPeriodRange.value;

  return weightRecords.value.filter(record => {
    const recordDate = new Date(record.date || record.created_at);
    return recordDate >= start && recordDate <= end;
  });
});
const weightUnitDisplay = computed(() => globalSettings.weightUnit);

// 排序后的体重记录
const sortedWeightRecords = computed(() => {
  if (!weightRecords.value.length) return [];
  
  const records = [...weightRecords.value];
  
  return records.sort((a, b) => {
    let valueA, valueB;
    
    if (sortConfig.value.field === 'date') {
      valueA = new Date(a.date || a.created_at).getTime();
      valueB = new Date(b.date || b.created_at).getTime();
    } else {
      valueA = a.weight;
      valueB = b.weight;
    }
    
    if (sortConfig.value.order === 'asc') {
      return valueA - valueB;
    } else {
      return valueB - valueA;
    }
  });
});



// 计算属性 - 基于筛选后的数据
const latestWeightRecord = computed(() => {
  if (!filteredWeightRecords.value.length) return null;
  return filteredWeightRecords.value
    .sort((a, b) => new Date(b.date || b.created_at).getTime() - new Date(a.date || a.created_at).getTime())[0];
});

const latestWeight = computed(() => {
  if (!latestWeightRecord.value) return '未记录';
  return formatWeight(latestWeightRecord.value.weight);
});

const weightTrend = computed(() => {
  if (filteredWeightRecords.value.length < 2) return '数据不足';

  const sorted = [...filteredWeightRecords.value]
    .sort((a, b) => new Date(a.date || a.created_at).getTime() - new Date(b.date || b.created_at).getTime());

  const latest = sorted[sorted.length - 1]?.weight;
  const previous = sorted[sorted.length - 2]?.weight;

  if (!latest || !previous) return '数据不足';

  const diff = latest - previous;
  if (Math.abs(diff) < 0.1) return '稳定';
  return diff > 0 ? '上升' : '下降';
});

const weightTrendClass = computed(() => {
  const trend = weightTrend.value;
  if (trend === '上升') return 'trend-up';
  if (trend === '下降') return 'trend-down';
  return 'trend-stable';
});

const weightTrendDescription = computed(() => {
  if (filteredWeightRecords.value.length < 2) return '需要更多数据';

  const sorted = [...filteredWeightRecords.value]
    .sort((a, b) => new Date(a.date || a.created_at).getTime() - new Date(b.date || b.created_at).getTime());

  const latest = sorted[sorted.length - 1]?.weight;
  const previous = sorted[sorted.length - 2]?.weight;

  if (!latest || !previous) return '数据异常';

  const diff = Math.abs(latest - previous);
  const convertedDiff = convertWeight(diff, 'g', globalSettings.weightUnit);
  return `变化 ${convertedDiff.toFixed(1)} ${globalSettings.weightUnit}`;
});

const weightRange = computed(() => {
  if (!filteredWeightRecords.value.length) return '无数据';

  const weights = filteredWeightRecords.value.map(r => r.weight);
  const max = Math.max(...weights);
  const min = Math.min(...weights);

  return `${formatWeight(min)} - ${formatWeight(max)}`;
});

// 生成图表数据的函数
const generateChartData = () => {
  const { start, end } = currentPeriodRange.value;
  const records = filteredWeightRecords.value;

  if (!records.length) {
    return { labels: [], data: [] };
  }

  switch (timeDimension.value) {
    case 'month':
      return generateMonthData(start, end, records);
    case 'quarter':
      return generateQuarterData(start, end, records);
    case 'year':
      return generateYearData(start, end, records);
    default:
      return { labels: [], data: [] };
  }
};

// 生成月视图数据（每天）
const generateMonthData = (start: Date, end: Date, records: any[]) => {
  // 月视图保持原有逻辑，只显示有数据的日期
  if (!records.length) {
    return { labels: [], data: [] };
  }

  // 只显示有数据的日期，避免断层
  const sortedRecords = records
    .sort((a, b) => new Date(a.date || a.created_at).getTime() - new Date(b.date || b.created_at).getTime());

  const labels: string[] = [];
  const data: number[] = [];

  sortedRecords.forEach(record => {
    const recordDate = new Date(record.date || record.created_at);
    const day = recordDate.getDate();
    labels.push(`${day}日`);
    data.push(convertWeight(record.weight, 'g', globalSettings.weightUnit));
  });

  return { labels, data };
};

// 生成季度视图数据（每月）
const generateQuarterData = (start: Date, end: Date, records: any[]) => {
  const labels: string[] = [];
  const data: number[] = [];
  const startMonth = start.getMonth();
  const endMonth = end.getMonth();
  const year = start.getFullYear();

  // 获取所有历史数据，按时间排序
  const allRecords = weightRecords.value
    .sort((a, b) => new Date(a.date || a.created_at).getTime() - new Date(b.date || b.created_at).getTime());

  let lastKnownWeight: number | null = null;

  for (let month = startMonth; month <= endMonth; month++) {
    labels.push(`${month + 1}月`);

    // 查找当月的记录
    const monthRecords = records.filter(record => {
      const recordDate = new Date(record.date || record.created_at);
      return recordDate.getMonth() === month;
    });

    if (monthRecords.length > 0) {
      // 计算当月平均体重
      const avgWeight = monthRecords.reduce((sum, record) => sum + record.weight, 0) / monthRecords.length;
      const convertedWeight = convertWeight(avgWeight, 'g', globalSettings.weightUnit);
      data.push(convertedWeight);
      lastKnownWeight = convertedWeight;
    } else {
      // 没有当月数据，查找最近的历史数据
      const currentMonthDate = new Date(year, month, 1);
      const previousRecords = allRecords.filter(record => {
        const recordDate = new Date(record.date || record.created_at);
        return recordDate < currentMonthDate;
      });

      if (previousRecords.length > 0) {
        // 使用最近的历史数据
        const latestRecord = previousRecords[previousRecords.length - 1];
        const convertedWeight = convertWeight(latestRecord.weight, 'g', globalSettings.weightUnit);
        data.push(convertedWeight);
        lastKnownWeight = convertedWeight;
      } else if (lastKnownWeight !== null) {
        // 如果没有历史数据但有之前的已知数据，使用之前的数据
        data.push(lastKnownWeight);
      } else {
        // 完全没有数据的情况，跳过这个月
        labels.pop();
      }
    }
  }

  return { labels, data };
};

// 生成年视图数据（每月）
const generateYearData = (start: Date, end: Date, records: any[]) => {
  const labels: string[] = [];
  const data: number[] = [];
  const year = start.getFullYear();

  // 获取所有历史数据，按时间排序
  const allRecords = weightRecords.value
    .sort((a, b) => new Date(a.date || a.created_at).getTime() - new Date(b.date || b.created_at).getTime());

  let lastKnownWeight: number | null = null;

  for (let month = 0; month < 12; month++) {
    labels.push(`${month + 1}月`);

    // 查找当月的记录
    const monthRecords = records.filter(record => {
      const recordDate = new Date(record.date || record.created_at);
      return recordDate.getMonth() === month;
    });

    if (monthRecords.length > 0) {
      // 计算当月平均体重
      const avgWeight = monthRecords.reduce((sum, record) => sum + record.weight, 0) / monthRecords.length;
      const convertedWeight = convertWeight(avgWeight, 'g', globalSettings.weightUnit);
      data.push(convertedWeight);
      lastKnownWeight = convertedWeight;
    } else {
      // 没有当月数据，查找最近的历史数据
      const currentMonthDate = new Date(year, month, 1);
      const previousRecords = allRecords.filter(record => {
        const recordDate = new Date(record.date || record.created_at);
        return recordDate < currentMonthDate;
      });

      if (previousRecords.length > 0) {
        // 使用最近的历史数据
        const latestRecord = previousRecords[previousRecords.length - 1];
        const convertedWeight = convertWeight(latestRecord.weight, 'g', globalSettings.weightUnit);
        data.push(convertedWeight);
        lastKnownWeight = convertedWeight;
      } else if (lastKnownWeight !== null) {
        // 如果没有历史数据但有之前的已知数据，使用之前的数据
        data.push(lastKnownWeight);
      } else {
        // 完全没有数据的情况，跳过这个月
        labels.pop();
      }
    }
  }

  return { labels, data };
};

// ECharts图表配置
const chartOption = computed(() => {
  if (!filteredWeightRecords.value.length) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#909399'
        }
      }
    };
  }

  // 根据时间维度生成图表数据
  const chartData = generateChartData();

  return {
    title: {
      text: '体重变化趋势',
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#409EFF',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff'
      },
      formatter: (params: any) => {
        const point = params[0];
        return `<div style="padding: 6px;">
          <div style="margin-bottom: 6px; font-weight: 600;">${point.name}</div>
          <div style="color: #409EFF;">● 体重: ${point.value} ${globalSettings.weightUnit}</div>
        </div>`;
      }
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: chartData.labels,
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: '#909399',
        margin: 12
      }
    },
    yAxis: {
      type: 'value',
      name: `体重 (${globalSettings.weightUnit})`,
      nameLocation: 'middle',
      nameGap: 50,
      nameTextStyle: {
        fontSize: 14,
        color: '#606266',
        fontWeight: '500'
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#F2F6FC',
          type: 'dashed'
        }
      },
      axisLabel: {
        fontSize: 12,
        color: '#909399',
        formatter: (value: number) => `${value.toFixed(1)}`
      }
    },
    series: [
      {
        name: `体重 (${globalSettings.weightUnit})`,
        type: 'line',
        data: chartData.data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        connectNulls: false,
        lineStyle: {
          width: 4,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#409EFF' },
              { offset: 1, color: '#67C23A' }
            ]
          },
          shadowColor: 'rgba(64, 158, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 3
        },
        itemStyle: {
          color: '#409EFF',
          borderColor: '#ffffff',
          borderWidth: 3,
          shadowColor: 'rgba(64, 158, 255, 0.4)',
          shadowBlur: 8
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.25)'
              },
              {
                offset: 0.5,
                color: 'rgba(103, 194, 58, 0.15)'
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.02)'
              }
            ]
          }
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(64, 158, 255, 0.6)'
          }
        },
        animationDuration: 2000,
        animationEasing: 'cubicOut'
      }
    ]
  };
});

// 表单验证规则
const rules = {
  weight: [
    { required: true, message: '请输入体重', trigger: 'blur' },
    { type: 'number', min: 0.1, message: '体重必须大于0', trigger: 'blur' }
  ],
  date: [
    { required: true, message: '请选择日期', trigger: 'change' }
  ]
};

// 方法
function formatWeight(weight: number): string {
  return utilFormatWeight(weight);
}

function formatDate(date: string): string {
  const dateObj = new Date(date);
  
  // 表格中始终显示完整的日期时间格式
  return dateObj.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric'
  });
}

function formatFullDateTime(date: string): string {
  return new Date(date).toLocaleString('zh-CN');
}

function tableRowClassName({ rowIndex }: { rowIndex: number }): string {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
}

async function fetchWeightRecords() {
  if (!currentPetId.value) {
    weightRecords.value = [];
    return;
  }
  
  loading.value = true;
  try {
    const { data, error } = await supabase
      .from('weight_records')
      .select('*')
      .eq('pet_id', currentPetId.value)
      .order('date', { ascending: false });
    
    if (error) throw error;
    weightRecords.value = data || [];
  } catch (error) {
    console.error('获取体重记录失败:', error);
    ElMessage.error('获取体重记录失败');
  } finally {
    loading.value = false;
  }
}

function openAddDialog() {
  isEditing.value = false;
  editingRecord.value = null;
  weightForm.value = {
    weight: null,
    date: new Date().toISOString().slice(0, 19),
    notes: ''
  };
  showDialog.value = true;
}

function openEditDialog(record: any) {
  isEditing.value = true;
  editingRecord.value = record;
  weightForm.value = {
    weight: convertWeight(record.weight, 'g', globalSettings.weightUnit),
    date: record.date || record.created_at,
    notes: record.notes || ''
  };
  showDialog.value = true;
}

function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  weightForm.value = {
    weight: null,
    date: '',
    notes: ''
  };
  isEditing.value = false;
  editingRecord.value = null;
}

async function submitForm() {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    submitting.value = true;
    
    const weightInGrams = convertWeight(weightForm.value.weight!, globalSettings.weightUnit, 'g');
    
    if (isEditing.value && editingRecord.value) {
      const { error } = await supabase
        .from('weight_records')
        .update({
          weight: weightInGrams,
          date: weightForm.value.date,
          notes: weightForm.value.notes
        })
        .eq('id', editingRecord.value.id);
      
      if (error) throw error;
      ElMessage.success('体重记录更新成功');
    } else {
      const { error } = await supabase
        .from('weight_records')
        .insert({
          pet_id: currentPetId.value,
          weight: weightInGrams,
          date: weightForm.value.date,
          notes: weightForm.value.notes
        });
      
      if (error) throw error;
      ElMessage.success('体重记录添加成功');
    }
    
    showDialog.value = false;
    await fetchWeightRecords();
  } catch (error) {
    console.error('保存体重记录失败:', error);
    ElMessage.error('保存体重记录失败');
  } finally {
    submitting.value = false;
  }
}

async function deleteRecord(record: any) {
  try {
    await ElMessageBox.confirm('确定要删除这条体重记录吗？', '确认删除', {
      type: 'warning'
    });
    
    const { error } = await supabase
      .from('weight_records')
      .delete()
      .eq('id', record.id);
    
    if (error) throw error;
    
    ElMessage.success('删除成功');
    await fetchWeightRecords();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除体重记录失败:', error);
      ElMessage.error('删除失败');
    }
  }
}

function handleSelectionChange(selection: any[]) {
  selectedRecords.value = selection;
}

// 处理排序
function handleSort(field: 'date' | 'weight', order: 'asc' | 'desc') {
  sortConfig.value = { field, order };
}

// 时间维度切换处理
const handleTimeDimensionChange = (dimension: 'month' | 'quarter' | 'year') => {
  timeDimension.value = dimension;
  currentDate.value = new Date();
};

async function batchDeleteRecords() {
  if (selectedRecords.value.length === 0) return;
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRecords.value.length} 条记录吗？`,
      '批量删除',
      { type: 'warning' }
    );
    
    const ids = selectedRecords.value.map(record => record.id);
    const { error } = await supabase
      .from('weight_records')
      .delete()
      .in('id', ids);
    
    if (error) throw error;
    
    ElMessage.success(`成功删除 ${selectedRecords.value.length} 条记录`);
    selectedRecords.value = [];
    await fetchWeightRecords();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }
}

// 初始化时间控制
const initializeTimeControls = () => {
  const now = new Date();
  currentDate.value = now;
};

// 生命周期
onMounted(async () => {
  // 初始化时间控制
  initializeTimeControls();

  // 确保petStore中有pets数据
  if (petStore.pets.length === 0) {
    await petStore.fetchPets();
  }

  fetchWeightRecords();

  // 监听当前宠物变化事件
  window.addEventListener('currentPetChanged', fetchWeightRecords, { passive: true });
});

onBeforeUnmount(() => {
  window.removeEventListener('currentPetChanged', fetchWeightRecords);
});

// 监听当前宠物变化
watch(currentPetId, (newPetId) => {
  if (newPetId) {
    fetchWeightRecords();
  } else {
    weightRecords.value = [];
  }
}, { immediate: true });


</script>

<style scoped>
.weight-tracking-view {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* 时间控制面板样式 */
.time-controls-section {
  margin-bottom: 20px;
}

.time-controls-card {
  border-radius: 12px;
  overflow: hidden;
}

.time-controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 12px 12px 0 0;
}

.controls-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.controls-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.current-period-info {
  font-size: 14px;
  color: #606266;
  opacity: 0.8;
}

.controls-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}



.no-pet-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 自定义按钮样式 */
.add-weight-btn {
  position: relative;
  background: linear-gradient(135deg, #409EFF 0%, #36D1DC 50%, #5B86E5 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.add-weight-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.add-weight-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);
}

.add-weight-btn:hover::before {
  left: 100%;
}

.add-weight-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.delete-batch-btn {
  background: linear-gradient(135deg, #F56C6C 0%, #FF6B9D 50%, #C44569 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.delete-batch-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(245, 108, 108, 0.4);
}

.delete-batch-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.add-icon, .delete-icon {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.add-weight-btn:hover .add-icon {
  transform: rotate(90deg) scale(1.1);
}

.delete-batch-btn:hover .delete-icon {
  transform: scale(1.1);
}

.btn-text {
  position: relative;
  z-index: 1;
}

/* 体重概览区域 */
.weight-overview-section {
  margin-bottom: 24px;
}

.weight-chart-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
}

.chart-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.period-title {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.time-dimension-selector {
  display: flex;
  align-items: center;
}

.navigation-controls {
  display: flex;
  align-items: center;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}



.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}



.chart-content {
  padding: 0;
  position: relative;
  min-height: 400px;
}

.chart-content > * {
  transition: opacity 0.3s ease-in-out;
}

.weight-chart {
  height: 400px;
  width: 100%;
}

/* 骨架屏样式 */
.chart-skeleton {
  padding: 20px;
}

.skeleton-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.skeleton-chart-area {
  width: 100%;
  margin-top: 20px;
}

.stat-skeleton {
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.table-skeleton {
  padding: 20px;
}

.skeleton-table {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-table-header,
.skeleton-table-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.skeleton-table-header {
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-table-row {
  padding: 12px 0;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 24px;
  gap: 16px;
  min-height: 96px; /* 确保骨架屏和实际内容高度一致 */
}

.stat-info {
  flex: 1;
  transition: opacity 0.3s ease-in-out;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.latest-weight .stat-icon {
  background: linear-gradient(135deg, #409EFF, #67C23A);
}

.weight-trend .stat-icon {
  background: linear-gradient(135deg, #E6A23C, #F56C6C);
}

.weight-range .stat-icon {
  background: linear-gradient(135deg, #909399, #606266);
}



.stat-label {
  display: block;
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 2px;
}

.stat-date {
  font-size: 12px;
  color: #C0C4CC;
}

.trend-up {
  color: #F56C6C;
}

.trend-down {
  color: #67C23A;
}

.trend-stable {
  color: #909399;
}

/* 记录表格区域 */
.weight-records-section {
  margin-bottom: 24px;
}

.weight-records-section .el-card__body {
  transition: opacity 0.3s ease-in-out;
  min-height: 200px;
}

.weight-records-section .el-card {
  border-radius: 12px;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table .even-row) {
  background-color: #fafafa;
}

:deep(.el-table .odd-row) {
  background-color: #ffffff;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

/* 列头排序样式 */
.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.sort-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-left: 8px;
}

.sort-buttons .el-button {
  padding: 2px 4px;
  height: auto;
  min-height: auto;
  font-size: 12px;
  border: none;
}

.sort-buttons .el-button .el-icon {
  font-size: 12px;
  margin: 0;
}

.sort-buttons .el-button:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.sort-buttons .el-button.is-type-primary {
  color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
}

.date-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-icon {
  color: #909399;
  font-size: 16px;
}

.weight-cell {
  display: flex;
  align-items: center;
}

.weight-tag {
  min-width: 80px;
  text-align: center;
  font-weight: 600;
}

.notes-text {
  color: #606266;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  font-weight: 600;
  color: #303133;
}

.weight-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.unit-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weight-tracking-view {
    padding: 16px;
  }


  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .add-weight-btn, .delete-batch-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
  
  .btn-text {
    display: none;
  }
  
  .add-icon, .delete-icon {
    margin-right: 0;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .weight-chart {
    height: 300px;
  }
  
  .stat-content {
    padding: 20px;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .time-span-selector {
    .el-radio-button__inner {
      padding: 6px 12px;
      font-size: 12px;
    }
  }
  
  .chart-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }

  .time-dimension-selector,
  .navigation-controls {
    width: 100%;
    justify-content: center;
  }
  
  .column-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .sort-buttons {
    flex-direction: row;
    margin-left: 0;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .weight-tracking-view {
    padding: 12px;
  }
  
  .page-title {
    font-size: 20px;
  }
  
  .weight-chart {
    height: 250px;
  }
  
  .stat-content {
    padding: 16px;
    gap: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .add-weight-btn, .delete-batch-btn {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 10px;
  }
  
  .header-actions {
    gap: 12px;
  }
}
</style>