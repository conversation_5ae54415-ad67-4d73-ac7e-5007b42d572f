import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElTable, ElTableColumn, ElButton, ElEmpty } from 'element-plus'
import PetList from '../PetList.vue'

// Mock Supabase
const mockInsert = vi.fn().mockResolvedValue({ data: [], error: null })
const mockDelete = vi.fn().mockResolvedValue({ error: null })
const mockSelect = vi.fn(() => ({
  eq: vi.fn(() => ({
    order: vi.fn().mockResolvedValue({ data: [], error: null })
  }))
}))

const mockSupabase = {
  auth: {
    getUser: vi.fn().mockResolvedValue({
      data: { user: { id: 'test-user-id' } }
    })
  },
  from: vi.fn((table) => {
    if (table === 'weights') {
      return {
        insert: mockInsert,
        select: mockSelect,
        delete: mockDelete
      }
    }
    return {
      insert: mockInsert,
      select: mockSelect,
      delete: mockDelete
    }
  })
}

// 测试数据
const mockPets = [
  {
    id: '1',
    name: '测试宠物1',
    species: '金毛',
    age: 2,
    gender: 'male',
    avatar_url: 'test-url-1',
    created_at: '2024-01-01T00:00:00Z',
    latest_weight: 25.5,
    notes: '很活泼的狗狗'
  },
  {
    id: '2',
    name: '测试宠物2',
    species: '英短',
    age: 1,
    gender: 'female',
    avatar_url: null,
    created_at: '2024-01-02T00:00:00Z',
    latest_weight: null,
    notes: ''
  }
]

// Mock provide/inject
const createWrapper = (props = {}) => {
  return mount(PetList, {
    props: {
      pets: [],
      ...props
    },
    global: {
      components: {
        ElTable,
        ElTableColumn,
        ElButton,
        ElEmpty
      },
      provide: {
        supabase: mockSupabase
      },
      stubs: {
        'el-card': { template: '<div><slot name="header"></slot><slot></slot></div>' },
        'el-avatar': { template: '<div></div>' },
        'el-link': { template: '<a><slot></slot></a>' },
        'el-tag': { template: '<span><slot></slot></span>' },
        'el-dialog': { template: '<div v-if="modelValue"><slot></slot></div>', props: ['modelValue'] },
        'el-date-picker': { template: '<input />' },
        'el-input-number': { template: '<input type="number" />' },
        'el-form': { template: '<form><slot></slot></form>' },
        'el-form-item': { template: '<div><slot></slot></div>' }
      }
    }
  })
}

describe('PetList', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该在没有宠物时显示空状态', () => {
    const wrapper = createWrapper({ pets: [] })
    
    // 检查是否显示空状态组件
    expect(wrapper.findComponent(ElEmpty).exists()).toBe(true)
    expect(wrapper.text()).toContain('还没有添加宠物')
  })

  it('应该正确显示宠物列表', () => {
    const wrapper = createWrapper({ pets: mockPets })
    
    // 检查是否显示表格
    expect(wrapper.findComponent(ElTable).exists()).toBe(true)
    expect(wrapper.findComponent(ElEmpty).exists()).toBe(false)
    
    // 检查表格数据
    const table = wrapper.findComponent(ElTable)
    expect(table.props('data')).toEqual(mockPets)
  })

  it('应该正确格式化性别显示', () => {
    const wrapper = createWrapper({ pets: mockPets })
    const vm = wrapper.vm
    
    // 测试性别文本转换
    expect(vm.getGenderText('male')).toBe('公')
    expect(vm.getGenderText('female')).toBe('母')
    expect(vm.getGenderText('unknown')).toBe('未知')
    
    // 测试性别标签类型
    expect(vm.getGenderTagType('male')).toBe('primary')
    expect(vm.getGenderTagType('female')).toBe('success')
    expect(vm.getGenderTagType('unknown')).toBe('info')
  })

  it('应该正确格式化日期', () => {
    const wrapper = createWrapper()
    const vm = wrapper.vm
    
    // 测试日期格式化
    const testDate = '2024-01-01T00:00:00Z'
    const formatted = vm.formatDate(testDate)
    expect(formatted).toMatch(/\d{4}\/\d{1,2}\/\d{1,2}/)
    
    // 测试空日期
    expect(vm.formatDate('')).toBe('')
    expect(vm.formatDate(null)).toBe('')
  })

  it('应该能够显示宠物详情对话框', async () => {
    const wrapper = createWrapper({ pets: mockPets })
    
    // 模拟加载体重记录方法
    const mockLoadWeightRecords = vi.fn().mockResolvedValue()
    // 在调用showPetDetail之前设置mock
    const originalLoadWeightRecords = wrapper.vm.loadWeightRecords
    wrapper.vm.loadWeightRecords = mockLoadWeightRecords
    
    // 显示宠物详情
    await wrapper.vm.showPetDetail(mockPets[0])
    
    // 检查对话框是否显示
    expect(wrapper.vm.detailDialogVisible).toBe(true)
    expect(wrapper.vm.selectedPet).toEqual(mockPets[0])
    expect(mockLoadWeightRecords).toHaveBeenCalledWith(mockPets[0].id)
    
    // 恢复原方法
    wrapper.vm.loadWeightRecords = originalLoadWeightRecords
  })

  it('应该能够添加体重记录', async () => {
    const wrapper = createWrapper({ pets: mockPets })
    
    // 设置选中的宠物
    wrapper.vm.selectedPet = mockPets[0]
    wrapper.vm.newWeight = {
      date: new Date('2024-01-01'),
      weight: 26.0
    }
    
    // 模拟相关方法
    const mockLoadWeightRecords = vi.fn().mockResolvedValue()
    wrapper.vm.loadWeightRecords = mockLoadWeightRecords
    
    // 添加体重记录
    await wrapper.vm.addWeight()
    
    // 检查是否调用了Supabase插入方法
    expect(mockSupabase.from).toHaveBeenCalledWith('weights')
    expect(mockInsert).toHaveBeenCalled()
  })

  it('应该在添加体重时验证必填字段', async () => {
    const wrapper = createWrapper({ pets: mockPets })
    
    // 重置mock调用计数
    mockInsert.mockClear()
    
    // 设置不完整的体重数据
    wrapper.vm.newWeight = {
      date: null,
      weight: null
    }
    
    // 尝试添加体重记录
    await wrapper.vm.addWeight()
    
    // 检查是否没有调用插入方法
    expect(mockInsert).not.toHaveBeenCalled()
  })

  it('应该触发refresh事件', async () => {
    const wrapper = createWrapper({ pets: mockPets })
    
    // 触发刷新
    await wrapper.vm.$emit('refresh')
    
    // 检查事件是否被触发
    expect(wrapper.emitted('refresh')).toBeTruthy()
  })

  it('应该正确处理编辑宠物功能', () => {
    const wrapper = createWrapper({ pets: mockPets })
    
    // 调用编辑功能（当前只是显示提示）
    wrapper.vm.editPet(mockPets[0])
    
    // 这里可以添加更多的编辑功能测试
    // 目前编辑功能还在开发中，所以只是简单测试
    expect(true).toBe(true)
  })
})