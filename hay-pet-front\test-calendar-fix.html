<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日历视图切换修复验证</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            margin: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #303133;
            margin-bottom: 40px;
        }

        .test-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }

        .test-section h2 {
            color: #303133;
            margin-bottom: 16px;
            border-bottom: 2px solid #E4E7ED;
            padding-bottom: 8px;
        }

        .demo-area {
            background: #F5F7FA;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            display: flex;
            justify-content: center;
        }

        /* 增强的一体化视图切换样式 */
        .enhanced-view-toggle {
            position: relative;
            display: flex;
            background: #f5f7fa;
            border-radius: 8px;
            padding: 3px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .enhanced-view-toggle.small {
            padding: 2px;
        }

        .toggle-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
            border-radius: 8px;
        }

        .toggle-fill {
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
            border-radius: 5px;
            z-index: 1;
            box-shadow: 
                0 2px 8px rgba(64, 158, 255, 0.3),
                0 1px 3px rgba(64, 158, 255, 0.4);
            transform-origin: center;
        }

        .enhanced-view-toggle.small .toggle-fill {
            top: 2px;
            left: 2px;
            height: calc(100% - 4px);
        }

        .toggle-glow {
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: radial-gradient(ellipse at center, rgba(64, 158, 255, 0.4) 0%, transparent 70%);
            border-radius: 5px;
            z-index: 0;
            opacity: 0;
            filter: blur(4px);
        }

        .enhanced-view-toggle.small .toggle-glow {
            top: 2px;
            left: 2px;
            height: calc(100% - 4px);
        }

        .enhanced-toggle-btn {
            position: relative;
            z-index: 2;
            background: transparent;
            border: none;
            border-radius: 5px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            white-space: nowrap;
            font-weight: 500;
            flex: 1;
            text-align: center;
            overflow: hidden;
            transition: transform 0.2s ease;
        }

        .enhanced-view-toggle.small .enhanced-toggle-btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .enhanced-toggle-btn:hover {
            transform: translateY(-1px);
        }

        .enhanced-toggle-btn:active {
            transform: translateY(0);
        }

        .btn-content {
            position: relative;
            z-index: 3;
            color: #606266;
            transition: color 0.3s ease;
        }

        .enhanced-toggle-btn.active .btn-content {
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .btn-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            pointer-events: none;
            z-index: 1;
        }

        .enhanced-toggle-btn:hover .btn-content {
            color: #409EFF;
        }

        .enhanced-toggle-btn.active:hover .btn-content {
            color: white;
        }

        .result {
            text-align: center;
            color: #606266;
            font-size: 14px;
            margin-top: 12px;
        }

        .test-log {
            background: #FAFAFA;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: monospace;
            font-size: 12px;
            color: #606266;
            max-height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }

        .log-success {
            color: #67C23A;
        }

        .log-warning {
            color: #E6A23C;
        }

        .log-error {
            color: #F56C6C;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .comparison-table th,
        .comparison-table td {
            border: 1px solid #E4E7ED;
            padding: 12px;
            text-align: left;
        }

        .comparison-table th {
            background: #F5F7FA;
            font-weight: 600;
        }

        .status-ok {
            color: #67C23A;
            font-weight: 600;
        }

        .status-blocked {
            color: #E6A23C;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 日历视图切换修复验证</h1>
        
        <div class="test-section">
            <h2>修复前后对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>操作</th>
                        <th>修复前</th>
                        <th>修复后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>点击"月视图"</td>
                        <td class="status-ok">✅ 正常切换 + 动画</td>
                        <td class="status-ok">✅ 正常切换 + 动画</td>
                    </tr>
                    <tr>
                        <td>点击"周视图"</td>
                        <td class="status-blocked">⚠️ 显示警告 + 错误动画</td>
                        <td class="status-ok">✅ 仅显示警告，无动画</td>
                    </tr>
                    <tr>
                        <td>点击"年视图"</td>
                        <td class="status-blocked">⚠️ 显示警告 + 错误动画</td>
                        <td class="status-ok">✅ 仅显示警告，无动画</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h2>日历视图切换测试</h2>
            <div class="demo-area">
                <div class="enhanced-view-toggle" id="calendarToggle" data-active="0">
                    <div class="toggle-background"></div>
                    <div class="toggle-fill" id="calendarFill"></div>
                    <div class="toggle-glow" id="calendarGlow"></div>
                    <button class="enhanced-toggle-btn active" onclick="testSwitchView('month', 0, event)">
                        <span class="btn-content">月视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="testSwitchView('week', 1, event)">
                        <span class="btn-content">周视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="testSwitchView('year', 2, event)">
                        <span class="btn-content">年视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                </div>
            </div>
            <div class="result" id="calendarResult">当前选择：月视图</div>
            
            <div class="test-log" id="testLog">
                <div class="log-entry log-success">[初始化] 页面加载完成</div>
            </div>
        </div>

        <div class="test-section">
            <h2>记录类型切换测试（对比组）</h2>
            <div class="demo-area">
                <div class="enhanced-view-toggle small" id="recordsToggle" data-active="0">
                    <div class="toggle-background"></div>
                    <div class="toggle-fill" id="recordsFill"></div>
                    <div class="toggle-glow" id="recordsGlow"></div>
                    <button class="enhanced-toggle-btn active" onclick="testRecordsView('cards', 0, event)">
                        <span class="btn-content">卡片</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="testRecordsView('timeline', 1, event)">
                        <span class="btn-content">时间线</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="testRecordsView('table', 2, event)">
                        <span class="btn-content">表格</span>
                        <div class="btn-ripple"></div>
                    </button>
                </div>
            </div>
            <div class="result" id="recordsResult">当前选择：卡片</div>
        </div>
    </div>

    <script>
        let currentCalendarView = 'month';
        let currentRecordsView = 'cards';
        let logEntries = [];

        // 添加日志
        function addLog(message, type = 'success') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logEntries.push({ message: logEntry, type });
            
            const logContainer = document.getElementById('testLog');
            const logElement = document.createElement('div');
            logElement.className = `log-entry log-${type}`;
            logElement.textContent = logEntry;
            logContainer.appendChild(logElement);
            
            // 滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 涟漪效果
        function createRippleEffect(button, event) {
            const ripple = button.querySelector('.btn-ripple');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;
            
            gsap.set(ripple, {
                width: size,
                height: size,
                x: x,
                y: y,
                scale: 0,
                opacity: 0.6
            });
            
            gsap.to(ripple, {
                scale: 1,
                opacity: 0,
                duration: 0.6,
                ease: "power2.out"
            });
        }

        // 更新填充位置
        function updateFillPosition(containerId, fillId, glowId, activeIndex) {
            const container = document.getElementById(containerId);
            const fill = document.getElementById(fillId);
            const glow = document.getElementById(glowId);
            
            if (!container || !fill || activeIndex < 0) return;
            
            const buttons = container.querySelectorAll('.enhanced-toggle-btn');
            if (buttons.length === 0) return;
            
            let offset = 0;
            for (let i = 0; i < activeIndex; i++) {
                offset += buttons[i].offsetWidth;
            }
            
            const activeButton = buttons[activeIndex];
            const width = activeButton.offsetWidth;
            
            // GSAP 动画：填充背景移动
            gsap.to(fill, {
                x: offset,
                width: width,
                duration: 0.4,
                ease: "power2.out"
            });
            
            // GSAP 动画：发光效果
            if (glow) {
                gsap.to(glow, {
                    x: offset,
                    width: width,
                    duration: 0.4,
                    ease: "power2.out"
                });
                
                // 发光脉冲效果
                gsap.fromTo(glow, 
                    { opacity: 0, scale: 0.8 },
                    { 
                        opacity: 0.3, 
                        scale: 1,
                        duration: 0.3,
                        ease: "power2.out",
                        yoyo: true,
                        repeat: 1
                    }
                );
            }
            
            // 按钮弹跳效果
            gsap.fromTo(activeButton,
                { scale: 0.95 },
                { 
                    scale: 1,
                    duration: 0.3,
                    ease: "back.out(1.7)"
                }
            );
            
            // 文字颜色渐变动画
            buttons.forEach((btn, index) => {
                const content = btn.querySelector('.btn-content');
                btn.classList.toggle('active', index === activeIndex);
                if (index === activeIndex) {
                    gsap.to(content, {
                        color: '#ffffff',
                        duration: 0.3,
                        ease: "power2.out"
                    });
                } else {
                    gsap.to(content, {
                        color: '#606266',
                        duration: 0.3,
                        ease: "power2.out"
                    });
                }
            });
        }

        // 测试日历视图切换（修复后的逻辑）
        function testSwitchView(newView, index, event) {
            addLog(`点击：${getViewLabel(newView)} (index: ${index})`);
            
            if (newView === currentCalendarView) {
                addLog('相同视图，跳过切换', 'warning');
                return;
            }
            
            // 创建涟漪效果
            if (event && event.currentTarget) {
                createRippleEffect(event.currentTarget, event);
                addLog('涟漪效果已触发');
            }
            
            // 检查是否为未实现的视图 - 修复：移到前面
            if (newView === 'year') {
                alert('年视图功能开发中，敬请期待！');
                addLog('年视图：功能开发中，不执行动画', 'warning');
                return; // 直接返回，不执行后续动画
            } else if (newView === 'week') {
                alert('周视图功能开发中，敬请期待！');
                addLog('周视图：功能开发中，不执行动画', 'warning');
                return; // 直接返回，不执行后续动画
            }
            
            // 只有在真正切换视图时才执行动画
            currentCalendarView = newView;
            document.getElementById('calendarResult').textContent = `当前选择：${getViewLabel(newView)}`;
            updateFillPosition('calendarToggle', 'calendarFill', 'calendarGlow', index);
            addLog(`成功切换到：${getViewLabel(newView)}，动画已执行`, 'success');
        }

        // 测试记录类型切换（正常工作的对比组）
        function testRecordsView(newView, index, event) {
            if (newView === currentRecordsView) return;
            
            // 创建涟漪效果
            if (event && event.currentTarget) {
                createRippleEffect(event.currentTarget, event);
            }
            
            currentRecordsView = newView;
            document.getElementById('recordsResult').textContent = `当前选择：${getRecordsLabel(newView)}`;
            updateFillPosition('recordsToggle', 'recordsFill', 'recordsGlow', index);
        }

        function getViewLabel(view) {
            const labels = {
                'month': '月视图',
                'week': '周视图',
                'year': '年视图'
            };
            return labels[view] || view;
        }

        function getRecordsLabel(view) {
            const labels = {
                'cards': '卡片',
                'timeline': '时间线',
                'table': '表格'
            };
            return labels[view] || view;
        }

        // 初始化
        window.addEventListener('load', () => {
            updateFillPosition('calendarToggle', 'calendarFill', 'calendarGlow', 0);
            updateFillPosition('recordsToggle', 'recordsFill', 'recordsGlow', 0);
            addLog('页面初始化完成，两个切换组件已就绪', 'success');
        });
    </script>
</body>
</html>
