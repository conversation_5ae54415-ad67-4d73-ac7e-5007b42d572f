<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>体重跟踪时间维度切换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .dimension-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .dimension-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .dimension-btn.active {
            background: #409EFF;
            color: white;
            border-color: #409EFF;
        }
        .dimension-btn:hover {
            background: #ecf5ff;
            border-color: #409EFF;
        }
        .dimension-btn.active:hover {
            background: #337ecc;
        }
        .navigation-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            justify-content: center;
        }
        .nav-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .nav-btn:hover {
            background: #f0f0f0;
        }
        .current-period {
            font-size: 18px;
            font-weight: bold;
            color: #409EFF;
            min-width: 150px;
            text-align: center;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            border: 1px solid #eee;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fafafa;
            margin: 20px 0;
        }
        .data-list {
            border: 1px solid #eee;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .data-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
        }
        .data-item:last-child {
            border-bottom: none;
        }
        .date {
            color: #666;
            font-size: 14px;
        }
        .weight {
            font-weight: bold;
            color: #409EFF;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .x-axis-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>体重跟踪时间维度切换测试</h1>
    
    <div class="test-section">
        <h2>时间维度切换功能</h2>
        <div class="dimension-controls">
            <button class="dimension-btn active" data-dimension="month">月视图</button>
            <button class="dimension-btn" data-dimension="quarter">季度视图</button>
            <button class="dimension-btn" data-dimension="year">年视图</button>
        </div>
        
        <div class="navigation-controls">
            <button class="nav-btn" id="prevBtn">← 上一个</button>
            <span class="current-period" id="currentPeriod">2024年12月</span>
            <button class="nav-btn" id="nextBtn">下一个 →</button>
            <button class="nav-btn" id="todayBtn">今天</button>
        </div>
        
        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-value" id="totalRecords">0</div>
                <div class="stat-label">总记录数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgWeight">0</div>
                <div class="stat-label">平均体重</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="weightChange">0</div>
                <div class="stat-label">体重变化</div>
            </div>
        </div>
        
        <div class="chart-container">
            <div style="text-align: center;">
                <div style="font-size: 18px; margin-bottom: 10px;">体重变化趋势图</div>
                <div id="chartDescription">月视图：显示当月每天的体重数据</div>
                <div class="x-axis-labels" id="xAxisLabels">
                    <!-- X轴标签将在这里动态生成 -->
                </div>
            </div>
        </div>
        
        <div class="data-list" id="dataList">
            <!-- 数据将在这里显示 -->
        </div>
    </div>

    <script>
        // 模拟体重数据
        const mockData = [
            { date: '2024-01-15', weight: 5.2, notes: '健康检查' },
            { date: '2024-02-05', weight: 5.4, notes: '换粮后' },
            { date: '2024-03-10', weight: 5.6, notes: '活动增加' },
            { date: '2024-04-08', weight: 5.8, notes: '定期称重' },
            { date: '2024-05-06', weight: 6.0, notes: '成长期' },
            { date: '2024-06-03', weight: 6.2, notes: '夏季调整' },
            { date: '2024-07-01', weight: 6.4, notes: '定期检查' },
            { date: '2024-08-01', weight: 6.6, notes: '夏季结束' },
            { date: '2024-09-01', weight: 6.8, notes: '换季调整' },
            { date: '2024-10-01', weight: 7.0, notes: '秋季检查' },
            { date: '2024-11-01', weight: 7.2, notes: '冬季准备' },
            { date: '2024-12-01', weight: 7.4, notes: '年末检查' },
            { date: '2024-12-05', weight: 7.3, notes: '体重微降' },
            { date: '2024-12-10', weight: 7.5, notes: '恢复正常' },
            { date: '2024-12-15', weight: 7.6, notes: '健康状态优秀' },
            { date: '2024-12-20', weight: 7.4, notes: '稳定期' }
        ];

        let currentDimension = 'month';
        let currentDate = new Date();
        let filteredData = [];

        // 获取当前时间段的标题
        function getCurrentPeriodTitle() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            
            switch (currentDimension) {
                case 'month':
                    return `${year}年${month}月`;
                case 'quarter':
                    const quarter = Math.floor((month - 1) / 3) + 1;
                    return `${year}年第${quarter}季度`;
                case 'year':
                    return `${year}年`;
                default:
                    return '';
            }
        }

        // 获取当前时间段的范围
        function getCurrentPeriodRange() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();
            
            switch (currentDimension) {
                case 'month':
                    return {
                        start: new Date(year, month, 1),
                        end: new Date(year, month + 1, 0, 23, 59, 59)
                    };
                case 'quarter':
                    const quarterStart = Math.floor(month / 3) * 3;
                    return {
                        start: new Date(year, quarterStart, 1),
                        end: new Date(year, quarterStart + 3, 0, 23, 59, 59)
                    };
                case 'year':
                    return {
                        start: new Date(year, 0, 1),
                        end: new Date(year, 11, 31, 23, 59, 59)
                    };
                default:
                    return { start: new Date(), end: new Date() };
            }
        }

        // 筛选数据
        function filterData() {
            const { start, end } = getCurrentPeriodRange();
            
            filteredData = mockData.filter(record => {
                const recordDate = new Date(record.date);
                return recordDate >= start && recordDate <= end;
            });
            
            updateDisplay();
        }

        // 更新显示
        function updateDisplay() {
            updatePeriodTitle();
            updateStats();
            updateChart();
            updateDataList();
        }

        // 更新时间段标题
        function updatePeriodTitle() {
            document.getElementById('currentPeriod').textContent = getCurrentPeriodTitle();
        }

        // 更新统计信息
        function updateStats() {
            const totalRecords = filteredData.length;
            const avgWeight = totalRecords > 0 ? 
                (filteredData.reduce((sum, item) => sum + item.weight, 0) / totalRecords).toFixed(1) : 0;
            
            let weightChange = 0;
            if (filteredData.length >= 2) {
                const sortedData = [...filteredData].sort((a, b) => new Date(a.date) - new Date(b.date));
                const firstWeight = sortedData[0].weight;
                const lastWeight = sortedData[sortedData.length - 1].weight;
                weightChange = (lastWeight - firstWeight).toFixed(1);
            }

            document.getElementById('totalRecords').textContent = totalRecords;
            document.getElementById('avgWeight').textContent = avgWeight + ' kg';
            document.getElementById('weightChange').textContent = 
                (weightChange > 0 ? '+' : '') + weightChange + ' kg';
        }

        // 更新图表
        function updateChart() {
            const descriptions = {
                month: '月视图：显示当月每天的体重数据',
                quarter: '季度视图：显示当季度每月的平均体重',
                year: '年视图：显示当年每月的平均体重'
            };
            
            document.getElementById('chartDescription').textContent = descriptions[currentDimension];
            
            // 生成X轴标签
            const labels = generateXAxisLabels();
            const labelsContainer = document.getElementById('xAxisLabels');
            labelsContainer.innerHTML = labels.map(label => `<span>${label}</span>`).join('');
        }

        // 生成X轴标签
        function generateXAxisLabels() {
            const { start, end } = getCurrentPeriodRange();
            
            switch (currentDimension) {
                case 'month':
                    const daysInMonth = end.getDate();
                    return Array.from({length: Math.min(daysInMonth, 10)}, (_, i) => 
                        `${Math.floor(i * daysInMonth / 10) + 1}日`);
                case 'quarter':
                    const startMonth = start.getMonth();
                    return Array.from({length: 3}, (_, i) => `${startMonth + i + 1}月`);
                case 'year':
                    return Array.from({length: 12}, (_, i) => `${i + 1}月`);
                default:
                    return [];
            }
        }

        // 更新数据列表
        function updateDataList() {
            const dataList = document.getElementById('dataList');
            dataList.innerHTML = '';
            
            const sortedData = [...filteredData].sort((a, b) => new Date(b.date) - new Date(a.date));
            
            sortedData.forEach(item => {
                const div = document.createElement('div');
                div.className = 'data-item';
                div.innerHTML = `
                    <div>
                        <div class="weight">${item.weight} kg</div>
                        <div style="font-size: 12px; color: #999;">${item.notes}</div>
                    </div>
                    <div class="date">${item.date}</div>
                `;
                dataList.appendChild(div);
            });
            
            if (sortedData.length === 0) {
                dataList.innerHTML = '<div style="text-align: center; padding: 20px; color: #999;">暂无数据</div>';
            }
        }

        // 切换时间维度
        function switchDimension(dimension) {
            currentDimension = dimension;
            currentDate = new Date(); // 重置到当前时间
            
            // 更新按钮状态
            document.querySelectorAll('.dimension-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.dimension === dimension);
            });
            
            filterData();
        }

        // 导航方法
        function navigatePrevious() {
            const date = new Date(currentDate);
            
            switch (currentDimension) {
                case 'month':
                    date.setMonth(date.getMonth() - 1);
                    break;
                case 'quarter':
                    date.setMonth(date.getMonth() - 3);
                    break;
                case 'year':
                    date.setFullYear(date.getFullYear() - 1);
                    break;
            }
            
            currentDate = date;
            filterData();
        }

        function navigateNext() {
            const date = new Date(currentDate);
            
            switch (currentDimension) {
                case 'month':
                    date.setMonth(date.getMonth() + 1);
                    break;
                case 'quarter':
                    date.setMonth(date.getMonth() + 3);
                    break;
                case 'year':
                    date.setFullYear(date.getFullYear() + 1);
                    break;
            }
            
            currentDate = date;
            filterData();
        }

        function navigateToday() {
            currentDate = new Date();
            filterData();
        }

        // 绑定事件
        document.querySelectorAll('.dimension-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                switchDimension(btn.dataset.dimension);
            });
        });

        document.getElementById('prevBtn').addEventListener('click', navigatePrevious);
        document.getElementById('nextBtn').addEventListener('click', navigateNext);
        document.getElementById('todayBtn').addEventListener('click', navigateToday);

        // 初始化
        filterData();
    </script>
</body>
</html>
