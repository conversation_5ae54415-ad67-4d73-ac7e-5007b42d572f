/* ========================================
   页面标题栏预设样式系统
   基于事件记录页面的标题栏设计
   ======================================== */

/* 基础页面头部样式 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-content:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 左侧标题区域 */
.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
  margin: 0;
}

/* 右侧操作区域 */
.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 添加按钮样式 */
.add-btn {
  position: relative;
  background: linear-gradient(135deg, #409EFF 0%, #36D1DC 50%, #5B86E5 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.add-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);
}

.add-btn:hover::before {
  left: 100%;
}

.add-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.add-icon {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.add-btn:hover .add-icon {
  transform: rotate(90deg) scale(1.1);
}

.btn-text {
  position: relative;
  z-index: 1;
}

/* ========================================
   样式变体
   ======================================== */

/* 紧凑变体 */
.page-header--compact {
  margin-bottom: 16px;
}

.header-content--compact {
  padding: 16px 20px;
  border-radius: 8px;
}

.header-content--compact .page-title {
  font-size: 20px;
}

.header-content--compact .add-btn {
  padding: 10px 20px;
  font-size: 13px;
  border-radius: 8px;
}

/* 最小化变体 */
.page-header--minimal {
  margin-bottom: 12px;
}

.header-content--minimal {
  padding: 12px 16px;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.header-content--minimal .page-title {
  font-size: 18px;
}

.header-content--minimal .add-btn {
  padding: 8px 16px;
  font-size: 12px;
  border-radius: 6px;
}

.header-content--minimal .header-actions {
  gap: 12px;
}

/* ========================================
   删除按钮样式（可选）
   ======================================== */

.delete-btn {
  background: linear-gradient(135deg, #F56C6C 0%, #FF6B9D 50%, #C44569 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.delete-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(245, 108, 108, 0.4);
}

.delete-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.delete-icon {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.delete-btn:hover .delete-icon {
  transform: scale(1.1);
}

/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .page-title {
    font-size: 20px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 16px;
  }
  
  .page-title {
    font-size: 18px;
  }
  
  .add-btn {
    padding: 10px 20px;
    font-size: 13px;
    border-radius: 10px;
  }
  
  .header-actions {
    gap: 12px;
  }
}

/* ========================================
   主题色变体（可扩展）
   ======================================== */

/* 成功主题 */
.page-header--success .page-title {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header--success .add-btn {
  background: linear-gradient(135deg, #67C23A 0%, #85CE61 50%, #95D475 100%);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.page-header--success .add-btn:hover {
  box-shadow: 0 8px 20px rgba(103, 194, 58, 0.4);
}

/* 警告主题 */
.page-header--warning .page-title {
  background: linear-gradient(135deg, #E6A23C, #F7BA2A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header--warning .add-btn {
  background: linear-gradient(135deg, #E6A23C 0%, #F7BA2A 50%, #FFCD56 100%);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.page-header--warning .add-btn:hover {
  box-shadow: 0 8px 20px rgba(230, 162, 60, 0.4);
}

/* 危险主题 */
.page-header--danger .page-title {
  background: linear-gradient(135deg, #F56C6C, #FF7875);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header--danger .add-btn {
  background: linear-gradient(135deg, #F56C6C 0%, #FF7875 50%, #FF9A9E 100%);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

.page-header--danger .add-btn:hover {
  box-shadow: 0 8px 20px rgba(245, 108, 108, 0.4);
}
