/**
 * 视图切换组件预设样式
 * 统一的视图切换按钮样式，遵循设计令牌系统
 */

/* ========== 基础视图切换容器 ========== */
.view-toggle-container {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-2);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  transition: all var(--duration-base) var(--ease-out);
}

.view-toggle-container:hover {
  box-shadow: var(--shadow-base);
  border-color: var(--color-border-base);
}

/* ========== 视图切换按钮组 ========== */
.view-toggle-group {
  display: flex;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-1);
  gap: var(--spacing-1);
  position: relative;
  overflow: hidden;
}

/* 选中状态背景滑块 */
.view-toggle-group::before {
  content: '';
  position: absolute;
  top: var(--spacing-1);
  left: var(--spacing-1);
  height: calc(100% - var(--spacing-2));
  background: var(--gradient-primary);
  border-radius: var(--radius-md);
  transition: all var(--duration-base) var(--ease-out);
  z-index: 1;
  box-shadow: var(--shadow-sm);
}

/* ========== 视图切换按钮基础样式 ========== */
.view-toggle-btn {
  position: relative;
  z-index: 2;
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-base) var(--ease-out);
  white-space: nowrap;
  min-width: 60px;
  text-align: center;
  user-select: none;
  outline: none;
}

.view-toggle-btn:hover {
  color: var(--color-text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.view-toggle-btn:active {
  transform: scale(0.98);
  transition: transform var(--duration-fast) var(--ease-out);
}

/* 选中状态 */
.view-toggle-btn.active {
  color: white;
  font-weight: var(--font-weight-semibold);
}

.view-toggle-btn.active:hover {
  color: white;
  background: transparent;
}

/* ========== 尺寸变体 ========== */
/* 小号 */
.view-toggle-sm .view-toggle-btn {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  min-width: 50px;
}

/* 中号（默认） */
.view-toggle-md .view-toggle-btn {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  min-width: 60px;
}

/* 大号 */
.view-toggle-lg .view-toggle-btn {
  padding: var(--spacing-3) var(--spacing-5);
  font-size: var(--font-size-base);
  min-width: 80px;
}

/* ========== 主题变体 ========== */
/* 标准主题（默认） */
.view-toggle-standard .view-toggle-group::before {
  background: var(--gradient-primary);
}

/* 成功主题 */
.view-toggle-success .view-toggle-group::before {
  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-light) 100%);
}

/* 警告主题 */
.view-toggle-warning .view-toggle-group::before {
  background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-light) 100%);
}

/* 信息主题 */
.view-toggle-info .view-toggle-group::before {
  background: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-light) 100%);
}

/* ========== 布局变体 ========== */
/* 紧凑布局 */
.view-toggle-compact {
  padding: var(--spacing-1);
}

.view-toggle-compact .view-toggle-group {
  padding: 2px;
  gap: 2px;
}

.view-toggle-compact .view-toggle-btn {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  min-width: 40px;
}

/* 宽松布局 */
.view-toggle-relaxed {
  padding: var(--spacing-3);
}

.view-toggle-relaxed .view-toggle-group {
  padding: var(--spacing-2);
  gap: var(--spacing-2);
}

.view-toggle-relaxed .view-toggle-btn {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
  min-width: 100px;
}

/* ========== 动画效果 ========== */
/* 滑块位置计算 - 通过 JavaScript 动态设置 */
.view-toggle-group[data-active="0"]::before {
  width: var(--btn-0-width, 60px);
  transform: translateX(0);
}

.view-toggle-group[data-active="1"]::before {
  width: var(--btn-1-width, 60px);
  transform: translateX(var(--btn-1-offset, 64px));
}

.view-toggle-group[data-active="2"]::before {
  width: var(--btn-2-width, 60px);
  transform: translateX(var(--btn-2-offset, 128px));
}

.view-toggle-group[data-active="3"]::before {
  width: var(--btn-3-width, 60px);
  transform: translateX(var(--btn-3-offset, 192px));
}

.view-toggle-group[data-active="4"]::before {
  width: var(--btn-4-width, 60px);
  transform: translateX(var(--btn-4-offset, 256px));
}

/* ========== 图标支持 ========== */
.view-toggle-btn .btn-icon {
  margin-right: var(--spacing-1);
  font-size: 1em;
  vertical-align: middle;
}

.view-toggle-btn .btn-text {
  vertical-align: middle;
}

/* 仅图标模式 */
.view-toggle-icon-only .view-toggle-btn {
  min-width: auto;
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-toggle-icon-only .view-toggle-btn .btn-icon {
  margin: 0;
  font-size: var(--font-size-lg);
}

.view-toggle-icon-only .view-toggle-btn .btn-text {
  display: none;
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .view-toggle-container {
    padding: var(--spacing-1);
  }
  
  .view-toggle-btn {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    min-width: 50px;
  }
  
  .view-toggle-lg .view-toggle-btn {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    min-width: 60px;
  }
}

@media (max-width: 480px) {
  /* 移动端自动切换为图标模式 */
  .view-toggle-responsive .view-toggle-btn .btn-text {
    display: none;
  }
  
  .view-toggle-responsive .view-toggle-btn {
    min-width: auto;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .view-toggle-responsive .view-toggle-btn .btn-icon {
    margin: 0;
    font-size: var(--font-size-base);
  }
}

/* ========== 工具类 ========== */
/* 快速应用视图切换样式 */
.view-toggle-preset {
  /* 组合：容器 + 标准主题 + 中号尺寸 */
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-2);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  transition: all var(--duration-base) var(--ease-out);
}

.view-toggle-preset:hover {
  box-shadow: var(--shadow-base);
  border-color: var(--color-border-base);
}

.view-toggle-preset .view-toggle-group {
  display: flex;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-1);
  gap: var(--spacing-1);
  position: relative;
  overflow: hidden;
}

.view-toggle-preset .view-toggle-group::before {
  content: '';
  position: absolute;
  top: var(--spacing-1);
  left: var(--spacing-1);
  height: calc(100% - var(--spacing-2));
  background: var(--gradient-primary);
  border-radius: var(--radius-md);
  transition: all var(--duration-base) var(--ease-out);
  z-index: 1;
  box-shadow: var(--shadow-sm);
}

.view-toggle-preset .view-toggle-btn {
  position: relative;
  z-index: 2;
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--duration-base) var(--ease-out);
  white-space: nowrap;
  min-width: 60px;
  text-align: center;
  user-select: none;
  outline: none;
}

.view-toggle-preset .view-toggle-btn:hover {
  color: var(--color-text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.view-toggle-preset .view-toggle-btn.active {
  color: white;
  font-weight: var(--font-weight-semibold);
}

/* 小号变体 */
.view-toggle-sm-preset {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-1);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border-light);
  transition: all var(--duration-base) var(--ease-out);
}

.view-toggle-sm-preset .view-toggle-btn {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  min-width: 50px;
}
