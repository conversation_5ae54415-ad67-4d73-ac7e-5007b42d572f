/**
 * 视图切换组件预设样式系统
 * Enhanced View Toggle Component Preset System
 */

// 预设尺寸配置
export const VIEW_TOGGLE_SIZES = {
  small: {
    name: 'small',
    padding: '2px',
    buttonPadding: '6px 12px',
    fontSize: '12px',
    fillOffset: '2px',
    mobilePadding: '4px 8px',
    mobileFontSize: '11px'
  },
  normal: {
    name: 'normal', 
    padding: '3px',
    buttonPadding: '8px 16px',
    fontSize: '14px',
    fillOffset: '3px',
    mobilePadding: '6px 12px',
    mobileFontSize: '12px'
  },
  large: {
    name: 'large',
    padding: '4px',
    buttonPadding: '10px 20px', 
    fontSize: '16px',
    fillOffset: '4px',
    mobilePadding: '8px 16px',
    mobileFontSize: '14px'
  }
};

// 预设主题配置
export const VIEW_TOGGLE_THEMES = {
  default: {
    name: 'default',
    background: '#f5f7fa',
    backgroundGradient: 'linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%)',
    fillBackground: 'linear-gradient(135deg, #409EFF 0%, #337ECC 100%)',
    fillShadow: '0 2px 8px rgba(64, 158, 255, 0.3), 0 1px 3px rgba(64, 158, 255, 0.4)',
    glowColor: 'rgba(64, 158, 255, 0.4)',
    activeTextColor: '#ffffff',
    inactiveTextColor: '#606266',
    hoverTextColor: '#409EFF',
    rippleColor: 'rgba(255, 255, 255, 0.6)'
  },
  success: {
    name: 'success',
    background: '#f0f9f0',
    backgroundGradient: 'linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%)',
    fillBackground: 'linear-gradient(135deg, #67C23A 0%, #5DAE34 100%)',
    fillShadow: '0 2px 8px rgba(103, 194, 58, 0.3), 0 1px 3px rgba(103, 194, 58, 0.4)',
    glowColor: 'rgba(103, 194, 58, 0.4)',
    activeTextColor: '#ffffff',
    inactiveTextColor: '#606266',
    hoverTextColor: '#67C23A',
    rippleColor: 'rgba(255, 255, 255, 0.6)'
  },
  warning: {
    name: 'warning',
    background: '#fdf6ec',
    backgroundGradient: 'linear-gradient(135deg, #fdf6ec 0%, #faecd8 100%)',
    fillBackground: 'linear-gradient(135deg, #E6A23C 0%, #CF9236 100%)',
    fillShadow: '0 2px 8px rgba(230, 162, 60, 0.3), 0 1px 3px rgba(230, 162, 60, 0.4)',
    glowColor: 'rgba(230, 162, 60, 0.4)',
    activeTextColor: '#ffffff',
    inactiveTextColor: '#606266',
    hoverTextColor: '#E6A23C',
    rippleColor: 'rgba(255, 255, 255, 0.6)'
  },
  danger: {
    name: 'danger',
    background: '#fef0f0',
    backgroundGradient: 'linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%)',
    fillBackground: 'linear-gradient(135deg, #F56C6C 0%, #E85A5A 100%)',
    fillShadow: '0 2px 8px rgba(245, 108, 108, 0.3), 0 1px 3px rgba(245, 108, 108, 0.4)',
    glowColor: 'rgba(245, 108, 108, 0.4)',
    activeTextColor: '#ffffff',
    inactiveTextColor: '#606266',
    hoverTextColor: '#F56C6C',
    rippleColor: 'rgba(255, 255, 255, 0.6)'
  }
};

// 预设动画配置
export const VIEW_TOGGLE_ANIMATIONS = {
  default: {
    name: 'default',
    fillDuration: 0.4,
    fillEasing: 'power2.out',
    glowDuration: 0.3,
    glowEasing: 'power2.out',
    rippleDuration: 0.6,
    rippleEasing: 'power2.out',
    buttonBounceDuration: 0.3,
    buttonBounceEasing: 'back.out(1.7)',
    textColorDuration: 0.3,
    textColorEasing: 'power2.out'
  },
  fast: {
    name: 'fast',
    fillDuration: 0.2,
    fillEasing: 'power2.out',
    glowDuration: 0.15,
    glowEasing: 'power2.out',
    rippleDuration: 0.4,
    rippleEasing: 'power2.out',
    buttonBounceDuration: 0.2,
    buttonBounceEasing: 'back.out(1.7)',
    textColorDuration: 0.2,
    textColorEasing: 'power2.out'
  },
  slow: {
    name: 'slow',
    fillDuration: 0.6,
    fillEasing: 'power2.out',
    glowDuration: 0.5,
    glowEasing: 'power2.out',
    rippleDuration: 0.8,
    rippleEasing: 'power2.out',
    buttonBounceDuration: 0.5,
    buttonBounceEasing: 'back.out(1.7)',
    textColorDuration: 0.5,
    textColorEasing: 'power2.out'
  },
  elastic: {
    name: 'elastic',
    fillDuration: 0.5,
    fillEasing: 'elastic.out(1, 0.3)',
    glowDuration: 0.4,
    glowEasing: 'elastic.out(1, 0.3)',
    rippleDuration: 0.7,
    rippleEasing: 'elastic.out(1, 0.3)',
    buttonBounceDuration: 0.4,
    buttonBounceEasing: 'elastic.out(1, 0.3)',
    textColorDuration: 0.3,
    textColorEasing: 'power2.out'
  }
};

// 常用预设组合
export const VIEW_TOGGLE_PRESETS = {
  // 默认组合
  default: {
    size: 'normal',
    theme: 'default',
    animation: 'default'
  },
  
  // 小尺寸组合
  small: {
    size: 'small',
    theme: 'default',
    animation: 'default'
  },
  
  // 大尺寸组合
  large: {
    size: 'large',
    theme: 'default',
    animation: 'default'
  },
  
  // 成功主题组合
  success: {
    size: 'normal',
    theme: 'success',
    animation: 'default'
  },
  
  // 警告主题组合
  warning: {
    size: 'normal',
    theme: 'warning',
    animation: 'default'
  },
  
  // 危险主题组合
  danger: {
    size: 'normal',
    theme: 'danger',
    animation: 'default'
  },
  
  // 快速动画组合
  fast: {
    size: 'normal',
    theme: 'default',
    animation: 'fast'
  },
  
  // 弹性动画组合
  elastic: {
    size: 'normal',
    theme: 'default',
    animation: 'elastic'
  },
  
  // 小尺寸成功主题
  smallSuccess: {
    size: 'small',
    theme: 'success',
    animation: 'default'
  },
  
  // 大尺寸弹性动画
  largeElastic: {
    size: 'large',
    theme: 'default',
    animation: 'elastic'
  }
};

// 工具函数：获取预设配置
export function getViewTogglePreset(presetName) {
  const preset = VIEW_TOGGLE_PRESETS[presetName];
  if (!preset) {
    console.warn(`View toggle preset "${presetName}" not found, using default`);
    return VIEW_TOGGLE_PRESETS.default;
  }
  
  return {
    size: VIEW_TOGGLE_SIZES[preset.size],
    theme: VIEW_TOGGLE_THEMES[preset.theme],
    animation: VIEW_TOGGLE_ANIMATIONS[preset.animation],
    preset: preset
  };
}

// 工具函数：创建自定义预设
export function createCustomViewTogglePreset(config) {
  const {
    size = 'normal',
    theme = 'default', 
    animation = 'default',
    customSize,
    customTheme,
    customAnimation
  } = config;
  
  return {
    size: customSize || VIEW_TOGGLE_SIZES[size],
    theme: customTheme || VIEW_TOGGLE_THEMES[theme],
    animation: customAnimation || VIEW_TOGGLE_ANIMATIONS[animation]
  };
}

// 导出所有配置
export default {
  sizes: VIEW_TOGGLE_SIZES,
  themes: VIEW_TOGGLE_THEMES,
  animations: VIEW_TOGGLE_ANIMATIONS,
  presets: VIEW_TOGGLE_PRESETS,
  getPreset: getViewTogglePreset,
  createCustom: createCustomViewTogglePreset
};
