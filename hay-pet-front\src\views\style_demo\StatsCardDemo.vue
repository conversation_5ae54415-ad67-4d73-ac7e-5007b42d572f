<template>
  <div class="stats-card-demo">
    <div class="demo-header">
      <h1>📊 统计卡片组件演示</h1>
      <p>展示 StandardStatsCard 和 StandardStatsGrid 组件的各种用法和样式</p>
    </div>

    <!-- 基础用法 -->
    <div class="demo-section">
      <h2>🎯 基础用法</h2>
      <div class="demo-content">
        <StandardStatsGrid
          :stats-items="basicStatsItems"
          :clickable="true"
          @card-click="handleCardClick"
        />
      </div>
      <div class="demo-code">
        <pre><code>&lt;StandardStatsGrid
  :stats-items="statsItems"
  :clickable="true"
  @card-click="handleCardClick"
/&gt;</code></pre>
      </div>
    </div>

    <!-- 预设主题 -->
    <div class="demo-section">
      <h2>🎨 预设主题</h2>
      <div class="demo-content">
        <StandardStatsGrid :stats-items="themeStatsItems" />
      </div>
    </div>

    <!-- 尺寸变体 -->
    <div class="demo-section">
      <h2>📏 尺寸变体</h2>
      
      <h3>紧凑尺寸</h3>
      <div class="demo-content">
        <StandardStatsGrid :stats-items="sizeStatsItems" size="compact" />
      </div>
      
      <h3>正常尺寸</h3>
      <div class="demo-content">
        <StandardStatsGrid :stats-items="sizeStatsItems" size="normal" />
      </div>
      
      <h3>大尺寸</h3>
      <div class="demo-content">
        <StandardStatsGrid :stats-items="sizeStatsItems" size="large" />
      </div>
    </div>

    <!-- 状态演示 -->
    <div class="demo-section">
      <h2>⚡ 状态演示</h2>
      <div class="demo-content">
        <StandardStatsGrid :stats-items="stateStatsItems" />
      </div>
      <div class="demo-controls">
        <el-button @click="toggleLoading">切换加载状态</el-button>
        <el-button @click="toggleError">切换错误状态</el-button>
        <el-button @click="updateValues">更新数值</el-button>
      </div>
    </div>

    <!-- 自定义样式 -->
    <div class="demo-section">
      <h2>🎭 自定义样式</h2>
      <div class="demo-content">
        <StandardStatsGrid :stats-items="customStatsItems" />
      </div>
    </div>

    <!-- 响应式演示 -->
    <div class="demo-section">
      <h2>📱 响应式演示</h2>
      <p>调整浏览器窗口大小查看响应式效果</p>
      <div class="demo-content">
        <StandardStatsGrid :stats-items="responsiveStatsItems" />
      </div>
    </div>

    <!-- 点击事件日志 -->
    <div class="demo-section" v-if="clickLogs.length > 0">
      <h2>📝 点击事件日志</h2>
      <div class="click-logs">
        <div v-for="(log, index) in clickLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Document, Calendar, Bell, Clock, TrendCharts, User, Star, Gift } from '@element-plus/icons-vue';
import StandardStatsGrid from '@/components/common/StandardStatsGrid.vue';

// 基础统计数据
const basicStatsItems = ref([
  {
    key: 'total',
    label: '总记录数',
    value: 42,
    date: '全部记录',
    icon: Document,
    variant: 'total-records'
  },
  {
    key: 'monthly',
    label: '本月记录',
    value: 8,
    date: '2024年6月',
    icon: Calendar,
    variant: 'month-records'
  },
  {
    key: 'pending',
    label: '待处理提醒',
    value: 3,
    date: '需要关注',
    icon: Bell,
    variant: 'pending-reminders'
  },
  {
    key: 'recent',
    label: '最近活动',
    value: '疫苗接种',
    date: '2小时前',
    icon: Clock,
    variant: 'recent-activity'
  },
  {
    key: 'frequency',
    label: '活动频率分析',
    value: '疫苗接种',
    date: '15 次记录',
    icon: TrendCharts,
    variant: 'activity-frequency'
  }
]);

// 主题演示数据
const themeStatsItems = ref([
  {
    key: 'primary',
    label: '主要主题',
    value: 100,
    date: '示例数据',
    icon: Star,
    variant: 'primary'
  },
  {
    key: 'success',
    label: '成功主题',
    value: 95,
    date: '示例数据',
    icon: Star,
    variant: 'success'
  },
  {
    key: 'warning',
    label: '警告主题',
    value: 75,
    date: '示例数据',
    icon: Star,
    variant: 'warning'
  },
  {
    key: 'danger',
    label: '危险主题',
    value: 25,
    date: '示例数据',
    icon: Star,
    variant: 'danger'
  }
]);

// 尺寸演示数据
const sizeStatsItems = ref([
  {
    key: 'size1',
    label: '用户数量',
    value: 1234,
    date: '活跃用户',
    icon: User,
    variant: 'primary'
  },
  {
    key: 'size2',
    label: '订单数量',
    value: 567,
    date: '本月订单',
    icon: Gift,
    variant: 'success'
  }
]);

// 状态演示数据
const stateStatsItems = reactive([
  {
    key: 'loading-demo',
    label: '加载演示',
    value: 123,
    date: '演示数据',
    icon: Document,
    variant: 'primary',
    loading: false
  },
  {
    key: 'error-demo',
    label: '错误演示',
    value: 456,
    date: '演示数据',
    icon: Calendar,
    variant: 'danger',
    error: false
  },
  {
    key: 'update-demo',
    label: '更新演示',
    value: 789,
    date: '演示数据',
    icon: TrendCharts,
    variant: 'success'
  }
]);

// 自定义样式数据
const customStatsItems = ref([
  {
    key: 'custom1',
    label: '自定义渐变1',
    value: 888,
    date: '自定义样式',
    icon: Star,
    variant: 'custom',
    customGradient: 'linear-gradient(135deg, #ff6b6b, #feca57)'
  },
  {
    key: 'custom2',
    label: '自定义渐变2',
    value: 999,
    date: '自定义样式',
    icon: Gift,
    variant: 'custom',
    customGradient: 'linear-gradient(135deg, #48cae4, #023e8a)'
  }
]);

// 响应式演示数据
const responsiveStatsItems = ref([
  {
    key: 'resp1',
    label: '桌面端',
    value: 1920,
    date: '≥1200px',
    icon: Document,
    variant: 'primary'
  },
  {
    key: 'resp2',
    label: '平板端',
    value: 768,
    date: '768-1199px',
    icon: Calendar,
    variant: 'warning'
  },
  {
    key: 'resp3',
    label: '移动端',
    value: 375,
    date: '<768px',
    icon: Bell,
    variant: 'success'
  },
  {
    key: 'resp4',
    label: '自适应',
    value: 'Auto',
    date: '响应式布局',
    icon: TrendCharts,
    variant: 'activity-frequency'
  }
]);

// 点击日志
const clickLogs = ref([]);

// 事件处理
const handleCardClick = ({ item, index }) => {
  const log = {
    time: new Date().toLocaleTimeString(),
    message: `点击了 "${item.label}" 卡片 (索引: ${index})`
  };
  clickLogs.value.unshift(log);
  
  // 限制日志数量
  if (clickLogs.value.length > 10) {
    clickLogs.value = clickLogs.value.slice(0, 10);
  }
};

// 状态控制
const toggleLoading = () => {
  stateStatsItems[0].loading = !stateStatsItems[0].loading;
};

const toggleError = () => {
  stateStatsItems[1].error = !stateStatsItems[1].error;
};

const updateValues = () => {
  stateStatsItems[2].value = Math.floor(Math.random() * 1000);
};
</script>

<style scoped>
.stats-card-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 48px;
}

.demo-header h1 {
  color: var(--color-text-primary);
  margin-bottom: 16px;
}

.demo-header p {
  color: var(--color-text-secondary);
  font-size: 16px;
}

.demo-section {
  margin-bottom: 48px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
  color: var(--color-text-primary);
  margin-bottom: 24px;
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: 12px;
}

.demo-section h3 {
  color: var(--color-text-secondary);
  margin: 24px 0 16px 0;
  font-size: 16px;
}

.demo-content {
  margin-bottom: 24px;
}

.demo-code {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid var(--color-primary);
}

.demo-code pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #333;
}

.demo-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.click-logs {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.log-item {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 14px;
}

.log-time {
  color: var(--color-text-secondary);
  font-family: monospace;
  min-width: 80px;
}

.log-message {
  color: var(--color-text-primary);
}

@media (max-width: 768px) {
  .stats-card-demo {
    padding: 16px;
  }
  
  .demo-section {
    padding: 16px;
    margin-bottom: 24px;
  }
}
</style>
