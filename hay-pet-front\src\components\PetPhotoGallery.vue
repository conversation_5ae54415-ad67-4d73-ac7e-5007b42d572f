<template>
  <el-dialog
    v-model="dialogVisible"
    :title="pet?.name + ' 的照片相册'"
    width="800px"
    @close="handleClose"
  >
    <div class="photo-gallery">
      <!-- 上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :on-change="handlePhotoChange"
          :on-remove="handlePhotoRemove"
          :file-list="uploadFileList"
          accept="image/*"
          multiple
          :limit="10"
          list-type="picture-card"
          :on-exceed="handleExceed"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
        
        <div class="upload-actions" v-if="uploadFileList.length > 0">
          <el-button type="primary" @click="uploadPhotos" :loading="uploading">
            上传照片 ({{ uploadFileList.length }})
          </el-button>
          <el-button @click="clearUploadList">清空</el-button>
        </div>
      </div>
      
      <!-- 照片展示区域 -->
      <div class="photos-section" v-if="photos.length > 0">
        <h4>已上传的照片 ({{ photos.length }})</h4>
        <div class="photos-grid">
          <div 
            v-for="(photo, index) in photos" 
            :key="photo.id"
            class="photo-item"
            @click="previewPhoto(index)"
          >
            <img :src="photo.url" :alt="photo.name" />
            <div class="photo-overlay">
              <div class="photo-actions">
                <el-button 
                  type="primary" 
                  :icon="View" 
                  circle 
                  size="small"
                  @click.stop="previewPhoto(index)"
                />
                <el-button 
                  type="danger" 
                  :icon="Delete" 
                  circle 
                  size="small"
                  @click.stop="deletePhoto(photo)"
                />
              </div>
            </div>
            <div class="photo-info">
              <span class="photo-date">{{ formatDate(photo.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <el-empty 
        v-else 
        description="还没有上传照片，快来添加第一张照片吧！"
        :image-size="100"
      />
    </div>
    
    <!-- 照片预览 -->
    <el-dialog
      v-model="previewVisible"
      title="照片预览"
      width="600px"
      append-to-body
    >
      <div class="photo-preview">
        <img 
          v-if="currentPhoto" 
          :src="currentPhoto.url" 
          :alt="currentPhoto.name"
          class="preview-image"
        />
        <div class="preview-actions">
          <el-button 
            @click="prevPhoto" 
            :disabled="currentPhotoIndex === 0"
            :icon="ArrowLeft"
          >
            上一张
          </el-button>
          <span class="photo-counter">
            {{ currentPhotoIndex + 1 }} / {{ photos.length }}
          </span>
          <el-button 
            @click="nextPhoto" 
            :disabled="currentPhotoIndex === photos.length - 1"
            :icon="ArrowRight"
          >
            下一张
          </el-button>
        </div>
        <div class="preview-info">
          <p><strong>上传时间：</strong>{{ formatDateTime(currentPhoto?.created_at) }}</p>
          <p><strong>文件大小：</strong>{{ formatFileSize(currentPhoto?.size) }}</p>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, inject, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, View, Delete, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pet: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

const supabase = inject('supabase')
const uploadRef = ref()
const uploading = ref(false)
const dialogVisible = ref(false)
const previewVisible = ref(false)
const uploadFileList = ref([])
const photos = ref([])
const currentPhotoIndex = ref(0)

// 当前预览照片
const currentPhoto = ref(null)

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.pet?.id) {
    loadPhotos()
  }
})

// 监听对话框关闭
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 监听当前照片索引变化
watch(currentPhotoIndex, (newIndex) => {
  if (photos.value[newIndex]) {
    currentPhoto.value = photos.value[newIndex]
  }
})

// 加载照片列表
const loadPhotos = async () => {
  if (!props.pet?.id) return
  
  try {
    const { data, error } = await supabase
      .from('photos')
      .select('*')
      .eq('pet_id', props.pet.id)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('加载照片失败:', error)
      photos.value = []
    } else {
      photos.value = data || []
    }
  } catch (err) {
    console.error('加载照片错误:', err)
    photos.value = []
  }
}

// 处理照片选择
const handlePhotoChange = (file, fileList) => {
  uploadFileList.value = fileList
}

// 处理照片移除
const handlePhotoRemove = (file, fileList) => {
  uploadFileList.value = fileList
}

// 处理超出限制
const handleExceed = (files, fileList) => {
  ElMessage.warning(`最多只能上传10张照片，当前已选择${fileList.length}张，新增${files.length}张`)
}

// 清空上传列表
const clearUploadList = () => {
  uploadFileList.value = []
}

// 上传照片
const uploadPhotos = async () => {
  if (uploadFileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的照片')
    return
  }
  
  uploading.value = true
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      ElMessage.error('请先登录')
      return
    }
    
    const uploadPromises = uploadFileList.value.map(async (fileItem) => {
      const file = fileItem.raw
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}/${props.pet.id}/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${fileExt}`
      

      
      // 上传到存储，明确指定Content-Type
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('pet-media')
        .upload(fileName, file, {
          contentType: file.type,
          cacheControl: '3600',
          upsert: false
        })
      
      if (uploadError) {
        throw new Error(`上传失败: ${uploadError.message}`)
      }
      
      // 获取公开URL
      const { data: { publicUrl } } = supabase.storage
        .from('pet-media')
        .getPublicUrl(fileName)
      
      // 保存照片记录到数据库
      const photoData = {
        pet_id: props.pet.id,
        url: publicUrl,
        name: file.name,
        size: file.size,
        type: file.type,
        storage_path: fileName,
        created_at: new Date().toISOString()
      }
      
      const { data, error } = await supabase
        .from('photos')
        .insert([photoData])
        .select()
      
      if (error) {
        throw error
      }
      
      return data[0]
    })
    
    await Promise.all(uploadPromises)
    
    ElMessage.success(`成功上传${uploadFileList.value.length}张照片！`)
    uploadFileList.value = []
    await loadPhotos()
    
  } catch (error) {
    console.error('上传照片失败:', error)
    ElMessage.error(`上传失败: ${error.message}`)
  } finally {
    uploading.value = false
  }
}

// 删除照片
const deletePhoto = async (photo) => {
  try {
    await ElMessageBox.confirm('确定要删除这张照片吗？', '确认删除', {
      type: 'warning'
    })
    
    // 从存储中删除文件
    await supabase.storage
      .from('pet-media')
      .remove([photo.storage_path])
    
    // 从数据库中删除记录
    const { error } = await supabase
      .from('photos')
      .delete()
      .eq('id', photo.id)
    
    if (error) {
      throw error
    }
    
    ElMessage.success('照片删除成功')
    await loadPhotos()
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除照片失败:', error)
      ElMessage.error(`删除失败: ${error.message}`)
    }
  }
}

// 预览照片
const previewPhoto = (index) => {
  currentPhotoIndex.value = index
  currentPhoto.value = photos.value[index]
  previewVisible.value = true
}

// 上一张照片
const prevPhoto = () => {
  if (currentPhotoIndex.value > 0) {
    currentPhotoIndex.value--
  }
}

// 下一张照片
const nextPhoto = () => {
  if (currentPhotoIndex.value < photos.value.length - 1) {
    currentPhotoIndex.value++
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return ''
  if (bytes < 1024) return bytes + ' B'
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB'
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB'
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  uploadFileList.value = []
  previewVisible.value = false
}
</script>

<style scoped>
.photo-gallery {
  padding: 10px;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-actions {
  margin-top: 15px;
  text-align: center;
}

.photos-section h4 {
  margin: 0 0 15px 0;
  color: #409eff;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s;
}

.photo-item:hover {
  transform: scale(1.05);
}

.photo-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.photo-item:hover .photo-overlay {
  opacity: 1;
}

.photo-actions {
  display: flex;
  gap: 10px;
}

.photo-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 10px 8px 5px;
  color: white;
}

.photo-date {
  font-size: 12px;
}

.photo-preview {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
}

.preview-actions {
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.photo-counter {
  font-weight: bold;
  color: #409eff;
}

.preview-info {
  text-align: left;
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-top: 15px;
}

.preview-info p {
  margin: 5px 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
  }
  
  .preview-actions {
    flex-direction: column;
    gap: 10px;
  }
}
</style>