<template>
  <div class="sidebar-content">
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <h3 class="sidebar-title">🐾 导航菜单</h3>
    </div>
    
    <!-- 宠物切换区域 -->
    <div class="pet-switcher">
      <div class="section-title">当前宠物</div>
      <el-select 
        v-model="currentPetId" 
        placeholder="选择宠物" 
        @change="handleSelectPet"
        style="width: 100%; margin-bottom: 12px;"
        size="default"
        filterable
      >
        <el-option
          v-for="pet in pets"
          :key="pet.id"
          :label="pet.name"
          :value="pet.id.toString()"
        >
          <div class="pet-option-content">
            <el-avatar :size="24" class="pet-option-avatar" :src="pet.avatar_url">
              {{ pet.avatar_url ? '' : pet.name.charAt(0) }}
            </el-avatar>
            <span class="pet-option-name">{{ pet.name }}</span>
          </div>
        </el-option>
        <template #empty>
          <div style="padding: 10px; text-align: center;">没有宠物，请先添加</div>
        </template>
      </el-select>
      
      <el-button 
        @click="openAddPetDialog" 
        type="primary" 
        style="width: 100%;"
        :icon="Plus"
        size="default"
      >
        添加新宠物
      </el-button>
    </div>

    <el-divider style="margin: 20px 0;" />

    <!-- 导航菜单 -->
    <nav class="nav-menu">
      <div class="section-title">功能菜单</div>
      
      <router-link to="/" class="nav-item" exact-active-class="active">
        <el-icon class="nav-icon"><IconHome /></el-icon>
        <span class="nav-text">首页总览</span>
      </router-link>
      
      <router-link v-if="currentPetId" :to="`/pet-profile/${encodeURIComponent(getCurrentPetName())}`" class="nav-item" active-class="active">
        <el-icon class="nav-icon"><IconDocument /></el-icon>
        <span class="nav-text">宠物档案</span>
      </router-link>
      <div v-else class="nav-item disabled">
        <el-icon class="nav-icon"><IconDocument /></el-icon>
        <span class="nav-text">宠物档案</span>
      </div>
      
      <router-link to="/records" class="nav-item" active-class="active">
        <el-icon class="nav-icon"><IconFirstAidKit /></el-icon>
        <span class="nav-text">事件记录</span>
      </router-link>
      
      <router-link to="/photo-album" class="nav-item" active-class="active">
        <el-icon class="nav-icon"><IconPicture /></el-icon>
        <span class="nav-text">相册</span>
      </router-link>
      
      <router-link to="/reminders" class="nav-item" active-class="active">
        <el-icon class="nav-icon"><IconAlarmClock /></el-icon>
        <span class="nav-text">提醒事项</span>
      </router-link>
      
      <router-link to="/weight-tracking" class="nav-item" active-class="active">
        <el-icon class="nav-icon"><IconScaleToOriginal /></el-icon>
        <span class="nav-text">体重追踪</span>
      </router-link>
      
      <router-link to="/expense-tracking" class="nav-item" active-class="active">
        <el-icon class="nav-icon"><IconMoney /></el-icon>
        <span class="nav-text">花费记录</span>
      </router-link>
      
      <router-link to="/settings" class="nav-item" active-class="active">
        <el-icon class="nav-icon"><IconSetting /></el-icon>
        <span class="nav-text">设置</span>
      </router-link>
    </nav>
  </div>

  <!-- 添加宠物弹窗 -->
  <el-dialog v-model="showAddPetDialog" title="添加新宠物" width="400px">
    <el-form :model="newPetForm" label-width="80px">
      <el-form-item label="昵称" required>
        <el-input v-model="newPetForm.name" placeholder="请输入宠物昵称"></el-input>
      </el-form-item>
      <el-form-item label="类型">
        <el-input v-model="newPetForm.type" placeholder="例如：猫、狗、兔子"></el-input>
      </el-form-item>
c      <el-form-item label="品种">
        <el-input v-model="newPetForm.species" placeholder="例如：拉布拉多、英国短毛猫"></el-input>
      </el-form-item>
      <el-form-item label="生日">
        <el-date-picker
          v-model="newPetForm.birth_date"
          type="date"
          placeholder="选择出生日期"
          style="width: 100%;"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="性别">
        <el-select v-model="newPetForm.gender" placeholder="选择性别" style="width: 100%;">
          <el-option label="雄性" value="male"></el-option>
          <el-option label="雌性" value="female"></el-option>
          <el-option label="未知" value="unknown"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="体重">
        <el-input-number v-model="newPetForm.weight" placeholder="请输入体重(kg)" :precision="2" :step="0.1" :min="0" style="width: 100%;"></el-input-number>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showAddPetDialog = false">取消</el-button>
        <el-button type="primary" @click="handleAddPet">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  HomeFilled as IconHome,
  Document as IconDocument,
  FirstAidKit as IconFirstAidKit,
  Picture as IconPicture,
  Clock as IconAlarmClock,
  TrendCharts as IconScaleToOriginal,
  Money as IconMoney,
  Setting as IconSetting
} from '@element-plus/icons-vue'

// 字符编码处理函数
const ensureUtf8 = (str) => {
  if (!str) return ''
  try {
    const strValue = String(str)
    // 检查是否包含常见的编码问题字符
    if (/[\uFFFD]/.test(strValue)) {
      console.warn('侧栏检测到乱码字符:', strValue)
      // 尝试使用TextDecoder进行修复
      const encoder = new TextEncoder()
      const decoder = new TextDecoder('utf-8', { fatal: false })
      const bytes = encoder.encode(strValue)
      return decoder.decode(bytes)
    }
    return strValue
  } catch (e) {
    console.warn('侧栏字符编码处理失败:', e, '原始值:', str)
    return String(str || '')
  }
}

export default {
  name: 'SidebarMenu',
  emits: ['pet-changed'],
  components: {
    Plus,
    IconHome,
    IconDocument,
    IconFirstAidKit,
    IconPicture,
    IconAlarmClock,
    IconScaleToOriginal,
    IconMoney,
    IconSetting
  },
  setup(props, { emit }) {
    const supabase = inject('supabase')
    const pets = ref([])
    const currentPetId = ref(localStorage.getItem('currentPetId') || '')
    const showAddPetDialog = ref(false)
    const newPetForm = ref({
      name: '',
      type: '',
      breed: '',
      birth_date: '',
      gender: '',
      weight: ''
    })

// 获取宠物列表
    // 获取当前宠物名称
    const getCurrentPetName = () => {
      if (!currentPetId.value) return '';
      const currentPet = pets.value.find(p => p.id.toString() === currentPetId.value);
      return currentPet ? currentPet.name : currentPetId.value;
    };

    const fetchPets = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          pets.value = []
          return
        }

        const { data, error } = await supabase
          .from('pets')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: true })
        
        if (error) throw error
        
        // 处理宠物数据的编码问题
        pets.value = (data || []).map(pet => ({
          ...pet,
          name: ensureUtf8(pet.name),
          type: ensureUtf8(pet.type),
          species: ensureUtf8(pet.species)
        }))
        

        
        // 如果没有选中的宠物且有宠物列表，选择第一个
        if (!currentPetId.value && pets.value.length > 0) {
          currentPetId.value = pets.value[0].id.toString()
          localStorage.setItem('currentPetId', currentPetId.value)
          emit('pet-changed', currentPetId.value)
        } else if (currentPetId.value && !pets.value.find(p => p.id.toString() === currentPetId.value)) {
          // 如果当前选中的宠物不存在于列表（可能已被删除），则清空或选择第一个
          if (pets.value.length > 0) {
            handleSelectPet(pets.value[0].id)
          } else {
            currentPetId.value = ''
            localStorage.removeItem('currentPetId')
          }
        }
      } catch (error) {
        console.error('获取宠物列表失败:', error)
        pets.value = []
      }
    }

    // 选择宠物
    const handleSelectPet = (petId) => {
      currentPetId.value = petId ? petId.toString() : ''
      if (petId) {
        localStorage.setItem('currentPetId', petId.toString())
      } else {
        localStorage.removeItem('currentPetId')
      }
      // 发射事件给父组件
      emit('pet-changed', currentPetId.value)
      // 触发全局事件，通知其他组件宠物已切换
      window.dispatchEvent(new CustomEvent('petChanged', { detail: currentPetId.value }))
    }

    // 打开添加宠物对话框
    const openAddPetDialog = () => {
      showAddPetDialog.value = true
      // 重置表单
      newPetForm.value = {
        name: '',
        type: '',
        breed: '',
        birth_date: '',
        gender: '',
        weight: ''
      }
    }
    
    // 添加宠物
    const handleAddPet = async () => {
      if (newPetForm.value.name.trim() === '') {
        ElMessage.error('宠物昵称不能为空')
        return
      }
      
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          ElMessage.error('请先登录')
          return
        }
        
        // 分离体重信息并处理编码
        const { weight, ...petData } = newPetForm.value
        
        // 处理宠物数据的编码
        const processedPetData = {
          ...petData,
          name: ensureUtf8(petData.name?.trim() || ''),
          type: ensureUtf8(petData.type?.trim() || ''),
          breed: ensureUtf8(petData.breed?.trim() || ''),
          user_id: user.id
        }
        

        
        // 添加宠物
        const { data, error } = await supabase
          .from('pets')
          .insert([processedPetData])
          .select()
          .single()
        
        if (error) throw error
        
        // 如果有体重信息，添加体重记录
        if (weight && weight > 0 && data) {
          const { error: weightError } = await supabase
            .from('weight_records')
            .insert([{
              pet_id: data.id,
              weight: parseFloat(weight),
              date: new Date().toISOString().split('T')[0]
            }])
          
          if (weightError) {
            console.error('添加体重记录失败:', weightError.message)
            ElMessage.warning('宠物添加成功，但体重记录添加失败')
          }
        }
        
        ElMessage.success('宠物添加成功！')
        showAddPetDialog.value = false
        await fetchPets()
        
        if (data) {
          handleSelectPet(data.id)
        }
      } catch (error) {
        console.error('添加宠物失败:', error.message)
        ElMessage.error(`添加宠物失败: ${error.message}`)
      }
    }
    
    // 监听全局添加宠物对话框事件
    const handleOpenAddPetDialog = () => {
      openAddPetDialog()
    }
    
    onMounted(() => {
      fetchPets()
      // 监听打开添加宠物对话框事件
      window.addEventListener('openAddPetDialog', handleOpenAddPetDialog, { passive: true })
    })
    
    onBeforeUnmount(() => {
      window.removeEventListener('openAddPetDialog', handleOpenAddPetDialog)
    })
    
    return {
      pets,
      currentPetId,
      showAddPetDialog,
      newPetForm,
      handleSelectPet,
      openAddPetDialog,
      handleAddPet,
      getCurrentPetName,
      // 图标
      Plus,
      IconHome,
      IconDocument,
      IconFirstAidKit,
      IconPicture,
      IconAlarmClock,
      IconScaleToOriginal,
      IconMoney,
      IconSetting
    }
  }
}

</script>

<style scoped>
.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  overflow-y: auto;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: 20px 20px 15px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

/* 宠物切换区域 */
.pet-switcher {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 宠物选项内容 */
.pet-option-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pet-option-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.pet-option-name {
  font-size: 14px;
  color: #333;
}

/* 导航菜单 */
.nav-menu {
  flex: 1;
  padding: 0 15px 20px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: #333;
  text-decoration: none;
  border-radius: 8px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-item:hover {
  background-color: #f8f9fa;
  color: #409EFF;
  transform: translateX(2px);
}

.nav-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.nav-item.disabled {
  color: #ccc;
  cursor: not-allowed;
}

.nav-item.disabled:hover {
  background-color: transparent;
  transform: none;
}

.nav-icon {
  margin-right: 12px;
  font-size: 16px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

/* 活跃状态的特殊样式 */
.nav-item.active .nav-icon,
.nav-item.active .nav-text {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-content {
    width: 100%;
  }
  
  .pet-switcher {
    padding: 15px;
  }
  
  .nav-menu {
    padding: 0 10px 15px;
  }
  
  .nav-item {
    padding: 10px 12px;
  }
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>