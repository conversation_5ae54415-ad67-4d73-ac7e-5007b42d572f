<template>
  <div style="padding: 20px; background: #f5f5f5; min-height: 100vh;">
    <h1 style="color: #333; text-align: center;">🎨 预设样式系统测试页面</h1>
    
    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <h2>📋 组件导航</h2>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
        
        <router-link to="/style_demo/standard-tag" style="text-decoration: none;">
          <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; border: 2px solid transparent; transition: all 0.3s;">
            <div style="font-size: 2rem; margin-bottom: 8px;">🏷️</div>
            <h3 style="color: #333; margin-bottom: 8px;">StandardTag 标签组件</h3>
            <p style="color: #666; margin: 0;">统一的标签组件，支持多种尺寸、颜色和交互状态</p>
          </div>
        </router-link>

        <router-link to="/style_demo/color-picker" style="text-decoration: none;">
          <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; border: 2px solid transparent; transition: all 0.3s;">
            <div style="font-size: 2rem; margin-bottom: 8px;">🎨</div>
            <h3 style="color: #333; margin-bottom: 8px;">StandardColorPicker 颜色选择器</h3>
            <p style="color: #666; margin: 0;">优化的颜色选择器组件，包含选中动画和渐变效果</p>
          </div>
        </router-link>

        <router-link to="/style_demo/design-tokens" style="text-decoration: none;">
          <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; border: 2px solid transparent; transition: all 0.3s;">
            <div style="font-size: 2rem; margin-bottom: 8px;">🎯</div>
            <h3 style="color: #333; margin-bottom: 8px;">Design Tokens 设计令牌</h3>
            <p style="color: #666; margin: 0;">统一的设计变量系统，包含颜色、间距、字体等</p>
          </div>
        </router-link>

        <router-link to="/style_demo/button-presets" style="text-decoration: none;">
          <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; border: 2px solid transparent; transition: all 0.3s;">
            <div style="font-size: 2rem; margin-bottom: 8px;">🔘</div>
            <h3 style="color: #333; margin-bottom: 8px;">Button Presets 按钮预设</h3>
            <p style="color: #666; margin: 0;">标准化的按钮样式，包含添加按钮和交互效果</p>
          </div>
        </router-link>

      </div>
    </div>

    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <h2>🎨 预设样式快速预览</h2>
      
      <!-- 颜色展示 -->
      <div style="margin-bottom: 24px;">
        <h3>颜色系统</h3>
        <div style="display: flex; gap: 16px; flex-wrap: wrap;">
          <div style="background: #409EFF; padding: 16px 24px; color: white; border-radius: 8px; font-weight: 500; text-align: center; min-width: 120px;">主色调</div>
          <div style="background: #67C23A; padding: 16px 24px; color: white; border-radius: 8px; font-weight: 500; text-align: center; min-width: 120px;">成功色</div>
          <div style="background: #E6A23C; padding: 16px 24px; color: white; border-radius: 8px; font-weight: 500; text-align: center; min-width: 120px;">警告色</div>
          <div style="background: #F56C6C; padding: 16px 24px; color: white; border-radius: 8px; font-weight: 500; text-align: center; min-width: 120px;">危险色</div>
        </div>
      </div>

      <!-- 按钮展示 -->
      <div style="margin-bottom: 24px;">
        <h3>按钮样式</h3>
        <div style="display: flex; gap: 16px; flex-wrap: wrap;">
          <button style="padding: 12px 24px; border: none; border-radius: 8px; font-weight: 500; cursor: pointer; background: #409EFF; color: white;">主要按钮</button>
          <button style="padding: 12px 24px; border: none; border-radius: 8px; font-weight: 500; cursor: pointer; background: #67C23A; color: white;">成功按钮</button>
          <button style="padding: 12px 24px; border: none; border-radius: 8px; font-weight: 500; cursor: pointer; background: #E6A23C; color: white;">警告按钮</button>
          <button style="padding: 12px 24px; border: none; border-radius: 8px; font-weight: 500; cursor: pointer; background: #F56C6C; color: white;">危险按钮</button>
        </div>
      </div>

      <!-- 标签展示 -->
      <div style="margin-bottom: 24px;">
        <h3>标签样式</h3>
        <div style="display: flex; gap: 12px; flex-wrap: wrap;">
          <span style="padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 500; color: white; background: #409EFF;">主要标签</span>
          <span style="padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 500; color: white; background: #67C23A;">成功标签</span>
          <span style="padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 500; color: white; background: #E6A23C;">警告标签</span>
          <span style="padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 500; color: white; background: #F56C6C;">危险标签</span>
        </div>
      </div>
    </div>

    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <h2>📖 使用说明</h2>
      <p style="color: #666; line-height: 1.6;">
        这是Hay!Pet项目的预设样式系统，提供了统一的组件样式和设计令牌。
        点击上方的导航卡片可以查看各个组件的详细演示和使用方法。
      </p>
      
      <h3>快速开始</h3>
      <ol style="color: #666; line-height: 1.6;">
        <li>在CSS文件中引入设计令牌：<code style="background: #f5f5f5; padding: 2px 6px; border-radius: 4px;">@import '@/styles/design-tokens.css'</code></li>
        <li>引入组件预设样式：<code style="background: #f5f5f5; padding: 2px 6px; border-radius: 4px;">@import '@/styles/tag-presets.css'</code></li>
        <li>在Vue组件中使用预设组件：<code style="background: #f5f5f5; padding: 2px 6px; border-radius: 4px;">&lt;StandardTag text="示例" /&gt;</code></li>
      </ol>
    </div>
  </div>
</template>

<script setup>
// 简单的测试页面，无需复杂逻辑
</script>
