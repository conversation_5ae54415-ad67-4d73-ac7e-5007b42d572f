<template>
  <div :class="['stats-section', sectionClass]">
    <div :class="['stats-grid', gridClass]">
      <StandardStatsCard
        v-for="(item, index) in statsItems"
        :key="item.key || index"
        :label="item.label"
        :value="item.value"
        :date="item.date"
        :icon="item.icon"
        :variant="item.variant || item.type"
        :size="size"
        :loading="item.loading || loading"
        :error="item.error"
        :clickable="clickable"
        :custom-gradient="item.customGradient"
        :icon-color="item.iconColor"
        @click="handleCardClick(item, index)"
        @update="handleCardUpdate(item, index, $event)"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import StandardStatsCard from './StandardStatsCard.vue';

const props = defineProps({
  // 数据属性
  statsItems: {
    type: Array,
    required: true,
    validator: (items) => {
      return items.every(item => 
        item.label && 
        (item.value !== undefined) && 
        item.icon
      );
    }
  },
  
  // 样式属性
  size: {
    type: String,
    default: 'normal',
    validator: (value) => ['compact', 'normal', 'large'].includes(value)
  },
  
  // 布局属性
  columns: {
    type: [Number, String],
    default: 'auto-fit'
  },
  minCardWidth: {
    type: String,
    default: '200px'
  },
  gap: {
    type: String,
    default: ''
  },
  
  // 状态属性
  loading: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  },
  
  // 自定义类名
  sectionClass: {
    type: String,
    default: ''
  },
  gridClass: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['card-click', 'card-update']);

// 计算属性
const gridStyle = computed(() => {
  const style = {};
  
  if (props.columns === 'auto-fit') {
    style.gridTemplateColumns = `repeat(auto-fit, minmax(${props.minCardWidth}, 1fr))`;
  } else if (typeof props.columns === 'number') {
    style.gridTemplateColumns = `repeat(${props.columns}, 1fr)`;
  } else {
    style.gridTemplateColumns = props.columns;
  }
  
  if (props.gap) {
    style.gap = props.gap;
  }
  
  return style;
});

// 事件处理
const handleCardClick = (item, index) => {
  emit('card-click', { item, index });
};

const handleCardUpdate = (item, index, updateEvent) => {
  emit('card-update', { item, index, ...updateEvent });
};
</script>

<style scoped>
.stats-grid {
  v-bind: gridStyle;
}

/* 尺寸变体 */
.stats-section.compact {
  margin-bottom: var(--spacing-4);
}

.stats-section.large {
  margin-bottom: var(--spacing-8);
}

/* 响应式优化 */
@media (max-width: 767px) {
  .stats-grid {
    grid-template-columns: 1fr !important;
  }
}
</style>
