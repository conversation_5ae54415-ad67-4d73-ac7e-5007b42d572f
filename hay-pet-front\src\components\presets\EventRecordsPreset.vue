<template>
  <div class="event-records-preset">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">{{ title }}</h2>
        <div class="header-actions">
          <el-button
            class="add-record-btn"
            type="primary"
            size="large"
            @click="handleAddClick"
          >
            <el-icon class="add-icon"><Plus /></el-icon>
            <span class="btn-text">{{ addButtonText }}</span>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 日历视图预设 -->
    <CalendarViewPreset
      :title="calendarTitle"
      :records="records"
      :record-types="recordTypes"
      :color-mapping="colorMapping"
      :label-mapping="labelMapping"
      :record-id-field="recordIdField"
      :record-type-field="recordTypeField"
      :record-date-field="recordDateField"
      :record-desc-field="recordDescField"
      :show-stats="showStats"
      :custom-stats-items="customStatsItems"
      @date-click="handleDateClick"
      @view-change="handleCalendarViewChange"
      @date-change="handleDateChange"
    />

    <!-- 分类筛选和视图切换 -->
    <div class="filter-section">
      <el-card shadow="hover">
        <div class="filter-content">
          <div class="filter-left">
            <div class="filter-group">
              <div class="filter-header">
                <div class="filter-label-section">
                  <span class="filter-label">记录类型：</span>
                  <span v-if="selectedRecordTypes.length > 0" class="selected-count">
                    已选择 {{ selectedRecordTypes.length }} 个类型
                  </span>
                </div>
                <EnhancedViewToggle
                  v-model="viewMode"
                  :options="viewModeOptions"
                  size="small"
                  @change="handleViewModeChange"
                />
              </div>
              <div class="record-types-container">
                <!-- 全部标签 -->
                <StandardTag
                  text="全部"
                  variant="primary"
                  :active="selectedRecordTypes.length === 0"
                  :editable="false"
                  :deletable="false"
                  :draggable="false"
                  :show-actions="false"
                  class="all-tag-standard"
                  @click="handleTypeFilter('')"
                />

                <!-- 记录类型标签 -->
                <StandardTag
                  v-for="type in recordTypes"
                  :key="type.value"
                  :text="type.label"
                  :variant="getTagVariant(type.color)"
                  :active="selectedRecordTypes.includes(type.value)"
                  :editable="false"
                  :deletable="false"
                  :draggable="false"
                  :color="getTagTextColor(type.color, selectedRecordTypes.includes(type.value))"
                  :background-color="getTagBackground(type.color, selectedRecordTypes.includes(type.value))"
                  :border-color="getTagBorderColor(type.color, selectedRecordTypes.includes(type.value))"
                  @click="handleTypeFilter(type.value)"
                />
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 记录展示区域 -->
    <div class="records-section">
      <slot name="records" :filtered-records="filteredRecords" :view-mode="viewMode">
        <!-- 默认记录展示 -->
        <div v-if="filteredRecords.length > 0" class="records-grid">
          <el-card
            v-for="record in filteredRecords"
            :key="record[recordIdField]"
            class="record-card"
            shadow="hover"
          >
            <div class="record-content">
              <div class="record-header">
                <div class="record-type-badge">
                  <el-tag :type="getRecordTypeTag(record[recordTypeField])" size="large">
                    {{ getRecordTypeLabel(record[recordTypeField]) }}
                  </el-tag>
                </div>
                <div class="record-date">
                  <el-icon><Calendar /></el-icon>
                  {{ formatDate(record[recordDateField]) }}
                </div>
              </div>
              <div class="record-body">
                <div class="record-description">
                  {{ record[recordDescField] || '无描述' }}
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <el-empty v-else description="暂无记录，快去添加吧！">
          <el-button type="primary" @click="handleAddClick">
            <el-icon><Plus /></el-icon>
            添加第一条记录
          </el-button>
        </el-empty>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Plus, Calendar } from '@element-plus/icons-vue'
import CalendarViewPreset from './CalendarViewPreset.vue'
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue'
import StandardTag from '@/components/common/StandardTag.vue'

// Props
const props = defineProps({
  // 基础配置
  title: {
    type: String,
    default: '事件记录'
  },
  calendarTitle: {
    type: String,
    default: '日历视图'
  },
  addButtonText: {
    type: String,
    default: '添加事件记录'
  },
  
  // 数据相关
  records: {
    type: Array,
    default: () => []
  },
  recordTypes: {
    type: Array,
    default: () => []
  },
  
  // 字段映射配置
  recordIdField: {
    type: String,
    default: 'id'
  },
  recordTypeField: {
    type: String,
    default: 'record_type'
  },
  recordDateField: {
    type: String,
    default: 'date'
  },
  recordDescField: {
    type: String,
    default: 'description'
  },
  
  // 颜色和标签映射
  colorMapping: {
    type: Object,
    default: () => ({})
  },
  labelMapping: {
    type: Object,
    default: () => ({})
  },
  
  // 统计卡片配置
  showStats: {
    type: Boolean,
    default: true
  },
  customStatsItems: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'add-click',
  'date-click',
  'view-change',
  'date-change',
  'type-filter',
  'view-mode-change'
])

// 响应式数据
const selectedRecordTypes = ref([])
const viewMode = ref('cards')

// 视图模式选项
const viewModeOptions = [
  { value: 'cards', label: '卡片视图' },
  { value: 'timeline', label: '时间线' },
  { value: 'table', label: '表格视图' }
]

// 计算属性
const filteredRecords = computed(() => {
  if (selectedRecordTypes.value.length === 0) {
    return props.records
  }
  return props.records.filter(record => 
    selectedRecordTypes.value.includes(record[props.recordTypeField])
  )
})

// 方法
const handleAddClick = () => {
  emit('add-click')
}

const handleDateClick = (dateStr, records) => {
  emit('date-click', dateStr, records)
}

const handleCalendarViewChange = (newView) => {
  emit('view-change', newView)
}

const handleDateChange = (date) => {
  emit('date-change', date)
}

const handleTypeFilter = (typeValue) => {
  if (typeValue === '') {
    selectedRecordTypes.value = []
  } else {
    const index = selectedRecordTypes.value.indexOf(typeValue)
    if (index > -1) {
      selectedRecordTypes.value.splice(index, 1)
    } else {
      selectedRecordTypes.value.push(typeValue)
    }
  }
  emit('type-filter', selectedRecordTypes.value)
}

const handleViewModeChange = (newMode) => {
  emit('view-mode-change', newMode)
}

// 标签样式方法
const getTagVariant = (color) => {
  return 'primary'
}

const getTagTextColor = (color, isActive) => {
  return isActive ? '#FFFFFF' : '#303133'
}

const getTagBackground = (color, isActive) => {
  if (isActive) {
    return `linear-gradient(135deg, ${color}, ${adjustColor(color, -20)})`
  }
  return '#FFFFFF'
}

const getTagBorderColor = (color, isActive) => {
  return isActive ? color : '#DCDFE6'
}

const getRecordTypeTag = (recordType) => {
  const tagMapping = {
    vaccination: 'success',
    deworming: 'warning',
    checkup: 'primary',
    illness: 'danger',
    medication: 'info',
    allergy: 'danger',
    surgery: 'danger',
    other: 'info'
  }
  return tagMapping[recordType] || 'info'
}

const getRecordTypeLabel = (recordType) => {
  const defaultLabels = {
    vaccination: '疫苗接种',
    deworming: '驱虫',
    checkup: '体检',
    illness: '疾病',
    medication: '用药',
    allergy: '过敏',
    surgery: '手术',
    other: '其他'
  }
  return props.labelMapping[recordType] || defaultLabels[recordType] || '其他'
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const adjustColor = (color, amount) => {
  // 简单的颜色调整函数
  return color
}
</script>

<style scoped>
/* 事件记录预设组件样式 */
.event-records-preset {
  padding: 0;
}

/* 页面标题栏 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin: 0;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.add-record-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  padding: 12px 24px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.add-record-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
  background: linear-gradient(135deg, #5dade2, #85ce61);
}

.add-record-btn .add-icon {
  margin-right: 8px;
  font-size: 18px;
  transition: transform 0.3s ease;
}

.add-record-btn:hover .add-icon {
  transform: rotate(90deg);
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-section .el-card {
  border-radius: 12px;
  overflow: hidden;
}

.filter-content {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 20px 0;
}

.filter-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 12px;
}

.filter-label-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.selected-count {
  font-size: 12px;
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.record-types-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  padding: 8px 0;
}

/* 记录展示区域 */
.records-section {
  margin-bottom: 24px;
}

.records-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.record-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.record-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.record-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
}

.record-card:hover::before {
  opacity: 1;
}

.record-content {
  padding: 20px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.record-type-badge .el-tag {
  font-size: 13px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 8px;
}

.record-date {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.record-date .el-icon {
  font-size: 16px;
}

.record-body {
  color: #606266;
  line-height: 1.6;
}

.record-description {
  font-size: 14px;
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .page-title {
    font-size: 24px;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
  }

  .add-record-btn {
    width: 100%;
    justify-content: center;
  }

  .filter-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
    padding: 16px 0;
  }

  .filter-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .records-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .record-content {
    padding: 16px;
  }
}
</style>
