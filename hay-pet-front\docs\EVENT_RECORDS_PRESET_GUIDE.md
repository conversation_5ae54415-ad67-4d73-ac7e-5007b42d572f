# EventRecordsPreset 事件记录预设组件使用指南

## 📋 概述

EventRecordsPreset 是一个完整的事件记录页面预设组件，基于现有的事件记录页面设计，提供了完整的日历视图、统计卡片、筛选功能和记录展示功能。

## 🎯 主要特性

- **完整的日历视图**：集成 CalendarViewPreset，支持月视图、周视图、年视图
- **统计卡片面板**：自动计算和显示记录统计信息
- **智能筛选功能**：支持按记录类型筛选
- **多种视图模式**：卡片视图、时间线视图、表格视图
- **响应式设计**：完美适配桌面端和移动端
- **高度可定制**：支持自定义统计项、样式主题等

## 🚀 基础用法

### 简单使用

```vue
<template>
  <EventRecordsPreset
    title="宠物健康记录"
    :records="healthRecords"
    :record-types="recordTypes"
    @add-click="handleAddRecord"
  />
</template>

<script setup>
import EventRecordsPreset from '@/components/presets/EventRecordsPreset.vue'

const healthRecords = ref([
  {
    id: 1,
    record_type: 'vaccination',
    date: '2024-06-20',
    description: '狂犬病疫苗第一针'
  }
])

const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'checkup', label: '体检', color: '#409EFF' }
])

const handleAddRecord = () => {
  console.log('添加新记录')
}
</script>
```

### 完整配置

```vue
<template>
  <EventRecordsPreset
    title="宠物健康记录"
    calendar-title="健康记录日历"
    add-button-text="添加健康记录"
    :records="healthRecords"
    :record-types="recordTypes"
    :color-mapping="colorMapping"
    :label-mapping="labelMapping"
    :show-stats="true"
    :custom-stats-items="customStats"
    record-id-field="id"
    record-type-field="record_type"
    record-date-field="date"
    record-desc-field="description"
    @add-click="handleAddRecord"
    @date-click="handleDateClick"
    @view-change="handleViewChange"
    @type-filter="handleTypeFilter"
  />
</template>
```

## 📊 统计卡片配置

### 默认统计项

组件会自动计算以下统计信息：
- 总记录数
- 本月记录数
- 待处理提醒数
- 最近活动
- 活动频率分析

### 自定义统计项

```javascript
const customStatsItems = ref([
  {
    key: 'custom-health',
    type: 'month-records',
    icon: Calendar,
    label: '健康指数',
    value: '优秀',
    date: '本月评估'
  },
  {
    key: 'custom-trend',
    type: 'activity-frequency',
    icon: TrendCharts,
    label: '趋势分析',
    value: '上升',
    date: '数据趋势'
  }
])
```

### 禁用统计卡片

```vue
<EventRecordsPreset
  :show-stats="false"
  :records="records"
/>
```

## 🎨 样式定制

### 记录类型配置

```javascript
const recordTypes = ref([
  { 
    value: 'vaccination', 
    label: '疫苗接种', 
    color: '#67C23A' 
  },
  { 
    value: 'checkup', 
    label: '体检', 
    color: '#409EFF' 
  }
])

const colorMapping = ref({
  vaccination: 'vaccination',
  checkup: 'checkup'
})

const labelMapping = ref({
  vaccination: '疫苗接种',
  checkup: '体检'
})
```

### 字段映射

```javascript
// 如果数据字段名不同，可以通过 props 映射
<EventRecordsPreset
  record-id-field="recordId"
  record-type-field="type"
  record-date-field="recordDate"
  record-desc-field="notes"
  :records="records"
/>
```

## 📱 事件处理

### 主要事件

```javascript
// 添加记录
const handleAddClick = () => {
  // 打开添加记录对话框
}

// 日期点击
const handleDateClick = (dateStr, records) => {
  console.log(`点击日期: ${dateStr}, 记录数: ${records.length}`)
}

// 视图切换
const handleViewChange = (newView) => {
  console.log(`切换到: ${newView}`)
}

// 类型筛选
const handleTypeFilter = (selectedTypes) => {
  console.log(`筛选类型: ${selectedTypes}`)
}

// 视图模式切换
const handleViewModeChange = (newMode) => {
  console.log(`视图模式: ${newMode}`)
}
```

## 🔧 高级用法

### 自定义记录展示

```vue
<EventRecordsPreset :records="records">
  <template #records="{ filteredRecords, viewMode }">
    <!-- 自定义记录展示内容 -->
    <div v-if="viewMode === 'cards'" class="custom-cards">
      <div 
        v-for="record in filteredRecords" 
        :key="record.id"
        class="custom-card"
      >
        {{ record.description }}
      </div>
    </div>
  </template>
</EventRecordsPreset>
```

### 集成到现有页面

```vue
<template>
  <div class="health-records-view">
    <!-- 其他内容 -->
    
    <EventRecordsPreset
      title="健康记录"
      :records="eventRecords"
      :record-types="recordTypes"
      @add-click="openAddDialog"
      @date-click="handleCalendarDateClick"
    />
    
    <!-- 添加记录对话框等其他组件 -->
  </div>
</template>
```

## 📋 Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | '事件记录' | 页面标题 |
| calendarTitle | String | '日历视图' | 日历标题 |
| addButtonText | String | '添加事件记录' | 添加按钮文本 |
| records | Array | [] | 记录数据 |
| recordTypes | Array | [] | 记录类型配置 |
| showStats | Boolean | true | 是否显示统计卡片 |
| customStatsItems | Array | [] | 自定义统计项 |
| recordIdField | String | 'id' | ID字段名 |
| recordTypeField | String | 'record_type' | 类型字段名 |
| recordDateField | String | 'date' | 日期字段名 |
| recordDescField | String | 'description' | 描述字段名 |

## 🎪 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| add-click | - | 点击添加按钮 |
| date-click | (dateStr, records) | 点击日历日期 |
| view-change | (newView) | 切换日历视图 |
| date-change | (date) | 日期变化 |
| type-filter | (selectedTypes) | 筛选记录类型 |
| view-mode-change | (newMode) | 切换视图模式 |

## 🎨 样式定制

组件使用了预设样式系统，支持通过 CSS 变量进行主题定制：

```css
.event-records-preset {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
}
```

## 📱 响应式支持

组件完全支持响应式设计：
- **桌面端**：完整功能展示
- **平板端**：适配中等屏幕
- **移动端**：垂直布局，优化触摸操作

## 🔗 相关组件

- [CalendarViewPreset](./CALENDAR_VIEW_PRESET_GUIDE.md) - 日历视图预设
- [StandardStatsCard](./COMPONENT_STYLE_GUIDE.md#统计卡片组件) - 统计卡片组件
- [EnhancedViewToggle](./COMPONENT_STYLE_GUIDE.md#视图切换组件) - 视图切换组件
- [StandardTag](./COMPONENT_STYLE_GUIDE.md#标签组件) - 标签组件

## 🎯 最佳实践

1. **数据结构标准化**：确保记录数据结构一致
2. **类型配置完整**：提供完整的记录类型配置
3. **事件处理**：合理处理各种用户交互事件
4. **性能优化**：大量数据时考虑分页或虚拟滚动
5. **用户体验**：提供清晰的操作反馈和状态提示

## 🚀 演示地址

访问 `/style_demo/event-records` 查看完整的组件演示和交互效果。
