<template>
  <div class="view-toggle-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🔄 ViewToggle 视图切换组件</h1>
        <p class="page-description">
          统一的视图切换组件，支持多种尺寸、主题和布局变体，提供流畅的滑块动画效果
        </p>
        <router-link to="/style_demo" class="back-link">
          ← 返回样式系统首页
        </router-link>
      </div>

      <!-- 基础用法 -->
      <div class="demo-section">
        <h2>📋 基础用法</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>标准视图切换</h3>
            <div class="demo-area">
              <div class="view-toggle-preset">
                <div class="view-toggle-group" :data-active="basicActiveIndex">
                  <button
                    v-for="(option, index) in basicOptions"
                    :key="option.value"
                    @click="setBasicActive(index)"
                    :class="['view-toggle-btn', { active: basicActiveIndex === index }]"
                  >
                    <span class="btn-text">{{ option.label }}</span>
                  </button>
                </div>
              </div>
              <div class="result-display">
                当前选择：{{ basicOptions[basicActiveIndex]?.label }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 尺寸变体 -->
      <div class="demo-section">
        <h2>📏 尺寸变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>不同尺寸</h3>
            <div class="demo-area">
              <div class="size-demo">
                <h4>小号 (sm)</h4>
                <div class="view-toggle-sm-preset">
                  <div class="view-toggle-group" :data-active="smActiveIndex">
                    <button
                      v-for="(option, index) in sizeOptions"
                      :key="option.value"
                      @click="setSmActive(index)"
                      :class="['view-toggle-btn', { active: smActiveIndex === index }]"
                    >
                      <span class="btn-text">{{ option.label }}</span>
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="size-demo">
                <h4>中号 (md) - 默认</h4>
                <div class="view-toggle-preset">
                  <div class="view-toggle-group" :data-active="mdActiveIndex">
                    <button
                      v-for="(option, index) in sizeOptions"
                      :key="option.value"
                      @click="setMdActive(index)"
                      :class="['view-toggle-btn', { active: mdActiveIndex === index }]"
                    >
                      <span class="btn-text">{{ option.label }}</span>
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="size-demo">
                <h4>大号 (lg)</h4>
                <div class="view-toggle-container view-toggle-lg">
                  <div class="view-toggle-group" :data-active="lgActiveIndex">
                    <button
                      v-for="(option, index) in sizeOptions"
                      :key="option.value"
                      @click="setLgActive(index)"
                      :class="['view-toggle-btn', { active: lgActiveIndex === index }]"
                    >
                      <span class="btn-text">{{ option.label }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主题变体 -->
      <div class="demo-section">
        <h2>🎨 主题变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>不同主题色彩</h3>
            <div class="demo-area">
              <div class="theme-demo">
                <h4>标准主题</h4>
                <div class="view-toggle-container view-toggle-standard">
                  <div class="view-toggle-group" :data-active="standardActiveIndex">
                    <button
                      v-for="(option, index) in themeOptions"
                      :key="option.value"
                      @click="setStandardActive(index)"
                      :class="['view-toggle-btn', { active: standardActiveIndex === index }]"
                    >
                      <span class="btn-text">{{ option.label }}</span>
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="theme-demo">
                <h4>成功主题</h4>
                <div class="view-toggle-container view-toggle-success">
                  <div class="view-toggle-group" :data-active="successActiveIndex">
                    <button
                      v-for="(option, index) in themeOptions"
                      :key="option.value"
                      @click="setSuccessActive(index)"
                      :class="['view-toggle-btn', { active: successActiveIndex === index }]"
                    >
                      <span class="btn-text">{{ option.label }}</span>
                    </button>
                  </div>
                </div>
              </div>
              
              <div class="theme-demo">
                <h4>警告主题</h4>
                <div class="view-toggle-container view-toggle-warning">
                  <div class="view-toggle-group" :data-active="warningActiveIndex">
                    <button
                      v-for="(option, index) in themeOptions"
                      :key="option.value"
                      @click="setWarningActive(index)"
                      :class="['view-toggle-btn', { active: warningActiveIndex === index }]"
                    >
                      <span class="btn-text">{{ option.label }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 图标支持 -->
      <div class="demo-section">
        <h2>🎯 图标支持</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>带图标的视图切换</h3>
            <div class="demo-area">
              <div class="view-toggle-preset">
                <div class="view-toggle-group" :data-active="iconActiveIndex">
                  <button
                    v-for="(option, index) in iconOptions"
                    :key="option.value"
                    @click="setIconActive(index)"
                    :class="['view-toggle-btn', { active: iconActiveIndex === index }]"
                  >
                    <span class="btn-icon">{{ option.icon }}</span>
                    <span class="btn-text">{{ option.label }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="showcase-item">
            <h3>仅图标模式</h3>
            <div class="demo-area">
              <div class="view-toggle-preset view-toggle-icon-only">
                <div class="view-toggle-group" :data-active="iconOnlyActiveIndex">
                  <button
                    v-for="(option, index) in iconOptions"
                    :key="option.value"
                    @click="setIconOnlyActive(index)"
                    :class="['view-toggle-btn', { active: iconOnlyActiveIndex === index }]"
                    :title="option.label"
                  >
                    <span class="btn-icon">{{ option.icon }}</span>
                    <span class="btn-text">{{ option.label }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实际应用示例 -->
      <div class="demo-section">
        <h2>💼 实际应用示例</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>健康记录页面 - 日历视图切换</h3>
            <div class="demo-area">
              <div class="application-demo">
                <div class="calendar-header-demo">
                  <div class="calendar-nav-demo">
                    <button class="nav-btn">‹</button>
                    <span class="date-display">2024年1月</span>
                    <button class="nav-btn">›</button>
                  </div>
                  <div class="view-toggle-preset">
                    <div class="view-toggle-group" :data-active="calendarActiveIndex">
                      <button
                        v-for="(option, index) in calendarOptions"
                        :key="option.value"
                        @click="setCalendarActive(index)"
                        :class="['view-toggle-btn', { active: calendarActiveIndex === index }]"
                      >
                        <span class="btn-text">{{ option.label }}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="showcase-item">
            <h3>记录展示 - 视图模式切换</h3>
            <div class="demo-area">
              <div class="application-demo">
                <div class="filter-header-demo">
                  <span class="filter-label">记录类型：</span>
                  <div class="view-toggle-sm-preset">
                    <div class="view-toggle-group" :data-active="recordsActiveIndex">
                      <button
                        v-for="(option, index) in recordsOptions"
                        :key="option.value"
                        @click="setRecordsActive(index)"
                        :class="['view-toggle-btn', { active: recordsActiveIndex === index }]"
                      >
                        <span class="btn-text">{{ option.label }}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- API 文档 -->
      <div class="demo-section">
        <h2>📖 API 文档</h2>
        <div class="api-tables">
          <div class="api-table">
            <h3>CSS 类名</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>说明</th>
                  <th>用法</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.view-toggle-preset</td>
                  <td>标准视图切换预设</td>
                  <td>快速应用标准样式</td>
                </tr>
                <tr>
                  <td>.view-toggle-sm-preset</td>
                  <td>小号视图切换预设</td>
                  <td>紧凑界面使用</td>
                </tr>
                <tr>
                  <td>.view-toggle-container</td>
                  <td>视图切换容器</td>
                  <td>与其他类组合使用</td>
                </tr>
                <tr>
                  <td>.view-toggle-group</td>
                  <td>按钮组容器</td>
                  <td>包含所有切换按钮</td>
                </tr>
                <tr>
                  <td>.view-toggle-btn</td>
                  <td>切换按钮</td>
                  <td>单个切换选项</td>
                </tr>
                <tr>
                  <td>.active</td>
                  <td>选中状态</td>
                  <td>应用于当前选中按钮</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="api-table">
            <h3>尺寸变体</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>尺寸</th>
                  <th>适用场景</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.view-toggle-sm</td>
                  <td>小号</td>
                  <td>紧凑界面、工具栏</td>
                </tr>
                <tr>
                  <td>.view-toggle-md</td>
                  <td>中号（默认）</td>
                  <td>标准界面</td>
                </tr>
                <tr>
                  <td>.view-toggle-lg</td>
                  <td>大号</td>
                  <td>重要操作、大屏幕</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="api-table">
            <h3>主题变体</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>主题色</th>
                  <th>使用场景</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.view-toggle-standard</td>
                  <td>蓝绿渐变</td>
                  <td>默认主题</td>
                </tr>
                <tr>
                  <td>.view-toggle-success</td>
                  <td>绿色渐变</td>
                  <td>成功状态</td>
                </tr>
                <tr>
                  <td>.view-toggle-warning</td>
                  <td>橙色渐变</td>
                  <td>警告状态</td>
                </tr>
                <tr>
                  <td>.view-toggle-info</td>
                  <td>灰色渐变</td>
                  <td>信息展示</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="api-table">
            <h3>布局变体</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>特点</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.view-toggle-compact</td>
                  <td>紧凑布局</td>
                  <td>减少内边距和间距</td>
                </tr>
                <tr>
                  <td>.view-toggle-relaxed</td>
                  <td>宽松布局</td>
                  <td>增加内边距和间距</td>
                </tr>
                <tr>
                  <td>.view-toggle-icon-only</td>
                  <td>仅图标</td>
                  <td>隐藏文字，只显示图标</td>
                </tr>
                <tr>
                  <td>.view-toggle-responsive</td>
                  <td>响应式</td>
                  <td>移动端自动切换图标模式</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 基础选项
const basicOptions = [
  { value: 'list', label: '列表' },
  { value: 'grid', label: '网格' },
  { value: 'card', label: '卡片' }
]
const basicActiveIndex = ref(0)
const setBasicActive = (index) => { basicActiveIndex.value = index }

// 尺寸选项
const sizeOptions = [
  { value: 'view1', label: '视图1' },
  { value: 'view2', label: '视图2' },
  { value: 'view3', label: '视图3' }
]
const smActiveIndex = ref(0)
const mdActiveIndex = ref(1)
const lgActiveIndex = ref(2)
const setSmActive = (index) => { smActiveIndex.value = index }
const setMdActive = (index) => { mdActiveIndex.value = index }
const setLgActive = (index) => { lgActiveIndex.value = index }

// 主题选项
const themeOptions = [
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' }
]
const standardActiveIndex = ref(0)
const successActiveIndex = ref(1)
const warningActiveIndex = ref(2)
const setStandardActive = (index) => { standardActiveIndex.value = index }
const setSuccessActive = (index) => { successActiveIndex.value = index }
const setWarningActive = (index) => { warningActiveIndex.value = index }

// 图标选项
const iconOptions = [
  { value: 'cards', label: '卡片', icon: '📋' },
  { value: 'timeline', label: '时间线', icon: '📅' },
  { value: 'table', label: '表格', icon: '📊' }
]
const iconActiveIndex = ref(0)
const iconOnlyActiveIndex = ref(1)
const setIconActive = (index) => { iconActiveIndex.value = index }
const setIconOnlyActive = (index) => { iconOnlyActiveIndex.value = index }

// 应用示例选项
const calendarOptions = [
  { value: 'month', label: '月视图' },
  { value: 'week', label: '周视图' },
  { value: 'year', label: '年视图' }
]
const calendarActiveIndex = ref(0)
const setCalendarActive = (index) => { calendarActiveIndex.value = index }

const recordsOptions = [
  { value: 'cards', label: '卡片' },
  { value: 'timeline', label: '时间线' },
  { value: 'table', label: '表格' }
]
const recordsActiveIndex = ref(0)
const setRecordsActive = (index) => { recordsActiveIndex.value = index }
</script>

<style scoped>
/* 引入设计令牌 */
@import '@/styles/design-tokens.css';

.view-toggle-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 18px;
  color: #909399;
  max-width: 800px;
  margin: 0 auto 16px;
  line-height: 1.6;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #409EFF;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #337ECC;
}

/* 演示区块 */
.demo-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.demo-section h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 20px;
  border-bottom: 2px solid #E4E7ED;
  padding-bottom: 8px;
}

.showcase-item h3 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 16px;
}

.demo-area {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 12px;
}

.result-display {
  margin-top: 16px;
  padding: 12px;
  background: #FAFAFA;
  border-radius: 8px;
  color: #606266;
  font-size: 14px;
}

/* 尺寸演示 */
.size-demo {
  margin-bottom: 24px;
}

.size-demo h4 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 14px;
}

/* 主题演示 */
.theme-demo {
  margin-bottom: 24px;
}

.theme-demo h4 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 14px;
}

/* 应用演示 */
.application-demo {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #E4E7ED;
}

.calendar-header-demo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.calendar-nav-demo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #F5F7FA;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: #E4E7ED;
  color: #303133;
}

.date-display {
  font-weight: 500;
  color: #303133;
  min-width: 100px;
  text-align: center;
}

.filter-header-demo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.filter-label {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

/* API 表格 */
.api-tables {
  display: grid;
  gap: 24px;
}

.api-table h3 {
  color: #303133;
  margin-bottom: 12px;
}

.api-table table {
  width: 100%;
  border-collapse: collapse;
  background: #F5F7FA;
  border-radius: 12px;
  overflow: hidden;
}

.api-table th,
.api-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #E4E7ED;
}

.api-table th {
  background: #FAFAFA;
  font-weight: 600;
  color: #303133;
}

.api-table td {
  color: #606266;
}

.api-table td:first-child {
  font-family: monospace;
  color: #409EFF;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .view-toggle-demo {
    padding: 16px;
  }
  
  .calendar-header-demo {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-header-demo {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .api-table {
    overflow-x: auto;
  }
}
</style>
