// Dashboard板块配置状态管理
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { DashboardConfig, DashboardBlock } from '@/types/dashboard';
import { DEFAULT_DASHBOARD_CONFIG } from '@/types/dashboard';
import { supabase } from '@/utils/supabase';

export const useDashboardStore = defineStore('dashboard', () => {
  const config = ref<DashboardConfig>(loadConfigFromStorage());
  
  // 获取可见的板块，按order排序
  const visibleBlocks = computed(() => {
    return config.value.blocks
      .filter(block => block.visible)
      .sort((a, b) => a.order - b.order);
  });
  
  // 获取所有板块，按order排序
  const allBlocks = computed(() => {
    return config.value.blocks.sort((a, b) => a.order - b.order);
  });
  
  // 更新板块可见性
  function updateBlockVisibility(blockId: string, visible: boolean) {
    const block = config.value.blocks.find(b => b.id === blockId);
    if (block && !block.required) {
      block.visible = visible;
      saveConfigToStorage();
    }
  }
  
  // 更新板块排序
  function updateBlockOrder(blockIds: string[]) {
    blockIds.forEach((id, index) => {
      const block = config.value.blocks.find(b => b.id === id);
      if (block) {
        block.order = index + 1;
      }
    });
    saveConfigToStorage();
  }
  
  // 重置为默认配置
  async function resetToDefault() {
    try {
      config.value = JSON.parse(JSON.stringify(DEFAULT_DASHBOARD_CONFIG));
      config.value.lastModified = new Date();
      
      // 先保存到localStorage
      localStorage.setItem('dashboard-config', JSON.stringify(config.value));
      
      // 再保存到数据库
      await saveConfigToDatabase();
      
      return { success: true };
    } catch (error) {
      console.error('Failed to reset dashboard config:', error);
      return { success: false, error: error.message };
    }
  }
  
  // 从localStorage加载配置（同步版本，用于初始化）
  function loadConfigFromStorage(): DashboardConfig {
    try {
      const stored = localStorage.getItem('dashboard-config');
      if (stored) {
        const parsed = JSON.parse(stored);
        // 合并默认配置，确保新增的板块也能显示
        return mergeWithDefault(parsed);
      }
    } catch (error) {
      console.warn('Failed to load dashboard config:', error);
    }
    return JSON.parse(JSON.stringify(DEFAULT_DASHBOARD_CONFIG));
  }
  
  // 异步加载配置（优先从数据库加载）
  async function loadConfigAsync(): Promise<DashboardConfig> {
    // 先尝试从数据库加载
    const dbConfig = await loadConfigFromDatabase();
    if (dbConfig) {
      config.value = dbConfig;
      // 同步到localStorage
      localStorage.setItem('dashboard-config', JSON.stringify(dbConfig));
      return dbConfig;
    }
    
    // 如果数据库没有，则从localStorage加载
    const localConfig = loadConfigFromStorage();
    config.value = localConfig;
    
    // 如果用户已登录，将localStorage的配置同步到数据库
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      await saveConfigToDatabase();
    }
    
    return localConfig;
  }
  
  // 保存配置到localStorage和数据库
  async function saveConfigToStorage() {
    try {
      config.value.lastModified = new Date();
      localStorage.setItem('dashboard-config', JSON.stringify(config.value));
      
      // 同时保存到数据库
      await saveConfigToDatabase();
    } catch (error) {
      console.error('Failed to save dashboard config:', error);
    }
  }
  
  // 保存配置到数据库
  async function saveConfigToDatabase() {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;
      
      // 使用更安全的upsert方式，指定冲突解决策略
      const { error } = await supabase
        .from('user_configs')
        .upsert({
          user_id: user.id,
          config_type: 'dashboard',
          config_data: config.value,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,config_type',
          ignoreDuplicates: false
        });
        
      if (error) {
        console.error('Failed to save config to database:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to save config to database:', error);
      // 不重新抛出错误，避免影响用户体验
    }
  }
  
  // 从数据库加载配置
  async function loadConfigFromDatabase(): Promise<DashboardConfig | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;
      
      const { data, error } = await supabase
        .from('user_configs')
        .select('config_data')
        .eq('user_id', user.id)
        .eq('config_type', 'dashboard')
        .single();
        
      if (error || !data) {
        return null;
      }
      
      return mergeWithDefault(data.config_data);
    } catch (error) {
      console.error('Failed to load config from database:', error);
      return null;
    }
  }
  
  // 合并默认配置
  function mergeWithDefault(userConfig: any): DashboardConfig {
    const defaultBlocks = DEFAULT_DASHBOARD_CONFIG.blocks;
    const userBlocks = userConfig.blocks || [];
    
    // 确保所有默认板块都存在
    const mergedBlocks = defaultBlocks.map(defaultBlock => {
      const userBlock = userBlocks.find((b: DashboardBlock) => b.id === defaultBlock.id);
      return userBlock ? { ...defaultBlock, ...userBlock } : { ...defaultBlock };
    });
    
    return {
      blocks: mergedBlocks,
      lastModified: userConfig.lastModified ? new Date(userConfig.lastModified) : new Date()
    };
  }
  
  // 手动加载配置（用于组件中调用）
  function loadConfig() {
    config.value = loadConfigFromStorage();
  }

  // 保存配置（用于组件中调用）
  function saveConfig() {
    saveConfigToStorage();
  }

  // 批量更新板块顺序（用于拖拽排序）
  function updateBlocksOrder(blocks: DashboardBlock[]) {
    config.value.blocks = blocks;
    saveConfigToStorage();
  }

  // 获取所有板块（用于设置页面）
  const blocks = computed(() => config.value.blocks);

  return {
    config,
    blocks,
    visibleBlocks,
    allBlocks,
    updateBlockVisibility,
    updateBlockOrder,
    updateBlocksOrder,
    resetToDefault,
    loadConfig,
    loadConfigAsync,
    saveConfig
  };
});