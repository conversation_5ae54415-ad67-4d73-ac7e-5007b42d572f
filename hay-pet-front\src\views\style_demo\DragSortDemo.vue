<template>
  <div class="drag-sort-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🔄 拖拽排序组件演示</h1>
        <p class="page-description">
          完整的拖拽排序解决方案，包括视觉反馈、数据库更新和错误处理机制
        </p>
      </div>

      <!-- 基础拖拽演示 -->
      <div class="demo-section">
        <h2>📋 基础标签拖拽</h2>
        <p class="section-description">
          使用 vuedraggable 和预设样式实现的标签拖拽排序功能
        </p>
        
        <div class="demo-content">
          <div class="demo-area">
            <draggable
              v-model="basicTags"
              v-bind="dragOptions"
              @start="onDragStart"
              @end="onBasicDragEnd"
              item-key="id"
              class="draggable-container"
            >
              <template #item="{ element: tag }">
                <StandardTag
                  :text="tag.label"
                  :variant="tag.variant"
                  :draggable="true"
                  :class="{ 'is-dragging': isDragging }"
                />
              </template>
            </draggable>
          </div>
          
          <div class="demo-info">
            <h4>拖拽状态</h4>
            <p>正在拖拽: {{ isDragging ? '是' : '否' }}</p>
            <p>当前顺序: {{ basicTags.map(t => t.label).join(' → ') }}</p>
          </div>
        </div>
      </div>

      <!-- 网格拖拽演示 -->
      <div class="demo-section">
        <h2>📊 网格布局拖拽</h2>
        <p class="section-description">
          统计卡片的网格拖拽排序，支持自适应布局
        </p>
        
        <div class="demo-content">
          <draggable
            v-model="gridItems"
            v-bind="dragOptions"
            @start="onDragStart"
            @end="onGridDragEnd"
            item-key="id"
            class="draggable-grid"
          >
            <template #item="{ element: item }">
              <div class="draggable-grid-item" :class="{ 'is-dragging': isDragging }">
                <StandardStatsCard
                  :label="item.label"
                  :value="item.value"
                  :date="item.date"
                  :icon="item.icon"
                  :variant="item.variant"
                />
              </div>
            </template>
          </draggable>
        </div>
      </div>

      <!-- 列表拖拽演示 -->
      <div class="demo-section">
        <h2>📝 列表拖拽</h2>
        <p class="section-description">
          带拖拽手柄的列表项排序，适用于复杂的列表界面
        </p>
        
        <div class="demo-content">
          <draggable
            v-model="listItems"
            v-bind="dragOptions"
            @start="onDragStart"
            @end="onListDragEnd"
            item-key="id"
            tag="ul"
            class="sortable-list"
          >
            <template #item="{ element: item }">
              <li class="sortable-list-item" :class="{ 'is-dragging': isDragging }">
                <div class="drag-handle">
                  <el-icon><Rank /></el-icon>
                </div>
                <div class="item-content">
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.description }}</p>
                </div>
                <div class="item-meta">
                  <span class="priority" :class="item.priority">{{ item.priority }}</span>
                </div>
              </li>
            </template>
          </draggable>
        </div>
      </div>

      <!-- 状态演示 -->
      <div class="demo-section">
        <h2>⚡ 状态和反馈</h2>
        <p class="section-description">
          拖拽过程中的各种状态和用户反馈效果
        </p>
        
        <div class="demo-content">
          <div class="status-demo">
            <div class="status-item">
              <h4>拖拽反馈</h4>
              <div v-if="showFeedback" class="drag-feedback show">
                {{ feedbackMessage }}
              </div>
              <el-button @click="showSuccessFeedback">成功反馈</el-button>
              <el-button @click="showErrorFeedback">错误反馈</el-button>
            </div>
            
            <div class="status-item">
              <h4>加载状态</h4>
              <div class="drag-loading-demo" :class="{ 'drag-loading': isLoading }">
                <StandardTag text="加载中的标签" variant="primary" />
              </div>
              <el-button @click="toggleLoading">切换加载状态</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 后端集成说明 -->
      <div class="demo-section">
        <h2>🔗 后端集成说明</h2>
        <p class="section-description">
          完整的拖拽排序功能需要后端数据库支持，以下是详细的集成要求和示例
        </p>

        <div class="integration-content">
          <div class="integration-item">
            <h4>📊 数据库字段要求</h4>
            <div class="database-schema">
              <pre><code>-- 示例表结构 (Supabase/PostgreSQL)
CREATE TABLE reminder_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  value TEXT NOT NULL,
  label TEXT NOT NULL,
  color TEXT NOT NULL,
  icon TEXT,
  description TEXT,
  is_system BOOLEAN DEFAULT false,
  sort_order INTEGER,  -- 排序字段（必需）
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引以优化排序查询
CREATE INDEX idx_reminder_categories_sort_order
ON reminder_categories(sort_order);</code></pre>
            </div>
          </div>

          <div class="integration-item">
            <h4>🔄 数据加载逻辑</h4>
            <div class="code-block">
              <pre><code>// 按 sort_order 排序加载数据
const loadCategories = async () => {
  try {
    const { data, error } = await supabase
      .from('reminder_categories')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) throw error;
    categories.value = data || [];
  } catch (error) {
    console.error('加载分类失败:', error);
    ElMessage.error('加载分类失败');
  }
};</code></pre>
            </div>
          </div>

          <div class="integration-item">
            <h4>💾 拖拽保存逻辑</h4>
            <div class="code-block">
              <pre><code>const onDragEnd = async (evt) => {
  isDragging.value = false;

  // 如果位置没有改变，不需要更新数据库
  if (evt.oldIndex === evt.newIndex) {
    return;
  }

  try {
    // 获取当前用户ID（如果需要权限验证）
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      ElMessage.error('用户未登录');
      return;
    }

    // 更新所有项目的排序
    const updates = categories.value.map((item, index) => ({
      id: item.id,
      sort_order: index + 1
    }));

    // 批量更新数据库
    for (const update of updates) {
      const { error } = await supabase
        .from('reminder_categories')
        .update({ sort_order: update.sort_order })
        .eq('id', update.id);

      if (error) throw error;
    }

    ElMessage.success('排序已保存');
  } catch (error) {
    console.error('保存排序失败:', error.message);
    ElMessage.error('保存排序失败: ' + error.message);
    // 重新获取数据以恢复原始顺序
    await loadCategories();
  }
};</code></pre>
            </div>
          </div>

          <div class="integration-item">
            <h4>➕ 新增项目时的排序处理</h4>
            <div class="code-block">
              <pre><code>const addNewCategory = async (formData) => {
  try {
    // 获取当前最大的sort_order
    const { data: maxOrderData } = await supabase
      .from('reminder_categories')
      .select('sort_order')
      .order('sort_order', { ascending: false })
      .limit(1);

    const nextSortOrder = maxOrderData && maxOrderData.length > 0
      ? (maxOrderData[0].sort_order || 0) + 1
      : 1;

    const categoryData = {
      ...formData,
      sort_order: nextSortOrder  // 设置新的排序值
    };

    const { error } = await supabase
      .from('reminder_categories')
      .insert(categoryData);

    if (error) throw error;
    ElMessage.success('分类添加成功！');
    await loadCategories();
  } catch (error) {
    console.error('添加分类失败:', error.message);
    ElMessage.error('添加分类失败: ' + error.message);
  }
};</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 代码示例 -->
      <div class="demo-section">
        <h2>💻 前端代码示例</h2>
        <div class="code-examples">
          <div class="code-example">
            <h4>基础用法</h4>
            <pre><code>&lt;template&gt;
  &lt;draggable
    v-model="items"
    v-bind="dragOptions"
    @start="onDragStart"
    @end="onDragEnd"
    item-key="id"
    class="draggable-container"
  &gt;
    &lt;template #item="{ element: item }"&gt;
      &lt;StandardTag
        :text="item.label"
        :draggable="true"
        :class="{ 'is-dragging': isDragging }"
      /&gt;
    &lt;/template&gt;
  &lt;/draggable&gt;
&lt;/template&gt;</code></pre>
          </div>
          
          <div class="code-example">
            <h4>拖拽配置</h4>
            <pre><code>const dragOptions = {
  animation: 300,
  group: 'items',
  disabled: false,
  ghostClass: 'ghost'
}

const onDragStart = () => {
  isDragging.value = true
}

const onDragEnd = async (evt) => {
  isDragging.value = false
  if (evt.oldIndex === evt.newIndex) return
  
  try {
    await updateOrder(items.value)
    ElMessage.success('排序已保存')
  } catch (error) {
    ElMessage.error('保存失败')
    await fetchItems() // 回滚
  }
}</code></pre>
          </div>
        </div>
      </div>

      <!-- 错误处理和最佳实践 -->
      <div class="demo-section">
        <h2>⚠️ 错误处理和最佳实践</h2>
        <div class="best-practices">
          <div class="practice-item">
            <h4>🔄 数据回滚机制</h4>
            <div class="code-block">
              <pre><code>const onDragEnd = async (evt) => {
  isDragging.value = false;

  if (evt.oldIndex === evt.newIndex) return;

  // 保存原始顺序用于回滚
  const originalOrder = [...categories.value];

  try {
    // 显示加载状态
    isUpdating.value = true;

    // 批量更新数据库
    await updateCategoriesOrder(categories.value);

    ElMessage.success('排序已保存');
  } catch (error) {
    console.error('保存排序失败:', error);
    ElMessage.error('保存排序失败，已恢复原始顺序');

    // 回滚到原始顺序
    categories.value = originalOrder;
  } finally {
    isUpdating.value = false;
  }
};</code></pre>
            </div>
          </div>

          <div class="practice-item">
            <h4>🚀 性能优化</h4>
            <div class="code-block">
              <pre><code>// 使用防抖避免频繁更新
import { debounce } from 'lodash-es';

const debouncedUpdate = debounce(async (updates) => {
  try {
    await batchUpdateOrder(updates);
    ElMessage.success('排序已保存');
  } catch (error) {
    ElMessage.error('保存失败');
  }
}, 300);

// 批量更新优化
const batchUpdateOrder = async (items) => {
  const updates = items.map((item, index) => ({
    id: item.id,
    sort_order: index + 1
  }));

  // 使用事务确保数据一致性
  const { error } = await supabase.rpc('update_categories_order', {
    updates: updates
  });

  if (error) throw error;
};</code></pre>
            </div>
          </div>

          <div class="practice-item">
            <h4>📱 移动端优化</h4>
            <div class="code-block">
              <pre><code>// 移动端拖拽配置
const dragOptions = computed(() => ({
  animation: 300,
  group: 'categories',
  disabled: false,
  ghostClass: 'ghost',
  // 移动端特殊配置
  touchStartThreshold: 10,
  delay: isMobile.value ? 200 : 0,
  delayOnTouchStart: true,
  forceFallback: isMobile.value
}));

// 检测移动端
const isMobile = computed(() => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i
    .test(navigator.userAgent);
});</code></pre>
            </div>
          </div>

          <div class="practice-item">
            <h4>🔐 权限验证</h4>
            <div class="code-block">
              <pre><code>const onDragEnd = async (evt) => {
  // 权限检查
  if (!hasEditPermission.value) {
    ElMessage.error('您没有编辑权限');
    await loadCategories(); // 恢复原始顺序
    return;
  }

  // 系统分类不允许拖拽
  const draggedItem = categories.value[evt.newIndex];
  if (draggedItem.is_system) {
    ElMessage.warning('系统分类不允许调整顺序');
    await loadCategories();
    return;
  }

  // 继续正常的拖拽处理...
};</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据库函数示例 -->
      <div class="demo-section">
        <h2>🗄️ 数据库函数示例 (PostgreSQL)</h2>
        <div class="database-functions">
          <div class="function-item">
            <h4>批量更新排序函数</h4>
            <div class="code-block">
              <pre><code>-- 创建批量更新排序的数据库函数
CREATE OR REPLACE FUNCTION update_categories_order(updates JSONB)
RETURNS VOID AS $$
DECLARE
  update_item JSONB;
BEGIN
  FOR update_item IN SELECT * FROM jsonb_array_elements(updates)
  LOOP
    UPDATE reminder_categories
    SET sort_order = (update_item->>'sort_order')::INTEGER,
        updated_at = NOW()
    WHERE id = (update_item->>'id')::UUID;
  END LOOP;
END;
$$ LANGUAGE plpgsql;</code></pre>
            </div>
          </div>

          <div class="function-item">
            <h4>自动重排序函数</h4>
            <div class="code-block">
              <pre><code>-- 删除项目后自动重排序
CREATE OR REPLACE FUNCTION reorder_categories_after_delete()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE reminder_categories
  SET sort_order = new_order.row_number
  FROM (
    SELECT id, ROW_NUMBER() OVER (ORDER BY sort_order) as row_number
    FROM reminder_categories
    WHERE sort_order > OLD.sort_order
  ) as new_order
  WHERE reminder_categories.id = new_order.id;

  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_reorder_after_delete
  AFTER DELETE ON reminder_categories
  FOR EACH ROW
  EXECUTE FUNCTION reorder_categories_after_delete();</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Rank, Document, Bell, TrendCharts, Calendar } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import StandardTag from '@/components/common/StandardTag.vue'
import StandardStatsCard from '@/components/common/StandardStatsCard.vue'

// 拖拽状态
const isDragging = ref(false)
const isLoading = ref(false)
const showFeedback = ref(false)
const feedbackMessage = ref('')

// 拖拽配置
const dragOptions = {
  animation: 300,
  group: 'demo-items',
  disabled: false,
  ghostClass: 'ghost'
}

// 基础标签数据
const basicTags = ref([
  { id: 1, label: '疫苗接种', variant: 'success' },
  { id: 2, label: '驱虫', variant: 'warning' },
  { id: 3, label: '体检', variant: 'primary' },
  { id: 4, label: '过敏记录', variant: 'danger' },
  { id: 5, label: '其他', variant: 'info' }
])

// 网格项目数据
const gridItems = ref([
  {
    id: 1,
    label: '总记录数',
    value: 42,
    date: '全部记录',
    icon: Document,
    variant: 'total-records'
  },
  {
    id: 2,
    label: '本月记录',
    value: 8,
    date: '2024年6月',
    icon: Calendar,
    variant: 'month-records'
  },
  {
    id: 3,
    label: '待处理',
    value: 3,
    date: '需要关注',
    icon: Bell,
    variant: 'pending-reminders'
  },
  {
    id: 4,
    label: '活跃度',
    value: 85,
    date: '本周统计',
    icon: TrendCharts,
    variant: 'activity-frequency'
  }
])

// 列表项目数据
const listItems = ref([
  {
    id: 1,
    title: '完成宠物疫苗接种',
    description: '带小猫去宠物医院接种疫苗',
    priority: 'high'
  },
  {
    id: 2,
    title: '购买宠物食品',
    description: '购买高质量的猫粮和零食',
    priority: 'medium'
  },
  {
    id: 3,
    title: '清理猫砂盆',
    description: '定期清理和更换猫砂',
    priority: 'low'
  },
  {
    id: 4,
    title: '预约宠物美容',
    description: '为宠物预约洗澡和美容服务',
    priority: 'medium'
  }
])

// 拖拽事件处理
const onDragStart = () => {
  isDragging.value = true
}

const onBasicDragEnd = () => {
  isDragging.value = false
  ElMessage.success('标签顺序已更新')
}

const onGridDragEnd = () => {
  isDragging.value = false
  ElMessage.success('网格布局已更新')
}

const onListDragEnd = () => {
  isDragging.value = false
  ElMessage.success('列表顺序已更新')
}

// 反馈演示
const showSuccessFeedback = () => {
  feedbackMessage.value = '排序保存成功！'
  showFeedback.value = true
  setTimeout(() => {
    showFeedback.value = false
  }, 2000)
}

const showErrorFeedback = () => {
  feedbackMessage.value = '保存失败，请重试'
  showFeedback.value = true
  setTimeout(() => {
    showFeedback.value = false
  }, 2000)
}

const toggleLoading = () => {
  isLoading.value = !isLoading.value
}
</script>

<style scoped>
.drag-sort-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 18px;
  color: #909399;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 演示区块 */
.demo-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 8px;
}

.section-description {
  color: #909399;
  margin-bottom: 20px;
  line-height: 1.6;
}

/* 演示内容 */
.demo-content {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.demo-area {
  flex: 1;
  min-height: 120px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #e5e7eb;
}

.demo-info {
  width: 200px;
  padding: 16px;
  background: #f0f2ff;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.demo-info h4 {
  margin: 0 0 12px 0;
  color: #409EFF;
  font-size: 14px;
  font-weight: 600;
}

.demo-info p {
  margin: 8px 0;
  font-size: 13px;
  color: #606266;
}

/* 状态演示 */
.status-demo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.status-item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.status-item h4 {
  margin-bottom: 16px;
  color: #303133;
}

.drag-loading-demo {
  margin: 16px 0;
  display: inline-block;
}

/* 代码示例 */
.code-examples {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.code-example {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.code-example h4 {
  background: #409EFF;
  color: white;
  margin: 0;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
}

.code-example pre {
  margin: 0;
  padding: 16px;
  background: #2d3748;
  color: #e2e8f0;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
}

.code-example code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 列表项样式 */
.sortable-list-item .item-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #303133;
}

.sortable-list-item .item-content p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.item-meta .priority {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.priority.high {
  background: #fef0f0;
  color: #f56c6c;
}

.priority.medium {
  background: #fdf6ec;
  color: #e6a23c;
}

.priority.low {
  background: #f0f9ff;
  color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-content {
    flex-direction: column;
  }

  .demo-info {
    width: 100%;
  }

  .status-demo {
    grid-template-columns: 1fr;
  }

  .code-examples {
    grid-template-columns: 1fr;
  }

  .draggable-grid {
    grid-template-columns: 1fr;
  }
}

/* 集成说明样式 */
.integration-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.integration-item {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.integration-item h4 {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  margin: 0;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
}

.database-schema,
.code-block {
  margin: 0;
}

.database-schema pre,
.code-block pre {
  margin: 0;
  padding: 16px;
  background: #2d3748;
  color: #e2e8f0;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
}

.database-schema code,
.code-block code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 最佳实践样式 */
.best-practices {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.practice-item {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.practice-item h4 {
  background: linear-gradient(135deg, #E6A23C 0%, #F56C6C 100%);
  color: white;
  margin: 0;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
}

/* 数据库函数样式 */
.database-functions {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.function-item {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.function-item h4 {
  background: linear-gradient(135deg, #909399 0%, #606266 100%);
  color: white;
  margin: 0;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .best-practices {
    grid-template-columns: 1fr;
  }

  .integration-content {
    gap: 16px;
  }

  .database-functions {
    gap: 16px;
  }
}
</style>
