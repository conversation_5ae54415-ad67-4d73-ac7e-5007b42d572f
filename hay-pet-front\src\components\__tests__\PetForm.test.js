import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElButton, ElForm, ElFormItem, ElInput, ElInputNumber } from 'element-plus'
import PetForm from '../PetForm.vue'

// Mock Supabase
const mockSupabase = {
  auth: {
    getUser: vi.fn().mockResolvedValue({
      data: { user: { id: 'test-user-id' } }
    })
  },
  from: vi.fn(() => ({
    insert: vi.fn(() => ({
      select: vi.fn().mockResolvedValue({ data: [{ id: 'test-pet-id' }], error: null })
    }))
  })),
  storage: {
    from: vi.fn(() => ({
      upload: vi.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null }),
      getPublicUrl: vi.fn().mockReturnValue({ data: { publicUrl: 'test-url' } })
    }))
  }
}

// Mock provide/inject
const createWrapper = (props = {}) => {
  return mount(PetForm, {
    props,
    global: {
      components: {
        ElButton,
        ElForm,
        ElFormItem,
        ElInput,
        ElInputNumber
      },
      provide: {
        supabase: mockSupabase
      },
      stubs: {
        'el-card': { template: '<div><slot name="header"></slot><slot></slot></div>' },
        'el-upload': { template: '<div><slot></slot></div>' },
        'el-radio-group': { template: '<div><slot></slot></div>' },
        'el-radio': { template: '<div><slot></slot></div>' }
      }
    }
  })
}

describe('PetForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染表单', () => {
    const wrapper = createWrapper()
    
    // 检查表单元素是否存在
    expect(wrapper.find('input[placeholder="请输入宠物姓名"]').exists()).toBe(true)
    expect(wrapper.find('input[placeholder="请输入宠物品种，如：金毛、英短、布偶猫等"]').exists()).toBe(true)
    expect(wrapper.find('.el-input-number').exists()).toBe(true)
    expect(wrapper.findComponent(ElButton).exists()).toBe(true)
  })

  it('应该正确初始化表单数据', () => {
    const wrapper = createWrapper()
    const vm = wrapper.vm
    
    // 检查初始表单数据
    expect(vm.form.name).toBe('')
    expect(vm.form.species).toBe('')
    expect(vm.form.age).toBe(null)
    expect(vm.form.gender).toBe('unknown')
    expect(vm.form.weight).toBe(null)
    expect(vm.form.notes).toBe('')
  })

  it('应该能够更新表单输入', async () => {
    const wrapper = createWrapper()
    
    // 测试姓名输入
    const nameInput = wrapper.find('input[placeholder="请输入宠物姓名"]')
    await nameInput.setValue('测试宠物')
    expect(wrapper.vm.form.name).toBe('测试宠物')
    
    // 测试品种输入
    const speciesInput = wrapper.find('input[placeholder="请输入宠物品种，如：金毛、英短、布偶猫等"]')
    await speciesInput.setValue('金毛')
    expect(wrapper.vm.form.species).toBe('金毛')
  })

  it('应该在提交时触发pet-added事件', async () => {
    const wrapper = createWrapper()
    
    // 填写必填字段
    wrapper.vm.form.name = '测试宠物'
    wrapper.vm.form.species = '金毛'
    wrapper.vm.form.age = 2
    
    // 模拟表单验证通过
    const mockValidate = vi.fn().mockResolvedValue(true)
    // 使用defineProperty来设置ref
    Object.defineProperty(wrapper.vm.$refs, 'formRef', {
      value: { validate: mockValidate },
      writable: true
    })
    
    // 点击提交按钮
    await wrapper.vm.submitForm()
    
    // 检查是否触发了事件
    expect(wrapper.emitted('pet-added')).toBeTruthy()
  })

  it('应该能够重置表单', async () => {
    const wrapper = createWrapper()
    
    // 设置一些表单数据
    wrapper.vm.form.name = '测试宠物'
    wrapper.vm.form.species = '金毛'
    wrapper.vm.form.age = 2
    
    // 模拟表单引用
    const mockResetFields = vi.fn()
    // 直接设置formRef的value
    wrapper.vm.formRef = { resetFields: mockResetFields }
    
    // 重置表单
    wrapper.vm.resetForm()
    
    // 检查表单是否被重置
    expect(mockResetFields).toHaveBeenCalled()
    expect(wrapper.vm.form.name).toBe('')
    expect(wrapper.vm.form.species).toBe('')
    expect(wrapper.vm.form.age).toBe(null)
  })

  it('应该正确处理头像上传', () => {
    const wrapper = createWrapper()
    
    // 模拟文件对象
    const mockFile = {
      raw: new File(['test'], 'test.jpg', { type: 'image/jpeg' }),
      name: 'test.jpg'
    }
    
    // 模拟FileReader
    const mockFileReader = {
      readAsDataURL: vi.fn(),
      onload: null,
      result: 'data:image/jpeg;base64,test'
    }
    
    global.FileReader = vi.fn(() => mockFileReader)
    
    // 调用头像处理函数
    wrapper.vm.handleAvatarChange(mockFile)
    
    // 检查文件是否被设置
    expect(wrapper.vm.avatarFile).toBe(mockFile.raw)
    expect(mockFileReader.readAsDataURL).toHaveBeenCalledWith(mockFile.raw)
  })
})