<template>
  <div class="view-toggle-demo">
    <div class="demo-header">
      <h1>视图切换组件样式演示</h1>
      <p>Enhanced View Toggle Component Style Demo</p>
    </div>

    <!-- 基础用法 -->
    <div class="demo-section">
      <h2>基础用法</h2>
      <div class="demo-item">
        <h3>默认尺寸</h3>
        <EnhancedViewToggle
          v-model="basicValue"
          :options="basicOptions"
        />
        <p class="demo-result">当前选择：{{ basicValue }}</p>
      </div>

      <div class="demo-item">
        <h3>小尺寸</h3>
        <EnhancedViewToggle
          v-model="smallValue"
          :options="basicOptions"
          size="small"
        />
        <p class="demo-result">当前选择：{{ smallValue }}</p>
      </div>
    </div>

    <!-- 不同选项数量 -->
    <div class="demo-section">
      <h2>不同选项数量</h2>
      <div class="demo-item">
        <h3>两个选项</h3>
        <EnhancedViewToggle
          v-model="twoOptionsValue"
          :options="twoOptions"
        />
      </div>

      <div class="demo-item">
        <h3>四个选项</h3>
        <EnhancedViewToggle
          v-model="fourOptionsValue"
          :options="fourOptions"
        />
      </div>

      <div class="demo-item">
        <h3>五个选项</h3>
        <EnhancedViewToggle
          v-model="fiveOptionsValue"
          :options="fiveOptions"
        />
      </div>
    </div>

    <!-- 实际应用场景 -->
    <div class="demo-section">
      <h2>实际应用场景</h2>
      <div class="demo-item">
        <h3>日历视图切换</h3>
        <EnhancedViewToggle
          v-model="calendarView"
          :options="calendarOptions"
        />
        <div class="mock-content">
          <div v-if="calendarView === 'month'" class="content-panel">
            📅 月视图内容
          </div>
          <div v-else-if="calendarView === 'week'" class="content-panel">
            📊 周视图内容
          </div>
          <div v-else-if="calendarView === 'year'" class="content-panel">
            📈 年视图内容
          </div>
        </div>
      </div>

      <div class="demo-item">
        <h3>记录类型切换（小尺寸）</h3>
        <EnhancedViewToggle
          v-model="recordView"
          :options="recordOptions"
          size="small"
        />
        <div class="mock-content">
          <div v-if="recordView === 'cards'" class="content-panel">
            🃏 卡片视图内容
          </div>
          <div v-else-if="recordView === 'timeline'" class="content-panel">
            ⏰ 时间线视图内容
          </div>
          <div v-else-if="recordView === 'table'" class="content-panel">
            📋 表格视图内容
          </div>
        </div>
      </div>
    </div>

    <!-- 响应式测试 -->
    <div class="demo-section">
      <h2>响应式测试</h2>
      <div class="demo-item">
        <h3>在不同屏幕宽度下的表现</h3>
        <div class="responsive-container">
          <EnhancedViewToggle
            v-model="responsiveValue"
            :options="responsiveOptions"
          />
        </div>
        <p class="demo-note">
          💡 提示：调整浏览器窗口大小查看响应式效果
        </p>
      </div>
    </div>

    <!-- 代码示例 -->
    <div class="demo-section">
      <h2>代码示例</h2>
      <div class="code-example">
        <h3>基础用法</h3>
        <pre><code>&lt;EnhancedViewToggle
  v-model="selectedValue"
  :options="[
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' },
    { value: 'option3', label: '选项3' }
  ]"
/&gt;</code></pre>
      </div>

      <div class="code-example">
        <h3>小尺寸用法</h3>
        <pre><code>&lt;EnhancedViewToggle
  v-model="selectedValue"
  :options="options"
  size="small"
  @change="handleChange"
/&gt;</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue'

// 基础演示数据
const basicValue = ref('option1')
const smallValue = ref('option2')

const basicOptions = [
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' }
]

// 不同选项数量
const twoOptionsValue = ref('on')
const twoOptions = [
  { value: 'on', label: '开启' },
  { value: 'off', label: '关闭' }
]

const fourOptionsValue = ref('spring')
const fourOptions = [
  { value: 'spring', label: '春' },
  { value: 'summer', label: '夏' },
  { value: 'autumn', label: '秋' },
  { value: 'winter', label: '冬' }
]

const fiveOptionsValue = ref('xs')
const fiveOptions = [
  { value: 'xs', label: 'XS' },
  { value: 's', label: 'S' },
  { value: 'm', label: 'M' },
  { value: 'l', label: 'L' },
  { value: 'xl', label: 'XL' }
]

// 实际应用场景
const calendarView = ref('month')
const calendarOptions = [
  { value: 'month', label: '月视图' },
  { value: 'week', label: '周视图' },
  { value: 'year', label: '年视图' }
]

const recordView = ref('cards')
const recordOptions = [
  { value: 'cards', label: '卡片' },
  { value: 'timeline', label: '时间线' },
  { value: 'table', label: '表格' }
]

// 响应式测试
const responsiveValue = ref('desktop')
const responsiveOptions = [
  { value: 'mobile', label: '手机' },
  { value: 'tablet', label: '平板' },
  { value: 'desktop', label: '桌面' },
  { value: 'large', label: '大屏' }
]
</script>

<style scoped>
.view-toggle-demo {
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 48px;
}

.demo-header h1 {
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-header p {
  font-size: 16px;
  color: #909399;
  margin: 0;
}

.demo-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.demo-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  border-bottom: 2px solid #E4E7ED;
  padding-bottom: 12px;
}

.demo-item {
  margin-bottom: 32px;
}

.demo-item:last-child {
  margin-bottom: 0;
}

.demo-item h3 {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 16px;
}

.demo-result {
  margin-top: 12px;
  padding: 8px 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  color: #0369a1;
  font-size: 14px;
}

.mock-content {
  margin-top: 16px;
}

.content-panel {
  padding: 24px;
  background: #f5f7fa;
  border-radius: 12px;
  text-align: center;
  font-size: 16px;
  color: #606266;
  border: 2px dashed #dcdfe6;
  transition: all 0.3s ease;
}

.responsive-container {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 12px;
  border: 2px dashed #dcdfe6;
}

.demo-note {
  margin-top: 12px;
  font-size: 14px;
  color: #909399;
  font-style: italic;
}

.code-example {
  margin-bottom: 24px;
}

.code-example h3 {
  font-size: 16px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 12px;
}

.code-example pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.code-example code {
  font-family: inherit;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .view-toggle-demo {
    padding: 20px;
  }
  
  .demo-header h1 {
    font-size: 24px;
  }
  
  .demo-section {
    padding: 20px;
  }
  
  .demo-section h2 {
    font-size: 20px;
  }
}
</style>
