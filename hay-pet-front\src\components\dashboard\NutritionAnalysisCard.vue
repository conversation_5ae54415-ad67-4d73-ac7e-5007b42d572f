<template>
  <div class="nutrition-analysis-card">
    <div class="card-header">
      <div class="header-left">
        <el-icon class="header-icon"><Food /></el-icon>
        <h3>营养分析</h3>
      </div>
      <div class="header-right">
        <el-button type="primary" size="small" @click="addNutrition">
          <el-icon><Plus /></el-icon>
          记录饮食
        </el-button>
      </div>
    </div>

    <div class="card-content">
      <!-- 今日营养摄入概览 -->
      <div class="nutrition-overview">
        <h4>今日营养摄入</h4>
        <div class="nutrition-stats">
          <div class="nutrition-item">
            <div class="nutrition-circle" :style="{ background: getProgressColor(proteinProgress) }">
              <div class="nutrition-value">{{ todayNutrition.protein }}g</div>
              <div class="nutrition-label">蛋白质</div>
            </div>
            <div class="nutrition-progress">
              <el-progress 
                :percentage="proteinProgress" 
                :color="getProgressColor(proteinProgress)"
                :show-text="false"
                :stroke-width="6"
              />
              <span class="progress-text">{{ proteinProgress }}%</span>
            </div>
          </div>
          
          <div class="nutrition-item">
            <div class="nutrition-circle" :style="{ background: getProgressColor(fatProgress) }">
              <div class="nutrition-value">{{ todayNutrition.fat }}g</div>
              <div class="nutrition-label">脂肪</div>
            </div>
            <div class="nutrition-progress">
              <el-progress 
                :percentage="fatProgress" 
                :color="getProgressColor(fatProgress)"
                :show-text="false"
                :stroke-width="6"
              />
              <span class="progress-text">{{ fatProgress }}%</span>
            </div>
          </div>
          
          <div class="nutrition-item">
            <div class="nutrition-circle" :style="{ background: getProgressColor(carbProgress) }">
              <div class="nutrition-value">{{ todayNutrition.carbohydrates }}g</div>
              <div class="nutrition-label">碳水</div>
            </div>
            <div class="nutrition-progress">
              <el-progress 
                :percentage="carbProgress" 
                :color="getProgressColor(carbProgress)"
                :show-text="false"
                :stroke-width="6"
              />
              <span class="progress-text">{{ carbProgress }}%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 营养趋势图表 -->
      <div class="chart-container">
        <h4>营养摄入趋势（最近7天）</h4>
        <div ref="chartRef" class="nutrition-chart"></div>
      </div>

      <!-- 营养建议 -->
      <div class="nutrition-suggestions">
        <h4>营养建议</h4>
        <div v-if="nutritionSuggestions.length" class="suggestion-list">
          <div 
            v-for="suggestion in nutritionSuggestions" 
            :key="suggestion.id"
            class="suggestion-item"
            :class="suggestion.type"
          >
            <el-icon class="suggestion-icon">
              <component :is="getSuggestionIcon(suggestion.type)" />
            </el-icon>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-desc">{{ suggestion.description }}</div>
            </div>
          </div>
        </div>
        <div v-else class="empty-suggestions">
          <p>暂无营养建议</p>
        </div>
      </div>

      <!-- 操作链接 -->
      <div class="card-actions">
        <!-- 营养分析功能暂未实现 -->
        <span class="disabled-link">
          查看详细营养分析
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { ElIcon, ElButton, ElProgress } from 'element-plus';
import { Food, Plus, ArrowRight, SuccessFilled, WarningFilled, InfoFilled } from '@element-plus/icons-vue';
import * as echarts from 'echarts';

// 响应式数据
const chartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 今日营养摄入数据
const todayNutrition = ref({
  protein: 0,
  fat: 0,
  carbohydrates: 0,
  calories: 0
});

// 营养需求目标
const nutritionTargets = ref({
  protein: 50,
  fat: 30,
  carbohydrates: 150,
  calories: 1000
});

// 计算营养摄入进度
const proteinProgress = computed(() => 
  Math.round((todayNutrition.value.protein / nutritionTargets.value.protein) * 100)
);
const fatProgress = computed(() => 
  Math.round((todayNutrition.value.fat / nutritionTargets.value.fat) * 100)
);
const carbProgress = computed(() => 
  Math.round((todayNutrition.value.carbohydrates / nutritionTargets.value.carbohydrates) * 100)
);

// 营养建议数据
const nutritionSuggestions = ref([]);

// 获取进度条颜色
const getProgressColor = (progress: number) => {
  if (progress >= 90) return '#67C23A';
  if (progress >= 70) return '#E6A23C';
  return '#F56C6C';
};

// 获取建议图标
const getSuggestionIcon = (type: string) => {
  switch (type) {
    case 'success': return SuccessFilled;
    case 'warning': return WarningFilled;
    default: return InfoFilled;
  }
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chartInstance = echarts.init(chartRef.value);
  
  // 生成最近7天的日期
  const dates = [];
  const proteinData = [];
  const fatData = [];
  const carbData = [];
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    dates.push(date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }));
    proteinData.push(0);
    fatData.push(0);
    carbData.push(0);
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['蛋白质(g)', '脂肪(g)', '碳水化合物(g)'],
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}g',
        fontSize: 11
      }
    },
    series: [
      {
        name: '蛋白质(g)',
        type: 'line',
        data: proteinData,
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '脂肪(g)',
        type: 'line',
        data: fatData,
        smooth: true,
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '碳水化合物(g)',
        type: 'line',
        data: carbData,
        smooth: true,
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  };
  
  chartInstance.setOption(option);
};

// 添加营养记录
const addNutrition = () => {

};

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize, { passive: true });
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.nutrition-analysis-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
  color: #67C23A;
}

.header-left h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-content {
  padding: 20px;
}

.nutrition-overview h4,
.chart-container h4,
.nutrition-suggestions h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.nutrition-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.nutrition-item {
  text-align: center;
}

.nutrition-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  color: white;
  background: #409EFF;
}

.nutrition-value {
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
}

.nutrition-label {
  font-size: 11px;
  margin-top: 2px;
}

.nutrition-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  min-width: 35px;
}

.chart-container {
  margin-bottom: 24px;
}

.nutrition-chart {
  width: 100%;
  height: 200px;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid;
}

.suggestion-item.success {
  background: #f0f9ff;
  border-left-color: #67C23A;
}

.suggestion-item.warning {
  background: #fdf6ec;
  border-left-color: #E6A23C;
}

.suggestion-item.info {
  background: #f4f4f5;
  border-left-color: #909399;
}

.suggestion-icon {
  font-size: 16px;
  margin-top: 2px;
}

.suggestion-item.success .suggestion-icon {
  color: #67C23A;
}

.suggestion-item.warning .suggestion-icon {
  color: #E6A23C;
}

.suggestion-item.info .suggestion-icon {
  color: #909399;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.suggestion-desc {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.card-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #67C23A;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
}

.action-link:hover {
  color: #85ce61;
}

.disabled-link {
  color: var(--el-text-color-disabled);
  font-size: 14px;
  cursor: not-allowed;
}

.empty-suggestions {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .card-header {
    padding: 16px;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .nutrition-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  
  .nutrition-circle {
    width: 60px;
    height: 60px;
  }
  
  .nutrition-value {
    font-size: 14px;
  }
  
  .nutrition-label {
    font-size: 10px;
  }
  
  .nutrition-chart {
    height: 160px;
  }
  
  .suggestion-item {
    padding: 10px;
  }
  
  .suggestion-title {
    font-size: 14px;
  }
  
  .suggestion-desc {
    font-size: 12px;
  }
}
</style>