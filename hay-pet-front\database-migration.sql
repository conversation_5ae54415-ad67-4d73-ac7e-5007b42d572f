-- 数据库迁移脚本：从breed字段迁移到species字段
-- 请在Supabase SQL编辑器中执行此脚本

-- 1. 检查当前表结构
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'pets' AND table_schema = 'public';

-- 2. 如果存在breed列，将数据迁移到species列
DO $$
BEGIN
    -- 检查breed列是否存在
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'pets' AND column_name = 'breed' AND table_schema = 'public') THEN
        
        -- 如果species列不存在，先创建它
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'pets' AND column_name = 'species' AND table_schema = 'public') THEN
            ALTER TABLE public.pets ADD COLUMN species TEXT;
        END IF;
        
        -- 将breed列的数据复制到species列
        UPDATE public.pets SET species = breed WHERE species IS NULL AND breed IS NOT NULL;
        
        -- 删除breed列
        ALTER TABLE public.pets DROP COLUMN breed;
        
        RAISE NOTICE 'Successfully migrated breed column to species column';
    ELSE
        -- 如果breed列不存在，确保species列存在
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'pets' AND column_name = 'species' AND table_schema = 'public') THEN
            ALTER TABLE public.pets ADD COLUMN species TEXT;
            RAISE NOTICE 'Added species column to pets table';
        ELSE
            RAISE NOTICE 'Species column already exists, no migration needed';
        END IF;
    END IF;
END $$;

-- 3. 验证最终表结构
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'pets' AND table_schema = 'public'
ORDER BY ordinal_position;

-- 4. 添加 avatar_path 列（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'pets' AND column_name = 'avatar_path' AND table_schema = 'public') THEN
        ALTER TABLE public.pets ADD COLUMN avatar_path TEXT;
        RAISE NOTICE 'Added avatar_path column to pets table';
    ELSE
        RAISE NOTICE 'avatar_path column already exists';
    END IF;
END $$;

-- 5. 显示pets表中的数据（如果有的话）
SELECT id, name, type, species, birth_date, gender, avatar_url, avatar_path 
FROM public.pets 
LIMIT 5;