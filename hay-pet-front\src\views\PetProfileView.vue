<template>
  <div class="pet-profile-view">
    <!-- 空状态 -->
    <div v-if="!currentPetId && !pet.id" class="empty-state">
      <el-empty description="请先在左侧选择或添加一个宠物">
        <template #image>
          <div class="empty-icon">
            <el-icon :size="80" color="#d3d3d3"><UserFilled /></el-icon>
          </div>
        </template>
      </el-empty>
    </div>
    
    <!-- 主要内容 -->
    <div v-else class="profile-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <div class="header-content">
          <div class="header-left">
            <el-button
              @click="goBack"
              type="primary"
              link
              class="back-button"
            >
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <h2 class="page-title">宠物档案</h2>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div v-if="pet.id" class="profile-content">
        <!-- 左侧主要信息 -->
        <div class="main-content">
          <!-- 基本信息卡片 -->
          <div class="info-section">
            <div class="section-header">
              <h3 class="section-title">
                <el-icon><User /></el-icon>
                基本信息
              </h3>
            </div>
            
            <div class="info-content">
              <!-- 头像区域 - 居中显示 -->
              <div class="avatar-section">
                <div class="avatar-wrapper">
                  <el-avatar 
                    :size="120" 
                    :src="getAvatarUrl(pet.avatar_url)"
                    :icon="UserFilled"
                    class="pet-avatar"
                    @error="handleAvatarError"
                  />
                  <div class="avatar-overlay">
                    <el-button 
                      type="primary" 
                      size="small" 
                      circle
                      @click="triggerImageCropper"
                    >
                      <el-icon><Camera /></el-icon>
                    </el-button>
                  </div>
                  <input type="file" ref="fileInput" @change="onFileSelected" accept="image/*" style="display: none;" />
                </div>
                <div class="avatar-actions">
                  <el-button 
                    v-if="pet.avatar_url" 
                    type="danger" 
                    size="small" 
                    @click="removeAvatar"
                    text
                  >
                    <el-icon><Delete /></el-icon>
                    删除头像
                  </el-button>
                </div>
              </div>
              
              <!-- 年龄显示区域 -->
              <div class="age-section">
                <div class="age-display">
                  <div class="age-icon">
                    <el-icon><Calendar /></el-icon>
                  </div>
                  <div class="age-content">
                    <div class="age-label">年龄</div>
                    <div class="age-value">{{ calculatedAge }}</div>
                  </div>
                </div>
                <el-radio-group v-model="ageUnit" size="small" class="age-unit-selector">
                  <el-radio-button value="year">年/月</el-radio-button>
                  <el-radio-button value="month">月/天</el-radio-button>
                  <el-radio-button value="week">周/天</el-radio-button>
                </el-radio-group>
              </div>
              
              <!-- 详细信息表单 -->
              <div class="detail-form">
                <div class="form-section">
                  <h4 class="form-section-title">
                    <el-icon><Edit /></el-icon>
                    详细信息
                  </h4>
                  <el-form :model="pet" label-position="top" class="modern-form">
                    <div class="form-grid">
                      <el-form-item label="宠物昵称" required class="form-item">
                        <el-input 
                          v-model="pet.name" 
                          placeholder="请输入宠物昵称" 
                          size="large"
                          class="modern-input"
                          :prefix-icon="UserFilled"
                        />
                      </el-form-item>
                      
                      <el-form-item label="宠物类型" class="form-item">
                        <el-input 
                          v-model="pet.type" 
                          placeholder="例如：猫、狗、兔子" 
                          size="large"
                          class="modern-input"
                          :prefix-icon="Star"
                        />
                      </el-form-item>
                      
                      <el-form-item label="宠物品种" class="form-item">
                        <el-input 
                          v-model="pet.species" 
                          placeholder="例如：拉布拉多、英国短毛猫" 
                          size="large"
                          class="modern-input"
                          :prefix-icon="Medal"
                        />
                      </el-form-item>
                      
                      <el-form-item label="性别" class="form-item">
                        <el-select 
                          v-model="pet.gender" 
                          placeholder="选择性别" 
                          size="large"
                          class="modern-select"
                        >
                          <el-option label="雄性" value="male">
                            <span class="option-content">
                              <el-icon color="#409EFF"><Male /></el-icon>
                              雄性
                            </span>
                          </el-option>
                          <el-option label="雌性" value="female">
                            <span class="option-content">
                              <el-icon color="#F56C6C"><Female /></el-icon>
                              雌性
                            </span>
                          </el-option>
                          <el-option label="未知" value="unknown">
                            <span class="option-content">
                              <el-icon color="#909399"><QuestionFilled /></el-icon>
                              未知
                            </span>
                          </el-option>
                        </el-select>
                      </el-form-item>
                      
                      <el-form-item label="出生日期" class="form-item">
                        <el-date-picker 
                          v-model="pet.birth_date" 
                          type="date" 
                          placeholder="选择日期" 
                          format="YYYY-MM-DD"
                          value-format="YYYY-MM-DD"
                          size="large"
                          class="modern-date-picker"
                        />
                      </el-form-item>
                    </div>
                  </el-form>
                </div>
              </div>
              
              <!-- 操作按钮区域 -->
              <div class="action-buttons">
                <el-button type="primary" @click="saveProfile" :loading="saving" size="large">
                  <el-icon><Check /></el-icon>
                  保存档案
                </el-button>
                <el-button type="danger" @click="deleteProfile" v-if="pet.id" size="large" plain>
                  <el-icon><Delete /></el-icon>
                  删除宠物
                </el-button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧辅助信息 -->
        <div class="sidebar-content">
          <!-- 标签管理 -->
          <div class="tags-section">
            <div class="section-header">
              <h3 class="section-title">
                <el-icon><Collection /></el-icon>
                标签管理
              </h3>
            </div>
            <div class="section-content">
              <PetTagsManager 
                :pet-id="pet.id" 
                @tags-updated="handleTagsUpdated"
                class="tags-manager"
              />
            </div>
          </div>
          
          <!-- 备注信息 -->
          <div class="notes-section">
            <div class="section-header">
              <h3 class="section-title">
                <el-icon><EditPen /></el-icon>
                备注信息
              </h3>
            </div>
            <div class="section-content">
              <PetNotesEditor 
                :pet-id="pet.id" 
                :notes="pet.notes"
                @notes-updated="handleNotesUpdated"
                class="notes-editor"
              />
            </div>
          </div>
        </div>
      </div>
      
      <el-empty v-else description="正在加载宠物信息或当前无选中宠物..." />
    </div>
    
    <!-- 图片裁剪对话框 -->
    <NewImageCropperDialog
      v-model="showImageCropper"
      :imageSrc="selectedImageSrc"
      :aspect-ratio="1"
      @confirm="handleCropConfirm"
      @cancel="handleCropCancel"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, inject, watch, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import { UserFilled, Upload, Delete, Check, User, Camera, Male, Female, QuestionFilled, Collection, EditPen, Calendar, Star, Medal, Edit, ArrowLeft } from '@element-plus/icons-vue';
import PetTagsManager from '@/components/pet/PetTagsManager.vue';
import PetNotesEditor from '@/components/pet/PetNotesEditor.vue';
import NewImageCropperDialog from '@/components/NewImageCropperDialog.vue';

// 字符编码处理函数
// const imageCropperDialog = ref(null); // Moved to setup
// const fileInput = ref(null); // Moved to setup

// const triggerImageCropper = () => { // Moved to setup
//   fileInput.value.click();
// };

// const onFileSelected = (event) => { // Moved to setup
//   const file = event.target.files[0];
//   if (file) {
//     imageCropperDialog.value.open(file);
//   }
//   // Reset file input to allow selecting the same file again
//   if (fileInput.value) {
//     fileInput.value.value = '';
//   }
// }; // Moved to setup

// const handleCropConfirm = async (croppedFile) => { // Moved to setup
//   // 这里替换原来的 handleImageUploadSuccess 逻辑
//   // 通常是上传 croppedFile 到服务器，然后更新宠物头像 URL
//   console.log('Cropped file:', croppedFile);
//   // 假设上传成功后，服务器返回新的头像 URL
//   // const newAvatarUrl = await uploadPetAvatar(pet.value.id, croppedFile);
//   // pet.value.avatar_url = newAvatarUrl;
//   // ElMessage.success('头像上传成功');
//   // 模拟上传成功
//   // const reader = new FileReader();
//   reader.onload = (e) => {
//     pet.value.avatar_url = e.target.result; // 本地预览，实际应为服务器URL
//     ElMessage.success('头像已更新 (本地预览)');
//   };
//   reader.readAsDataURL(croppedFile);
//   // TODO: 实现真实的上传逻辑
//   // 触发 petStore 更新
//   const petStore = usePetStore();
//   petStore.updatePetAvatar(pet.value.id, pet.value.avatar_url); 
// }; // Moved to setup

// const handleCropCancel = () => { // Moved to setup
//   console.log('Cropping cancelled');
// }; // Moved to setup

// 移除旧的上传面板相关逻辑
// const showUploadPanel = ref(false);
// const handleImageUploadSuccess = (newAvatarUrl) => {
//   pet.value.avatar_url = newAvatarUrl;
//   showUploadPanel.value = false;
//   ElMessage.success('头像上传成功');
//   // 触发 petStore 更新
//   const petStore = usePetStore();
//   petStore.updatePetAvatar(pet.value.id, newAvatarUrl);
// };
// const handleImageUploadCancel = () => {
//   showUploadPanel.value = false;
// };

import { usePetStore } from '@/stores/pet'; // 确保引入 petStore

const ensureUtf8 = (str) => {
  if (!str) return '';
  try {
    const strValue = String(str);
    // 检查是否包含常见的编码问题字符
    if (/[\uFFFD]/.test(strValue)) {
      console.warn('检测到乱码字符:', strValue);
      // 尝试使用TextDecoder进行修复
      const encoder = new TextEncoder();
      const decoder = new TextDecoder('utf-8', { fatal: false });
      const bytes = encoder.encode(strValue);
      return decoder.decode(bytes);
    }
    return strValue;
  } catch (e) {
    console.warn('字符编码处理失败:', e, '原始值:', str);
    return String(str || '');
  }
};

// 准备保存的字符串
const prepareForSave = (str) => {
  if (!str) return '';
  try {
    // 简单清理和trim，保持原始UTF-8编码
    return str.trim();
  } catch (e) {
    console.warn('字符编码准备失败:', e);
    return String(str || '').trim();
  }
};

export default {
  name: 'PetProfileView',
  components: {
    UserFilled,
    Upload,
    Delete,
    Check,
    Edit,
    PetTagsManager,
    PetNotesEditor,
    NewImageCropperDialog // Register the component
  },
  setup() {
    const imageCropperDialog = ref(null); // Define ref here
    const fileInput = ref(null); // Define ref here
    const supabase = inject('supabase');
    const router = useRouter();
    const route = useRoute();
    const refreshPetList = inject('refreshPetList', null);

    const pet = ref({
      id: null,
      name: '',
      type: '',
      species: '',
      birth_date: '',
      gender: '',
      avatar_url: '',
      avatar_path: '',
      notes: ''
    });

    const avatarLoadStatus = ref('未加载');

    // 处理头像URL
    const getAvatarUrl = (url) => {
      if (!url) {
        avatarLoadStatus.value = '无URL';
        return null;
      }
      
      // 如果已经是完整的URL，直接返回
      if (url.startsWith('http')) {
        avatarLoadStatus.value = '使用完整URL';
        return url;
      }
      
      // 如果是相对路径，构建完整的Supabase存储URL
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      if (supabaseUrl && supabaseUrl !== 'https://placeholder.supabase.co') {
        const fullUrl = `${supabaseUrl}/storage/v1/object/public/pet-media/${url}`;
        avatarLoadStatus.value = '构建完整URL';
        return fullUrl;
      }
      
      avatarLoadStatus.value = 'Supabase未配置';
      return null;
    };

    // 处理头像加载错误
    const handleAvatarError = (event) => {
      avatarLoadStatus.value = '加载失败';
    };

    // 处理路由参数中的编码问题
    const getPetByNameOrId = (nameOrId) => {
      if (!nameOrId) return null;
      try {
        const decoded = decodeURIComponent(nameOrId);
        return decoded;
      } catch (error) {
        return nameOrId;
      }
    };

    // 优先使用localStorage中的值
    const currentPetId = ref(localStorage.getItem('currentPetId'));
    const ageUnit = ref('year'); // year, month, week
    const saving = ref(false);
    
    // 图片上传面板相关
    const showUploadPanel = ref(false);

    // 图片裁剪对话框相关
    const showImageCropper = ref(false);
    const selectedImageSrc = ref('');
    
    // 处理图片上传成功
    const handleImageUploadSuccess = async (file) => {
      if (!pet.value.id) {
        ElMessage.error('请先保存宠物信息');
        return;
      }

      try {
        const fileName = `${pet.value.id}_avatar_${Date.now()}.${file.name.split('.').pop()}`;
        const filePath = `${pet.value.id}/${fileName}`;

        // 如果已有头像，先删除旧的
        if (pet.value.avatar_path) {
          try {
            await supabase.storage
              .from(AVATAR_BUCKET_NAME)
              .remove([pet.value.avatar_path]);
          } catch (removeError) {
            console.warn('删除旧头像失败:', removeError);
          }
        }

        // 上传新头像到 Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(AVATAR_BUCKET_NAME)
          .upload(filePath, file, {
            contentType: file.type,
            cacheControl: '3600',
            upsert: false
          });

        if (uploadError) throw uploadError;

        // 获取公共URL
        const { data: urlData } = supabase.storage
          .from(AVATAR_BUCKET_NAME)
          .getPublicUrl(uploadData.path);

        // 更新数据库中的头像URL
        const { error: updateError } = await supabase
          .from('pets')
          .update({ 
            avatar_url: urlData.publicUrl, 
            avatar_path: uploadData.path 
          })
          .eq('id', pet.value.id);

        if (updateError) throw updateError;

        // 更新本地数据
        pet.value.avatar_url = urlData.publicUrl;
        pet.value.avatar_path = uploadData.path;
        
        // 关闭上传面板
        showUploadPanel.value = false;
        
        ElMessage.success('头像上传成功！');
        
        // 通知其他组件更新
        window.dispatchEvent(new CustomEvent('petsUpdated'));
      } catch (error) {
        console.error('头像上传失败:', error);
        ElMessage.error('头像上传失败: ' + error.message);
      }
    };
    
    // 处理图片上传取消
    const handleImageUploadCancel = () => {
      showUploadPanel.value = false;
    };

    const calculatedAge = computed(() => {
      if (!pet.value.birth_date) return 'N/A';
      const birth = new Date(pet.value.birth_date);
      const today = new Date();
      
      let ageYears = today.getFullYear() - birth.getFullYear();
      let ageMonths = today.getMonth() - birth.getMonth();
      let ageDays = today.getDate() - birth.getDate();

      if (ageDays < 0) {
        ageMonths--;
        const lastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
        ageDays += lastMonth.getDate();
      }
      if (ageMonths < 0) {
        ageYears--;
        ageMonths += 12;
      }

      if (ageUnit.value === 'year') return `${ageYears} 年 ${ageMonths} 月`;
      
      // 总月数
      const totalMonths = ageYears * 12 + ageMonths;
      if (ageUnit.value === 'month') return `${totalMonths} 月 ${ageDays} 天`;

      // 总周数
      const diffTime = Math.abs(today - birth);
      const diffDaysTotal = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      const diffWeeks = Math.floor(diffDaysTotal / 7);
      const remainingDays = diffDaysTotal % 7;
      if (ageUnit.value === 'week') return `${diffWeeks} 周 ${remainingDays} 天`;
      
      return `${ageYears} 年 ${ageMonths} 月`;
    });



    // 头像相关常量
    const AVATAR_BUCKET_NAME = 'pet-media';
    const avatarUpload = ref();

    // 头像上传前验证
    const beforeAvatarUpload = (file) => {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        ElMessage.error('只能上传图片文件!');
        return false;
      }
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!');
        return false;
      }
      return true;
    };

    // 自定义头像上传
    const uploadAvatar = async (options) => {
      if (!pet.value.id) {
        ElMessage.error('请先保存宠物信息');
        return;
      }

      const file = options.file;
      const fileName = `${pet.value.id}_avatar_${Date.now()}.${file.name.split('.').pop()}`;
      const filePath = `${pet.value.id}/${fileName}`;

      try {
        // 如果已有头像，先删除旧的
        if (pet.value.avatar_url) {
          await removeOldAvatar();
        }

        // 上传新头像到 Supabase Storage，明确指定Content-Type
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(AVATAR_BUCKET_NAME)
          .upload(filePath, file, {
            contentType: file.type,
            cacheControl: '3600',
            upsert: false
          });

        if (uploadError) throw uploadError;

        // 获取公共URL
        const { data: urlData } = supabase.storage
          .from(AVATAR_BUCKET_NAME)
          .getPublicUrl(uploadData.path);

        // 更新数据库中的头像URL
        const { error: updateError } = await supabase
          .from('pets')
          .update({ avatar_url: urlData.publicUrl, avatar_path: uploadData.path })
          .eq('id', pet.value.id);

        if (updateError) throw updateError;

        pet.value.avatar_url = urlData.publicUrl;
        pet.value.avatar_path = uploadData.path;
        ElMessage.success('头像上传成功!');
        
        // 通知其他组件更新
        window.dispatchEvent(new CustomEvent('petsUpdated'));
      } catch (error) {
        console.error('头像上传失败:', error.message);
        ElMessage.error('头像上传失败: ' + error.message);
      }
    };

    // 带进度回调的上传方法
    const handleImageUploadSuccessWithProgress = async (file, callbacks = {}) => {
      if (!pet.value.id) {
        ElMessage.error('请先保存宠物信息');
        if (callbacks.onError) {
          callbacks.onError(new Error('请先保存宠物信息'));
        }
        return;
      }

      const fileName = `${pet.value.id}_avatar_${Date.now()}.${file.name.split('.').pop()}`;
      const filePath = `${pet.value.id}/${fileName}`;

      try {
        // 步骤1: 删除旧头像
        if (pet.value.avatar_url) {
          if (callbacks.onProgress) {
            callbacks.onProgress(20, '正在删除旧头像...', '清理存储空间');
          }
          await removeOldAvatar();
        }

        // 步骤2: 上传新头像
        if (callbacks.onProgress) {
          callbacks.onProgress(40, '正在上传头像...', '传输文件数据');
        }

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(AVATAR_BUCKET_NAME)
          .upload(filePath, file, {
            contentType: file.type,
            cacheControl: '3600',
            upsert: false
          });

        if (uploadError) throw uploadError;

        // 步骤3: 获取公共URL
        if (callbacks.onProgress) {
          callbacks.onProgress(70, '正在生成访问链接...', '配置文件权限');
        }

        const { data: urlData } = supabase.storage
          .from(AVATAR_BUCKET_NAME)
          .getPublicUrl(uploadData.path);

        // 步骤4: 更新数据库
        if (callbacks.onProgress) {
          callbacks.onProgress(90, '正在更新数据库...', '保存头像信息');
        }

        const { error: updateError } = await supabase
          .from('pets')
          .update({ avatar_url: urlData.publicUrl, avatar_path: uploadData.path })
          .eq('id', pet.value.id);

        if (updateError) throw updateError;

        // 步骤5: 更新本地数据
        pet.value.avatar_url = urlData.publicUrl;
        pet.value.avatar_path = uploadData.path;

        // 通知其他组件更新
        window.dispatchEvent(new CustomEvent('petsUpdated'));

        // 调用成功回调
        if (callbacks.onSuccess) {
          callbacks.onSuccess();
        } else {
          ElMessage.success('头像上传成功!');
        }

        // 关闭裁剪对话框
        showImageCropper.value = false;
        selectedImageSrc.value = '';

      } catch (error) {
        console.error('头像上传失败:', error.message);
        if (callbacks.onError) {
          callbacks.onError(error);
        } else {
          ElMessage.error('头像上传失败: ' + error.message);
        }
        throw error;
      }
    };

    // 删除头像
    const removeAvatar = async () => {
      if (!pet.value.avatar_url) return;

      try {
        await ElMessageBox.confirm('确定要删除头像吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });

        await removeOldAvatar();
        
        // 更新数据库
        const { error } = await supabase
          .from('pets')
          .update({ avatar_url: null, avatar_path: null })
          .eq('id', pet.value.id);

        if (error) throw error;

        pet.value.avatar_url = '';
        pet.value.avatar_path = '';
        ElMessage.success('头像删除成功!');
        
        // 通知其他组件更新
        window.dispatchEvent(new CustomEvent('petsUpdated'));
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除头像失败:', error.message);
          ElMessage.error('删除头像失败: ' + error.message);
        }
      }
    };

    // 删除旧头像文件
    const removeOldAvatar = async () => {
      if (!pet.value.avatar_url) return;
      
      try {
        // 从数据库获取avatar_path
        const { data: petData, error: fetchError } = await supabase
          .from('pets')
          .select('avatar_path')
          .eq('id', pet.value.id)
          .single();
        
        if (fetchError) throw fetchError;
        
        if (petData?.avatar_path) {
          const { error: deleteError } = await supabase.storage
            .from(AVATAR_BUCKET_NAME)
            .remove([petData.avatar_path]);
          
          if (deleteError) {
            console.warn('删除旧头像文件失败:', deleteError.message);
          }
        }
      } catch (error) {
        console.warn('删除旧头像时出错:', error.message);
      }
    };

    const triggerImageCropper = () => {
      fileInput.value.click();
    };

    const onFileSelected = (event) => {
      const file = event.target.files[0];
      if (file) {
        // Create a data URL for the image
        const reader = new FileReader();
        reader.onload = (e) => {
          selectedImageSrc.value = e.target.result;
          showImageCropper.value = true;
        };
        reader.readAsDataURL(file);
      }
      // Reset file input to allow selecting the same file again
      if (fileInput.value) {
        fileInput.value.value = '';
      }
    };

    const handleCropConfirm = async (croppedFile, callbacks = {}) => {
      try {
        console.log('Cropped file:', croppedFile);

        if (!pet.value.id) {
          ElMessage.error('请先保存宠物信息');
          if (callbacks.onError) {
            callbacks.onError(new Error('请先保存宠物信息'));
          }
          return;
        }

        // 调用进度回调
        if (callbacks.onProgress) {
          callbacks.onProgress(10, '正在上传到服务器...', '连接存储服务');
        }

        // 使用现有的上传逻辑，但添加进度回调
        await handleImageUploadSuccessWithProgress(croppedFile, callbacks);

      } catch (error) {
        console.error('头像上传失败:', error);
        if (callbacks.onError) {
          callbacks.onError(error);
        } else {
          ElMessage.error('头像上传失败: ' + error.message);
        }
      }
    };

    const handleCropCancel = () => {
      console.log('Cropping cancelled');
      showImageCropper.value = false;
      selectedImageSrc.value = '';
    };

    const fetchPetProfile = async (petNameOrId) => {
      if (!petNameOrId) {
        pet.value = { 
          id: null, 
          name: '', 
          type: '', 
          species: '', 
          birth_date: '', 
          gender: '', 
          avatar_url: '',
          avatar_path: '',
          notes: ''
        };
        return;
      }
      try {
        // 获取当前用户
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          throw new Error('用户未登录');
        }

        // 检查是否为有效的UUID格式
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        const isValidUUID = uuidRegex.test(petNameOrId);
        
        let data, error;
        
        if (isValidUUID) {
          // 如果是有效UUID，尝试通过ID查找
          const result = await supabase
            .from('pets')
            .select('*')
            .eq('id', petNameOrId)
            .eq('user_id', user.id)
            .single();
          data = result.data;
          error = result.error;
        } else {
          // 如果不是UUID，直接通过名称查找
          const result = await supabase
            .from('pets')
            .select('*')
            .eq('name', petNameOrId)
            .eq('user_id', user.id)
            .single();
          data = result.data;
          error = result.error;
        }
        
        if (error) throw error;
        
        if (data) {
          // 确保中文字符正确显示
          pet.value = {
            ...data,
            name: ensureUtf8(data.name),
            type: ensureUtf8(data.type),
            species: ensureUtf8(data.species),
            notes: ensureUtf8(data.notes || ''),
            avatar_url: data.avatar_url || ''
          };
          

        } else {
          pet.value = { 
            id: null, 
            name: '', 
            type: '', 
            species: '', 
            birth_date: '', 
            gender: '', 
            avatar_url: '',
            avatar_path: '',
            notes: ''
          };
        }
      } catch (error) {
        console.error('获取宠物档案失败:', error.message);
        ElMessage.error('获取宠物档案失败: ' + error.message);
        pet.value = { 
          id: null, 
          name: '', 
          type: '', 
          species: '', 
          birth_date: '', 
          gender: '', 
          avatar_url: '',
          avatar_path: '',
          notes: ''
        };
      }
    };

    // 监听路由参数变化
    watch(() => route.params.petId, async (newPetId) => {
      const petNameOrId = getPetByNameOrId(newPetId);
      if (petNameOrId) {
        // 通过名称或ID获取宠物信息
        await fetchPetProfile(petNameOrId);
        // 如果成功获取到宠物信息，更新currentPetId为实际的ID
        if (pet.value.id && pet.value.id !== currentPetId.value) {
          currentPetId.value = pet.value.id;
          localStorage.setItem('currentPetId', pet.value.id);
        }
      }
    }, { immediate: true });

    // 保存宠物档案
    const saveProfile = async () => {
      if (!pet.value.id) {
        ElMessage.error('没有选择要保存的宠物');
        return;
      }
      if (!pet.value.name.trim()) {
        ElMessage.error('宠物昵称不能为空');
        return;
      }
      
      saving.value = true;
      try {
        // 更新宠物基本信息，确保中文字符正确保存
        const updateData = {
          name: prepareForSave(pet.value.name),
          type: prepareForSave(pet.value.type),
          species: prepareForSave(pet.value.species),
          birth_date: pet.value.birth_date,
          gender: pet.value.gender,
          notes: prepareForSave(pet.value.notes)
        };
        

        
        const { data, error } = await supabase
          .from('pets')
          .update(updateData)
          .eq('id', pet.value.id);
          
        if (error) throw error;
        
        ElMessage.success('宠物档案保存成功！');
        // 通知 SidebarMenu 更新宠物列表（如果名称更改）
        window.dispatchEvent(new CustomEvent('petsUpdated'));
      } catch (error) {
        console.error('保存宠物档案失败:', error.message);
        ElMessage.error('保存宠物档案失败: ' + error.message);
      } finally {
        saving.value = false;
      }
    };

    const deleteProfile = async () => {
      if (!pet.value.id) {
        ElMessage.error('没有选择要删除的宠物');
        return;
      }
      try {
        await ElMessageBox.confirm(
          `确定要删除宠物 "${pet.value.name}" 的所有信息吗？此操作不可恢复。`,
          '警告',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );
        
        // 删除与该宠物相关的记录
        const tablesToDeleteFrom = ['health_records', 'reminders', 'expenses', 'photos', 'weight_records', 'pet_tag_relations'];
        for (const table of tablesToDeleteFrom) {
          const { error: deleteRelatedError } = await supabase
            .from(table)
            .delete()
            .eq('pet_id', pet.value.id);
          if (deleteRelatedError) {
            console.warn(`删除 ${table} 中关联记录失败:`, deleteRelatedError.message);
          }
        }

        const { error } = await supabase
          .from('pets')
          .delete()
          .eq('id', pet.value.id);
        if (error) throw error;
        
        ElMessage.success('宠物档案已删除！');
        localStorage.removeItem('currentPetId');
        window.dispatchEvent(new CustomEvent('petsUpdated'));
        window.dispatchEvent(new CustomEvent('currentPetChanged', { detail: { petId: null } }));
        router.push('/');
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除宠物档案失败:', error.message);
          ElMessage.error('删除宠物档案失败: ' + error.message);
        }
      }
    };

    // 处理标签更新
    const handleTagsUpdated = (tags) => {

    };

    // 处理备注更新
    const handleNotesUpdated = (notes) => {
      pet.value.notes = notes;
    };

    const handleCurrentPetChanged = (event) => {
      currentPetId.value = event.detail.petId;
      fetchPetProfile(currentPetId.value);
    };

    onMounted(() => {
      // 优先使用路由参数，如果没有则使用localStorage中的值
      const routePetId = route.params.petId;
      const petNameOrId = routePetId ? getPetByNameOrId(routePetId) : currentPetId.value;
      fetchPetProfile(petNameOrId);
      window.addEventListener('currentPetChanged', handleCurrentPetChanged, { passive: true });
    });

    // 处理编辑档案按钮点击
    const handleEditProfile = () => {
      // 这里可以添加编辑功能，比如切换到编辑模式或打开编辑对话框
      ElMessage.info('编辑功能开发中...');
    };

    // 返回上一页
    const goBack = () => {
      router.push('/');
    };

    onBeforeUnmount(() => {
      window.removeEventListener('currentPetChanged', handleCurrentPetChanged);
    });

    return {
      pet,
      currentPetId,
      ageUnit,
      calculatedAge,
      saving,
      avatarLoadStatus,
      showUploadPanel,
      handleImageUploadSuccess,
      handleImageUploadCancel,
      beforeAvatarUpload,
      uploadAvatar,
      removeAvatar,
      saveProfile,
      deleteProfile,
      handleTagsUpdated,
      handleNotesUpdated,
      handleEditProfile,
      getAvatarUrl,
      handleAvatarError,
      UserFilled,
      Upload,
      Delete,
      Check,
      User,
      Camera,
      Male,
      Female,
      QuestionFilled,
      Collection,
      EditPen,
      Star,
      Medal,
      Edit,
      ArrowLeft,
      // Expose refs and methods to the template
      imageCropperDialog,
      fileInput,
      triggerImageCropper,
      onFileSelected,
      handleCropConfirm,
      handleCropCancel,
      showImageCropper,
      selectedImageSrc,
      goBack
    };
  }
};
</script>

<style scoped lang="scss">
/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-button {
  font-size: 16px;
  font-weight: 500;
  color: #409eff;

  &:hover {
    color: #66b1ff;
  }

  .el-icon {
    margin-right: 4px;
  }
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}



/* 自定义按钮样式 */


.pet-profile-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  overflow-y: auto;
  
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    
    .empty-icon {
      margin-bottom: 20px;
      opacity: 0.6;
    }
  }
  
  .profile-container {
    width: 100%;
    padding: 0;
  }
}

// 顶部操作栏样式已移除

// 主要内容区域
.profile-content {
  display: grid;
  grid-template-columns: 2fr 1.5fr;
  gap: 24px;
  align-items: start;
  width: 100%;

  // 大屏幕
  @media (max-width: 1400px) {
    grid-template-columns: 1.8fr 1.2fr;
    gap: 20px;
  }

  // 中等屏幕 - 平板横屏
  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  // 平板竖屏
  @media (max-width: 768px) {
    gap: 16px;
  }

  // 手机
  @media (max-width: 480px) {
    gap: 12px;
  }
}

// 左侧主要内容
.main-content {
  display: flex;
  flex-direction: column;

  .info-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: none;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    height: fit-content;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);

      &::before {
        opacity: 1;
      }
    }

    .info-content {
        display: flex;
        flex-direction: column;
        gap: 6px;
        max-width: 600px;
        margin: 0 auto;
        flex: 1;
        width: 100%;
      }
      
      .avatar-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        padding: 12px 8px;
        
        .avatar-wrapper {
          position: relative;
          display: inline-block;
          
          .pet-avatar {
            border: 4px solid #ffffff;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
          }
          
          .avatar-overlay {
            position: absolute;
            bottom: -8px;
            right: -8px;
            opacity: 0;
            transition: opacity 0.3s ease;
            
            .avatar-upload {
              .el-button {
                box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
                border: 2px solid #ffffff;
                
                &:hover {
                  transform: scale(1.1);
                  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
                }
              }
            }
          }
          
          &:hover .avatar-overlay {
            opacity: 1;
          }
        }
        
        .avatar-actions {
          display: flex;
          gap: 8px;

          .el-button {
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
          }
        }
      }
      
      .age-section {
        padding: 12px 16px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        border-radius: 10px;
        text-align: center;
        margin-top: 4px;
        
        .age-display {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          margin-bottom: 12px;
          
          .age-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;

            &:hover {
              transform: scale(1.05);
              box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
            }
          }
          
          .age-content {
            text-align: left;
            
            .age-label {
              font-size: 12px;
              font-weight: 600;
              color: #6c757d;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              margin-bottom: 4px;
            }
            
            .age-value {
              font-size: 18px;
              font-weight: 700;
              color: #212529;
              line-height: 1.2;
            }
          }
        }
        
        .age-unit-selector {
          display: flex;
          justify-content: center;

          .el-radio-button {
            .el-radio-button__inner {
              border-radius: 12px;
              font-size: 12px;
              padding: 8px 16px;
              font-weight: 600;
              transition: all 0.3s ease;
              border: 1px solid #e5e7eb;

              &:hover {
                border-color: #409EFF;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
              }
            }

            &.is-active .el-radio-button__inner {
              background: linear-gradient(135deg, #409EFF, #67C23A);
              border-color: #409EFF;
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            }
          }
        }
      }

      .detail-form {
        padding: 12px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        border-radius: 10px;
        
        .form-section {
          .form-section-title {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            .el-icon {
              font-size: 20px;
              color: #409EFF;
            }
          }
        }
        
        .modern-form {
          .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 12px;
            
            @media (max-width: 768px) {
              grid-template-columns: 1fr;
              gap: 10px;
            }
            
            .form-item {
              margin-bottom: 0 !important;
              
              .el-form-item__label {
                font-weight: 600;
                color: #374151;
                margin-bottom: 4px;
                font-size: 12px;
                line-height: 1.2;
              }
              
              .modern-input,
              .modern-select,
              .modern-date-picker {
                .el-input__wrapper,
                .el-select__wrapper {
                  border-radius: 12px;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
                  border: 1px solid #e5e7eb;
                  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                  background: white;

                  &:hover {
                    border-color: #409EFF;
                    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
                    transform: translateY(-1px);
                  }

                  &.is-focus {
                    border-color: #409EFF;
                    box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
                    transform: translateY(-1px);
                  }
                }
              }
              
              .option-content {
                display: flex;
                align-items: center;
                gap: 8px;
              }
            }
          }
        }
      }
      
      // 操作按钮区域
      .action-buttons {
        display: flex;
        gap: 12px;
        justify-content: center;
        padding: 16px 0 0 0;
        border-top: 1px solid #e5e7eb;
        margin-top: 16px;

        .el-button {
          border-radius: 12px;
          font-weight: 600;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;

          &.el-button--primary {
            background: linear-gradient(135deg, #409EFF 0%, #36D1DC 50%, #5B86E5 100%);
            border: none;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
              transition: left 0.5s;
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);

              &::before {
                left: 100%;
              }
            }

            &:active {
              transform: translateY(0);
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
            }
          }

          &.el-button--default {
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }
          }
        }
      }
    }
  }

// 右侧辅助信息
.sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 20px;

  // 平板横屏时改为网格布局
  @media (max-width: 1024px) and (min-width: 769px) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 18px;
  }

  // 手机和小平板时恢复垂直布局
  @media (max-width: 768px) {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .tags-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: none;
    transition: all 0.3s ease;
    height: fit-content;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);

      &::before {
        opacity: 1;
      }
    }
  }

  .notes-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: none;
    transition: all 0.3s ease;
    height: fit-content;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);

      &::before {
        opacity: 1;
      }
    }
    
    .section-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .notes-editor {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
    }
  }
}

// 通用区块标题
.section-header {
  margin-bottom: 16px;
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    background: linear-gradient(135deg, #409EFF, #67C23A);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    .el-icon {
      font-size: 20px;
      color: #409EFF;
    }
  }
}

// 头像区域
.avatar-section {
  margin-bottom: 32px;
  
  .avatar-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    
    .avatar-wrapper {
      position: relative;
      display: inline-block;
      
      .pet-avatar {
        border: 4px solid #ffffff;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: 0 12px 32px rgba(0, 0, 0, 0.18);
        }
      }
      
      .avatar-overlay {
        position: absolute;
        bottom: -8px;
        right: -8px;
        opacity: 0;
        transition: opacity 0.3s ease;
        
        .avatar-upload {
          .el-button {
            box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
            border: 2px solid #ffffff;
            
            &:hover {
              transform: scale(1.1);
              box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
            }
          }
        }
      }
      
      &:hover .avatar-overlay {
        opacity: 1;
      }
    }
    
    .avatar-actions {
      display: flex;
      gap: 8px;

      .el-button {
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
  
  // 快速信息卡片
  .quick-info-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    // 平板和手机优化
    @media (max-width: 768px) {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
      margin-bottom: 20px;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .info-card {
      background: white;
      border: none;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);

        &::before {
          opacity: 1;
        }
      }
      
      .card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #409EFF, #67C23A);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        flex-shrink: 0;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
        }
      }

      &.age-card .card-icon {
        background: linear-gradient(135deg, #409EFF, #67C23A);
      }

      &.gender-card .card-icon {
        background: linear-gradient(135deg, #E6A23C, #F56C6C);
      }

      &.type-card .card-icon {
        background: linear-gradient(135deg, #909399, #606266);
      }

      &.breed-card .card-icon {
        background: linear-gradient(135deg, #67C23A, #E6A23C);
      }
      
      .card-content {
        flex: 1;
        min-width: 0;
        
        .card-label {
          font-size: 12px;
          font-weight: 600;
          color: #6c757d;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 4px;
        }
        
        .card-value {
          font-size: 16px;
          font-weight: 700;
          color: #212529;
          margin-bottom: 8px;
        }
        
        .age-unit-selector {
          margin-top: 8px;

          .el-radio-button {
            --el-radio-button-font-size: 11px;

            .el-radio-button__inner {
              border-radius: 12px;
              font-size: 12px;
              padding: 6px 12px;
              font-weight: 600;
              transition: all 0.3s ease;
              border: 1px solid #e5e7eb;

              &:hover {
                border-color: #409EFF;
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
              }
            }

            &.is-active .el-radio-button__inner {
              background: linear-gradient(135deg, #409EFF, #67C23A);
              border-color: #409EFF;
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            }
          }
        }
      }
    }
  }
}

// 详细表单区域
.detail-form {
  .form-section {
    .form-section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 20px 0;
      font-size: 16px;
      font-weight: 600;
      color: #374151;
      
      .el-icon {
        font-size: 18px;
        color: #667eea;
      }
    }
    
    .modern-form {
      .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 12px;
        
        .form-item {
          margin-bottom: 0 !important;
          
          .el-form-item__label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
          }
          
          .modern-input,
          .modern-select,
          .modern-date-picker {
            .el-input__wrapper,
            .el-select__wrapper {
              border-radius: 12px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
              border: 1px solid #e5e7eb;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #667eea;
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
              }
              
              &.is-focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
              }
            }
          }
          
          .option-content {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }
      }
    }
  }
}

// 标签管理器样式
.tags-manager {
  // 这里可以添加特定的标签管理器样式
}

// 备注编辑器样式
.notes-editor {
  // 这里可以添加特定的备注编辑器样式
}

// 响应式设计优化
@media (min-width: 1600px) {
  .main-content .info-section {
    padding: 24px;
  }

  .sidebar-content {
    .tags-section,
    .notes-section {
      padding: 20px;
    }
  }
}

// 手机和小平板 (480px - 768px)
@media (max-width: 768px) {
  .pet-profile-view {
    padding: 16px;
  }

  .profile-content {
    gap: 16px;
  }

  .profile-header {
    flex-direction: column;
    gap: 16px;
    padding: 20px 16px;
    text-align: center;

    .header-actions {
      justify-content: center;
      flex-wrap: wrap;
      gap: 12px;

      .el-button {
        flex: 1;
        min-width: 120px;
        max-width: 160px;
        padding: 10px 20px;
        font-size: 13px;
      }
    }
  }

  .pet-overview {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .detail-form .modern-form .form-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .sidebar-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .main-content .info-section {
    padding: 16px;
  }

  .sidebar-content .tags-section,
  .sidebar-content .notes-section {
    padding: 16px;
  }
}

// 小手机 (320px - 480px)
@media (max-width: 480px) {
  .pet-profile-view {
    padding: 12px;
  }

  .profile-content {
    gap: 12px;
  }

  .profile-header {
    padding: 16px 12px;
    gap: 12px;

    .pet-name {
      font-size: 20px !important;
    }

    .header-actions {
      flex-direction: column;
      width: 100%;
      gap: 8px;

      .el-button {
        width: 100%;
        min-width: auto;
        max-width: none;
        padding: 12px 16px;
        font-size: 14px;
      }
    }
  }

  .pet-overview {
    gap: 12px;
  }

  .main-content .info-section {
    padding: 16px 12px;
    min-height: auto;
    border-radius: 12px;
  }

  .sidebar-content {
    gap: 12px;

    .tags-section,
    .notes-section {
      padding: 16px 12px;
      border-radius: 12px;
    }
  }

  .detail-form .modern-form .form-grid {
    gap: 8px;

    .form-item {
      .el-form-item__label {
        font-size: 11px;
      }

      .el-input__wrapper {
        border-radius: 8px;
      }
    }
  }

  // 统计卡片优化
  .quick-stats {
    gap: 8px;

    .stat-card {
      padding: 12px;
      border-radius: 10px;

      .card-icon {
        width: 32px;
        height: 32px;
        font-size: 16px;
      }

      .card-content {
        .card-value {
          font-size: 14px;
        }

        .card-label {
          font-size: 11px;
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-container {
  animation: fadeInUp 0.6s ease-out;
}

.info-section,
.tags-section,
.notes-section {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.tags-section {
  animation-delay: 0.1s;
}

.notes-section {
  animation-delay: 0.2s;
}

// 通用响应式工具类
.responsive-text {
  font-size: clamp(14px, 2vw, 18px);
  line-height: 1.5;
}

.responsive-title {
  font-size: clamp(18px, 3vw, 24px);
  line-height: 1.3;
}

.responsive-padding {
  padding: clamp(12px, 2vw, 24px);
}

.responsive-margin {
  margin: clamp(8px, 1.5vw, 20px);
}

// 响应式网格工具类
.responsive-grid {
  display: grid;
  gap: 20px;

  @media (min-width: 1200px) {
    grid-template-columns: 2fr 1.5fr;
  }

  @media (max-width: 1199px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  @media (max-width: 768px) {
    gap: 12px;
  }
}

// 确保在所有设备上的可访问性
@media (prefers-reduced-motion: reduce) {
  .info-section,
  .tags-section,
  .notes-section {
    animation: none;
  }

  .el-card {
    transition: none;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .info-section,
  .tags-section,
  .notes-section {
    border: 2px solid var(--el-border-color);
    background: var(--el-bg-color);
  }
}
</style>