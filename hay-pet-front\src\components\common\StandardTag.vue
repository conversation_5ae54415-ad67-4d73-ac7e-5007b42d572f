<template>
  <div
    :class="tagClasses"
    :style="customStyle"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @focus="handleFocus"
    @blur="handleBlur"
    :tabindex="clickable ? 0 : -1"
    :title="tooltip"
  >
    <!-- 标签内容区域 -->
    <div class="tag-content" :class="contentClasses">
      <!-- 左侧图标 -->
      <el-icon v-if="leftIcon" class="tag-icon tag-icon-left">
        <component :is="leftIcon" />
      </el-icon>
      
      <!-- 标签文本 -->
      <span class="tag-text" :class="textClasses">
        <slot>{{ text }}</slot>
      </span>
      
      <!-- 右侧图标 -->
      <el-icon v-if="rightIcon" class="tag-icon tag-icon-right">
        <component :is="rightIcon" />
      </el-icon>
      
      <!-- 操作按钮区域 -->
      <div 
        v-if="showActions && (editable || deletable)" 
        class="tag-actions"
        :class="{ 'actions-visible': isHovered || actionsAlwaysVisible }"
      >
        <el-icon 
          v-if="editable" 
          @click.stop="handleEdit"
          class="tag-action-icon tag-edit-icon"
          title="编辑"
        >
          <Edit />
        </el-icon>
        <el-icon 
          v-if="deletable" 
          @click.stop="handleDelete"
          class="tag-action-icon tag-delete-icon"
          title="删除"
        >
          <Close />
        </el-icon>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="tag-loading">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { Edit, Close, Loading } from '@element-plus/icons-vue'

// Props 定义
const props = defineProps({
  // 基础属性
  text: {
    type: String,
    default: ''
  },
  
  // 样式预设
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
  },
  
  // 尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  
  // 状态
  active: {
    type: Boolean,
    default: false
  },
  
  clickable: {
    type: Boolean,
    default: true
  },
  
  disabled: {
    type: Boolean,
    default: false
  },
  
  loading: {
    type: Boolean,
    default: false
  },
  
  // 交互功能
  editable: {
    type: Boolean,
    default: false
  },
  
  deletable: {
    type: Boolean,
    default: false
  },
  
  draggable: {
    type: Boolean,
    default: false
  },
  
  // 图标
  leftIcon: {
    type: [String, Object],
    default: null
  },
  
  rightIcon: {
    type: [String, Object],
    default: null
  },
  
  // 自定义样式
  color: {
    type: String,
    default: ''
  },
  
  backgroundColor: {
    type: String,
    default: ''
  },
  
  borderColor: {
    type: String,
    default: ''
  },
  
  // 其他属性
  tooltip: {
    type: String,
    default: ''
  },
  
  showActions: {
    type: Boolean,
    default: true
  },
  
  actionsAlwaysVisible: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits([
  'click',
  'edit',
  'delete',
  'mouseenter',
  'mouseleave',
  'focus',
  'blur'
])

// 响应式状态
const isHovered = ref(false)
const isFocused = ref(false)

// 计算属性
const tagClasses = computed(() => {
  const classes = ['tag-base']
  
  // 尺寸类
  if (props.size !== 'md') {
    classes.push(`tag-${props.size}`)
  }
  
  // 变体类
  classes.push(`tag-${props.variant}`)
  
  // 状态类
  if (props.active) classes.push('tag-active')
  if (props.clickable && !props.disabled) classes.push('tag-clickable')
  if (props.disabled) classes.push('tag-disabled')
  if (props.loading) classes.push('tag-loading-state')
  if (props.draggable) classes.push('tag-draggable')
  
  return classes
})

const contentClasses = computed(() => {
  const classes = []
  
  if (isHovered.value && props.showActions && (props.editable || props.deletable)) {
    classes.push('content-with-actions')
  }
  
  return classes
})

const textClasses = computed(() => {
  const classes = []
  
  if (isHovered.value && props.showActions && (props.editable || props.deletable)) {
    classes.push('text-with-actions')
  }
  
  return classes
})

const customStyle = computed(() => {
  const style = {}
  
  if (props.color) {
    style.color = props.color
  }
  
  if (props.backgroundColor) {
    style.background = props.backgroundColor
  }
  
  if (props.borderColor) {
    style.borderColor = props.borderColor
  }
  
  return style
})

// 事件处理
const handleClick = (event) => {
  if (props.disabled || props.loading) return
  emit('click', event)
}

const handleEdit = (event) => {
  if (props.disabled || props.loading) return
  emit('edit', event)
}

const handleDelete = (event) => {
  if (props.disabled || props.loading) return
  emit('delete', event)
}

const handleMouseEnter = (event) => {
  isHovered.value = true
  emit('mouseenter', event)
}

const handleMouseLeave = (event) => {
  isHovered.value = false
  emit('mouseleave', event)
}

const handleFocus = (event) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  isFocused.value = false
  emit('blur', event)
}
</script>

<style scoped>
/* 引入预设样式 */
@import '@/styles/design-tokens.css';
@import '@/styles/tag-presets.css';

/* 组件特定样式 */
.tag-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  transition: all var(--duration-base) var(--ease-in-out);
}

.tag-text {
  text-align: center;
  transition: all var(--duration-base) var(--ease-in-out);
  flex: 1;
}

.tag-icon {
  font-size: 1em;
  transition: all var(--duration-base) var(--ease-in-out);
}

.tag-icon-left {
  margin-right: var(--spacing-1);
}

.tag-icon-right {
  margin-left: var(--spacing-1);
}

/* 操作按钮样式 */
.tag-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  opacity: 0;
  transition: all var(--duration-base) var(--ease-in-out);
  position: absolute;
  right: var(--spacing-2);
  top: 50%;
  transform: translateY(-50%) translateX(20px);
  z-index: 2;
}

.tag-actions.actions-visible {
  opacity: 1;
  transform: translateY(-50%) translateX(0);
}

.tag-action-icon {
  font-size: 12px;
  cursor: pointer;
  padding: 2px;
  border-radius: var(--radius-sm);
  transition: all var(--duration-fast) var(--ease-in-out);
}

.tag-edit-icon:hover {
  background: rgba(64, 158, 255, 0.1);
  color: var(--color-primary);
}

.tag-delete-icon:hover {
  background: rgba(245, 108, 108, 0.1);
  color: var(--color-danger);
}

/* 内容布局调整 */
.content-with-actions {
  justify-content: flex-start !important;
  padding-left: var(--spacing-2) !important;
  padding-right: calc(var(--spacing-8) + var(--spacing-2)) !important;
}

.text-with-actions {
  margin-right: auto !important;
}

/* 加载状态 */
.tag-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: inherit;
}

.tag-loading-state {
  pointer-events: none;
}

/* 拖拽状态 */
.tag-draggable {
  cursor: grab;
}

.tag-draggable:active {
  cursor: grabbing;
}
</style>
