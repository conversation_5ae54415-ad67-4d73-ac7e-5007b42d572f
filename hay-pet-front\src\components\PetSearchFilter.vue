<template>
  <el-card class="search-filter-card">
    <template #header>
      <div class="card-header">
        <span>搜索和筛选</span>
        <el-button @click="resetFilters" link>重置</el-button>
      </div>
    </template>
    
    <el-form :inline="true" :model="filters" class="search-form">
      <!-- 搜索框 -->
      <el-form-item label="搜索">
        <el-input
          v-model="filters.search"
          placeholder="搜索宠物名称或品种"
          :prefix-icon="Search"
          clearable
          style="width: 200px;"
          @input="handleSearch"
        />
      </el-form-item>
      
      <!-- 品种筛选 -->
      <el-form-item label="品种">
        <el-select
          v-model="filters.species"
          placeholder="选择品种"
          clearable
          style="width: 150px;"
          @change="handleFilter"
        >
          <el-option
            v-for="species in speciesList"
            :key="species"
            :label="species"
            :value="species"
          />
        </el-select>
      </el-form-item>
      
      <!-- 性别筛选 -->
      <el-form-item label="性别">
        <el-select
          v-model="filters.gender"
          placeholder="选择性别"
          clearable
          style="width: 120px;"
          @change="handleFilter"
        >
          <el-option label="公" value="male" />
          <el-option label="母" value="female" />
          <el-option label="未知" value="unknown" />
        </el-select>
      </el-form-item>
      
      <!-- 年龄范围 -->
      <el-form-item label="年龄">
        <el-slider
          v-model="filters.ageRange"
          range
          :min="0"
          :max="30"
          :step="1"
          style="width: 200px;"
          @change="handleFilter"
        />
      </el-form-item>
      
      <!-- 排序方式 -->
      <el-form-item label="排序">
        <el-select
          v-model="filters.sortBy"
          placeholder="排序方式"
          style="width: 150px;"
          @change="handleFilter"
        >
          <el-option label="添加时间（新到旧）" value="created_at_desc" />
          <el-option label="添加时间（旧到新）" value="created_at_asc" />
          <el-option label="名称（A-Z）" value="name_asc" />
          <el-option label="名称（Z-A）" value="name_desc" />
          <el-option label="年龄（小到大）" value="age_asc" />
          <el-option label="年龄（大到小）" value="age_desc" />
        </el-select>
      </el-form-item>
    </el-form>
    
    <!-- 筛选结果统计 -->
    <div class="filter-stats">
      <el-tag v-if="filteredCount !== totalCount" type="info">
        显示 {{ filteredCount }} / {{ totalCount }} 只宠物
      </el-tag>
      <el-tag v-else type="success">
        共 {{ totalCount }} 只宠物
      </el-tag>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { debounce } from '../utils/debounce'

const props = defineProps({
  pets: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['filtered'])

const filters = ref({
  search: '',
  species: '',
  gender: '',
  ageRange: [0, 30],
  sortBy: 'created_at_desc'
})

// 计算品种列表
const speciesList = computed(() => {
  const species = [...new Set(props.pets.map(pet => pet.species).filter(Boolean))]
  return species.sort()
})

// 计算筛选后的宠物列表
const filteredPets = computed(() => {
  let result = [...props.pets]
  
  // 搜索筛选
  if (filters.value.search) {
    const searchTerm = filters.value.search.toLowerCase()
    result = result.filter(pet => 
      pet.name.toLowerCase().includes(searchTerm) ||
      pet.species.toLowerCase().includes(searchTerm)
    )
  }
  
  // 品种筛选
  if (filters.value.species) {
    result = result.filter(pet => pet.species === filters.value.species)
  }
  
  // 性别筛选
  if (filters.value.gender) {
    result = result.filter(pet => pet.gender === filters.value.gender)
  }
  
  // 年龄范围筛选
  const [minAge, maxAge] = filters.value.ageRange
  result = result.filter(pet => {
    const age = pet.age || 0
    return age >= minAge && age <= maxAge
  })
  
  // 排序
  const [sortField, sortOrder] = filters.value.sortBy.split('_')
  result.sort((a, b) => {
    let aVal = a[sortField]
    let bVal = b[sortField]
    
    // 处理字符串比较
    if (typeof aVal === 'string') {
      aVal = aVal.toLowerCase()
      bVal = bVal.toLowerCase()
    }
    
    // 处理日期比较
    if (sortField === 'created') {
      aVal = new Date(aVal)
      bVal = new Date(bVal)
    }
    
    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })
  
  return result
})

// 统计信息
const totalCount = computed(() => props.pets.length)
const filteredCount = computed(() => filteredPets.value.length)

// 防抖搜索
const handleSearch = debounce(() => {
  handleFilter()
}, 300)

// 处理筛选
const handleFilter = () => {
  emit('filtered', filteredPets.value)
}

// 重置筛选
const resetFilters = () => {
  filters.value = {
    search: '',
    species: '',
    gender: '',
    ageRange: [0, 30],
    sortBy: 'created_at_desc'
  }
  handleFilter()
}

// 监听筛选结果变化
watch(filteredPets, (newPets) => {
  emit('filtered', newPets)
}, { immediate: true })

// 监听宠物列表变化
watch(() => props.pets, () => {
  handleFilter()
}, { deep: true })
</script>

<style scoped>
.search-filter-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: end;
}

.filter-stats {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 10px;
  }
  
  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
  }
}
</style>