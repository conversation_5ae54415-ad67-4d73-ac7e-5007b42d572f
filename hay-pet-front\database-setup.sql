-- 宠物管理应用数据库表结构
-- 请在Supabase SQL编辑器中执行此脚本

-- 创建宠物表
CREATE TABLE IF NOT EXISTS public.pets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- 宠物类型，例如：猫、狗
    species TEXT, -- 宠物品种
    birth_date DATE, -- 出生日期
    gender TEXT CHECK (gender IN ('male', 'female', 'unknown')),
    avatar_url TEXT,
    avatar_path TEXT,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建体重记录表
CREATE TABLE IF NOT EXISTS public.weight_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    weight DECIMAL(5,2) NOT NULL,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建宠物照片表
CREATE TABLE IF NOT EXISTS public.photos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    name TEXT NOT NULL,
    size INTEGER,
    type TEXT,
    storage_path TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建用户配置表
CREATE TABLE IF NOT EXISTS public.user_configs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    config_type TEXT NOT NULL, -- 配置类型：dashboard, general, etc.
    config_data JSONB NOT NULL, -- 配置数据
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, config_type)
);

-- 启用行级安全策略 (RLS)
ALTER TABLE public.pets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.weight_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.photos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_configs ENABLE ROW LEVEL SECURITY;

-- 创建宠物表的RLS策略
-- 用户只能查看自己的宠物
DROP POLICY IF EXISTS "Users can view own pets" ON public.pets;
CREATE POLICY "Users can view own pets" ON public.pets
    FOR SELECT USING (auth.uid() = user_id);

-- 用户只能插入自己的宠物
DROP POLICY IF EXISTS "Users can insert own pets" ON public.pets;
CREATE POLICY "Users can insert own pets" ON public.pets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 用户只能更新自己的宠物
DROP POLICY IF EXISTS "Users can update own pets" ON public.pets;
CREATE POLICY "Users can update own pets" ON public.pets
    FOR UPDATE USING (auth.uid() = user_id);

-- 用户只能删除自己的宠物
DROP POLICY IF EXISTS "Users can delete own pets" ON public.pets;
CREATE POLICY "Users can delete own pets" ON public.pets
    FOR DELETE USING (auth.uid() = user_id);

-- 创建体重记录表的RLS策略
-- 用户只能查看自己宠物的体重记录
DROP POLICY IF EXISTS "Users can view own pet weight records" ON public.weight_records;
CREATE POLICY "Users can view own pet weight records" ON public.weight_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = weight_records.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能为自己的宠物添加体重记录
DROP POLICY IF EXISTS "Users can insert weight records for own pets" ON public.weight_records;
CREATE POLICY "Users can insert weight records for own pets" ON public.weight_records
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = weight_records.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能更新自己宠物的体重记录
DROP POLICY IF EXISTS "Users can update own pet weight records" ON public.weight_records;
CREATE POLICY "Users can update own pet weight records" ON public.weight_records
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = weight_records.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能删除自己宠物的体重记录
DROP POLICY IF EXISTS "Users can delete own pet weight records" ON public.weight_records;
CREATE POLICY "Users can delete own pet weight records" ON public.weight_records
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = weight_records.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 创建健康记录表
CREATE TABLE IF NOT EXISTS public.health_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    record_type TEXT NOT NULL, -- 例如: 'vaccination', 'checkup', 'medication'
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    description TEXT, -- 详细描述或医生备注
    next_due_date DATE, -- 下次复查或接种日期
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
ALTER TABLE public.health_records ENABLE ROW LEVEL SECURITY;

-- 创建提醒事项表
CREATE TABLE IF NOT EXISTS public.reminders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    title TEXT NOT NULL, -- 提醒标题
    notes TEXT, -- 详细描述
    due_date TIMESTAMPTZ NOT NULL, -- 提醒日期和时间
    priority TEXT DEFAULT '中' CHECK (priority IN ('高', '中', '低')), -- 优先级
    is_completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
ALTER TABLE public.reminders ENABLE ROW LEVEL SECURITY;

-- 创建花费记录表
CREATE TABLE IF NOT EXISTS public.expense_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    category TEXT NOT NULL, -- 花费类别，例如：食物、医疗、玩具、美容等
    amount DECIMAL(10,2) NOT NULL, -- 花费金额
    currency TEXT DEFAULT 'CNY', -- 货币类型
    date DATE NOT NULL DEFAULT CURRENT_DATE, -- 花费日期
    description TEXT, -- 花费描述
    notes TEXT, -- 备注
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
ALTER TABLE public.expense_records ENABLE ROW LEVEL SECURITY;

-- 创建宠物照片表的RLS策略
-- 用户只能查看自己宠物的照片
DROP POLICY IF EXISTS "Users can view own pet photos" ON public.photos;
CREATE POLICY "Users can view own pet photos" ON public.photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = photos.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能为自己的宠物添加照片
DROP POLICY IF EXISTS "Users can insert photos for own pets" ON public.photos;
CREATE POLICY "Users can insert photos for own pets" ON public.photos
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = photos.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能更新自己宠物的照片
DROP POLICY IF EXISTS "Users can update own pet photos" ON public.photos;
CREATE POLICY "Users can update own pet photos" ON public.photos
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = photos.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能删除自己宠物的照片
DROP POLICY IF EXISTS "Users can delete own pet photos" ON public.photos;
CREATE POLICY "Users can delete own pet photos" ON public.photos
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = photos.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 创建健康记录表的RLS策略
-- 用户只能查看自己宠物的健康记录
DROP POLICY IF EXISTS "Users can view own pet health records" ON public.health_records;
CREATE POLICY "Users can view own pet health records" ON public.health_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = health_records.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能为自己的宠物添加健康记录
DROP POLICY IF EXISTS "Users can insert health records for own pets" ON public.health_records;
CREATE POLICY "Users can insert health records for own pets" ON public.health_records
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = health_records.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能更新自己宠物的健康记录
DROP POLICY IF EXISTS "Users can update own pet health records" ON public.health_records;
CREATE POLICY "Users can update own pet health records" ON public.health_records
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = health_records.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能删除自己宠物的健康记录
DROP POLICY IF EXISTS "Users can delete own pet health records" ON public.health_records;
CREATE POLICY "Users can delete own pet health records" ON public.health_records
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = health_records.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 创建提醒事项表的RLS策略
-- 用户只能查看自己宠物的提醒事项
DROP POLICY IF EXISTS "Users can view own pet reminders" ON public.reminders;
CREATE POLICY "Users can view own pet reminders" ON public.reminders
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = reminders.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能为自己的宠物添加提醒事项
DROP POLICY IF EXISTS "Users can insert reminders for own pets" ON public.reminders;
CREATE POLICY "Users can insert reminders for own pets" ON public.reminders
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = reminders.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能更新自己宠物的提醒事项
DROP POLICY IF EXISTS "Users can update own pet reminders" ON public.reminders;
CREATE POLICY "Users can update own pet reminders" ON public.reminders
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = reminders.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能删除自己宠物的提醒事项
DROP POLICY IF EXISTS "Users can delete own pet reminders" ON public.reminders;
CREATE POLICY "Users can delete own pet reminders" ON public.reminders
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = reminders.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 创建花费记录表的RLS策略
-- 用户只能查看自己宠物的花费记录
DROP POLICY IF EXISTS "Users can view own pet expense records" ON public.expense_records;
CREATE POLICY "Users can view own pet expense records" ON public.expense_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = expense_records.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能为自己的宠物添加花费记录
DROP POLICY IF EXISTS "Users can insert expense records for own pets" ON public.expense_records;
CREATE POLICY "Users can insert expense records for own pets" ON public.expense_records
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = expense_records.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能更新自己宠物的花费记录
DROP POLICY IF EXISTS "Users can update own pet expense records" ON public.expense_records;
CREATE POLICY "Users can update own pet expense records" ON public.expense_records
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = expense_records.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能删除自己宠物的花费记录
DROP POLICY IF EXISTS "Users can delete own pet expense records" ON public.expense_records;
CREATE POLICY "Users can delete own pet expense records" ON public.expense_records
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = expense_records.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 创建用户配置表的RLS策略
-- 用户只能查看自己的配置
DROP POLICY IF EXISTS "Users can view own configs" ON public.user_configs;
CREATE POLICY "Users can view own configs" ON public.user_configs
    FOR SELECT USING (user_id = auth.uid());

-- 用户只能插入自己的配置
DROP POLICY IF EXISTS "Users can insert own configs" ON public.user_configs;
CREATE POLICY "Users can insert own configs" ON public.user_configs
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- 用户只能更新自己的配置
DROP POLICY IF EXISTS "Users can update own configs" ON public.user_configs;
CREATE POLICY "Users can update own configs" ON public.user_configs
    FOR UPDATE USING (user_id = auth.uid());

-- 用户只能删除自己的配置
DROP POLICY IF EXISTS "Users can delete own configs" ON public.user_configs;
CREATE POLICY "Users can delete own configs" ON public.user_configs
    FOR DELETE USING (user_id = auth.uid());

-- 创建存储桶用于宠物头像 (如果不存在)
-- 注意：这需要在Supabase Storage中手动创建，或者使用Supabase客户端代码创建
-- INSERT INTO storage.buckets (id, name, public) VALUES ('pet-media', 'pet-media', true);

-- 创建存储策略 (如果使用Supabase Storage)
-- CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
--     FOR SELECT USING (bucket_id = 'pet-media');

-- CREATE POLICY "Users can upload avatar images" ON storage.objects
--     FOR INSERT WITH CHECK (bucket_id = 'pet-media' AND auth.role() = 'authenticated');

-- CREATE POLICY "Users can update own avatar images" ON storage.objects
--     FOR UPDATE USING (bucket_id = 'pet-media' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can delete own avatar images" ON storage.objects
--     FOR DELETE USING (bucket_id = 'pet-media' AND auth.uid()::text = (storage.foldername(name))[1]);

-- 创建更新时间戳的函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为pets表创建更新时间戳的触发器
DROP TRIGGER IF EXISTS update_pets_updated_at ON public.pets;
CREATE TRIGGER update_pets_updated_at
    BEFORE UPDATE ON public.pets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为health_records表创建更新时间戳的触发器
DROP TRIGGER IF EXISTS update_health_records_updated_at ON public.health_records;
CREATE TRIGGER update_health_records_updated_at
    BEFORE UPDATE ON public.health_records
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为reminders表创建更新时间戳的触发器
DROP TRIGGER IF EXISTS update_reminders_updated_at ON public.reminders;
CREATE TRIGGER update_reminders_updated_at
    BEFORE UPDATE ON public.reminders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为expense_records表创建更新时间戳的触发器
DROP TRIGGER IF EXISTS update_expense_records_updated_at ON public.expense_records;
CREATE TRIGGER update_expense_records_updated_at
    BEFORE UPDATE ON public.expense_records
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为user_configs表创建更新时间戳的触发器
DROP TRIGGER IF EXISTS update_user_configs_updated_at ON public.user_configs;
CREATE TRIGGER update_user_configs_updated_at
    BEFORE UPDATE ON public.user_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_pets_user_id ON public.pets(user_id);
CREATE INDEX IF NOT EXISTS idx_weight_records_pet_id ON public.weight_records(pet_id);
CREATE INDEX IF NOT EXISTS idx_weight_records_date ON public.weight_records(date);
CREATE INDEX IF NOT EXISTS idx_photos_pet_id ON public.photos(pet_id);
CREATE INDEX IF NOT EXISTS idx_photos_created_at ON public.photos(created_at);
CREATE INDEX IF NOT EXISTS idx_health_records_pet_id ON public.health_records(pet_id);
CREATE INDEX IF NOT EXISTS idx_health_records_date ON public.health_records(date);
CREATE INDEX IF NOT EXISTS idx_reminders_pet_id ON public.reminders(pet_id);
CREATE INDEX IF NOT EXISTS idx_reminders_due_date ON public.reminders(due_date);
CREATE INDEX IF NOT EXISTS idx_expense_records_pet_id ON public.expense_records(pet_id);
CREATE INDEX IF NOT EXISTS idx_expense_records_date ON public.expense_records(date);
CREATE INDEX IF NOT EXISTS idx_user_configs_user_id ON public.user_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_configs_type ON public.user_configs(config_type);
CREATE INDEX IF NOT EXISTS idx_user_configs_user_type ON public.user_configs(user_id, config_type);
CREATE INDEX IF NOT EXISTS idx_expense_records_category ON public.expense_records(category);

-- 完成提示
-- 执行完成后，您的数据库将包含以下表：
-- 1. pets - 宠物信息表
-- 2. weight_records - 体重记录表
-- 3. photos - 宠物照片表
-- 4. health_records - 健康记录表
-- 5. reminders - 提醒事项表
-- 6. expense_records - 花费记录表
-- 以及相应的RLS策略确保数据安全