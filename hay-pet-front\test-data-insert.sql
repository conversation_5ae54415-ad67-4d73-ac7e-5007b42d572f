-- 测试数据插入脚本
-- 请在Supabase SQL编辑器中执行此脚本来添加测试数据

-- 注意：请确保您已经有一个用户账户和至少一只宠物
-- 您需要将下面的 user_id 和 pet_id 替换为实际的值

-- 假设用户ID和宠物ID（请根据实际情况修改）
-- 您可以通过以下查询获取实际的ID：
-- SELECT auth.uid() as current_user_id;
-- SELECT id, name FROM pets WHERE user_id = auth.uid();

-- 插入健康记录示例数据
INSERT INTO public.health_records (pet_id, record_type, description, date, notes) VALUES
-- 请将 'your-pet-id-here' 替换为实际的宠物ID
('your-pet-id-here', 'vaccination', '狂犬病疫苗', '2024-01-15', '年度疫苗接种'),
('your-pet-id-here', 'vaccination', '五联疫苗', '2024-02-20', '预防多种疾病'),
('your-pet-id-here', 'deworming', '体内驱虫', '2024-03-10', '使用拜耳驱虫药'),
('your-pet-id-here', 'deworming', '体外驱虫', '2024-03-15', '福来恩滴剂'),
('your-pet-id-here', 'medical', '常规体检', '2024-04-05', '身体状况良好'),
('your-pet-id-here', 'medical', '皮肤检查', '2024-04-20', '轻微皮炎，已开药');

-- 插入提醒事项示例数据
INSERT INTO public.reminders (pet_id, title, notes, due_date, priority, is_completed) VALUES
-- 请将 'your-pet-id-here' 替换为实际的宠物ID
('your-pet-id-here', '疫苗接种提醒', '该接种年度疫苗了', '2024-12-15', '高', false),
('your-pet-id-here', '驱虫提醒', '定期驱虫时间到了', '2024-12-20', '中', false),
('your-pet-id-here', '体检提醒', '半年度健康检查', '2024-12-25', '中', false),
('your-pet-id-here', '洗澡提醒', '该给宠物洗澡了', '2024-12-10', '低', false),
('your-pet-id-here', '美容提醒', '修剪指甲和毛发', '2024-12-18', '低', false);

-- 插入花费记录示例数据
INSERT INTO public.expense_records (pet_id, category, amount, description, date) VALUES
-- 请将 'your-pet-id-here' 替换为实际的宠物ID
('your-pet-id-here', '医疗', 200.00, '疫苗接种费用', '2024-01-15'),
('your-pet-id-here', '食物', 150.00, '优质狗粮', '2024-01-20'),
('your-pet-id-here', '用品', 80.00, '玩具和零食', '2024-02-01'),
('your-pet-id-here', '医疗', 120.00, '驱虫药物', '2024-03-10'),
('your-pet-id-here', '美容', 100.00, '洗澡美容', '2024-03-25'),
('your-pet-id-here', '食物', 160.00, '湿粮罐头', '2024-04-05');

-- 插入体重记录示例数据
INSERT INTO public.weight_records (pet_id, weight, date, notes) VALUES
-- 请将 'your-pet-id-here' 替换为实际的宠物ID
-- 体重以克为单位存储
('your-pet-id-here', 5200, '2024-01-01', '新年体重记录'),
('your-pet-id-here', 5350, '2024-02-01', '体重略有增加'),
('your-pet-id-here', 5400, '2024-03-01', '春季体重'),
('your-pet-id-here', 5300, '2024-04-01', '体重稳定'),
('your-pet-id-here', 5450, '2024-05-01', '体重正常增长');

-- 插入照片记录示例数据（可选）
-- 注意：这里只是创建记录，实际的图片文件需要通过应用上传
INSERT INTO public.photos (pet_id, title, description, url, created_at) VALUES
-- 请将 'your-pet-id-here' 替换为实际的宠物ID
('your-pet-id-here', '可爱瞬间', '在公园玩耍', 'https://example.com/photo1.jpg', '2024-01-15'),
('your-pet-id-here', '睡觉时光', '午后小憩', 'https://example.com/photo2.jpg', '2024-02-20'),
('your-pet-id-here', '户外探险', '海边散步', 'https://example.com/photo3.jpg', '2024-03-10');

-- 执行完成后的提示
-- 请记住：
-- 1. 将所有 'your-pet-id-here' 替换为您实际的宠物ID
-- 2. 确保您已经登录并且有权限访问这些表
-- 3. 如果遇到权限错误，请检查RLS策略是否正确设置
-- 4. 照片URL是示例，实际使用时需要上传真实的图片文件