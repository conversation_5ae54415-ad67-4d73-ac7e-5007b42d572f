// 设置管理工具
import { ref, reactive } from 'vue';

// 默认设置
const defaultSettings = {
  theme: 'light',
  notificationsEnabled: true,
  weightUnit: 'kg', // 默认使用千克
  dateFormat: 'YYYY-MM-DD', // 默认日期格式
};

// 全局设置状态
export const globalSettings = reactive({ ...defaultSettings });

// 体重单位转换函数
export const convertWeight = (weight, fromUnit, toUnit) => {
  if (fromUnit === toUnit) return weight;
  if (!weight || isNaN(weight)) return weight;
  
  // 先转换为克作为基准单位
  let weightInGrams;
  switch (fromUnit) {
    case 'kg':
      weightInGrams = weight * 1000;
      break;
    case 'lb':
      weightInGrams = weight * 453.592;
      break;
    case 'g':
    default:
      weightInGrams = weight;
      break;
  }
  
  // 从克转换为目标单位
  switch (toUnit) {
    case 'kg':
      return parseFloat((weightInGrams / 1000).toFixed(2));
    case 'lb':
      return parseFloat((weightInGrams / 453.592).toFixed(2));
    case 'g':
    default:
      return Math.round(weightInGrams);
  }
};

// 格式化体重显示
export const formatWeight = (weight, unit = null) => {
  const targetUnit = unit || globalSettings.weightUnit;
  if (!weight || isNaN(weight)) return `0 ${targetUnit}`;
  
  // 数据库存储的体重单位是克，需要从克转换为目标单位
  const formattedWeight = convertWeight(weight, 'g', targetUnit);
  return `${formattedWeight} ${targetUnit}`;
};

// 获取体重单位标签
export const getWeightUnitLabel = (unit = null) => {
  const targetUnit = unit || globalSettings.weightUnit;
  switch (targetUnit) {
    case 'kg':
      return '千克';
    case 'g':
      return '克';
    case 'lb':
      return '磅';
    default:
      return '千克';
  }
};

// 保存设置到本地存储
export const saveSettings = () => {
  try {
    localStorage.setItem('hay-pet-settings', JSON.stringify(globalSettings));
    return true;
  } catch (error) {
    console.error('保存设置失败:', error);
    return false;
  }
};

// 从本地存储加载设置
export const loadSettings = () => {
  try {
    const savedSettings = localStorage.getItem('hay-pet-settings');
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings);
      Object.assign(globalSettings, { ...defaultSettings, ...parsed });
    }
    return true;
  } catch (error) {
    console.error('加载设置失败:', error);
    return false;
  }
};

// 重置设置为默认值
export const resetSettings = () => {
  Object.assign(globalSettings, defaultSettings);
  saveSettings();
};

// 更新单个设置项
export const updateSetting = (key, value) => {
  if (key in globalSettings) {
    globalSettings[key] = value;
    saveSettings();
    return true;
  }
  return false;
};

// 初始化设置（应用启动时调用）
export const initSettings = () => {
  loadSettings();
};