-- 创建事件记录表（从health_records重命名）
-- 如果health_records表存在，先重命名为event_records
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'health_records') THEN
        ALTER TABLE health_records RENAME TO event_records;
    END IF;
END $$;

-- 如果event_records表不存在，则创建
CREATE TABLE IF NOT EXISTS event_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pet_id UUID NOT NULL REFERENCES pets(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    record_type VARCHAR(50) NOT NULL DEFAULT 'other',
    description TEXT,
    next_due_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建记录类型管理表
CREATE TABLE IF NOT EXISTS record_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    value VARCHAR(50) UNIQUE NOT NULL,
    label VARCHAR(100) NOT NULL,
    color VARCHAR(7) NOT NULL DEFAULT '#409EFF',
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入默认的系统记录类型
INSERT INTO record_types (value, label, color, is_system) VALUES
    ('vaccination', '疫苗接种', '#67C23A', TRUE),
    ('deworming', '驱虫', '#E6A23C', TRUE),
    ('checkup', '体检', '#409EFF', TRUE),
    ('illness', '疾病就诊', '#F56C6C', TRUE),
    ('medication', '用药记录', '#909399', TRUE),
    ('allergy', '过敏记录', '#FF5722', TRUE),
    ('surgery', '手术记录', '#9C27B0', TRUE),
    ('other', '其他', '#607D8B', TRUE)
ON CONFLICT (value) DO NOTHING;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_event_records_pet_id ON event_records(pet_id);
CREATE INDEX IF NOT EXISTS idx_event_records_date ON event_records(date);
CREATE INDEX IF NOT EXISTS idx_event_records_type ON event_records(record_type);
CREATE INDEX IF NOT EXISTS idx_event_records_next_due ON event_records(next_due_date);

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为event_records表创建更新时间戳触发器
DROP TRIGGER IF EXISTS update_event_records_updated_at ON event_records;
CREATE TRIGGER update_event_records_updated_at
    BEFORE UPDATE ON event_records
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为record_types表创建更新时间戳触发器
DROP TRIGGER IF EXISTS update_record_types_updated_at ON record_types;
CREATE TRIGGER update_record_types_updated_at
    BEFORE UPDATE ON record_types
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全策略（RLS）
ALTER TABLE event_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE record_types ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略 - 用户只能访问自己宠物的记录
CREATE POLICY "Users can view their own pet event records" ON event_records
    FOR SELECT USING (
        pet_id IN (
            SELECT id FROM pets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own pet event records" ON event_records
    FOR INSERT WITH CHECK (
        pet_id IN (
            SELECT id FROM pets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own pet event records" ON event_records
    FOR UPDATE USING (
        pet_id IN (
            SELECT id FROM pets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own pet event records" ON event_records
    FOR DELETE USING (
        pet_id IN (
            SELECT id FROM pets WHERE user_id = auth.uid()
        )
    );

-- 记录类型表的RLS策略 - 所有用户都可以查看，但只有管理员可以修改系统类型
CREATE POLICY "Everyone can view record types" ON record_types
    FOR SELECT USING (true);

CREATE POLICY "Users can insert custom record types" ON record_types
    FOR INSERT WITH CHECK (is_system = FALSE);

CREATE POLICY "Users can update custom record types" ON record_types
    FOR UPDATE USING (is_system = FALSE);

CREATE POLICY "Users can delete custom record types" ON record_types
    FOR DELETE USING (is_system = FALSE);
