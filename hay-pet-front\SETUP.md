# Hay!Pet 项目设置指南

## 数据库设置

### 1. 执行数据库初始化脚本
在 Supabase SQL 编辑器中依次执行以下脚本：

```sql
-- 1. 首先执行基础数据库结构
-- 复制 database-setup.sql 文件内容并执行
```

### 2. 执行数据库迁移脚本
```sql
-- 2. 执行数据库迁移（添加缺失的列）
-- 复制 database-migration.sql 文件内容并执行
```

### 3. 设置存储桶
```sql
-- 3. 创建存储桶和策略
-- 复制 storage-setup.sql 文件内容并执行
```

## 常见问题解决

### 1. 头像上传失败 - "Bucket not found"
**原因**: Supabase Storage 中没有创建 `pet-media` 存储桶

**解决方案**:
1. 在 Supabase 控制台进入 Storage 页面
2. 创建名为 `pet-media` 的存储桶
3. 或者在 SQL 编辑器中执行 `storage-setup.sql` 脚本

### 2. 健康记录保存失败 - "null value in column 'id'"
**原因**: 前端在新增记录时包含了 id 字段

**解决方案**: 已修复，新增记录时会自动删除 id 字段

### 3. 字段名不匹配错误
**原因**: 前端使用的字段名与数据库列名不一致

**已修复的字段映射**:
- 健康记录: `type` → `record_type`
- 头像路径: `avatar_path` 列已添加到数据库

## 环境变量配置

确保 `.env` 文件包含正确的 Supabase 配置：

```env
VITE_SUPABASE_URL=你的_supabase_url
VITE_SUPABASE_ANON_KEY=你的_supabase_anon_key
```

## 验证设置

### 检查数据库表
```sql
-- 验证所有表是否存在
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('pets', 'health_records', 'reminders', 'weight_records', 'photos');

-- 验证 pets 表结构
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'pets' AND table_schema = 'public';
```

### 检查存储桶
```sql
-- 验证存储桶是否存在
SELECT * FROM storage.buckets WHERE id = 'pet-media';
```

## 部署后检查清单

- [ ] 数据库表已创建
- [ ] 存储桶 `pet-media` 已创建
- [ ] RLS 策略已设置
- [ ] 环境变量已配置
- [ ] 前端可以正常连接 Supabase
- [ ] 用户注册/登录功能正常
- [ ] 宠物添加功能正常
- [ ] 头像上传功能正常
- [ ] 健康记录添加功能正常
- [ ] 照片上传功能正常