/**
 * Hay!Pet 设计令牌系统
 * 统一的设计变量配置，确保整个项目的视觉一致性
 */

:root {
  /* ========== 颜色系统 ========== */
  
  /* 主色调 - 基于现有项目的渐变色彩方案 */
  --color-primary: #409EFF;
  --color-primary-light: #79BBFF;
  --color-primary-dark: #337ECC;
  --color-primary-lighter: #A0CFFF;
  --color-primary-darker: #2B6CB0;
  
  /* 辅助色彩 */
  --color-success: #67C23A;
  --color-success-light: #85CE61;
  --color-success-dark: #529B2E;
  --color-success-lighter: #B3E19D;
  --color-success-darker: #3E7B1F;
  
  --color-warning: #E6A23C;
  --color-warning-light: #F7BA2A;
  --color-warning-dark: #B88230;
  --color-warning-lighter: #F3D19E;
  --color-warning-darker: #8A5A1F;
  
  --color-danger: #F56C6C;
  --color-danger-light: #F78989;
  --color-danger-dark: #C45656;
  --color-danger-lighter: #FAB6B6;
  --color-danger-darker: #924040;
  
  --color-info: #909399;
  --color-info-light: #A6A9AD;
  --color-info-dark: #73767A;
  --color-info-lighter: #C8C9CC;
  --color-info-darker: #565859;
  
  /* 中性色彩 */
  --color-text-primary: #303133;
  --color-text-regular: #606266;
  --color-text-secondary: #909399;
  --color-text-placeholder: #C0C4CC;
  --color-text-disabled: #E4E7ED;
  
  /* 背景色彩 */
  --color-bg-primary: #FFFFFF;
  --color-bg-secondary: #F5F7FA;
  --color-bg-tertiary: #FAFAFA;
  --color-bg-quaternary: #F8F9FA;
  
  /* 边框色彩 */
  --color-border-light: #EBEEF5;
  --color-border-base: #DCDFE6;
  --color-border-dark: #D4D7DE;
  --color-border-darker: #CDD0D6;
  
  /* 渐变色彩 - 基于现有项目的渐变方案 */
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-success) 100%);
  --gradient-primary-light: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-success-light) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-warning) 0%, var(--color-danger) 100%);
  --gradient-neutral: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  --gradient-hover: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  
  /* ========== 字体系统 ========== */
  
  /* 字体大小 */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  
  /* 字体权重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* ========== 间距系统 ========== */
  
  /* 基础间距 */
  --spacing-0: 0;
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-8: 32px;
  --spacing-10: 40px;
  --spacing-12: 48px;
  --spacing-16: 64px;
  --spacing-20: 80px;
  --spacing-24: 96px;
  
  /* 语义化间距 */
  --spacing-xs: var(--spacing-1);
  --spacing-sm: var(--spacing-2);
  --spacing-md: var(--spacing-4);
  --spacing-lg: var(--spacing-6);
  --spacing-xl: var(--spacing-8);
  --spacing-2xl: var(--spacing-12);
  
  /* ========== 圆角系统 ========== */
  
  --radius-none: 0;
  --radius-sm: 4px;
  --radius-base: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;
  --radius-full: 50%;
  
  /* 语义化圆角 */
  --radius-button: var(--radius-md);
  --radius-card: var(--radius-xl);
  --radius-tag: var(--radius-3xl);
  --radius-input: var(--radius-md);
  
  /* ========== 阴影系统 ========== */
  
  /* 基础阴影 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-base: 0 3px 8px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 8px 20px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  
  /* 特殊阴影 */
  --shadow-focus: 0 0 0 3px rgba(64, 158, 255, 0.2);
  --shadow-primary: 0 4px 12px rgba(64, 158, 255, 0.3);
  --shadow-success: 0 4px 12px rgba(103, 194, 58, 0.3);
  --shadow-warning: 0 4px 12px rgba(230, 162, 60, 0.3);
  --shadow-danger: 0 4px 12px rgba(245, 108, 108, 0.3);
  
  /* ========== 动画系统 ========== */
  
  /* 过渡时间 */
  --duration-fast: 0.15s;
  --duration-base: 0.3s;
  --duration-slow: 0.5s;
  --duration-slower: 0.8s;
  
  /* 缓动函数 */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* ========== Z-Index 层级系统 ========== */
  
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
  
  /* ========== 断点系统 ========== */
  
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 992px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1920px;
}

/* ========== 暗色主题支持 ========== */
@media (prefers-color-scheme: dark) {
  :root {
    /* 暗色模式下的颜色调整 */
    --color-bg-primary: #1a1a1a;
    --color-bg-secondary: #2d2d2d;
    --color-bg-tertiary: #3a3a3a;
    --color-bg-quaternary: #404040;
    
    --color-text-primary: #ffffff;
    --color-text-regular: #e0e0e0;
    --color-text-secondary: #b0b0b0;
    --color-text-placeholder: #808080;
    
    --color-border-light: #404040;
    --color-border-base: #505050;
    --color-border-dark: #606060;
    --color-border-darker: #707070;
  }
}

/* ========== 工具类 ========== */

/* 快速应用设计令牌的工具类 */
.text-primary { color: var(--color-text-primary); }
.text-regular { color: var(--color-text-regular); }
.text-secondary { color: var(--color-text-secondary); }

.bg-primary { background-color: var(--color-bg-primary); }
.bg-secondary { background-color: var(--color-bg-secondary); }

.shadow-base { box-shadow: var(--shadow-base); }
.shadow-lg { box-shadow: var(--shadow-lg); }

.radius-base { border-radius: var(--radius-base); }
.radius-lg { border-radius: var(--radius-lg); }
.radius-tag { border-radius: var(--radius-tag); }

.transition-base { 
  transition: all var(--duration-base) var(--ease-in-out); 
}
