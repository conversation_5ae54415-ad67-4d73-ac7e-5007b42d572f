<template>
  <el-card class="dashboard-card expense-overview-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">花费记录概览</span>
        <el-button type="primary" link @click="router.push('/expenses')">
          <el-icon><Money /></el-icon>
          查看详细
        </el-button>
      </div>
    </template>
    <div class="expense-overview-content">
      <div v-if="expenseRecords.length" class="expense-content">
        <!-- 简约视图 -->
        <div v-if="props.viewMode === 'compact'" class="compact-view">
          <div class="compact-stats">
            <div class="compact-stat-item">
              <span class="compact-label">本月总花费</span>
              <span class="compact-value">¥{{ monthlyTotal.toFixed(2) }}</span>
            </div>
            <div class="compact-stat-item">
              <span class="compact-label">记录数</span>
              <span class="compact-value">{{ expenseRecords.length }}</span>
            </div>
          </div>
          <div class="compact-recent">
            <div v-for="expense in recentExpenses.slice(0, 3)" :key="expense.id" class="compact-expense-item">
              <span class="compact-category">{{ expense.category }}</span>
              <span class="compact-amount">¥{{ expense.amount.toFixed(2) }}</span>
            </div>
          </div>
        </div>
        
        <!-- 卡片视图 -->
        <div v-else class="card-view">
          <div class="expense-stats">
            <div class="stat-item primary">
              <div class="stat-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <span class="stat-label">本月总花费</span>
                <span class="stat-value">¥{{ monthlyTotal.toFixed(2) }}</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <span class="stat-label">平均每日</span>
                <span class="stat-value">¥{{ dailyAverage.toFixed(2) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 花费分布图表 -->
          <div class="expense-chart" v-if="categoryData.length > 0">
            <h4 class="section-title">本月花费分布</h4>
            <v-chart 
              class="pie-chart" 
              :option="pieChartOption" 
              :loading="loading"
              autoresize
            />
          </div>
          
          <div class="recent-expenses">
            <h4 class="section-title">最近花费</h4>
            <div class="expense-list">
              <div v-for="expense in recentExpenses" :key="expense.id" class="expense-item">
                <div class="expense-info">
                  <span class="expense-category">{{ expense.category }}</span>
                  <span class="expense-description">{{ expense.description }}</span>
                </div>
                <div class="expense-details">
                  <span class="expense-amount">¥{{ expense.amount.toFixed(2) }}</span>
                  <span class="expense-date">{{ formatDate(expense.date) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无花费记录">
        <el-button type="primary" @click="router.push('/expenses')">
          <el-icon><Plus /></el-icon>
          添加花费记录
        </el-button>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Money, TrendCharts, Plus } from '@element-plus/icons-vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import { usePetStore } from '@/stores/pet';
import { supabase } from '@/utils/supabase';
import type { DashboardBlock } from '@/types/dashboard';

// 注册ECharts组件
use([
  CanvasRenderer,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent
]);

interface Props {
  blockConfig: DashboardBlock;
  viewMode?: 'card' | 'compact';
}

const props = defineProps<Props>();

const router = useRouter();
const petStore = usePetStore();

const expenseRecords = ref<any[]>([]);
const loading = ref(false);

const currentPet = computed(() => petStore.currentPet);

const monthlyTotal = computed(() => {
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  
  return expenseRecords.value
    .filter(expense => {
      const expenseDate = new Date(expense.date);
      return expenseDate.getMonth() === currentMonth && 
             expenseDate.getFullYear() === currentYear;
    })
    .reduce((total, expense) => total + expense.amount, 0);
});

const dailyAverage = computed(() => {
  const now = new Date();
  const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
  const currentDay = now.getDate();
  
  return monthlyTotal.value / currentDay;
});

const recentExpenses = computed(() => {
  return [...expenseRecords.value]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);
});

// 按类别统计本月花费
const categoryData = computed(() => {
  const now = new Date();
  const currentMonth = now.getMonth();
  const currentYear = now.getFullYear();
  
  const monthlyExpenses = expenseRecords.value.filter(expense => {
    const expenseDate = new Date(expense.date);
    return expenseDate.getMonth() === currentMonth && 
           expenseDate.getFullYear() === currentYear;
  });
  
  const categoryMap = new Map<string, number>();
  
  monthlyExpenses.forEach(expense => {
    const category = expense.category || '其他';
    categoryMap.set(category, (categoryMap.get(category) || 0) + expense.amount);
  });
  
  return Array.from(categoryMap.entries())
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);
});

// 饼图配置选项
const pieChartOption = computed(() => {
  if (!categoryData.value.length) {
    return {};
  }

  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#C0C4CC'];
  
  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: '0%',
      left: 'center',
      textStyle: {
        fontSize: 12,
        color: '#606266'
      }
    },
    series: [
      {
        name: '花费分布',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold',
            formatter: '{b}\n¥{c}'
          }
        },
        labelLine: {
          show: false
        },
        data: categoryData.value.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index % colors.length]
          }
        }))
      }
    ]
  };
});

async function fetchExpenseRecords() {
  if (!currentPet.value?.id) return;
  
  loading.value = true;
  try {
    const { data, error } = await supabase
      .from('expense_records')
      .select('*')
      .eq('pet_id', currentPet.value.id)
      .order('date', { ascending: false })
      .limit(20);
    
    if (error) throw error;
    expenseRecords.value = data || [];
  } catch (error) {
    console.error('获取花费记录失败:', error);
  } finally {
    loading.value = false;
  }
}

function formatDate(date: string): string {
  return new Date(date).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  });
}

onMounted(() => {
  fetchExpenseRecords();
});

// 监听当前宠物变化
watch(currentPet, (newPet) => {
  if (newPet) {
    fetchExpenseRecords();
  } else {
    expenseRecords.value = [];
  }
}, { immediate: true });
</script>

<style scoped>
.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.expense-overview-content {
  padding: 0;
}

.expense-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.expense-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.stat-item.primary {
  background: linear-gradient(135deg, var(--el-color-primary-light-9), var(--el-color-primary-light-8));
  border-color: var(--el-color-primary-light-7);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--el-color-primary-light-9);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-color-primary);
}

.primary .stat-icon {
  background: var(--el-color-primary);
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.expense-chart {
  margin: 16px 0;
}

.pie-chart {
  height: 280px;
  width: 100%;
}

.primary .stat-value {
  color: var(--el-color-primary);
}

.recent-expenses {
  margin-top: 8px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.expense-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.expense-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.expense-info {
  flex: 1;
}

.expense-category {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.expense-description {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.expense-details {
  text-align: right;
}

.expense-amount {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-color-primary);
  margin-bottom: 2px;
}

.expense-date {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 简约视图样式 */
.compact-view {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.compact-stats {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.compact-stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.compact-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.compact-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.compact-recent {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.compact-expense-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
}

.compact-category {
  font-size: 13px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.compact-amount {
  font-size: 13px;
  font-weight: 600;
  color: var(--el-color-primary);
}

@media (max-width: 768px) {
  .expense-stats {
    grid-template-columns: 1fr;
  }
  
  .pie-chart {
    height: 240px;
  }
  
  .expense-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .expense-details {
    text-align: left;
    width: 100%;
  }
}
</style>