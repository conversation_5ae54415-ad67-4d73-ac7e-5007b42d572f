<template>
  <div class="settings-view">
    <h2>设置</h2>

    <el-tabs v-model="activeTab" class="settings-tabs">
      <el-tab-pane label="通用设置" name="general">
        <el-form label-width="150px" style="max-width: 600px;">
          <el-form-item label="体重单位">
            <el-radio-group v-model="settings.weightUnit" @change="handleWeightUnitChange">
              <el-radio value="g">克 (g)</el-radio>
              <el-radio value="kg">千克 (kg)</el-radio>
              <el-radio value="lb">磅 (lb)</el-radio>
            </el-radio-group>
            <div style="margin-top: 8px; color: #909399; font-size: 12px;">
              此设置将应用于所有体重相关的显示和输入
            </div>
          </el-form-item>
          
          <el-form-item label="日期格式">
            <el-radio-group v-model="settings.dateFormat">
              <el-radio value="YYYY-MM-DD">YYYY-MM-DD</el-radio>
              <el-radio value="MM/DD/YYYY">MM/DD/YYYY</el-radio>
              <el-radio value="DD/MM/YYYY">DD/MM/YYYY</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="主题">
            <el-select v-model="settings.theme" placeholder="选择主题">
              <el-option label="浅色模式" value="light"></el-option>
              <el-option label="深色模式" value="dark"></el-option>
              <el-option label="跟随系统" value="auto"></el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="通知">
            <el-switch v-model="settings.notificationsEnabled" />
            <span> 开启应用内通知</span>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="账户设置" name="account">
        <el-form label-width="120px" style="max-width: 500px;">
          <el-form-item label="邮箱">
            <el-input :value="userEmail" disabled />
          </el-form-item>
          <el-form-item label="修改密码">
            <el-button type="primary" @click="showChangePasswordDialog = true">修改密码</el-button>
          </el-form-item>
          <!-- 更多账户相关操作 -->
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="首页设置" name="dashboard">
        <DashboardBlockManager />
      </el-tab-pane>
      
      <el-tab-pane label="关于" name="about">
        <p><strong>Hay!Pet - 宠物成长记录</strong></p>
        <p>版本: 1.0.0 (原型阶段)</p>
        <p>一款帮助您记录和管理爱宠日常的应用。</p>
      </el-tab-pane>
    </el-tabs>

    <div style="margin-top: 30px; max-width: 600px;">
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="showChangePasswordDialog" title="修改密码" width="400px">
      <el-form :model="passwordForm" label-width="100px">
        <el-form-item label="当前密码">
          <el-input type="password" v-model="passwordForm.currentPassword" show-password></el-input>
        </el-form-item>
        <el-form-item label="新密码">
          <el-input type="password" v-model="passwordForm.newPassword" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认新密码">
          <el-input type="password" v-model="passwordForm.confirmPassword" show-password></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showChangePasswordDialog = false">取消</el-button>
          <el-button type="primary" @click="handleChangePassword">确认修改</el-button>
        </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, computed, inject, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { globalSettings, saveSettings as saveGlobalSettings, loadSettings, convertWeight } from '@/utils/settings';
import DashboardBlockManager from '@/components/settings/DashboardBlockManager.vue';

const supabase = inject('supabase');
const user = ref(null);
const activeTab = ref('general');

// 使用全局设置
const settings = globalSettings;
const showChangePasswordDialog = ref(false);
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const userEmail = computed(() => user.value?.email || '未登录');

// 处理体重单位变化
const handleWeightUnitChange = (newUnit) => {

  // 自动保存设置
  saveSettings();
};

// 保存设置
const saveSettings = () => {
  const success = saveGlobalSettings();
  if (success) {

    ElMessage.success('设置已保存！');
  } else {
    ElMessage.error('保存设置失败，请重试');
  }
};

const handleChangePassword = async () => {
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    alert('新密码两次输入不一致！');
    return;
  }
  if (!passwordForm.value.newPassword) {
    alert('新密码不能为空！');
    return;
  }
  // 实际项目中，修改密码需要先验证旧密码，然后调用Supabase的API

  alert('修改密码功能正在开发中！ (模拟)');
  // const { error } = await supabase.auth.updateUser({ password: passwordForm.value.newPassword })
  // if (error) {
  //   alert('修改密码失败: ' + error.message)
  // } else {
  //   alert('密码修改成功！')
  //   showChangePasswordDialog.value = false;
  // }
  showChangePasswordDialog.value = false;
  passwordForm.value = { currentPassword: '', newPassword: '', confirmPassword: '' };
};

// 获取当前用户
supabase.auth.getUser().then(({ data }) => {
  user.value = data.user;
});

supabase.auth.onAuthStateChange((_event, session) => {
  user.value = session?.user ?? null;
});

// 组件挂载时加载设置
onMounted(() => {
  loadSettings();
});

</script>

<style scoped>
.settings-view {
  padding: 20px;
}
.settings-tabs {
  margin-top: 20px;
}
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>