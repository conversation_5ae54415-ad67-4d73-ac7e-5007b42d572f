<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced View Toggle 组件测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            margin: 0;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #303133;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #409EFF, #67C23A);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .test-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }

        .test-section h2 {
            color: #303133;
            margin-bottom: 16px;
            border-bottom: 2px solid #E4E7ED;
            padding-bottom: 8px;
        }

        .demo-area {
            background: #F5F7FA;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            display: flex;
            justify-content: center;
        }

        .result {
            text-align: center;
            color: #606266;
            font-size: 14px;
            margin-top: 12px;
        }

        .success-message {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            color: #0369a1;
        }

        /* Enhanced View Toggle 样式 */
        .enhanced-view-toggle {
            position: relative;
            display: flex;
            background: #f5f7fa;
            border-radius: 8px;
            padding: 3px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .enhanced-view-toggle.small {
            padding: 2px;
        }

        .toggle-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
            border-radius: 8px;
        }

        .toggle-fill {
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
            border-radius: 5px;
            z-index: 1;
            box-shadow: 
                0 2px 8px rgba(64, 158, 255, 0.3),
                0 1px 3px rgba(64, 158, 255, 0.4);
            transform-origin: center;
        }

        .enhanced-view-toggle.small .toggle-fill {
            top: 2px;
            left: 2px;
            height: calc(100% - 4px);
        }

        .toggle-glow {
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: radial-gradient(ellipse at center, rgba(64, 158, 255, 0.4) 0%, transparent 70%);
            border-radius: 5px;
            z-index: 0;
            opacity: 0;
            filter: blur(4px);
        }

        .enhanced-view-toggle.small .toggle-glow {
            top: 2px;
            left: 2px;
            height: calc(100% - 4px);
        }

        .enhanced-toggle-btn {
            position: relative;
            z-index: 2;
            background: transparent;
            border: none;
            border-radius: 5px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            white-space: nowrap;
            font-weight: 500;
            flex: 1;
            text-align: center;
            overflow: hidden;
            transition: transform 0.2s ease;
        }

        .enhanced-view-toggle.small .enhanced-toggle-btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .enhanced-toggle-btn:hover {
            transform: translateY(-1px);
        }

        .enhanced-toggle-btn:active {
            transform: translateY(0);
        }

        .btn-content {
            position: relative;
            z-index: 3;
            color: #606266;
            transition: color 0.3s ease;
        }

        .enhanced-toggle-btn.active .btn-content {
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .btn-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            pointer-events: none;
            z-index: 1;
        }

        .enhanced-toggle-btn:hover .btn-content {
            color: #409EFF;
        }

        .enhanced-toggle-btn.active:hover .btn-content {
            color: white;
        }

        @media (max-width: 768px) {
            .enhanced-toggle-btn {
                padding: 6px 12px;
                font-size: 12px;
            }
            
            .enhanced-view-toggle.small .enhanced-toggle-btn {
                padding: 4px 8px;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ Enhanced View Toggle 组件测试</h1>
        
        <div class="test-section">
            <h2>✅ 组件集成成功</h2>
            <div class="success-message">
                <h3>🎉 恭喜！Enhanced View Toggle 组件已成功集成到预设样式系统</h3>
                <ul>
                    <li>✅ 组件文件已创建：<code>src/components/common/EnhancedViewToggle.vue</code></li>
                    <li>✅ 预设样式系统已配置：<code>src/styles/presets/view-toggle.js</code></li>
                    <li>✅ HealthRecordsView 已更新使用新组件</li>
                    <li>✅ 样式演示页面已创建：<code>src/views/StyleDemo/ViewToggleDemo.vue</code></li>
                    <li>✅ 使用文档已创建：<code>docs/ENHANCED_VIEW_TOGGLE_GUIDE.md</code></li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2>基础功能测试</h2>
            <div class="demo-area">
                <div class="enhanced-view-toggle" id="basicToggle">
                    <div class="toggle-background"></div>
                    <div class="toggle-fill" id="basicFill"></div>
                    <div class="toggle-glow" id="basicGlow"></div>
                    <button class="enhanced-toggle-btn active" onclick="testBasic('month', 0, event)">
                        <span class="btn-content">月视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="testBasic('week', 1, event)">
                        <span class="btn-content">周视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="testBasic('year', 2, event)">
                        <span class="btn-content">年视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                </div>
            </div>
            <div class="result" id="basicResult">当前选择：月视图</div>
        </div>

        <div class="test-section">
            <h2>小尺寸版本测试</h2>
            <div class="demo-area">
                <div class="enhanced-view-toggle small" id="smallToggle">
                    <div class="toggle-background"></div>
                    <div class="toggle-fill" id="smallFill"></div>
                    <div class="toggle-glow" id="smallGlow"></div>
                    <button class="enhanced-toggle-btn active" onclick="testSmall('cards', 0, event)">
                        <span class="btn-content">卡片</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="testSmall('timeline', 1, event)">
                        <span class="btn-content">时间线</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="testSmall('table', 2, event)">
                        <span class="btn-content">表格</span>
                        <div class="btn-ripple"></div>
                    </button>
                </div>
            </div>
            <div class="result" id="smallResult">当前选择：卡片</div>
        </div>

        <div class="test-section">
            <h2>使用说明</h2>
            <div class="success-message">
                <h3>📖 如何在项目中使用</h3>
                <ol>
                    <li><strong>导入组件</strong>：<code>import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue'</code></li>
                    <li><strong>在模板中使用</strong>：<code>&lt;EnhancedViewToggle v-model="selectedView" :options="options" /&gt;</code></li>
                    <li><strong>查看演示</strong>：访问 <code>/style_demo/view-toggle</code> 查看完整演示</li>
                    <li><strong>阅读文档</strong>：查看 <code>docs/ENHANCED_VIEW_TOGGLE_GUIDE.md</code> 获取详细使用说明</li>
                </ol>
                
                <h3>🎯 主要特性</h3>
                <ul>
                    <li>🎨 丰富的动画效果（涟漪、填充移动、发光脉冲、按钮弹跳）</li>
                    <li>📱 完全响应式设计，支持移动端</li>
                    <li>⚡ 高性能 GSAP 动画</li>
                    <li>🎛️ 支持多种尺寸（normal、small）</li>
                    <li>🔧 预设样式系统集成</li>
                    <li>♿ 良好的可访问性支持</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let basicValue = 'month';
        let smallValue = 'cards';

        // 涟漪效果
        function createRippleEffect(button, event) {
            const ripple = button.querySelector('.btn-ripple');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;
            
            gsap.set(ripple, {
                width: size,
                height: size,
                x: x,
                y: y,
                scale: 0,
                opacity: 0.6
            });
            
            gsap.to(ripple, {
                scale: 1,
                opacity: 0,
                duration: 0.6,
                ease: "power2.out"
            });
        }

        // 更新填充位置
        function updateFillPosition(containerId, fillId, glowId, activeIndex) {
            const container = document.getElementById(containerId);
            const fill = document.getElementById(fillId);
            const glow = document.getElementById(glowId);
            
            if (!container || !fill || activeIndex < 0) return;
            
            const buttons = container.querySelectorAll('.enhanced-toggle-btn');
            if (buttons.length === 0) return;
            
            let offset = 0;
            for (let i = 0; i < activeIndex; i++) {
                offset += buttons[i].offsetWidth;
            }
            
            const activeButton = buttons[activeIndex];
            const width = activeButton.offsetWidth;
            
            // GSAP 动画：填充背景移动
            gsap.to(fill, {
                x: offset,
                width: width,
                duration: 0.4,
                ease: "power2.out"
            });
            
            // GSAP 动画：发光效果
            if (glow) {
                gsap.to(glow, {
                    x: offset,
                    width: width,
                    duration: 0.4,
                    ease: "power2.out"
                });
                
                // 发光脉冲效果
                gsap.fromTo(glow, 
                    { opacity: 0, scale: 0.8 },
                    { 
                        opacity: 0.3, 
                        scale: 1,
                        duration: 0.3,
                        ease: "power2.out",
                        yoyo: true,
                        repeat: 1
                    }
                );
            }
            
            // 按钮弹跳效果
            gsap.fromTo(activeButton,
                { scale: 0.95 },
                { 
                    scale: 1,
                    duration: 0.3,
                    ease: "back.out(1.7)"
                }
            );
            
            // 文字颜色渐变动画
            buttons.forEach((btn, index) => {
                const content = btn.querySelector('.btn-content');
                btn.classList.toggle('active', index === activeIndex);
                if (index === activeIndex) {
                    gsap.to(content, {
                        color: '#ffffff',
                        duration: 0.3,
                        ease: "power2.out"
                    });
                } else {
                    gsap.to(content, {
                        color: '#606266',
                        duration: 0.3,
                        ease: "power2.out"
                    });
                }
            });
        }

        // 基础测试
        function testBasic(value, index, event) {
            if (value === basicValue) return;
            
            createRippleEffect(event.currentTarget, event);
            basicValue = value;
            
            const labels = { month: '月视图', week: '周视图', year: '年视图' };
            document.getElementById('basicResult').textContent = `当前选择：${labels[value]}`;
            
            updateFillPosition('basicToggle', 'basicFill', 'basicGlow', index);
        }

        // 小尺寸测试
        function testSmall(value, index, event) {
            if (value === smallValue) return;
            
            createRippleEffect(event.currentTarget, event);
            smallValue = value;
            
            const labels = { cards: '卡片', timeline: '时间线', table: '表格' };
            document.getElementById('smallResult').textContent = `当前选择：${labels[value]}`;
            
            updateFillPosition('smallToggle', 'smallFill', 'smallGlow', index);
        }

        // 初始化
        window.addEventListener('load', () => {
            updateFillPosition('basicToggle', 'basicFill', 'basicGlow', 0);
            updateFillPosition('smallToggle', 'smallFill', 'smallGlow', 0);
        });
    </script>
</body>
</html>
