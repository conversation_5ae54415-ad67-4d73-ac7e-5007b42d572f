# 数据库设置说明

## 事件记录系统数据库迁移

### 1. 执行SQL脚本

请在Supabase控制台的SQL编辑器中执行以下脚本：

```sql
-- 执行 setup_event_records.sql 中的所有内容
```

### 2. 数据库结构说明

#### event_records 表（事件记录）
- `id`: UUID主键
- `pet_id`: 宠物ID（外键）
- `date`: 事件日期
- `record_type`: 记录类型（关联record_types表）
- `description`: 描述
- `next_due_date`: 下次到期日期
- `created_at`: 创建时间
- `updated_at`: 更新时间

#### record_types 表（记录类型管理）
- `id`: UUID主键
- `value`: 类型值（唯一）
- `label`: 显示标签
- `color`: 颜色代码
- `is_system`: 是否为系统类型
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 3. 权限设置

脚本已自动配置了行级安全策略（RLS）：

- 用户只能访问自己宠物的事件记录
- 所有用户都可以查看记录类型
- 用户只能修改自定义记录类型（非系统类型）

### 4. 默认数据

脚本会自动插入以下默认记录类型：

- 疫苗接种 (#67C23A)
- 驱虫 (#E6A23C)
- 体检 (#409EFF)
- 疾病就诊 (#F56C6C)
- 用药记录 (#909399)
- 过敏记录 (#FF5722)
- 手术记录 (#9C27B0)
- 其他 (#607D8B)

### 5. 迁移注意事项

如果您之前有 `health_records` 表，脚本会自动将其重命名为 `event_records`，数据不会丢失。

### 6. 验证安装

执行脚本后，您可以通过以下查询验证安装：

```sql
-- 检查表是否创建成功
SELECT table_name FROM information_schema.tables 
WHERE table_name IN ('event_records', 'record_types');

-- 检查默认记录类型是否插入
SELECT * FROM record_types ORDER BY is_system DESC, created_at;

-- 检查RLS策略是否启用
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('event_records', 'record_types');
```
