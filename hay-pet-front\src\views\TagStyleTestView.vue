<template>
  <div class="tag-style-test">
    <div class="test-container">
      <h1>标签样式对比测试</h1>
      
      <section class="test-section">
        <h2>原始设计对比</h2>
        <div class="comparison-row">
          <div class="comparison-item">
            <h3>修复后的样式</h3>
            <div class="tag-group">
              <!-- 全部标签 - 非激活状态 -->
              <StandardTag
                text="全部"
                variant="primary"
                :active="false"
                :background-color="getAllTagBackground(false)"
                :color="getAllTagColor(false)"
                :border-color="getAllTagBorderColor(false)"
                :editable="false"
                :deletable="false"
                :show-actions="false"
                class="all-tag-standard"
              />
              
              <!-- 全部标签 - 激活状态 -->
              <StandardTag
                text="全部"
                variant="primary"
                :active="true"
                :background-color="getAllTagBackground(true)"
                :color="getAllTagColor(true)"
                :border-color="getAllTagBorderColor(true)"
                :editable="false"
                :deletable="false"
                :show-actions="false"
                class="all-tag-standard"
              />
              
              <!-- 其他标签示例 -->
              <StandardTag
                text="疫苗接种"
                :variant="getTagVariant('#67C23A')"
                :active="false"
                :background-color="getTagBackground('#67C23A', false)"
                :color="getTagTextColor('#67C23A', false)"
                :border-color="getTagBorderColor('#67C23A', false)"
                :editable="true"
                :deletable="true"
              />

              <StandardTag
                text="疫苗接种"
                :variant="getTagVariant('#67C23A')"
                :active="true"
                :background-color="getTagBackground('#67C23A', true)"
                :color="getTagTextColor('#67C23A', true)"
                :border-color="getTagBorderColor('#67C23A', true)"
                :editable="true"
                :deletable="true"
              />
            </div>
          </div>
        </div>
      </section>

      <section class="test-section">
        <h2>交互式测试</h2>
        <div class="interactive-test">
          <p>点击标签查看激活状态切换：</p>
          <div class="tag-group">
            <StandardTag
              text="全部"
              variant="primary"
              :active="selectedType === ''"
              :background-color="getAllTagBackground(selectedType === '')"
              :color="getAllTagColor(selectedType === '')"
              :border-color="getAllTagBorderColor(selectedType === '')"
              :editable="false"
              :deletable="false"
              :show-actions="false"
              class="all-tag-standard"
              @click="selectType('')"
            />
            
            <StandardTag
              v-for="type in testTypes"
              :key="type.value"
              :text="type.label"
              :variant="getTagVariant(type.color)"
              :active="selectedType === type.value"
              :background-color="getTagBackground(type.color, selectedType === type.value)"
              :color="getTagTextColor(type.color, selectedType === type.value)"
              :border-color="getTagBorderColor(type.color, selectedType === type.value)"
              :editable="true"
              :deletable="true"
              @click="selectType(type.value)"
            />
          </div>
          
          <div class="current-selection">
            当前选中：{{ selectedType === '' ? '全部' : testTypes.find(t => t.value === selectedType)?.label || '无' }}
          </div>
        </div>
      </section>

      <section class="test-section">
        <h2>颜色规范说明</h2>
        <div class="color-guide">
          <div class="color-item">
            <div class="color-sample all-inactive"></div>
            <div class="color-info">
              <strong>全部标签（非激活）</strong><br>
              背景：linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)<br>
              文字：#495057<br>
              边框：#dee2e6
            </div>
          </div>
          
          <div class="color-item">
            <div class="color-sample all-active"></div>
            <div class="color-info">
              <strong>全部标签（激活）</strong><br>
              背景：linear-gradient(135deg, #409EFF 0%, #67C23A 100%)<br>
              文字：white<br>
              边框：rgba(255, 255, 255, 0.3)
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import StandardTag from '@/components/common/StandardTag.vue'

// 响应式数据
const selectedType = ref('')

// 测试数据
const testTypes = [
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病就诊', color: '#F56C6C' }
]

// 辅助方法（从HealthRecordsView复制）
const adjustBrightness = (color, percent) => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  const factor = percent / 100;
  const newR = Math.round(r + (255 - r) * factor);
  const newG = Math.round(g + (255 - g) * factor);
  const newB = Math.round(b + (255 - b) * factor);

  const toHex = (n) => {
    const hex = n.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
};

const getTagVariant = (color) => {
  const colorMap = {
    '#67C23A': 'success',
    '#E6A23C': 'warning',
    '#409EFF': 'primary',
    '#F56C6C': 'danger',
    '#909399': 'info'
  };
  return colorMap[color] || 'primary';
};

const getTagBackground = (color, isActive) => {
  if (isActive) {
    return `linear-gradient(135deg, ${color} 0%, ${adjustBrightness(color, 20)} 100%)`;
  } else {
    return `linear-gradient(135deg, ${adjustBrightness(color, 80)} 0%, ${adjustBrightness(color, 90)} 100%)`;
  }
};

const getTagTextColor = (color, isActive) => {
  if (isActive) {
    return 'white';
  } else {
    return color;
  }
};

const getTagBorderColor = (color, isActive) => {
  if (isActive) {
    return 'rgba(255, 255, 255, 0.3)';
  } else {
    return adjustBrightness(color, 60);
  }
};

// "全部"标签的特殊样式方法
const getAllTagBackground = (isActive) => {
  if (isActive) {
    return 'linear-gradient(135deg, #409EFF 0%, #67C23A 100%)';
  } else {
    return 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
  }
};

const getAllTagColor = (isActive) => {
  if (isActive) {
    return 'white';
  } else {
    return '#495057';
  }
};

const getAllTagBorderColor = (isActive) => {
  if (isActive) {
    return 'rgba(255, 255, 255, 0.3)';
  } else {
    return '#dee2e6';
  }
};

// 事件处理
const selectType = (type) => {
  selectedType.value = type;
};
</script>

<style scoped>
.tag-style-test {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.test-container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-8);
  font-size: var(--font-size-3xl);
}

.test-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
}

h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

h3 {
  color: var(--color-text-regular);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.comparison-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

.comparison-item {
  padding: var(--spacing-4);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-lg);
  background: var(--color-bg-secondary);
}

.interactive-test p {
  color: var(--color-text-regular);
  margin-bottom: var(--spacing-3);
}

.current-selection {
  padding: var(--spacing-3);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  color: var(--color-text-regular);
  font-weight: var(--font-weight-medium);
  border-left: 4px solid var(--color-primary);
}

.color-guide {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.color-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border: 1px solid var(--color-border-light);
  border-radius: var(--radius-lg);
  background: var(--color-bg-secondary);
}

.color-sample {
  width: 60px;
  height: 40px;
  border-radius: var(--radius-lg);
  flex-shrink: 0;
  border: 2px solid var(--color-border-base);
}

.color-sample.all-inactive {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.color-sample.all-active {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
}

.color-info {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-relaxed);
}

.color-info strong {
  color: var(--color-text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-style-test {
    padding: var(--spacing-4);
  }
  
  .test-section {
    padding: var(--spacing-4);
  }
  
  .color-guide {
    grid-template-columns: 1fr;
  }
  
  .color-item {
    flex-direction: column;
    text-align: center;
  }
}
</style>
