<template>
  <el-card class="statistics-card">
    <template #header>
      <div class="card-header">
        <span>📊 统计信息</span>
        <el-button @click="refreshStats" :icon="Refresh" link>刷新</el-button>
      </div>
    </template>
    
    <!-- 基础统计 -->
    <div class="stats-grid">
      <div class="stat-item">
        <div class="stat-icon">🐾</div>
        <div class="stat-content">
          <div class="stat-number">{{ totalPets }}</div>
          <div class="stat-label">总宠物数</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon">📈</div>
        <div class="stat-content">
          <div class="stat-number">{{ totalWeightRecords }}</div>
          <div class="stat-label">体重记录</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon">🎂</div>
        <div class="stat-content">
          <div class="stat-number">{{ averageAge }}</div>
          <div class="stat-label">平均年龄</div>
        </div>
      </div>
      
      <div class="stat-item">
        <div class="stat-icon">📅</div>
        <div class="stat-content">
          <div class="stat-number">{{ daysSinceFirst }}</div>
          <div class="stat-label">使用天数</div>
        </div>
      </div>
    </div>
    
    <!-- 详细统计 -->
    <el-divider>详细统计</el-divider>
    
    <el-row :gutter="20">
      <!-- 性别分布 -->
      <el-col :span="12">
        <div class="chart-container">
          <h4>性别分布</h4>
          <div class="gender-stats">
            <div class="gender-item" v-for="item in genderStats" :key="item.gender">
              <el-tag :type="getGenderTagType(item.gender)" size="small">
                {{ getGenderText(item.gender) }}
              </el-tag>
              <span class="gender-count">{{ item.count }}只</span>
              <div class="gender-bar">
                <div 
                  class="gender-bar-fill" 
                  :style="{ width: item.percentage + '%', backgroundColor: getGenderColor(item.gender) }"
                ></div>
              </div>
              <span class="gender-percentage">{{ item.percentage }}%</span>
            </div>
          </div>
        </div>
      </el-col>
      
      <!-- 品种分布 -->
      <el-col :span="12">
        <div class="chart-container">
          <h4>品种分布</h4>
          <div class="species-stats">
            <div class="species-item" v-for="item in topSpecies" :key="item.species">
              <span class="species-name">{{ item.species }}</span>
              <span class="species-count">{{ item.count }}只</span>
              <div class="species-bar">
                <div 
                  class="species-bar-fill" 
                  :style="{ width: item.percentage + '%' }"
                ></div>
              </div>
              <span class="species-percentage">{{ item.percentage }}%</span>
            </div>
            <div v-if="otherSpeciesCount > 0" class="species-item">
              <span class="species-name">其他</span>
              <span class="species-count">{{ otherSpeciesCount }}只</span>
              <div class="species-bar">
                <div 
                  class="species-bar-fill" 
                  :style="{ width: otherSpeciesPercentage + '%' }"
                ></div>
              </div>
              <span class="species-percentage">{{ otherSpeciesPercentage }}%</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 年龄分布 -->
    <el-divider>年龄分布</el-divider>
    <div class="age-distribution">
      <div class="age-group" v-for="group in ageGroups" :key="group.label">
        <div class="age-label">{{ group.label }}</div>
        <div class="age-bar">
          <div 
            class="age-bar-fill" 
            :style="{ width: group.percentage + '%' }"
          ></div>
        </div>
        <div class="age-info">
          <span class="age-count">{{ group.count }}只</span>
          <span class="age-percentage">({{ group.percentage }}%)</span>
        </div>
      </div>
    </div>
    
    <!-- 最近活动 -->
    <el-divider>最近活动</el-divider>
    <div class="recent-activity">
      <div class="activity-item">
        <el-icon><Calendar /></el-icon>
        <span>最近添加的宠物：{{ latestPet?.name || '暂无' }}</span>
        <span v-if="latestPet" class="activity-time">
          {{ formatRelativeTime(latestPet.created_at) }}
        </span>
      </div>
      <div class="activity-item">
        <el-icon><TrendCharts /></el-icon>
        <span>本月新增宠物：{{ thisMonthPets }}只</span>
      </div>
      <div class="activity-item">
        <el-icon><DataAnalysis /></el-icon>
        <span>本月体重记录：{{ thisMonthWeights }}条</span>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, inject } from 'vue'
import { Refresh, Calendar, TrendCharts, DataAnalysis } from '@element-plus/icons-vue'

const props = defineProps({
  pets: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['refresh'])

const supabase = inject('supabase')
const totalWeightRecords = ref(0)

// 基础统计
const totalPets = computed(() => props.pets.length)

const averageAge = computed(() => {
  if (props.pets.length === 0) return '0岁'
  const totalAge = props.pets.reduce((sum, pet) => sum + (pet.age || 0), 0)
  const avg = (totalAge / props.pets.length).toFixed(1)
  return `${avg}岁`
})

const daysSinceFirst = computed(() => {
  if (props.pets.length === 0) return 0
  const firstPet = props.pets.reduce((earliest, pet) => {
    return new Date(pet.created_at) < new Date(earliest.created_at) ? pet : earliest
  })
  const days = Math.floor((new Date() - new Date(firstPet.created_at)) / (1000 * 60 * 60 * 24))
  return days
})

// 性别统计
const genderStats = computed(() => {
  const stats = { male: 0, female: 0, unknown: 0 }
  props.pets.forEach(pet => {
    stats[pet.gender] = (stats[pet.gender] || 0) + 1
  })
  
  return Object.entries(stats)
    .map(([gender, count]) => ({
      gender,
      count,
      percentage: totalPets.value > 0 ? Math.round((count / totalPets.value) * 100) : 0
    }))
    .filter(item => item.count > 0)
    .sort((a, b) => b.count - a.count)
})

// 品种统计
const speciesStats = computed(() => {
  const stats = {}
  props.pets.forEach(pet => {
    stats[pet.species] = (stats[pet.species] || 0) + 1
  })
  
  return Object.entries(stats)
    .map(([species, count]) => ({
      species,
      count,
      percentage: totalPets.value > 0 ? Math.round((count / totalPets.value) * 100) : 0
    }))
    .sort((a, b) => b.count - a.count)
})

const topSpecies = computed(() => speciesStats.value.slice(0, 5))
const otherSpeciesCount = computed(() => {
  return speciesStats.value.slice(5).reduce((sum, item) => sum + item.count, 0)
})
const otherSpeciesPercentage = computed(() => {
  return totalPets.value > 0 ? Math.round((otherSpeciesCount.value / totalPets.value) * 100) : 0
})

// 年龄分组
const ageGroups = computed(() => {
  const groups = {
    '幼年 (0-1岁)': 0,
    '青年 (2-5岁)': 0,
    '成年 (6-10岁)': 0,
    '老年 (11岁以上)': 0
  }
  
  props.pets.forEach(pet => {
    const age = pet.age || 0
    if (age <= 1) groups['幼年 (0-1岁)']++
    else if (age <= 5) groups['青年 (2-5岁)']++
    else if (age <= 10) groups['成年 (6-10岁)']++
    else groups['老年 (11岁以上)']++
  })
  
  return Object.entries(groups).map(([label, count]) => ({
    label,
    count,
    percentage: totalPets.value > 0 ? Math.round((count / totalPets.value) * 100) : 0
  }))
})

// 最新宠物
const latestPet = computed(() => {
  if (props.pets.length === 0) return null
  return props.pets.reduce((latest, pet) => {
    return new Date(pet.created_at) > new Date(latest.created_at) ? pet : latest
  })
})

// 本月统计
const thisMonthPets = computed(() => {
  const now = new Date()
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  return props.pets.filter(pet => new Date(pet.created_at) >= thisMonth).length
})

const thisMonthWeights = ref(0)

// 工具函数
const getGenderTagType = (gender) => {
  switch (gender) {
    case 'male': return 'primary'
    case 'female': return 'success'
    default: return 'info'
  }
}

const getGenderText = (gender) => {
  switch (gender) {
    case 'male': return '公'
    case 'female': return '母'
    default: return '未知'
  }
}

const getGenderColor = (gender) => {
  switch (gender) {
    case 'male': return '#409eff'
    case 'female': return '#67c23a'
    default: return '#909399'
  }
}

const formatRelativeTime = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now - date
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '昨天'
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  return `${Math.floor(diffDays / 30)}个月前`
}

// 加载体重记录统计
const loadWeightStats = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return
    
    // 获取总体重记录数
    const { data: weights, error } = await supabase
      .from('weight_records')
      .select('id, record_date')
      .in('pet_id', props.pets.map(pet => pet.id))
    
    if (!error && weights) {
      totalWeightRecords.value = weights.length
      
      // 计算本月体重记录
      const now = new Date()
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      thisMonthWeights.value = weights.filter(w => 
        new Date(w.record_date) >= thisMonth
      ).length
    }
  } catch (error) {
    console.error('加载体重统计失败:', error)
  }
}

// 刷新统计
const refreshStats = () => {
  loadWeightStats()
  emit('refresh')
}

// 初始化
loadWeightStats()
</script>

<style scoped>
.statistics-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 24px;
  margin-right: 15px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  line-height: 1;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.chart-container {
  padding: 15px;
  background: #fafafa;
  border-radius: 8px;
}

.chart-container h4 {
  margin: 0 0 15px 0;
  color: #409eff;
  font-size: 14px;
}

.gender-stats, .species-stats {
  space-y: 10px;
}

.gender-item, .species-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.gender-count, .species-count {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

.gender-bar, .species-bar {
  flex: 1;
  height: 8px;
  background: #eee;
  border-radius: 4px;
  overflow: hidden;
}

.gender-bar-fill, .species-bar-fill {
  height: 100%;
  background: #409eff;
  transition: width 0.3s;
}

.gender-percentage, .species-percentage {
  font-size: 12px;
  color: #666;
  min-width: 35px;
  text-align: right;
}

.species-name {
  min-width: 60px;
  font-size: 12px;
}

.age-distribution {
  space-y: 15px;
}

.age-group {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.age-label {
  min-width: 100px;
  font-size: 14px;
  color: #333;
}

.age-bar {
  flex: 1;
  height: 12px;
  background: #eee;
  border-radius: 6px;
  overflow: hidden;
}

.age-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #67c23a);
  transition: width 0.3s;
}

.age-info {
  display: flex;
  gap: 5px;
  min-width: 80px;
}

.age-count {
  font-weight: bold;
  color: #409eff;
}

.age-percentage {
  color: #666;
  font-size: 12px;
}

.recent-activity {
  space-y: 10px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 10px;
}

.activity-time {
  margin-left: auto;
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .age-group {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
  
  .age-label {
    min-width: auto;
  }
}
</style>