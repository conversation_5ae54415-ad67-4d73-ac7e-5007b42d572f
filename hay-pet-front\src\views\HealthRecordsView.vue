<template>
  <div class="health-records-view">
    <div v-if="!currentPetId" class="no-pet-selected">
      <el-empty description="请先在左侧选择一个宠物以查看事件记录" />
    </div>
    <div v-else>
      <!-- 页面标题和操作按钮 -->
      <div class="page-header">
        <div class="header-content">
          <h2 class="page-title">事件记录</h2>
          <div class="header-actions">
            <el-button
              class="add-record-btn"
              type="primary"
              size="large"
              @click="openAddDialog"
            >
              <el-icon class="add-icon"><Plus /></el-icon>
              <span class="btn-text">添加事件记录</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 日历面板 - 使用优化后的 CalendarViewPreset -->
      <div class="calendar-section">
        <CalendarViewPreset
          title="事件记录日历"
          :records="eventRecords"
          :record-types="recordTypes"
          :color-mapping="colorMapping"
          :label-mapping="labelMapping"
          :show-stats="false"
          record-id-field="id"
          record-type-field="record_type"
          record-date-field="originalDate"
          record-desc-field="description"
          @date-click="handleCalendarDateClick"
          @date-dblclick="handleCalendarDateDoubleClick"
          @view-change="handleCalendarViewChange"
          @date-change="handleCalendarDateChange"
          @record-click="handleRecordClick"
          @record-edit="handleRecordEdit"
        />
      </div>

      <!-- 日历导航组件 -->
      <div class="calendar-navigation-section">
        <el-card shadow="hover">
          <div class="navigation-content">
            <!-- 导航按钮组 -->
            <div class="navigation-buttons">
              <el-button
                @click="previousPeriod"
                size="small"
                circle
                class="nav-btn"
                :icon="ArrowLeft"
                :title="getNavigationTitle('prev')"
              ></el-button>
              <el-button
                @click="goToToday"
                size="small"
                class="today-btn"
                title="今天"
              >
                <el-icon><Calendar /></el-icon>
                <span class="today-text">今天</span>
              </el-button>
              <el-button
                @click="nextPeriod"
                size="small"
                circle
                class="nav-btn"
                :icon="ArrowRight"
                :title="getNavigationTitle('next')"
              ></el-button>
            </div>

            <!-- 日期选择器组 -->
            <div class="date-selectors">
              <!-- 年份选择器 -->
              <el-select
                v-model="currentYear"
                @change="handleYearChange"
                size="small"
                class="year-selector"
                placeholder="年份"
              >
                <el-option
                  v-for="year in yearOptions"
                  :key="year"
                  :label="`${year}年`"
                  :value="year"
                />
              </el-select>

              <!-- 月份选择器 -->
              <el-select
                v-model="currentMonth"
                @change="handleMonthChange"
                size="small"
                class="month-selector"
                placeholder="月份"
              >
                <el-option
                  v-for="(month, index) in monthOptions"
                  :key="index"
                  :label="month"
                  :value="index"
                />
              </el-select>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 统计卡片面板 -->
      <div v-if="eventRecords.length > 0" class="stats-section">
        <div class="stats-grid">
          <el-card
            v-for="stat in statsItems"
            :key="stat.key"
            class="stat-card"
            :class="stat.type"
            shadow="hover"
          >
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon>
                  <component :is="stat.icon" />
                </el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">{{ stat.label }}</div>
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-date">{{ stat.date }}</div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 分类筛选和视图切换 -->
      <div class="filter-section">
        <el-card shadow="hover">
          <div class="filter-content">
            <div class="filter-left">
              <div class="filter-group">
                <div class="filter-header">
                  <div class="filter-label-section">
                    <span class="filter-label">记录类型：</span>
                    <span v-if="selectedRecordTypes.length > 0" class="selected-count">
                      已选择 {{ selectedRecordTypes.length }} 个类型
                    </span>
                  </div>
                  <EnhancedViewToggle
                    v-model="viewMode"
                    :options="viewModeOptions"
                    size="small"
                    @change="handleViewModeChange"
                  />
                </div>
                <div class="record-types-container">
                  <!-- 全部标签（不可拖拽） -->
                  <StandardTag
                    text="全部"
                    variant="primary"
                    :active="selectedRecordTypes.length === 0"
                    :background-color="getAllTagBackground(selectedRecordTypes.length === 0)"
                    :color="getAllTagColor(selectedRecordTypes.length === 0)"
                    :border-color="getAllTagBorderColor(selectedRecordTypes.length === 0)"
                    :editable="false"
                    :deletable="false"
                    :draggable="false"
                    :show-actions="false"
                    class="all-tag-standard"
                    @click="handleTypeFilter('')"
                  />

                  <!-- 可拖拽的记录类型标签 -->
                  <draggable
                    v-model="recordTypes"
                    v-bind="dragOptions"
                    @start="onDragStart"
                    @end="onDragEnd"
                    item-key="id"
                    :key="recordTypes.length"
                    class="draggable-container"
                  >
                    <template #item="{ element: type }">
                      <StandardTag
                        :text="type.label"
                        :variant="getTagVariant(type.color)"
                        :active="selectedRecordTypes.includes(type.value)"
                        :editable="true"
                        :deletable="true"
                        :draggable="true"
                        :color="getTagTextColor(type.color, selectedRecordTypes.includes(type.value))"
                        :background-color="getTagBackground(type.color, selectedRecordTypes.includes(type.value))"
                        :border-color="getTagBorderColor(type.color, selectedRecordTypes.includes(type.value))"
                        :class="{
                          'is-dragging': isDragging,
                          'multi-selected': selectedRecordTypes.includes(type.value)
                        }"
                        @click="handleTypeFilter(type.value)"
                        @edit="editRecordType(type)"
                        @delete="deleteRecordType(type.value)"
                        @contextmenu.prevent="showTypeContextMenu($event, type)"
                      />
                    </template>
                  </draggable>

                  <!-- 添加按钮（不可拖拽） -->
                  <button
                    @click="showAddTypeDialog = true"
                    title="添加新类型"
                    class="add-new-button"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="40px"
                      height="40px"
                      viewBox="0 0 24 24"
                      class="add-icon-svg"
                    >
                      <path
                        d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                        stroke-width="1.5"
                      ></path>
                      <path d="M8 12H16" stroke-width="1.5"></path>
                      <path d="M12 16V8" stroke-width="1.5"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

          </div>
        </el-card>
      </div>

      <!-- 记录展示区域 -->
      <div class="records-section">


        <!-- 视图切换动画容器 -->
        <transition name="view-fade" mode="out-in">
          <!-- 卡片视图 -->
          <div v-if="viewMode === 'cards'" key="cards" class="cards-view">
            <div v-if="filteredRecords.length > 0" class="records-grid">
              <el-card
                v-for="record in filteredRecords"
                :key="record.id"
                class="record-card"
                :class="{
                  'selected': selectedRecords.includes(record),
                  'is-completed': record.is_completed
                }"
                :style="{ borderColor: record.is_completed ? '#d1d5db' : getRecordTypeColor(record.record_type) }"
                :data-record-id="record.id"
                @dblclick="editRecord(record)"
              >
                <div class="record-content">
                  <div class="record-header">
                    <div class="record-type-badge">
                      <el-tag :type="getRecordTypeTag(record.record_type)" size="large">
                        {{ getRecordTypeLabel(record.record_type) }}
                      </el-tag>
                    </div>
                    <div class="record-date">
                      <el-icon><Calendar /></el-icon>
                      {{ formatDate(record.date) }}
                    </div>
                  </div>
                  <div class="record-body">
                    <div class="record-description">
                      {{ record.description || '无描述' }}
                    </div>
                    <div v-if="record.next_due_date" class="record-reminder">
                      <el-icon><Bell /></el-icon>
                      下次提醒：{{ formatDate(record.next_due_date) }}
                    </div>
                  </div>
                  <div class="record-actions">
                    <div
                      class="completion-button"
                      :class="{ 'is-completed': record.is_completed }"
                      @click.stop="toggleRecordCompletion(record)"
                      :title="record.is_completed ? '标记为未完成' : '标记为已完成'"
                    >
                      <el-icon v-if="record.is_completed" class="check-icon">
                        <Check />
                      </el-icon>
                    </div>
                    <div
                      class="delete-button"
                      @click.stop="deleteRecord(record.id)"
                      :title="'删除记录'"
                    >
                      <el-icon><Delete /></el-icon>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
            <el-empty v-else description="暂无事件记录，快去添加吧！">
              <el-button type="primary" @click="openAddDialog">
                <el-icon><Plus /></el-icon>
                添加第一条记录
              </el-button>
            </el-empty>
          </div>

          <!-- 时间线视图 -->
          <div v-else-if="viewMode === 'timeline'" key="timeline" class="timeline-view">
            <div class="simple-timeline">
              <div v-if="filteredRecords.length > 0" class="timeline-container">
                <div
                  v-for="(group, date) in groupedRecords"
                  :key="date"
                  class="timeline-group"
                >
                  <!-- 日期分组头部 -->
                  <div class="group-header">
                    <div class="date-label">
                      <span class="date-text">{{ formatGroupDate(date) }}</span>
                      <span class="date-sub">{{ formatGroupDay(date) }}</span>
                    </div>
                  </div>

                  <!-- 该日期的记录列表 -->
                  <div class="group-items">
                    <div
                      v-for="(record, index) in group"
                      :key="record.id"
                      class="timeline-item"
                      :class="[
                        {
                          'has-reminder': record.next_due_date,
                          'is-completed': record.is_completed
                        }
                      ]"
                      :style="{ borderColor: record.is_completed ? '#d1d5db' : getRecordTypeColor(record.record_type) }"
                      @dblclick="editRecord(record)"
                    >
                      <!-- 左侧完成状态按钮 -->
                      <div class="item-completion-left">
                        <div
                          class="completion-button"
                          :class="{ 'is-completed': record.is_completed }"
                          @click.stop="toggleRecordCompletion(record)"
                          :title="record.is_completed ? '标记为未完成' : '标记为已完成'"
                        >
                          <el-icon v-if="record.is_completed" class="check-icon">
                            <Check />
                          </el-icon>
                        </div>
                      </div>

                      <!-- 主要内容区域 -->
                      <div class="item-content">
                        <div class="content-header">
                          <div class="item-category">
                            <span
                              class="category-dot"
                              :style="{ backgroundColor: getRecordTypeColor(record.record_type) }"
                            ></span>
                            <span class="category-name">{{ getRecordTypeLabel(record.record_type) }}</span>
                          </div>
                          <div class="item-time">{{ formatRecordTime(record.date) }}</div>
                        </div>

                        <div v-if="record.description" class="item-description">
                          {{ record.description }}
                        </div>
                      </div>

                      <!-- 右侧删除按钮 -->
                      <div class="item-actions-right">
                        <el-button
                          type="danger"
                          text
                          size="small"
                          @click.stop="deleteRecord(record.id)"
                          class="delete-button"
                          :title="'删除记录'"
                        >
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 简化空状态 -->
              <div v-else class="simple-empty">
                <div class="empty-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="empty-text">
                  <h3>暂无健康记录</h3>
                  <p>开始记录您宠物的健康历程</p>
                </div>
                <el-button type="primary" @click="openAddDialog">
                  <el-icon><Plus /></el-icon>
                  添加记录
                </el-button>
              </div>
            </div>
          </div>

          <!-- 表格视图 -->
          <div v-else-if="viewMode === 'table'" key="table" class="table-view">
            <el-card shadow="hover">
              <template #header>
                <div class="table-header">
                  <span class="table-title">记录列表</span>
                  <div v-if="selectedRecords.length > 0" class="table-batch-actions">
                    <el-button
                      type="success"
                      size="small"
                      @click="batchCompleteRecords"
                    >
                      <el-icon><Check /></el-icon>
                      完成选中 ({{ selectedRecords.length }})
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="batchDeleteRecords"
                    >
                      <el-icon><Delete /></el-icon>
                      删除选中 ({{ selectedRecords.length }})
                    </el-button>
                  </div>
                </div>
              </template>
              <el-table
                v-if="filteredRecords.length > 0"
                :data="filteredRecords"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                :row-class-name="tableRowClassName"
                @row-dblclick="editRecord"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="日期" width="160" align="center">
                  <template #default="{ row }">
                    <div class="date-cell">
                      <el-icon class="date-icon"><Calendar /></el-icon>
                      <span :title="formatFullDateTime(row.date)">{{ formatDate(row.date) }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="类型" width="140" align="center">
                  <template #default="{ row }">
                    <el-tag
                      :type="getRecordTypeTag(row.record_type)"
                      size="large"
                      effect="light"
                    >
                      {{ getRecordTypeLabel(row.record_type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="描述" min-width="200">
                  <template #default="{ row }">
                    <span class="description-text" :title="row.description">
                      {{ row.description || '无描述' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="下次提醒" width="160" align="center">
                  <template #default="{ row }">
                    <span v-if="row.next_due_date" class="reminder-text">
                      <el-icon><Bell /></el-icon>
                      {{ formatDate(row.next_due_date) }}
                    </span>
                    <span v-else class="no-reminder">-</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120" align="center">
                  <template #default="{ row }">
                    <div class="action-buttons">
                      <div
                        class="completion-button"
                        :class="{ 'is-completed': row.is_completed }"
                        @click.stop="toggleRecordCompletion(row)"
                        :title="row.is_completed ? '标记为未完成' : '标记为已完成'"
                      >
                        <el-icon v-if="row.is_completed" class="check-icon">
                          <Check />
                        </el-icon>
                      </div>
                      <div
                        class="delete-button"
                        @click.stop="deleteRecord(row.id)"
                        :title="'删除记录'"
                      >
                        <el-icon><Delete /></el-icon>
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-empty v-else description="暂无事件记录，快去添加吧！">
                <el-button type="primary" @click="openAddDialog">
                  <el-icon><Plus /></el-icon>
                  添加第一条记录
                </el-button>
              </el-empty>
            </el-card>
          </div>
        </transition>
      </div>

      <!-- 添加/编辑记录对话框 -->
      <el-dialog v-model="showAddDialog" :title="isEditMode ? '编辑事件记录' : '添加事件记录'" width="500px">
        <el-form :model="currentRecord" label-width="100px">
          <el-form-item label="日期" required>
            <el-date-picker 
              v-model="currentRecord.date" 
              type="date" 
              placeholder="选择记录日期" 
              style="width: 100%;"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="记录类型" required>
            <el-select v-model="currentRecord.record_type" placeholder="选择类型" style="width: 100%;">
              <el-option
                v-for="item in recordTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="描述/备注">
            <el-input type="textarea" :rows="3" v-model="currentRecord.notes" placeholder="请输入详细描述或备注信息"></el-input>
          </el-form-item>
          <el-form-item label="下次提醒日期">
            <el-date-picker 
              v-model="currentRecord.next_due_date" 
              type="date" 
              placeholder="选择下次提醒日期 (可选)" 
              style="width: 100%;"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showAddDialog = false">取消</el-button>
            <el-button type="primary" @click="saveRecord">保存</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加/编辑记录类型对话框 -->
      <TypeManagementDialog
        v-model="showAddTypeDialog"
        :is-edit-mode="isEditTypeMode"
        type-label="记录类型"
        :data="currentRecordType"
        :predefined-colors="predefinedColors"
        @save="handleRecordTypeSave"
        @cancel="handleRecordTypeCancel"
        @closed="resetTypeDialog"
      />

      <!-- 右键菜单 -->
      <div
        v-if="showContextMenu"
        class="context-menu"
        :style="contextMenuStyle"
        @click.stop
      >
        <div class="context-menu-item" @click="editRecordType(contextMenuType)">
          <el-icon><Edit /></el-icon>
          编辑
        </div>
        <div class="context-menu-item danger" @click="deleteRecordType(contextMenuType.value)">
          <el-icon><Delete /></el-icon>
          删除
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch, computed, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Edit, Calendar, Bell, Clock, Document, ArrowLeft, ArrowRight, Close, Check, TrendCharts, Monitor, Warning, Operation, Star, CircleCheck } from '@element-plus/icons-vue';
import draggable from 'vuedraggable';
import StandardTag from '@/components/common/StandardTag.vue';
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue';
import TypeManagementDialog from '@/components/common/TypeManagementDialog.vue';
import CalendarViewPreset from '@/components/presets/CalendarViewPreset.vue';
import { gsap } from 'gsap';
import { useMotion } from '@vueuse/motion';

const supabase = inject('supabase');
const eventRecords = ref([]);
const selectedRecords = ref([]);
const showAddDialog = ref(false);
const isEditMode = ref(false);

// 新增的响应式数据
const selectedDate = ref(new Date());
const calendarView = ref('month');
const viewMode = ref('cards'); // cards, timeline, table
const selectedRecordTypes = ref([]); // 筛选的记录类型（多选）

// 日历导航相关
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const calendarRef = ref(null);

// 悬浮提示相关
const showTooltip = ref(false);
const tooltipDate = ref('');
const tooltipRecords = ref([]);
const tooltipStyle = ref({});
const tooltipTimer = ref(null);

// 日期筛选相关
const selectedDateForFilter = ref('');
const isDateFiltered = ref(false);

// 周视图相关
const currentWeekStart = ref(new Date());

// 记录类型管理相关
const showAddTypeDialog = ref(false);
const isEditTypeMode = ref(false);
const showContextMenu = ref(false);
const contextMenuStyle = ref({});
const contextMenuType = ref(null);
const currentRecordType = ref({
  value: '',
  label: '',
  color: '#409EFF'
});

// 拖拽排序相关
const isDragging = ref(false);
const dragOptions = {
  animation: 300,
  group: 'record-types',
  disabled: false,
  ghostClass: 'ghost'
};

// 预定义颜色
const predefinedColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
  '#909399', '#9C27B0', '#FF9800', '#4CAF50',
  '#2196F3', '#FF5722', '#795548', '#607D8B'
];

// 颜色选择相关 - 已简化，使用Element Plus内置颜色选择器
const currentRecord = ref({
  id: null,
  pet_id: null,
  date: '',
  record_type: '',
  description: '',
  notes: '', // 对应数据库的 description
  next_due_date: null
});

const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病就诊', color: '#F56C6C' },
  { value: 'medication', label: '用药记录', color: '#909399' },
  { value: 'allergy', label: '过敏记录', color: '#FF5722' },
  { value: 'surgery', label: '手术记录', color: '#9C27B0' },
  { value: 'other', label: '其他', color: '#607D8B' },
]);

const currentPetId = ref(localStorage.getItem('currentPetId'));

// 视图切换选项
const calendarViewOptions = [
  { value: 'month', label: '月视图' },
  { value: 'week', label: '周视图' },
  { value: 'year', label: '年视图' }
];

const viewModeOptions = [
  { value: 'cards', label: '卡片' },
  { value: 'timeline', label: '时间线' },
  { value: 'table', label: '表格' }
];

// 为 CalendarViewPreset 组件准备的映射
const colorMapping = computed(() => {
  const mapping = {};
  recordTypes.value.forEach(type => {
    mapping[type.value] = type.color;
  });
  return mapping;
});

const labelMapping = computed(() => {
  const mapping = {};
  recordTypes.value.forEach(type => {
    mapping[type.value] = type.label;
  });
  return mapping;
});

// 年份选项
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear();
  const years = [];
  // 扩大年份范围：从1990年到未来20年
  for (let i = 1990; i <= currentYear + 20; i++) {
    years.push(i);
  }
  return years;
});

// 月份选项
const monthOptions = computed(() => {
  return [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ];
});

// 获取导航按钮标题
const getNavigationTitle = (direction) => {
  const viewTitles = {
    month: direction === 'prev' ? '上一月' : '下一月',
    week: direction === 'prev' ? '上一周' : '下一周',
    year: direction === 'prev' ? '上一年' : '下一年'
  };
  return viewTitles[calendarView.value] || '';
};

const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString(); // 使用本地化日期格式
};

// 格式化完整日期时间（用于悬浮显示）
const formatFullDateTime = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString(); // 显示完整的日期和时间
};

// 格式化时间线日期显示
const formatTimelineDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const recordDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const diffTime = today.getTime() - recordDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays < 7) {
    return `${diffDays}天前 ` + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    }) + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }
};

// 获取记录类型标签样式
const getRecordTypeTag = (type) => {
  const tagMap = {
    'vaccination': 'success',
    'deworming': 'warning', 
    'checkup': 'info',
    'medication': 'info',
    'allergy': 'danger',
    'surgery': 'danger',
    'other': 'info'
  };
  return tagMap[type] || 'info';
};

// 获取记录类型标签文本
const getRecordTypeLabel = (type) => {
  const record = recordTypes.value.find(rt => rt.value === type);
  return record ? record.label : type;
};

// 调整颜色亮度的辅助函数
const adjustBrightness = (color, percent) => {
  // 将十六进制颜色转换为RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // 调整亮度
  const factor = percent / 100;
  const newR = Math.round(r + (255 - r) * factor);
  const newG = Math.round(g + (255 - g) * factor);
  const newB = Math.round(b + (255 - b) * factor);

  // 转换回十六进制
  const toHex = (n) => {
    const hex = n.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
};

// StandardTag 组件辅助方法
// 根据颜色获取标签变体
const getTagVariant = (color) => {
  const colorMap = {
    '#67C23A': 'success',
    '#E6A23C': 'warning',
    '#409EFF': 'primary',
    '#F56C6C': 'danger',
    '#909399': 'info'
  };
  return colorMap[color] || 'primary';
};

// 获取标签背景色
const getTagBackground = (color, isActive) => {
  if (isActive) {
    return `linear-gradient(135deg, ${color} 0%, ${adjustBrightness(color, 20)} 100%)`;
  } else {
    return `linear-gradient(135deg, ${adjustBrightness(color, 80)} 0%, ${adjustBrightness(color, 90)} 100%)`;
  }
};

// 获取标签文字颜色
const getTagTextColor = (color, isActive) => {
  if (isActive) {
    return 'white';
  } else {
    return color;
  }
};

// 获取标签边框色
const getTagBorderColor = (color, isActive) => {
  if (isActive) {
    return 'rgba(255, 255, 255, 0.3)';
  } else {
    return adjustBrightness(color, 60);
  }
};

// "全部"标签的特殊样式方法
const getAllTagBackground = (isActive) => {
  if (isActive) {
    // 激活状态：蓝绿渐变（与原设计一致）
    return 'linear-gradient(135deg, #409EFF 0%, #67C23A 100%)';
  } else {
    // 非激活状态：浅灰色渐变
    return 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
  }
};

const getAllTagColor = (isActive) => {
  if (isActive) {
    return 'white';
  } else {
    return '#495057';
  }
};

const getAllTagBorderColor = (isActive) => {
  if (isActive) {
    return 'rgba(255, 255, 255, 0.3)';
  } else {
    return '#dee2e6';
  }
};

// 新增方法
// 获取指定日期的记录
const getRecordsForDate = (dateStr) => {
  const targetDate = new Date(dateStr);
  targetDate.setHours(0, 0, 0, 0);

  return eventRecords.value.filter(record => {
    const recordDate = new Date(record.date || record.created_at);
    recordDate.setHours(0, 0, 0, 0);
    return recordDate.getTime() === targetDate.getTime();
  });
};

// 获取记录类型对应的CSS类
const getRecordTypeClass = (type) => {
  const classMap = {
    'vaccination': 'vaccination',
    'deworming': 'deworming',
    'checkup': 'checkup',
    'illness': 'illness',
    'medication': 'medication',
    'allergy': 'allergy',
    'surgery': 'surgery',
    'other': 'other'
  };
  return classMap[type] || 'other';
};

// 获取时间线类型
const getTimelineType = (recordType) => {
  const typeMap = {
    'vaccination': 'success',
    'deworming': 'warning',
    'checkup': 'primary',
    'illness': 'danger',
    'medication': 'info',
    'allergy': 'danger',
    'surgery': 'danger',
    'other': 'info'
  };
  return typeMap[recordType] || 'info';
};

// 获取时间线点的样式类
const getTimelineDotClass = (recordType) => {
  const classMap = {
    'vaccination': 'dot-success',
    'deworming': 'dot-warning',
    'checkup': 'dot-primary',
    'illness': 'dot-danger',
    'medication': 'dot-info',
    'allergy': 'dot-danger',
    'surgery': 'dot-danger',
    'other': 'dot-info'
  };
  return classMap[recordType] || 'dot-info';
};

// 获取记录类型图标
const getRecordIcon = (recordType) => {
  const iconMap = {
    'vaccination': 'CircleCheck',
    'deworming': 'Star',
    'checkup': 'Monitor',
    'illness': 'Warning',
    'medication': 'Plus',
    'allergy': 'Warning',
    'surgery': 'Operation',
    'other': 'Document'
  };
  return iconMap[recordType] || 'Document';
};

// 获取记录类型颜色
const getRecordTypeColor = (recordType) => {
  const record = recordTypes.value.find(rt => rt.value === recordType);
  return record ? record.color : '#909399';
};

// 获取标记样式类
const getMarkerClass = (recordType) => {
  const classMap = {
    'vaccination': 'marker-success',
    'deworming': 'marker-warning',
    'checkup': 'marker-primary',
    'illness': 'marker-danger',
    'medication': 'marker-info',
    'allergy': 'marker-danger',
    'surgery': 'marker-danger',
    'other': 'marker-info'
  };
  return classMap[recordType] || 'marker-info';
};

// 按日期分组记录
const groupedRecords = computed(() => {
  const groups = {};
  sortedRecords.value.forEach(record => {
    const date = record.date.split(' ')[0]; // 只取日期部分
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(record);
  });
  return groups;
});

// 获取日期范围
const getDateRange = () => {
  if (filteredRecords.value.length === 0) return '';
  const dates = filteredRecords.value.map(r => new Date(r.date));
  const minDate = new Date(Math.min(...dates));
  const maxDate = new Date(Math.max(...dates));

  if (minDate.toDateString() === maxDate.toDateString()) {
    return minDate.toLocaleDateString('zh-CN');
  }

  return `${minDate.toLocaleDateString('zh-CN')} - ${maxDate.toLocaleDateString('zh-CN')}`;
};

// 获取活跃的记录类型
const getActiveTypes = () => {
  const activeTypes = new Set(filteredRecords.value.map(r => r.record_type));
  return recordTypes.value.filter(type => activeTypes.has(type.value));
};

// 获取类型记录数量
const getTypeCount = (typeValue) => {
  return filteredRecords.value.filter(r => r.record_type === typeValue).length;
};

// 格式化分组日期（主要显示）
const formatGroupDate = (dateStr) => {
  const date = new Date(dateStr);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (date.toDateString() === today.toDateString()) {
    return '今天';
  } else if (date.toDateString() === yesterday.toDateString()) {
    return '昨天';
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' });
  }
};

// 格式化分组日期（次要显示）
const formatGroupDay = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN', { weekday: 'long' });
};

// 格式化记录时间
const formatRecordTime = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

// 格式化提醒日期
const formatReminderDate = (dateStr) => {
  const date = new Date(dateStr);
  const today = new Date();
  const diffTime = date.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '明天';
  } else if (diffDays > 0) {
    return `${diffDays}天后`;
  } else {
    return '已过期';
  }
};

// 查看记录详情
const viewRecordDetail = (record) => {
  // 这里可以添加查看详情的逻辑，暂时使用编辑功能
  editRecord(record);
};

// 切换记录完成状态
const toggleRecordCompletion = async (record) => {
  try {
    const newCompletionStatus = !record.is_completed;

    const { error } = await supabase
      .from('event_records')
      .update({ is_completed: newCompletionStatus })
      .eq('id', record.id);

    if (error) throw error;

    // 更新本地数据
    const recordIndex = eventRecords.value.findIndex(r => r.id === record.id);
    if (recordIndex !== -1) {
      eventRecords.value[recordIndex].is_completed = newCompletionStatus;
    }

    ElMessage.success(newCompletionStatus ? '记录已标记为完成' : '记录已标记为未完成');
  } catch (error) {
    console.error('更新记录状态失败:', error.message);
    ElMessage.error('更新记录状态失败: ' + error.message);
  }
};

// 处理日期选择
const handleDatePick = (date) => {
  selectedDate.value = date;
  // 可以在这里添加根据日期筛选记录的逻辑
};

// 处理记录类型筛选（多选模式）
const handleTypeFilter = (type) => {
  if (type === '') {
    // 点击"全部"标签，清空所有选择
    selectedRecordTypes.value = [];
  } else {
    // 切换选中状态
    const index = selectedRecordTypes.value.indexOf(type);
    if (index > -1) {
      // 如果已选中，则取消选中
      selectedRecordTypes.value.splice(index, 1);
    } else {
      // 如果未选中，则添加到选中列表
      selectedRecordTypes.value.push(type);
    }
  }
};

// 处理日历视图切换
const handleCalendarViewChange = (newView) => {
  // 添加切换动画类
  const calendarContainer = document.querySelector('.calendar-content');
  if (calendarContainer) {
    calendarContainer.classList.add('view-switching');
    setTimeout(() => {
      calendarContainer.classList.remove('view-switching');
    }, 200);
  }
};

// 处理视图模式切换
const handleViewModeChange = (mode) => {
  // 视图模式已通过 v-model 自动更新
};

// 选择记录（用于卡片视图）
const selectRecord = (record) => {
  const index = selectedRecords.value.findIndex(r => r.id === record.id);
  if (index > -1) {
    selectedRecords.value.splice(index, 1);
  } else {
    selectedRecords.value.push(record);
  }
};

// 表格行类名
const tableRowClassName = ({ row, rowIndex }) => {
  let className = rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
  if (row.is_completed) {
    className += ' is-completed';
  }
  return className;
};

// 新增方法 - 日历导航相关
// 格式化当前日期显示
const formatCurrentDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long'
  });
};

// 处理年份变化
const handleYearChange = (year) => {
  const newDate = new Date(selectedDate.value);
  newDate.setFullYear(year);
  selectedDate.value = newDate;
  currentYear.value = year;
};

// 处理月份变化
const handleMonthChange = (month) => {
  const newDate = new Date(selectedDate.value);
  newDate.setMonth(month);
  selectedDate.value = newDate;
  currentMonth.value = month;
};

// 回到今天
const goToToday = () => {
  const today = new Date();
  selectedDate.value = today;
  currentYear.value = today.getFullYear();
  currentMonth.value = today.getMonth();

  // 如果是周视图，也要更新周的开始日期
  if (calendarView.value === 'week') {
    currentWeekStart.value = initializeWeekStart(today);
  }

  // 清除日期筛选
  isDateFiltered.value = false;
  selectedDateForFilter.value = '';
};

// 判断是否为选中日期
const isSelectedDate = (dateStr) => {
  if (!isDateFiltered.value || !selectedDateForFilter.value) return false;
  const targetDate = new Date(selectedDateForFilter.value);
  const cellDate = new Date(dateStr);
  targetDate.setHours(0, 0, 0, 0);
  cellDate.setHours(0, 0, 0, 0);
  return targetDate.getTime() === cellDate.getTime();
};

// 处理日期点击
const handleDateClick = (dateStr) => {
  const records = getRecordsForDate(dateStr);
  if (records.length > 0) {
    selectedDateForFilter.value = dateStr;
    isDateFiltered.value = true;

    // 滚动到记录展示区域
    setTimeout(() => {
      const recordsSection = document.querySelector('.records-section');
      if (recordsSection) {
        recordsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  }
};

// 悬浮提示相关方法
// 处理日期悬浮
const handleDateHover = (dateStr, event) => {
  const records = getRecordsForDate(dateStr);
  if (records.length === 0) return;

  // 清除之前的定时器
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value);
  }

  // 延迟显示提示框
  tooltipTimer.value = setTimeout(() => {
    tooltipDate.value = dateStr;
    tooltipRecords.value = records;

    // 计算提示框位置
    const rect = event.target.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    tooltipStyle.value = {
      position: 'absolute',
      left: `${rect.left + scrollLeft + rect.width / 2}px`,
      top: `${rect.top + scrollTop - 10}px`,
      transform: 'translateX(-50%) translateY(-100%)',
      zIndex: 9999
    };

    showTooltip.value = true;
  }, 300);
};

// 处理鼠标离开
const handleDateLeave = () => {
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value);
  }

  // 延迟隐藏提示框
  setTimeout(() => {
    showTooltip.value = false;
  }, 200);
};

// 格式化提示框日期
const formatTooltipDate = (dateStr) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
};

// 截断描述文本
const truncateDescription = (description) => {
  if (!description) return '无描述';
  return description.length > 20 ? description.substring(0, 20) + '...' : description;
};

// 周视图导航方法
// 上一周
const previousWeek = () => {
  const newStart = new Date(currentWeekStart.value);
  newStart.setDate(newStart.getDate() - 7);
  currentWeekStart.value = newStart;
};

// 下一周
const nextWeek = () => {
  const newStart = new Date(currentWeekStart.value);
  newStart.setDate(newStart.getDate() + 7);
  currentWeekStart.value = newStart;
};

// 初始化当前周的开始日期（周日开始）
const initializeWeekStart = (date) => {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day;
  const weekStart = new Date(d.setDate(diff));
  weekStart.setHours(0, 0, 0, 0);
  return weekStart;
};

// 统一的导航方法
// 上一个周期（月或周）
const previousPeriod = () => {
  if (calendarView.value === 'week') {
    previousWeek();
  } else if (calendarView.value === 'year') {
    previousYear();
  } else {
    // 月视图：上一月
    const newDate = new Date(selectedDate.value);
    newDate.setMonth(newDate.getMonth() - 1);
    selectedDate.value = newDate;
    currentYear.value = newDate.getFullYear();
    currentMonth.value = newDate.getMonth();
  }
};

// 下一个周期（月或周）
const nextPeriod = () => {
  if (calendarView.value === 'week') {
    nextWeek();
  } else if (calendarView.value === 'year') {
    nextYear();
  } else {
    // 月视图：下一月
    const newDate = new Date(selectedDate.value);
    newDate.setMonth(newDate.getMonth() + 1);
    selectedDate.value = newDate;
    currentYear.value = newDate.getFullYear();
    currentMonth.value = newDate.getMonth();
  }
};

// 年视图导航方法
// 上一年
const previousYear = () => {
  const newDate = new Date(selectedDate.value);
  newDate.setFullYear(newDate.getFullYear() - 1);
  selectedDate.value = newDate;
  currentYear.value = newDate.getFullYear();
};

// 下一年
const nextYear = () => {
  const newDate = new Date(selectedDate.value);
  newDate.setFullYear(newDate.getFullYear() + 1);
  selectedDate.value = newDate;
  currentYear.value = newDate.getFullYear();
};

// 年视图相关方法
// 获取指定月份的记录数量
const getMonthRecordsCount = (monthIndex) => {
  const year = currentYear.value;
  return eventRecords.value.filter(record => {
    const recordDate = new Date(record.date || record.created_at);
    return recordDate.getFullYear() === year && recordDate.getMonth() === monthIndex;
  }).length;
};

// 获取指定月份的记录类型
const getMonthRecordTypes = (monthIndex) => {
  const year = currentYear.value;
  const monthRecords = eventRecords.value.filter(record => {
    const recordDate = new Date(record.date || record.created_at);
    return recordDate.getFullYear() === year && recordDate.getMonth() === monthIndex;
  });

  // 获取唯一的记录类型
  const types = [...new Set(monthRecords.map(record => record.record_type))];
  return types.slice(0, 4); // 最多显示4种类型
};

// 获取月份密度等级
const getMonthDensityClass = (monthIndex) => {
  const count = getMonthRecordsCount(monthIndex);
  if (count === 0) return 'density-none';
  if (count <= 2) return 'density-low';
  if (count <= 5) return 'density-medium';
  return 'density-high';
};

// 判断是否为当前月份
const isCurrentMonth = (monthIndex) => {
  const today = new Date();
  return today.getFullYear() === currentYear.value && today.getMonth() === monthIndex;
};

// 跳转到指定月份
const goToMonth = (monthIndex) => {
  const newDate = new Date(selectedDate.value);
  newDate.setMonth(monthIndex);
  selectedDate.value = newDate;
  calendarView.value = 'month';
};

// 处理日历组件的事件
const handleCalendarDateClick = (dateStr) => {
  // 处理日期点击事件，可以筛选该日期的记录
  selectedDateForFilter.value = dateStr;
  isDateFiltered.value = true;

  // 滚动到记录展示区域
  nextTick(() => {
    const recordsSection = document.querySelector('.records-section');
    if (recordsSection) {
      recordsSection.scrollIntoView({ behavior: 'smooth' });
    }
  });
};

// 处理日历日期双击事件 - 新建事件记录
const handleCalendarDateDoubleClick = (dateInfo) => {
  if (!currentPetId.value) {
    ElMessage.warning('请先选择一个宠物');
    return;
  }

  // 重置当前记录并预设日期
  resetCurrentRecord();
  currentRecord.value.date = dateInfo.date; // 使用 YYYY-MM-DD 格式的日期

  // 打开新建对话框
  isEditMode.value = false;
  showAddDialog.value = true;
};

// 处理悬浮面板中的记录点击事件
const handleRecordClick = (record) => {
  // 滚动到对应记录的位置
  nextTick(() => {
    // 首先确保记录在当前筛选结果中可见
    // 优先使用原始日期，如果没有则尝试解析格式化日期
    let dateForFilter = record.originalDate;

    // 如果没有原始日期，尝试从格式化日期中解析
    if (!dateForFilter && record.date) {
      if (typeof record.date === 'string' && record.date.includes('年')) {
        const match = record.date.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
        if (match) {
          const [, year, month, day] = match;
          dateForFilter = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      } else if (typeof record.date === 'string' && record.date.includes('-')) {
        dateForFilter = record.date;
      }
    }

    // 如果还是没有正确的格式，尝试从原始记录中获取
    if (!dateForFilter) {
      const originalRecord = eventRecords.value.find(r => r.id === record.id);
      if (originalRecord) {
        dateForFilter = originalRecord.originalDate || originalRecord.date;
        // 如果还是格式化的日期，再次尝试解析
        if (typeof dateForFilter === 'string' && dateForFilter.includes('年')) {
          const match = dateForFilter.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
          if (match) {
            const [, year, month, day] = match;
            dateForFilter = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
          }
        }
      }
    }

    selectedDateForFilter.value = dateForFilter;
    isDateFiltered.value = true;

    // 等待DOM更新后再滚动
    setTimeout(() => {
      const recordElement = document.querySelector(`[data-record-id="${record.id}"]`);
      if (recordElement) {
        recordElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        // 添加高亮效果
        recordElement.classList.add('highlight-record');
        setTimeout(() => {
          recordElement.classList.remove('highlight-record');
        }, 2000);
      } else {
        // 如果找不到具体记录元素，滚动到记录区域
        const recordsSection = document.querySelector('.records-section');
        if (recordsSection) {
          recordsSection.scrollIntoView({ behavior: 'smooth' });
        }
      }
    }, 100);
  });
};

// 处理悬浮面板中的记录双击编辑事件
const handleRecordEdit = (record) => {
  editRecord(record);
};

// 处理日历日期变化事件
const handleCalendarDateChange = (newDate) => {
  selectedDate.value = newDate;
  currentYear.value = newDate.getFullYear();
  currentMonth.value = newDate.getMonth();
};

// 旧的视图切换代码已移除，现在使用 EnhancedViewToggle 组件

// 计算属性：是否全选
const isAllSelected = computed(() => {
  return eventRecords.value.length > 0 && selectedRecords.value.length === eventRecords.value.length;
});

// 新增计算属性
// 筛选后的记录
const filteredRecords = computed(() => {
  let records = eventRecords.value;

  // 按记录类型筛选（多选模式）
  if (selectedRecordTypes.value.length > 0) {
    records = records.filter(record =>
      selectedRecordTypes.value.includes(record.record_type)
    );
  }

  // 按日期筛选
  if (isDateFiltered.value && selectedDateForFilter.value) {
    const targetDate = new Date(selectedDateForFilter.value);
    targetDate.setHours(0, 0, 0, 0);

    records = records.filter(record => {
      // 使用原始日期进行比较，避免格式化日期导致的问题
      const recordDate = new Date(record.originalDate || record.created_at);
      recordDate.setHours(0, 0, 0, 0);
      return recordDate.getTime() === targetDate.getTime();
    });
  }

  return records;
});

// 排序后的记录（用于时间线视图）
const sortedRecords = computed(() => {
  return [...filteredRecords.value].sort((a, b) => {
    return new Date(b.originalDate || b.created_at).getTime() - new Date(a.originalDate || a.created_at).getTime();
  });
});

// 本月记录数
const monthlyRecords = computed(() => {
  const currentMonthNum = new Date().getMonth();
  const currentYearNum = new Date().getFullYear();
  return eventRecords.value.filter(record => {
    const recordDate = new Date(record.originalDate || record.created_at);
    return recordDate.getMonth() === currentMonthNum && recordDate.getFullYear() === currentYearNum;
  }).length;
});

// 当前月份显示
const currentMonthDisplay = computed(() => {
  return new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' });
});

// 待处理提醒数
const pendingReminders = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return eventRecords.value.filter(record => {
    if (!record.next_due_date) return false;
    const dueDate = new Date(record.next_due_date);
    dueDate.setHours(0, 0, 0, 0);
    return dueDate <= today;
  }).length;
});

// 最近活动
const recentActivity = computed(() => {
  if (eventRecords.value.length === 0) return '无记录';
  const latestRecord = eventRecords.value.reduce((latest, current) => {
    const latestDate = new Date(latest.created_at || latest.date);
    const currentDate = new Date(current.created_at || current.date);
    return currentDate > latestDate ? current : latest;
  });
  return getRecordTypeLabel(latestRecord.record_type);
});

// 最新记录日期
const latestRecordDate = computed(() => {
  if (eventRecords.value.length === 0) return '无记录';
  const latestRecord = eventRecords.value.reduce((latest, current) => {
    const latestDate = new Date(latest.created_at || latest.date);
    const currentDate = new Date(current.created_at || current.date);
    return currentDate > latestDate ? current : latest;
  });
  return formatDate(latestRecord.date || latestRecord.created_at);
});

// 活动频率分析
const activityFrequency = computed(() => {
  if (eventRecords.value.length === 0) return { type: '暂无数据', count: 0 };

  // 统计各类型记录的数量
  const typeCount = {};
  eventRecords.value.forEach(record => {
    const type = record.record_type || 'other';
    typeCount[type] = (typeCount[type] || 0) + 1;
  });

  // 找出最频繁的类型
  let maxCount = 0;
  let mostFrequentType = 'other';

  Object.entries(typeCount).forEach(([type, count]) => {
    if (count > maxCount) {
      maxCount = count;
      mostFrequentType = type;
    }
  });

  return {
    type: getRecordTypeLabel(mostFrequentType),
    count: maxCount
  };
});

// 统计卡片数据
const statsItems = computed(() => {
  const items = [
    {
      key: 'total-records',
      type: 'total-records',
      icon: Document,
      label: '总记录数',
      value: eventRecords.value.length,
      date: '全部记录'
    },
    {
      key: 'month-records',
      type: 'month-records',
      icon: Calendar,
      label: '本月记录',
      value: monthlyRecords.value,
      date: currentMonthDisplay.value
    },
    {
      key: 'pending-reminders',
      type: 'pending-reminders',
      icon: Bell,
      label: '待处理提醒',
      value: pendingReminders.value,
      date: '需要关注'
    },
    {
      key: 'recent-activity',
      type: 'recent-activity',
      icon: Clock,
      label: '最近活动',
      value: recentActivity.value,
      date: latestRecordDate.value
    },
    {
      key: 'activity-frequency',
      type: 'activity-frequency',
      icon: TrendCharts,
      label: '活动频率分析',
      value: activityFrequency.value.type,
      date: activityFrequency.value.count > 0 ? `${activityFrequency.value.count} 次记录` : '暂无数据'
    }
  ];

  return items;
});

// 周视图相关计算属性
// 星期标题
const weekDays = computed(() => {
  return ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
});

// 当前周的日期范围显示
const currentWeekRange = computed(() => {
  const start = new Date(currentWeekStart.value);
  const end = new Date(start);
  end.setDate(start.getDate() + 6);

  return `${start.getMonth() + 1}月${start.getDate()}日 - ${end.getMonth() + 1}月${end.getDate()}日`;
});

// 当前周数
const currentWeekNumber = computed(() => {
  const start = new Date(currentWeekStart.value);
  const year = start.getFullYear();
  const firstDayOfYear = new Date(year, 0, 1);

  // 计算第一周的开始日期（第一个周日）
  const firstSunday = new Date(firstDayOfYear);
  const dayOfWeek = firstDayOfYear.getDay();
  if (dayOfWeek !== 0) {
    firstSunday.setDate(firstDayOfYear.getDate() - dayOfWeek);
  }

  // 计算当前周是第几周
  const diffTime = start.getTime() - firstSunday.getTime();
  const diffWeeks = Math.floor(diffTime / (7 * 24 * 60 * 60 * 1000)) + 1;

  return diffWeeks;
});

// 当前周的所有日期
const currentWeekDates = computed(() => {
  const dates = [];
  const start = new Date(currentWeekStart.value);
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = 0; i < 7; i++) {
    const date = new Date(start);
    date.setDate(start.getDate() + i);

    const dateStr = date.toISOString().split('T')[0];
    const isToday = date.getTime() === today.getTime();

    dates.push({
      dateStr,
      day: date.getDate(),
      isToday
    });
  }

  return dates;
});

// 年视图相关计算属性
// 年视图的月份列表
const yearMonths = computed(() => {
  return [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ];
});

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedRecords.value = selection;
};

// 切换全选状态
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedRecords.value = [];
  } else {
    selectedRecords.value = [...eventRecords.value];
  }
};

// 批量删除记录
const batchDeleteRecords = async () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请先选择要删除的记录');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRecords.value.length} 条健康记录吗？此操作不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const idsToDelete = selectedRecords.value.map(record => record.id);
    
    const { error } = await supabase
      .from('event_records')
      .delete()
      .in('id', idsToDelete);

    if (error) throw error;

    ElMessage.success(`成功删除 ${selectedRecords.value.length} 条记录`);
    selectedRecords.value = [];
    fetchEventRecords();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除记录失败:', error.message);
      ElMessage.error('批量删除记录失败: ' + error.message);
    }
  }
};

// 批量完成记录
const batchCompleteRecords = async () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请先选择要完成的记录');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedRecords.value.length} 条记录标记为已完成吗？`,
      '批量完成确认',
      {
        confirmButtonText: '确定完成',
        cancelButtonText: '取消',
        type: 'info',
      }
    );

    const idsToComplete = selectedRecords.value.map(record => record.id);

    const { error } = await supabase
      .from('event_records')
      .update({ is_completed: true })
      .in('id', idsToComplete);

    if (error) throw error;

    ElMessage.success(`成功完成 ${selectedRecords.value.length} 条记录`);
    selectedRecords.value = [];
    fetchEventRecords();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量完成记录失败:', error.message);
      ElMessage.error('批量完成记录失败: ' + error.message);
    }
  }
};

const fetchEventRecords = async () => {
  if (!currentPetId.value) {
    eventRecords.value = [];
    return;
  }
  try {
    const { data, error } = await supabase
      .from('event_records')
      .select('*')
      .eq('pet_id', currentPetId.value)
      .order('date', { ascending: false });
    if (error) throw error;
    eventRecords.value = data.map(record => ({
      ...record,
      // 保留原始日期用于比较和计算
      originalDate: record.date,
      originalNextDueDate: record.next_due_date,
      // 格式化日期用于显示
      date: formatDate(record.date),
      next_due_date: record.next_due_date ? formatDate(record.next_due_date) : null
    }));
  } catch (error) {
    console.error('获取事件记录失败:', error.message);
    ElMessage.error('获取事件记录失败: ' + error.message);
    eventRecords.value = [];
  }
};

const openAddDialog = () => {
  resetCurrentRecord();
  isEditMode.value = false;
  showAddDialog.value = true;
};

const saveRecord = async () => {
  if (!currentPetId.value) {
    ElMessage.error('请先选择一个宠物');
    return;
  }
  if (!currentRecord.value.date || !currentRecord.value.record_type) {
    ElMessage.error('日期和记录类型不能为空');
    return;
  }

  const recordToSave = {
    pet_id: currentPetId.value,
    record_type: currentRecord.value.record_type,
    // 确保日期是 YYYY-MM-DD 格式，如果已经是此格式则不需要转换
    date: currentRecord.value.date instanceof Date ? currentRecord.value.date.toISOString().split('T')[0] : currentRecord.value.date,
    next_due_date: currentRecord.value.next_due_date instanceof Date ? currentRecord.value.next_due_date.toISOString().split('T')[0] : (currentRecord.value.next_due_date || null),
    description: currentRecord.value.notes // 将表单的 notes 映射到数据库的 description
  };

  // 如果是编辑模式，添加ID
  if (isEditMode.value) {
    recordToSave.id = currentRecord.value.id;
  }

  try {
    if (isEditMode.value) {
      const { error } = await supabase
        .from('event_records')
        .update(recordToSave)
        .eq('id', currentRecord.value.id);
      if (error) throw error;
      ElMessage.success('事件记录更新成功！');
    } else {
      // 新增记录时删除id字段，让数据库自动生成
      delete recordToSave.id;
      const { error } = await supabase
        .from('event_records')
        .insert([recordToSave]);
      if (error) throw error;
      ElMessage.success('事件记录添加成功！');
    }
    showAddDialog.value = false;
    fetchEventRecords();
  } catch (error) {
    console.error('保存事件记录失败:', error.message);
    ElMessage.error('保存事件记录失败: ' + error.message);
  }
};

const editRecord = (record) => {
  isEditMode.value = true;
  // 从 eventRecords 查找原始记录，因为 record 可能是格式化后的
  const originalRecord = eventRecords.value.find(r => r.id === record.id);
  if (originalRecord) {
      currentRecord.value = {
          ...originalRecord,
          // 使用原始日期，如果没有则解析格式化日期
          date: originalRecord.originalDate || (originalRecord.date ? originalRecord.date.replace(/(\d{4})年(\d{1,2})月(\d{1,2})日/, '$1-$2-$3') : ''),
          next_due_date: originalRecord.originalNextDueDate || (originalRecord.next_due_date ? originalRecord.next_due_date.replace(/(\d{4})年(\d{1,2})月(\d{1,2})日/, '$1-$2-$3') : null),
          notes: originalRecord.description // 将数据库的 description 映射回表单的 notes
      };
  } else {
      // Fallback if not found, though ideally it should always be found
      currentRecord.value = {
          ...record,
          date: record.originalDate || (record.date ? record.date.replace(/(\d{4})年(\d{1,2})月(\d{1,2})日/, '$1-$2-$3') : ''),
          next_due_date: record.originalNextDueDate || (record.next_due_date ? record.next_due_date.replace(/(\d{4})年(\d{1,2})月(\d{1,2})日/, '$1-$2-$3') : null),
          notes: record.description
      };
  }
  showAddDialog.value = true;
};

const deleteRecord = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这条事件记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    const { error } = await supabase
      .from('event_records')
      .delete()
      .eq('id', id);
    if (error) throw error;
    ElMessage.success('事件记录删除成功！');
    fetchEventRecords();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除事件记录失败:', error.message);
      ElMessage.error('删除事件记录失败: ' + error.message);
    }
  }
};

const resetCurrentRecord = () => {
  currentRecord.value = {
    id: null,
    pet_id: currentPetId.value,
    date: '',
    record_type: '',
    description: '', // 数据库字段
    notes: '',       // 表单字段
    next_due_date: null
  };
};

const handleCurrentPetChanged = (event) => {
  currentPetId.value = event.detail.petId;
  fetchEventRecords();
};

// 监听selectedDate变化，同步年月选择器
watch(selectedDate, (newDate) => {
  currentYear.value = newDate.getFullYear();
  currentMonth.value = newDate.getMonth();

  // 如果是周视图，也要更新周的开始日期
  if (calendarView.value === 'week') {
    currentWeekStart.value = initializeWeekStart(newDate);
  }
}, { immediate: true });

// 监听视图模式变化
watch(calendarView, (newView) => {
  if (newView === 'week') {
    // 切换到周视图时，初始化当前周
    currentWeekStart.value = initializeWeekStart(selectedDate.value);
  }
});

// 记录类型管理方法
// 显示右键菜单
const showTypeContextMenu = (event, type) => {
  event.preventDefault();
  contextMenuType.value = type;
  contextMenuStyle.value = {
    position: 'fixed',
    left: `${event.clientX}px`,
    top: `${event.clientY}px`,
    zIndex: 9999
  };
  showContextMenu.value = true;

  // 点击其他地方隐藏菜单
  const hideMenu = () => {
    showContextMenu.value = false;
    document.removeEventListener('click', hideMenu);
  };
  setTimeout(() => {
    document.addEventListener('click', hideMenu);
  }, 100);
};

// 编辑记录类型
const editRecordType = (type) => {
  isEditTypeMode.value = true;
  currentRecordType.value = { ...type };
  showAddTypeDialog.value = true;
  showContextMenu.value = false;
};

// 删除记录类型
const deleteRecordType = async (typeValue) => {
  try {
    // 获取当前用户ID
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      ElMessage.error('用户未登录');
      return;
    }

    const typeToDelete = recordTypes.value.find(type => type.value === typeValue);

    let confirmMessage = '';
    let isSystemType = false;

    if (typeToDelete?.is_system && !typeToDelete?.user_id) {
      // 系统默认类型
      isSystemType = true;
      confirmMessage = '确定要删除这个系统默认类型吗？删除后该类型将从您的列表中隐藏，相关记录的类型将变为"其他"。';
    } else if (typeToDelete?.user_id && typeToDelete?.is_system === false) {
      // 用户个人化类型
      confirmMessage = '确定要删除这个个人化记录类型吗？删除后将恢复为系统默认设置。';
    } else {
      // 用户自定义类型
      confirmMessage = '确定要删除这个记录类型吗？删除后相关记录的类型将变为"其他"。';
    }

    await ElMessageBox.confirm(confirmMessage, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    if (isSystemType) {
      // 对于系统默认类型，创建一个隐藏标记
      const hiddenType = {
        value: typeValue,
        label: typeToDelete.label,
        color: typeToDelete.color,
        is_system: false,
        user_id: user.id,
        sort_order: typeToDelete.sort_order,
        is_hidden: true // 标记为隐藏
      };

      const { error } = await supabase
        .from('record_types')
        .insert([hiddenType]);

      if (error) throw error;

      // 更新使用该类型的记录为"其他"
      const recordsToUpdate = eventRecords.value.filter(record => record.record_type === typeValue);
      if (recordsToUpdate.length > 0) {
        for (const record of recordsToUpdate) {
          await supabase
            .from('event_records')
            .update({ record_type: 'other' })
            .eq('id', record.id);
        }
        await fetchEventRecords();
      }

      ElMessage.success('系统默认类型已隐藏！');
    } else if (typeToDelete?.user_id && typeToDelete?.is_system === false) {
      // 删除用户个人化类型
      const { error } = await supabase
        .from('record_types')
        .delete()
        .eq('value', typeValue)
        .eq('user_id', user.id);

      if (error) throw error;
      ElMessage.success('个人化设置已删除，已恢复为系统默认！');
    } else {
      // 更新使用该类型的记录为"其他"
      const recordsToUpdate = eventRecords.value.filter(record => record.record_type === typeValue);
      if (recordsToUpdate.length > 0) {
        for (const record of recordsToUpdate) {
          await supabase
            .from('event_records')
            .update({ record_type: 'other' })
            .eq('id', record.id);
        }
      }

      // 从数据库删除记录类型
      const { error } = await supabase
        .from('record_types')
        .delete()
        .eq('value', typeValue)
        .eq('user_id', user.id);

      if (error) throw error;

      if (recordsToUpdate.length > 0) {
        await fetchEventRecords();
      }
      ElMessage.success('记录类型删除成功！');
    }

    // 重新获取数据
    await fetchRecordTypes();
    showContextMenu.value = false;

    // 强制更新DOM以确保悬浮效果正常
    await nextTick();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除记录类型失败:', error.message);
      ElMessage.error('删除记录类型失败: ' + error.message);
    }
  }
};

// 颜色管理相关方法
// 选择预设颜色
const selectPresetColor = (color) => {
  currentRecordType.value.color = color;
};

// 处理颜色选择器变化
const handleColorPickerChange = (color) => {
  if (color) {
    currentRecordType.value.color = color;
  }
};



// 获取颜色名称（用于提示）
const getColorName = (color) => {
  const colorNames = {
    '#409EFF': '蓝色',
    '#67C23A': '绿色',
    '#E6A23C': '橙色',
    '#F56C6C': '红色',
    '#909399': '灰色',
    '#9C27B0': '紫色',
    '#FF9800': '琥珀色',
    '#4CAF50': '青绿色',
    '#2196F3': '天蓝色',
    '#FF5722': '深橙色',
    '#795548': '棕色',
    '#607D8B': '蓝灰色'
  };
  return colorNames[color] || '自定义颜色';
};

// 处理记录类型保存
const handleRecordTypeSave = async (formData) => {
  if (!formData.label.trim()) {
    ElMessage.warning('请输入类型名称');
    return;
  }

  try {
    // 获取当前用户ID
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      ElMessage.error('用户未登录');
      return;
    }

    if (isEditTypeMode.value) {
      // 编辑模式
      const typeToUpdate = recordTypes.value.find(type => type.value === currentRecordType.value.value);

      if (typeToUpdate?.is_system && !typeToUpdate?.user_id) {
        // 如果是系统默认类型，创建用户个人化版本
        const personalizedType = {
          value: currentRecordType.value.value,
          label: formData.label,
          color: formData.color,
          is_system: false,
          user_id: user.id
        };

        const { error } = await supabase
          .from('record_types')
          .insert([personalizedType]);

        if (error) throw error;
        ElMessage.success('已创建个人化类型设置！');
      } else {
        // 更新用户自己的类型
        const { error } = await supabase
          .from('record_types')
          .update({
            label: formData.label,
            color: formData.color
          })
          .eq('value', currentRecordType.value.value)
          .eq('user_id', user.id);

        if (error) throw error;
        ElMessage.success('记录类型更新成功！');
      }
    } else {
      // 新增模式 - 获取当前最大的sort_order值
      const maxSortOrder = Math.max(...recordTypes.value.map(type => type.sort_order || 0), 0);

      const newType = {
        value: `custom_${Date.now()}`, // 生成唯一值
        label: formData.label,
        color: formData.color,
        is_system: false,
        user_id: user.id,
        sort_order: maxSortOrder + 1
      };

      const { error } = await supabase
        .from('record_types')
        .insert([newType]);

      if (error) throw error;
      ElMessage.success('记录类型添加成功！');
    }

    // 重新获取记录类型
    await fetchRecordTypes();
    showAddTypeDialog.value = false;
    resetCurrentRecordType();

    // 强制更新DOM以确保悬浮效果正常
    await nextTick();
  } catch (error) {
    console.error('保存记录类型失败:', error.message);
    ElMessage.error('保存记录类型失败: ' + error.message);
  }
};

// 处理记录类型取消
const handleRecordTypeCancel = () => {
  showAddTypeDialog.value = false;
  resetCurrentRecordType();
};

// 重置类型对话框
const resetTypeDialog = () => {
  resetCurrentRecordType();
};



// 重置当前记录类型
const resetCurrentRecordType = () => {
  currentRecordType.value = {
    value: '',
    label: '',
    color: '#409EFF'
  };
  isEditTypeMode.value = false;
};

// 拖拽事件处理
const onDragStart = () => {
  isDragging.value = true;
  showContextMenu.value = false; // 隐藏右键菜单
};

const onDragEnd = async (evt) => {
  isDragging.value = false;

  // 如果位置没有改变，不需要更新数据库
  if (evt.oldIndex === evt.newIndex) {
    return;
  }

  try {
    // 获取当前用户ID
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      ElMessage.error('用户未登录');
      return;
    }

    // 更新所有记录类型的排序
    const updates = recordTypes.value.map((type, index) => ({
      id: type.id,
      sort_order: index + 1
    }));

    // 批量更新数据库
    for (const update of updates) {
      const { error } = await supabase
        .from('record_types')
        .update({ sort_order: update.sort_order })
        .eq('id', update.id);

      if (error) throw error;
    }

    ElMessage.success('排序已保存');
  } catch (error) {
    console.error('保存排序失败:', error.message);
    ElMessage.error('保存排序失败: ' + error.message);
    // 重新获取数据以恢复原始顺序
    await fetchRecordTypes();
  }
};

// 获取记录类型
const fetchRecordTypes = async () => {
  try {
    // 获取当前用户ID
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('用户未登录');
    }

    // 获取用户自定义的记录类型（包括个人化设置和隐藏标记）
    const { data: userTypes, error: userError } = await supabase
      .from('record_types')
      .select('*')
      .eq('user_id', user.id)
      .order('sort_order', { ascending: true });

    if (userError) throw userError;

    // 获取系统默认类型
    const { data: systemTypes, error: systemError } = await supabase
      .from('record_types')
      .select('*')
      .is('user_id', null)
      .eq('is_system', true)
      .order('sort_order', { ascending: true });

    if (systemError) throw systemError;

    // 获取用户隐藏的类型值
    const hiddenTypeValues = userTypes?.filter(type => type.is_hidden).map(type => type.value) || [];

    // 获取用户个人化的类型值（非隐藏）
    const userTypeValues = userTypes?.filter(type => !type.is_hidden).map(type => type.value) || [];

    // 过滤系统类型：排除被隐藏的和已被个人化的
    const filteredSystemTypes = systemTypes?.filter(type =>
      !hiddenTypeValues.includes(type.value) && !userTypeValues.includes(type.value)
    ) || [];

    // 只包含非隐藏的用户类型
    const visibleUserTypes = userTypes?.filter(type => !type.is_hidden) || [];

    // 按照sort_order排序合并后的结果
    const allTypes = [...visibleUserTypes, ...filteredSystemTypes];
    recordTypes.value = allTypes.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
  } catch (error) {
    console.error('获取记录类型失败:', error.message);
    // 如果获取失败，使用默认类型
    recordTypes.value = [
      { value: 'vaccination', label: '疫苗接种', color: '#67C23A', is_system: true, sort_order: 1 },
      { value: 'deworming', label: '驱虫', color: '#E6A23C', is_system: true, sort_order: 2 },
      { value: 'checkup', label: '体检', color: '#409EFF', is_system: true, sort_order: 3 },
      { value: 'illness', label: '疾病就诊', color: '#F56C6C', is_system: true, sort_order: 4 },
      { value: 'medication', label: '用药记录', color: '#909399', is_system: true, sort_order: 5 },
      { value: 'allergy', label: '过敏记录', color: '#FF5722', is_system: true, sort_order: 6 },
      { value: 'surgery', label: '手术记录', color: '#9C27B0', is_system: true, sort_order: 7 },
      { value: 'other', label: '其他', color: '#607D8B', is_system: true, sort_order: 8 },
    ];
  }
};

onMounted(() => {
  fetchRecordTypes();
  fetchEventRecords();

  window.addEventListener('currentPetChanged', handleCurrentPetChanged, { passive: true });
});

import { onBeforeUnmount } from 'vue';
onBeforeUnmount(() => {
  window.removeEventListener('currentPetChanged', handleCurrentPetChanged);
});

</script>

<style scoped>
/* 引入类型管理对话框样式 */
@import '@/styles/type-management-dialog.css';
.health-records-view {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.no-pet-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 自定义按钮样式 */
.add-record-btn {
  position: relative;
  background: linear-gradient(135deg, #409EFF 0%, #36D1DC 50%, #5B86E5 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.add-record-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.add-record-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);
}

.add-record-btn:hover::before {
  left: 100%;
}

.add-record-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.delete-batch-btn {
  background: linear-gradient(135deg, #F56C6C 0%, #FF6B9D 50%, #C44569 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.delete-batch-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(245, 108, 108, 0.4);
}

.delete-batch-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.add-icon, .delete-icon {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.add-record-btn:hover .add-icon {
  transform: rotate(90deg) scale(1.1);
}

.delete-batch-btn:hover .delete-icon {
  transform: scale(1.1);
}

.btn-text {
  position: relative;
  z-index: 1;
}

/* 日历面板 */
.calendar-section {
  margin-bottom: 24px;
}

.calendar-card {
  border-radius: 12px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.calendar-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.calendar-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.current-date-info {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.calendar-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.navigation-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #606266;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background: #ecf5ff;
  border-color: #409EFF;
  color: #409EFF;
}

.date-selectors {
  display: flex;
  align-items: center;
  gap: 8px;
}

.year-selector, .month-selector {
  width: 80px;
}

.today-btn {
  background: #67C23A;
  border: 1px solid #67C23A;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
}

.today-btn:hover {
  background: #85CE61;
  border-color: #85CE61;
}

.today-text {
  margin-left: 4px;
}

/* 视图切换样式现在由 EnhancedViewToggle 组件提供 */

/* 旧的按钮和动画样式已移除，现在由 EnhancedViewToggle 组件提供 */

/* 日历内容切换动画 */
.calendar-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.calendar-content.view-switching {
  opacity: 0;
  transform: translateY(10px);
}

.health-calendar {
  margin-top: 16px;
}

/* 隐藏Element Plus日历的默认导航按钮 */
.health-calendar :deep(.el-calendar__header) {
  display: none;
}

.health-calendar :deep(.el-calendar__body) {
  padding: 0;
}

/* 周视图样式 */
.week-calendar {
  margin-top: 16px;
}

.week-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 8px;
}

.week-navigation {
  display: flex;
  align-items: center;
  gap: 16px;
}

.week-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.week-range {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.week-number {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.week-grid {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.week-days-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f5f7fa;
}

.week-day-header {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: #606266;
  border-right: 1px solid #e5e7eb;
}

.week-day-header:last-child {
  border-right: none;
}

.week-dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  min-height: 120px;
}

.week-date-cell {
  position: relative;
  padding: 12px 8px;
  border-right: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.week-date-cell:last-child {
  border-right: none;
}

.week-date-cell:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.week-date-cell.has-records {
  background-color: rgba(64, 158, 255, 0.05);
}

.week-date-cell.has-records:hover {
  background-color: rgba(64, 158, 255, 0.15);
}

.week-date-cell.selected-date {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2), rgba(103, 194, 58, 0.2));
  border: 2px solid #409EFF;
}

.week-date-cell.today {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.1), rgba(64, 158, 255, 0.1));
  font-weight: 600;
}

.week-date-cell.today .week-date-number {
  color: #67C23A;
  font-weight: 700;
}

.week-date-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.week-record-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  justify-content: center;
  max-width: 100%;
}

/* 年视图样式 */
.year-calendar {
  margin-top: 16px;
}

.year-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 8px;
}

.year-navigation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.year-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  min-width: 100px;
  text-align: center;
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.year-month-cell {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.year-month-cell:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.year-month-cell.has-records {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(103, 194, 58, 0.05));
}

.year-month-cell.current-month {
  border-color: #67C23A;
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.1), rgba(64, 158, 255, 0.1));
}

.month-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  text-align: center;
  margin-bottom: 12px;
}

.month-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.records-count {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.records-density {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
  min-height: 20px;
}

.density-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 0.8;
}

/* 密度等级样式 */
.density-none {
  opacity: 0.3;
}

.density-low {
  opacity: 0.5;
}

.density-medium {
  opacity: 0.7;
}

.density-high {
  opacity: 1;
}

/* 右键菜单样式 */
.context-menu {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 120px;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
}

.context-menu-item.danger {
  color: #f56c6c;
}

.context-menu-item.danger:hover {
  background-color: #fef0f0;
}

.calendar-cell {
  position: relative;
  height: 100%;
  padding: 4px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.calendar-cell:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.calendar-cell.has-records {
  background-color: rgba(64, 158, 255, 0.05);
}

.calendar-cell.has-records:hover {
  background-color: rgba(64, 158, 255, 0.15);
  transform: scale(1.02);
}

.calendar-cell.selected-date {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2), rgba(103, 194, 58, 0.2));
  border: 2px solid #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.date-number {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.record-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-top: 4px;
}

.record-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.record-dot.vaccination { background-color: #67C23A; }
.record-dot.deworming { background-color: #E6A23C; }
.record-dot.checkup { background-color: #409EFF; }
.record-dot.illness { background-color: #F56C6C; }
.record-dot.medication { background-color: #909399; }
.record-dot.allergy { background-color: #F56C6C; }
.record-dot.surgery { background-color: #F56C6C; }
.record-dot.other { background-color: #C0C4CC; }

.more-indicator {
  font-size: 10px;
  color: #909399;
  font-weight: 500;
}

/* 悬浮提示框样式 */
.records-tooltip {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
  padding: 0;
  min-width: 280px;
  max-width: 400px;
  z-index: 9999;
  animation: tooltipFadeIn 0.3s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) scale(1);
  }
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 12px 12px 0 0;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.tooltip-header .el-icon {
  color: #409EFF;
  font-size: 16px;
}

.tooltip-content {
  padding: 12px 16px;
  max-height: 300px;
  overflow-y: auto;
}

.tooltip-record {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  border-bottom: 1px solid #f5f7fa;
}

.tooltip-record:last-child {
  border-bottom: none;
}

.record-type-tag {
  flex-shrink: 0;
  font-size: 11px;
  font-weight: 600;
  border-radius: 6px;
}

.record-desc {
  flex: 1;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.more-records-hint {
  text-align: center;
  color: #909399;
  font-size: 12px;
  font-style: italic;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f5f7fa;
}

/* 提示框箭头 */
.records-tooltip::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 统计卡片样式已移至预设样式系统 (stats-card-presets.css) */



/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-section .el-card {
  border-radius: 12px;
  overflow: hidden;
}

.filter-content {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 20px 0;
}

.filter-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}



/* 记录类型管理样式 */
.filter-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 12px;
}

.filter-label-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-weight: 500;
  color: var(--color-text-primary);
  font-size: var(--font-size-sm);
}

.selected-count {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  background: rgba(64, 158, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.add-type-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  border: none;
  color: #6c757d;
  flex-shrink: 0;
}

.add-type-tag:hover {
  color: #409EFF;
  transform: scale(1.1);
}

.add-type-tag:active {
  transform: scale(1.05);
  transition: all 0.1s ease;
}

.add-icon {
  font-size: 20px;
  font-weight: bold;
}

/* 移除旧的视图切换样式，使用新的预设样式 */

.record-types-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  padding: 8px 0;
  min-height: auto;
}

/* 拖拽容器样式 */
.draggable-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

/* StandardTag 组件样式增强 */
.add-type-tag-standard {
  min-width: 40px !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  background: transparent !important;
  border: none !important;
  color: #6c757d !important;
}

.add-type-tag-standard:hover {
  color: var(--color-primary) !important;
  transform: scale(1.1) !important;
  background: transparent !important;
}

.add-type-tag-standard:active {
  transform: scale(1.05) !important;
}

/* 拖拽状态样式 */
.is-dragging {
  transform: rotate(5deg) scale(1.05) !important;
  z-index: 1000 !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
  cursor: grabbing !important;
}

/* 多选标签样式增强 */
.multi-selected {
  position: relative;
  animation: multiSelectPulse 0.3s ease-out;
}

.multi-selected::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid rgba(64, 158, 255, 0.6);
  border-radius: inherit;
  pointer-events: none;
  animation: multiSelectGlow 2s ease-in-out infinite;
}

@keyframes multiSelectPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes multiSelectGlow {
  0%, 100% {
    opacity: 0.6;
    box-shadow: 0 0 5px rgba(64, 158, 255, 0.3);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 15px rgba(64, 158, 255, 0.6);
  }
}

/* 新的添加按钮样式 */
.add-new-button {
  cursor: pointer;
  outline: none;
  background: transparent;
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.add-new-button:hover {
  transform: rotate(90deg);
}

.add-icon-svg {
  stroke: #6c757d;
  fill: none;
  transition: all 0.3s ease;
}

.add-new-button:hover .add-icon-svg {
  stroke: #409EFF;
  fill: rgba(64, 158, 255, 0.1);
}

.add-new-button:active {
  transition: none;
}

.add-new-button:active .add-icon-svg {
  stroke: #337ECC;
  fill: rgba(64, 158, 255, 0.2);
}

/* ========== 类型管理对话框样式已移至全局样式文件 ========== */

/* "全部"标签特殊样式 */
.all-tag-standard {
  /* 确保不显示编辑/删除按钮 */
}

.all-tag-standard:hover {
  /* 悬停时保持特殊的渐变效果 */
  background: linear-gradient(135deg, #5dade2 0%, #85ce61 100%) !important;
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.4) !important;
}

/* 非激活状态的"全部"标签悬停效果 */
.all-tag-standard:not(.tag-active):hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
}

.type-tag {
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
  padding: 10px 18px;
  border-radius: 24px;
  font-size: 14px;
  font-weight: 600;
  border: 2px solid transparent;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #495057;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: 80px;
  flex-shrink: 0;
  overflow: hidden;
}

.tag-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.type-tag.is-active {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transform: translateY(-1px) scale(1.02);
}

.type-tag.is-active::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 26px;
  z-index: -1;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

.clickable-tag:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.clickable-tag:hover.is-active {
  background: linear-gradient(135deg, #5dade2 0%, #85ce61 100%);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.4);
  transform: translateY(-3px) scale(1.05);
}

.clickable-tag:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s ease;
}

.clickable-tag:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
}

.tag-text {
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tag-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(20px);
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%) translateX(20px);
  z-index: 2;
}

/* 默认状态：文本居中，操作按钮隐藏 */
.type-tag .tag-content {
  justify-content: center;
  position: relative;
}

.type-tag .tag-text {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
}

/* 悬浮状态：文本向左偏移，操作按钮显示 */
.type-tag:hover .tag-content {
  justify-content: flex-start !important;
  padding-left: 12px !important;
  padding-right: 60px !important; /* 为操作按钮预留足够空间 */
}

.type-tag:hover .tag-text {
  margin-right: auto !important; /* 确保文本靠左 */
}

.type-tag:hover .tag-actions {
  opacity: 1 !important;
  transform: translateY(-50%) translateX(0) !important;
}

/* 确保全部标签不显示操作按钮，且悬浮时文本保持居中 */
.type-tag.all-tag .tag-actions {
  display: none !important;
}

.type-tag.all-tag:hover .tag-content {
  justify-content: center !important;
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.type-tag.all-tag:hover .tag-text {
  margin-right: 0 !important;
}

/* 激活状态的特殊处理 */
.type-tag.is-active:hover .tag-content {
  padding-right: 60px;
}

.edit-type-icon,
.delete-type-icon {
  padding: 4px;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-type-icon:hover {
  background: rgba(64, 158, 255, 0.8);
  color: white;
  transform: scale(1.2) rotate(15deg);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.delete-type-icon:hover {
  background: rgba(245, 108, 108, 0.8);
  color: white;
  transform: scale(1.2) rotate(90deg);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.edit-type-icon:active,
.delete-type-icon:active {
  transform: scale(0.9);
  transition: all 0.1s ease;
}

/* 拖拽相关样式 */
.draggable-tag {
  cursor: move;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.draggable-tag.is-dragging {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.draggable-tag.is-dragging .tag-actions {
  display: none !important;
}

/* 拖拽时的幽灵元素样式 */
.ghost {
  opacity: 0.5;
  background: linear-gradient(135deg, #c8ebfb 0%, #e3f2fd 100%) !important;
  border: 2px dashed #409EFF !important;
  transform: scale(0.95);
  color: #409EFF !important;
}

/* 拖拽时的占位符样式 */
.sortable-chosen {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  z-index: 999;
}

.sortable-drag {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

.filter-label {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  white-space: nowrap;
}

.view-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.filter-group .el-radio-group,
.view-controls .el-radio-group {
  display: flex;
  gap: 4px;
}

.filter-group .el-radio-button,
.view-controls .el-radio-button {
  margin: 0;
}

.filter-group .el-radio-button__inner,
.view-controls .el-radio-button__inner {
  border-radius: 8px;
  font-size: 13px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.filter-group .el-radio-button__inner:hover,
.view-controls .el-radio-button__inner:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.filter-group .el-radio-button.is-active .el-radio-button__inner,
.view-controls .el-radio-button.is-active .el-radio-button__inner {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 记录展示区域 */
.records-section {
  margin-bottom: 24px;
}

/* 视图切换动画 */
.view-fade-enter-active,
.view-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.view-fade-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.view-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

.view-fade-enter-to,
.view-fade-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}



/* 卡片视图 */
.cards-view {
  margin-bottom: 24px;
}

.records-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.record-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  border: 2px solid;
}

.record-card:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.record-card.selected {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.record-card.is-completed {
  background: #f8f9fa;
  opacity: 0.7;
}

.record-card.is-completed .record-description,
.record-card.is-completed .record-type-badge {
  color: #999999;
}

.record-content {
  padding: 20px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.record-type-badge .el-tag {
  font-size: 13px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 8px;
}

.record-date {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.record-date .el-icon {
  font-size: 16px;
}

.record-body {
  margin-bottom: 16px;
}

.record-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 12px;
  min-height: 44px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.record-reminder {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #E6A23C;
  font-weight: 500;
  background: rgba(230, 162, 60, 0.1);
  padding: 6px 10px;
  border-radius: 6px;
}

.record-reminder .el-icon {
  font-size: 14px;
}

.record-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.record-actions .completion-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.record-actions .completion-button:hover {
  border-color: #10b981;
  background: #f0fdf4;
  transform: scale(1.1);
}

.record-actions .completion-button.is-completed {
  border-color: #10b981;
  background: #ffffff;
}

.record-actions .completion-button.is-completed:hover {
  background: #f0fdf4;
  transform: scale(1.1);
}

.record-actions .check-icon {
  font-size: 14px;
  font-weight: bold;
  color: #10b981;
  animation: checkmark-appear 0.3s ease-in-out;
}

.record-actions .delete-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: 2px solid #d1d5db;
  color: #6b7280;
  cursor: pointer;
}

.record-actions .delete-button:hover {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
}

/* 简约时间线视图 */
.timeline-view {
  margin-bottom: 24px;
}

.simple-timeline {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.timeline-container {
  padding: 24px;
}

/* 日期分组 */
.timeline-group {
  margin-bottom: 32px;
}

.timeline-group:last-child {
  margin-bottom: 0;
}

.group-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.date-label {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.date-text {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.date-sub {
  font-size: 13px;
  color: #6b7280;
}

/* 时间线项目 */
.group-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fafbfc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 60px;
}

.timeline-item:hover {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.timeline-item.has-reminder {
  border-left: 3px solid #f59e0b;
  background: #fffbeb;
}

.timeline-item.is-completed {
  background: #f8f9fa;
  opacity: 0.7;
}

.timeline-item.is-completed .item-description,
.timeline-item.is-completed .category-name {
  color: #999999;
}



/* 主要内容 */
.item-content {
  flex: 1;
  min-width: 0;
  padding: 0 8px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-time {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.item-category {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.category-name {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.item-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
}

/* 左侧完成状态按钮 */
.item-completion-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 12px;
}

.completion-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.completion-button:hover {
  border-color: #10b981;
  background: #f0fdf4;
  transform: scale(1.1);
}

.completion-button.is-completed {
  border-color: #10b981;
  background: #ffffff;
}

.completion-button.is-completed:hover {
  background: #f0fdf4;
  transform: scale(1.1);
}

.check-icon {
  font-size: 14px;
  font-weight: bold;
  color: #10b981;
  animation: checkmark-appear 0.3s ease-in-out;
}

@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 右侧删除按钮 */
.item-actions-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: 12px;
}

.delete-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: 2px solid #d1d5db;
  color: #6b7280;
}

.delete-button:hover {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
}

/* 右侧状态（保留提醒功能） */
.item-status {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.status-reminder {
  position: relative;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #f59e0b;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* 简化空状态 */
.simple-empty {
  padding: 60px 24px;
  text-align: center;
  background: #fafbfc;
}

.empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-text h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-text p {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
}





/* 表格视图 */
.table-view {
  margin-bottom: 24px;
}

.table-view .el-card {
  border-radius: 12px;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-batch-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table .even-row) {
  background-color: #fafafa;
}

:deep(.el-table .odd-row) {
  background-color: #ffffff;
}

:deep(.el-table .is-completed) {
  background-color: #f8f9fa !important;
  opacity: 0.7;
  border-left: 3px solid #d1d5db;
}

:deep(.el-table .is-completed td) {
  color: #999999;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

.date-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.date-icon {
  color: #909399;
  font-size: 16px;
}

.description-text {
  color: #606266;
  line-height: 1.5;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.no-reminder {
  color: #c0c4cc;
  font-style: italic;
}

.reminder-text {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #E6A23C;
  font-weight: 500;
}

.reminder-text .el-icon {
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.action-buttons .completion-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.action-buttons .completion-button:hover {
  border-color: #10b981;
  background: #f0fdf4;
  transform: scale(1.1);
}

.action-buttons .completion-button.is-completed {
  border-color: #10b981;
  background: #ffffff;
}

.action-buttons .completion-button.is-completed:hover {
  background: #f0fdf4;
  transform: scale(1.1);
}

.action-buttons .check-icon {
  font-size: 14px;
  font-weight: bold;
  color: #10b981;
  animation: checkmark-appear 0.3s ease-in-out;
}

.action-buttons .delete-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: 2px solid #d1d5db;
  color: #6b7280;
  cursor: pointer;
}

.action-buttons .delete-button:hover {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
}

/* 对话框样式 - 只应用于非类型管理对话框 */
:deep(.el-dialog:not(.type-management-dialog)) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog:not(.type-management-dialog) .el-dialog__header) {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  padding: 20px 24px;
}

:deep(.el-dialog:not(.type-management-dialog) .el-dialog__title) {
  font-weight: 600;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .health-records-view {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .add-record-btn, .delete-batch-btn {
    padding: 10px 20px;
    font-size: 13px;
  }

  .btn-text {
    display: none;
  }

  .add-icon, .delete-icon {
    margin-right: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-content {
    padding: 20px;
  }

  .stat-value {
    font-size: 18px;
  }

  .filter-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
    padding: 16px 0;
  }

  .filter-left {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding-bottom: 12px;
  }

  .record-types-container {
    padding: 12px;
    gap: 8px;
    justify-content: center;
  }

  .type-tag {
    min-width: 70px;
    padding: 8px 14px;
    font-size: 13px;
    min-height: 36px;
    flex: 0 1 auto;
  }

  .tag-actions {
    margin-left: 6px;
  }

  .edit-type-icon,
  .delete-type-icon {
    font-size: 11px;
    padding: 1px;
  }

  .view-control-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .view-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .view-controls .el-radio-group {
    width: 100%;
    justify-content: space-between;
  }

  .view-actions {
    justify-content: center;
  }

  .records-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .record-content {
    padding: 16px;
  }

  .calendar-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .calendar-title-section {
    width: 100%;
  }

  .calendar-controls {
    width: 100%;
    justify-content: space-between;
    flex-direction: column;
    gap: 12px;
  }

  .navigation-buttons {
    order: 2;
    justify-content: center;
  }

  .date-selectors {
    order: 1;
    justify-content: center;
    flex-wrap: wrap;
    gap: 6px;
  }

  .view-toggle {
    order: 3;
    align-self: center;
  }

  .year-selector, .month-selector {
    width: 70px;
  }

  .today-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .today-text {
    display: none;
  }

  .week-calendar {
    font-size: 14px;
  }

  .week-range {
    font-size: 14px;
  }

  .week-number {
    font-size: 12px;
  }

  .week-date-cell {
    padding: 8px 4px;
    min-height: 80px;
  }

  .week-date-number {
    font-size: 16px;
  }

  .year-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .year-month-cell {
    padding: 12px;
    min-height: 100px;
  }

  .month-name {
    font-size: 16px;
  }

  .year-title {
    font-size: 18px;
  }

  .records-tooltip {
    min-width: 250px;
    max-width: 300px;
  }

  .calendar-cell {
    min-height: 50px;
  }

  /* 简约时间线响应式 */
  .simple-timeline {
    border-radius: 8px;
    margin: 0 -8px;
  }

  .timeline-container {
    padding: 16px;
  }

  .timeline-group {
    margin-bottom: 24px;
  }

  .group-header {
    margin-bottom: 12px;
  }

  .date-text {
    font-size: 15px;
  }

  .date-sub {
    font-size: 12px;
  }

  .group-items {
    gap: 8px;
  }

  .timeline-item {
    padding: 12px;
    gap: 12px;
  }

  .item-time {
    min-width: 50px;
  }

  .time-text {
    font-size: 12px;
  }

  .time-dot {
    width: 6px;
    height: 6px;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-title {
    font-size: 14px;
  }

  .item-actions {
    opacity: 1;
    width: 100%;
    justify-content: flex-end;
  }

  .item-description {
    font-size: 13px;
  }

  .item-meta {
    gap: 12px;
    font-size: 11px;
  }

  .simple-empty {
    padding: 40px 16px;
  }

  .empty-icon {
    font-size: 40px;
  }

  .empty-text h3 {
    font-size: 16px;
  }

  .empty-text p {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .health-records-view {
    padding: 12px;
  }

  .page-title {
    font-size: 20px;
  }



  .add-record-btn, .delete-batch-btn {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 10px;
  }

  .header-actions {
    gap: 12px;
  }

  .record-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .view-controls .el-radio-button__inner {
    padding: 6px 12px;
    font-size: 12px;
    gap: 4px;
  }

  .view-controls .btn-text {
    display: none;
  }

  .delete-batch-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .delete-batch-btn .btn-text {
    display: none;
  }

  .record-types-container {
    padding: 8px;
    gap: 6px;
    min-height: 44px;
  }

  .type-tag {
    min-width: 60px;
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
    border-radius: 18px;
  }

  .tag-text {
    font-size: 12px;
  }

  .tag-actions {
    margin-left: 4px;
    gap: 2px;
  }

  .edit-type-icon,
  .delete-type-icon {
    font-size: 10px;
    padding: 1px;
  }

  .filter-header {
    gap: 8px;
    padding-bottom: 8px;
  }

  .add-type-btn {
    padding: 6px 10px;
    font-size: 11px;
  }

  .calendar-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .calendar-controls {
    flex-direction: column;
    gap: 8px;
  }

  .navigation-buttons {
    justify-content: center;
  }

  .date-selectors {
    justify-content: center;
  }

  .view-toggle {
    align-self: center;
  }

  .week-range {
    font-size: 12px;
  }

  .week-number {
    font-size: 10px;
  }

  .week-date-cell {
    padding: 6px 2px;
    min-height: 60px;
  }

  .week-date-number {
    font-size: 14px;
  }

  .week-day-header {
    padding: 8px 4px;
    font-size: 12px;
  }

  .year-selector, .month-selector {
    width: 60px;
  }

  .today-btn {
    padding: 4px 8px;
    font-size: 11px;
  }

  .records-tooltip {
    min-width: 200px;
    max-width: 250px;
    font-size: 12px;
  }

  .tooltip-header {
    padding: 8px 12px;
    font-size: 12px;
  }

  .tooltip-content {
    padding: 8px 12px;
  }

  .tooltip-record {
    padding: 4px 0;
  }

  .record-type-tag {
    font-size: 10px;
  }

  .record-desc {
    font-size: 11px;
  }

  .calendar-cell {
    min-height: 40px;
    padding: 2px;
  }

  .date-number {
    font-size: 12px;
  }

  .record-dot {
    width: 4px;
    height: 4px;
  }

  .more-indicator {
    font-size: 8px;
  }

  .year-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .year-month-cell {
    padding: 8px;
    min-height: 80px;
  }

  .month-name {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .records-count {
    font-size: 12px;
  }

  .year-title {
    font-size: 16px;
    min-width: 80px;
  }

  .year-navigation {
    gap: 12px;
  }

  .density-dot {
    width: 6px;
    height: 6px;
  }
}

/* 记录高亮效果 */
.highlight-record {
  animation: highlight-pulse 2s ease-in-out;
  transform: scale(1.02);
  box-shadow: 0 8px 32px rgba(64, 158, 255, 0.3) !important;
}

@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(64, 158, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

/* 悬浮提示框交互样式 */
.records-tooltip {
  pointer-events: auto !important;
}

.tooltip-record {
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.tooltip-record:hover {
  background-color: rgba(64, 158, 255, 0.1);
  transform: translateX(2px);
}

.tooltip-record:active {
  transform: translateX(1px) scale(0.98);
}

/* 日历导航组件样式 */
.calendar-navigation-section {
  margin-bottom: 20px;
}

.navigation-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 12px;
}

.navigation-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #e4e7ed;
  background: white;
  color: #606266;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
  transform: scale(1.05);
}

.today-btn {
  padding: 8px 16px;
  border-radius: 18px;
  border: 1px solid #e4e7ed;
  background: white;
  color: #606266;
  font-weight: 500;
  transition: all 0.3s ease;
}

.today-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
  background: rgba(64, 158, 255, 0.05);
}

.today-text {
  margin-left: 4px;
}

.date-selectors {
  display: flex;
  align-items: center;
  gap: 12px;
}

.year-selector,
.month-selector {
  min-width: 80px;
}

.year-selector .el-input__inner,
.month-selector .el-input__inner {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  background: white;
  transition: all 0.3s ease;
}

.year-selector .el-input__inner:hover,
.month-selector .el-input__inner:hover {
  border-color: #409EFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navigation-content {
    flex-direction: column;
    gap: 16px;
    padding: 12px 16px;
  }

  .navigation-buttons {
    order: 2;
  }

  .date-selectors {
    order: 1;
  }

  .today-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .calendar-navigation-section {
    margin-bottom: 16px;
  }

  .navigation-content {
    padding: 10px 12px;
    gap: 12px;
  }

  .nav-btn {
    width: 32px;
    height: 32px;
  }

  .today-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .year-selector,
  .month-selector {
    min-width: 70px;
  }
}
</style>