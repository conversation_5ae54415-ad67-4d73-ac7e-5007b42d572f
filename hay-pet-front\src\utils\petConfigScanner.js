/**
 * 高级宠物配置扫描器
 * 支持多种文件格式、智能命名解析、缓存优化
 */

// 支持的图片格式
const SUPPORTED_FORMATS = ['png', 'jpg', 'jpeg', 'webp', 'svg', 'avif']
const FORMAT_PATTERN = `\\.(${SUPPORTED_FORMATS.join('|')})$`

/**
 * 扫描宠物类型图片
 * @returns {Object} 类型图片映射
 */
export const scanPetTypes = () => {
  // 使用 Vite 的 import.meta.glob 扫描类型图片
  const typeImages = import.meta.glob(
    '/src/public/avatar_type/**/*.(png|jpg|jpeg|webp|svg|avif)',
    { 
      eager: true,
      as: 'url'
    }
  )
  
  const types = new Map()
  
  Object.entries(typeImages).forEach(([path, url]) => {
    const parsed = parseTypeFilename(path)
    if (parsed) {
      types.set(parsed.id, {
        id: parsed.id,
        name: parsed.name,
        displayName: parsed.displayName,
        englishName: parsed.englishName,
        image: url,
        originalPath: path,
        format: parsed.format,
        breeds: []
      })
    }
  })
  
  return types
}

/**
 * 扫描宠物品种图片
 * @returns {Array} 品种图片数组
 */
export const scanPetBreeds = () => {
  // 扫描品种图片
  const breedImages = import.meta.glob(
    '/src/public/avatar_kind/**/*.(png|jpg|jpeg|webp|svg|avif)',
    { 
      eager: true,
      as: 'url'
    }
  )
  
  const breeds = []
  
  Object.entries(breedImages).forEach(([path, url]) => {
    const parsed = parseBreedFilename(path)
    if (parsed) {
      breeds.push({
        id: parsed.id,
        name: parsed.name,
        typeId: parsed.typeId,
        typeName: parsed.typeName,
        image: url,
        originalPath: path,
        format: parsed.format,
        description: generateDescription(parsed.name),
        popularity: calculatePopularity(parsed.name),
        characteristics: generateCharacteristics(parsed.name)
      })
    }
  })
  
  return breeds
}

/**
 * 智能解析类型文件名
 * @param {string} filePath - 文件路径
 * @returns {Object|null} 解析结果
 */
export const parseTypeFilename = (filePath) => {
  try {
    // 提取文件名（不含扩展名）
    const fileName = filePath.split('/').pop()
    const nameWithoutExt = fileName.replace(new RegExp(FORMAT_PATTERN, 'i'), '')
    const format = fileName.match(new RegExp(FORMAT_PATTERN, 'i'))?.[1]?.toLowerCase()
    
    if (!nameWithoutExt || !format) return null
    
    // 清理文件名
    const cleanName = cleanFileName(nameWithoutExt)
    
    // 生成ID和显示名
    const id = generateTypeId(cleanName)
    const { name, englishName, displayName } = parseTypeName(cleanName)
    
    return {
      id,
      name,
      englishName,
      displayName,
      format,
      originalName: nameWithoutExt
    }
  } catch (error) {
    console.warn('解析类型文件名失败:', filePath, error)
    return null
  }
}

/**
 * 智能解析品种文件名
 * @param {string} filePath - 文件路径
 * @returns {Object|null} 解析结果
 */
export const parseBreedFilename = (filePath) => {
  try {
    const fileName = filePath.split('/').pop()
    const nameWithoutExt = fileName.replace(new RegExp(FORMAT_PATTERN, 'i'), '')
    const format = fileName.match(new RegExp(FORMAT_PATTERN, 'i'))?.[1]?.toLowerCase()
    
    if (!nameWithoutExt || !format) return null
    
    // 分割类型和品种
    const parts = nameWithoutExt.split('_')
    if (parts.length < 2) return null
    
    const typeName = cleanFileName(parts[0])
    const breedName = cleanFileName(parts.slice(1).join('_'))
    
    const typeId = generateTypeId(typeName)
    const breedId = generateBreedId(breedName)
    
    return {
      id: breedId,
      name: breedName,
      typeId,
      typeName,
      format,
      originalName: nameWithoutExt
    }
  } catch (error) {
    console.warn('解析品种文件名失败:', filePath, error)
    return null
  }
}

/**
 * 生成类型ID
 * @param {string} name - 类型名称
 * @returns {string} 类型ID
 */
export const generateTypeId = (name) => {
  // 检测是否为英文
  const isEnglish = /^[a-zA-Z\s_-]+$/.test(name)
  
  if (isEnglish) {
    return name.toLowerCase().replace(/[\s_-]+/g, '_')
  } else {
    // 中文或混合，使用拼音或保持原样
    const englishPart = name.match(/[a-zA-Z]+/g)?.join('_').toLowerCase()
    const chinesePart = name.replace(/[a-zA-Z\s_-]/g, '')
    
    if (englishPart && chinesePart) {
      return `${englishPart}_${chinesePart}`
    } else if (englishPart) {
      return englishPart
    } else {
      return chinesePart
    }
  }
}

/**
 * 生成品种ID
 * @param {string} name - 品种名称
 * @returns {string} 品种ID
 */
export const generateBreedId = (name) => {
  // 简化处理，移除特殊字符并转换为小写
  return name
    .replace(/[\s_-]+/g, '_')
    .replace(/[^\w\u4e00-\u9fff_]/g, '')
    .toLowerCase()
}

/**
 * 解析类型名称
 * @param {string} name - 原始名称
 * @returns {Object} 解析后的名称信息
 */
const parseTypeName = (name) => {
  // 类型名称映射
  const typeMapping = {
    '猫': { name: '猫咪', english: 'cat', emoji: '🐱' },
    '猫咪': { name: '猫咪', english: 'cat', emoji: '🐱' },
    'cat': { name: '猫咪', english: 'cat', emoji: '🐱' },
    '狗': { name: '狗狗', english: 'dog', emoji: '🐕' },
    '狗狗': { name: '狗狗', english: 'dog', emoji: '🐕' },
    'dog': { name: '狗狗', english: 'dog', emoji: '🐕' },
    '鸟': { name: '鸟类', english: 'bird', emoji: '🐦' },
    '鸟类': { name: '鸟类', english: 'bird', emoji: '🐦' },
    'bird': { name: '鸟类', english: 'bird', emoji: '🐦' },
    '兔': { name: '兔子', english: 'rabbit', emoji: '🐰' },
    '兔子': { name: '兔子', english: 'rabbit', emoji: '🐰' },
    'rabbit': { name: '兔子', english: 'rabbit', emoji: '🐰' },
    '鱼': { name: '鱼类', english: 'fish', emoji: '🐠' },
    '鱼类': { name: '鱼类', english: 'fish', emoji: '🐠' },
    'fish': { name: '鱼类', english: 'fish', emoji: '🐠' }
  }
  
  const lowerName = name.toLowerCase()
  const mapping = typeMapping[lowerName] || typeMapping[name]
  
  if (mapping) {
    return {
      name: mapping.name,
      englishName: mapping.english,
      displayName: `${mapping.name} ${mapping.emoji}`
    }
  }
  
  // 默认处理
  return {
    name: name,
    englishName: name.toLowerCase(),
    displayName: name
  }
}

/**
 * 清理文件名
 * @param {string} fileName - 原始文件名
 * @returns {string} 清理后的文件名
 */
const cleanFileName = (fileName) => {
  return fileName
    .trim()
    .replace(/[\s_-]+/g, '_')
    .replace(/^_+|_+$/g, '')
}

/**
 * 生成品种描述
 * @param {string} breedName - 品种名称
 * @returns {string} 描述
 */
const generateDescription = (breedName) => {
  // 简单的描述生成逻辑
  const descriptions = {
    '金毛': '温和友善，适合家庭',
    '哈士奇': '精力充沛，需要大量运动',
    '布偶': '大型长毛猫，性格温顺',
    '英短': '圆脸短毛，性格温和',
    '虎皮': '小型鹦鹉，容易饲养',
    '金丝雀': '歌声优美的小鸟'
  }
  
  for (const [key, desc] of Object.entries(descriptions)) {
    if (breedName.includes(key)) {
      return desc
    }
  }
  
  return `可爱的${breedName}`
}

/**
 * 计算品种受欢迎程度
 * @param {string} breedName - 品种名称
 * @returns {number} 受欢迎程度 (1-5)
 */
const calculatePopularity = (breedName) => {
  const popularBreeds = {
    '金毛': 5,
    '布偶': 5,
    '英短': 5,
    '哈士奇': 4,
    '虎皮': 4,
    '金丝雀': 3
  }
  
  for (const [key, popularity] of Object.entries(popularBreeds)) {
    if (breedName.includes(key)) {
      return popularity
    }
  }
  
  return 3 // 默认受欢迎程度
}

/**
 * 生成品种特征
 * @param {string} breedName - 品种名称
 * @returns {Array} 特征数组
 */
const generateCharacteristics = (breedName) => {
  const characteristics = {
    '金毛': ['友善', '聪明', '易训练'],
    '哈士奇': ['活跃', '独立', '耐寒'],
    '布偶': ['温顺', '粘人', '长毛'],
    '英短': ['温和', '安静', '适合家庭'],
    '虎皮': ['活泼', '会说话', '群居'],
    '金丝雀': ['歌声美', '小巧', '温和']
  }
  
  for (const [key, chars] of Object.entries(characteristics)) {
    if (breedName.includes(key)) {
      return chars
    }
  }
  
  return ['可爱', '友好'] // 默认特征
}

/**
 * 生成完整的宠物配置
 * @returns {Object} 完整配置对象
 */
export const generatePetConfig = () => {
  try {
    const types = scanPetTypes()
    const breeds = scanPetBreeds()
    
    // 将品种分配到对应的类型
    breeds.forEach(breed => {
      const type = types.get(breed.typeId)
      if (type) {
        type.breeds.push(breed)
      } else {
        console.warn(`未找到品种 ${breed.name} 对应的类型: ${breed.typeId}`)
      }
    })
    
    // 转换为数组并排序
    const petTypes = Array.from(types.values())
      .sort((a, b) => a.name.localeCompare(b.name))
    
    // 为每个类型的品种排序
    petTypes.forEach(type => {
      type.breeds.sort((a, b) => {
        // 按受欢迎程度降序，然后按名称升序
        if (a.popularity !== b.popularity) {
          return b.popularity - a.popularity
        }
        return a.name.localeCompare(b.name)
      })
    })
    
    const config = {
      version: '2.0.0',
      lastScanTime: new Date().toISOString(),
      scanInfo: {
        totalTypes: petTypes.length,
        totalBreeds: breeds.length,
        supportedFormats: SUPPORTED_FORMATS,
        typesWithBreeds: petTypes.filter(t => t.breeds.length > 0).length,
        typesWithoutBreeds: petTypes.filter(t => t.breeds.length === 0).length
      },
      petTypes,
      metadata: {
        generatedBy: 'petConfigScanner',
        scanPath: '/src/public/avatar_type/',
        cacheKey: `pet_config_v2_${Date.now()}`,
        validUntil: Date.now() + (24 * 60 * 60 * 1000)
      }
    }
    
    return validateConfig(config)
  } catch (error) {
    console.error('生成宠物配置失败:', error)
    throw new Error(`配置生成失败: ${error.message}`)
  }
}

/**
 * 验证配置完整性
 * @param {Object} config - 配置对象
 * @returns {Object} 验证后的配置
 */
export const validateConfig = (config) => {
  const errors = []
  
  // 基本结构验证
  if (!config.petTypes || !Array.isArray(config.petTypes)) {
    errors.push('缺少 petTypes 数组')
  }
  
  // 类型验证
  config.petTypes?.forEach((type, index) => {
    if (!type.id) errors.push(`第 ${index + 1} 个类型缺少 id`)
    if (!type.name) errors.push(`第 ${index + 1} 个类型缺少 name`)
    if (!type.image) errors.push(`第 ${index + 1} 个类型缺少 image`)
    
    // 品种验证
    if (type.breeds && Array.isArray(type.breeds)) {
      type.breeds.forEach((breed, breedIndex) => {
        if (!breed.id) errors.push(`${type.name} 的第 ${breedIndex + 1} 个品种缺少 id`)
        if (!breed.name) errors.push(`${type.name} 的第 ${breedIndex + 1} 个品种缺少 name`)
        if (!breed.image) errors.push(`${type.name} 的第 ${breedIndex + 1} 个品种缺少 image`)
      })
    }
  })
  
  if (errors.length > 0) {
    console.warn('配置验证发现问题:', errors)
    config.validationErrors = errors
  }
  
  config.isValid = errors.length === 0
  return config
} 