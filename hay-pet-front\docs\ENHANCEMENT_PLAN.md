# Hay!Pet 应用重构与功能增强计划

> 基于 APP_RESTRUCTURE_PLAN.md 的重构需求，本文档详细规划了从多宠物列表管理向单宠物详细展示的架构转变。

## 当前功能分析

### 已实现功能
- ✅ 用户登录/注册（Supabase Auth）
- ✅ 宠物基本信息管理（增删查改）
- ✅ 宠物头像上传
- ✅ 体重记录管理（增删查改）
- ✅ 健康记录管理（疫苗、驱虫、医疗日志）
- ✅ 花费记录管理
- ✅ 照片相册管理
- ✅ 提醒事项管理
- ✅ 响应式设计
- ✅ 数据持久化（Supabase）

### 核心架构重构需求
- 🔄 **布局框架重构**：从列表式管理转向单宠物详细展示
- 🔄 **导航系统重构**：实现顶部导航栏 + 可收缩侧边栏布局
- 🔄 **仪表盘系统**：创建以单宠物为核心的首页总览
- 🔄 **宠物切换机制**：通过顶部导航实现宠物快速切换
- 🔄 **全局设置系统**：统一的单位转换和显示偏好管理

## 第一阶段：核心架构重构

### 1. 布局框架重构 🎯
**目标**：实现"顶部导航栏 + 可收缩侧边栏 + 主内容区"的新布局

**核心组件开发**：
- `TopNavigation.vue` - 顶部导航栏组件
  - 菜单按钮（控制侧边栏显示/隐藏）
  - 应用Logo和名称
  - 当前宠物信息展示和切换入口
  - 用户账户/设置快捷入口
- `SidebarMenu.vue` - 可收缩侧边导航栏
  - 默认隐藏状态，支持抽屉式弹出
  - 展开/收起动画效果
  - 针对当前宠物的功能模块导航
- `App.vue` 重构 - 整体应用框架
  - 集成新的布局组件
  - 响应式设计适配（桌面端/移动端）

### 2. 仪表盘系统开发 📊
**目标**：创建以单宠物为核心的首页总览，采用垂直排版展示所有功能模块

**仪表盘板块组件**：
- `PetInfoCard.vue` - 宠物信息卡片
  - 显示头像、昵称、种类、年龄、体重等核心信息
  - 快速编辑入口和宠物切换功能
- `WeightTrendCard.vue` - 体重变化趋势
  - 最近30天体重变化曲线图
  - 体重变化趋势分析和关键统计
  - 集成 Chart.js 或 ECharts
- `ExpenseOverviewCard.vue` - 花费记录概览
  - 本月总支出和最近花费记录
  - 按类别展示支出分布（饼图/条形图）
- `RemindersCard.vue` - 提醒事项
  - 今日和近期待办提醒
  - 快速标记完成功能
- `HealthSummaryCard.vue` - 健康记录摘要
  - 最近疫苗、驱虫、医疗记录
  - 下次预约或到期提醒
- `PhotoGalleryCard.vue` - 最新照片
  - 网格形式展示最近4-6张照片缩略图
  - 支持点击查看大图

### 3. 宠物切换机制 🔄
**目标**：通过顶部导航实现便捷的宠物管理和切换

**功能实现**：
- 宠物切换下拉菜单/模态框
- 宠物列表展示和选择
- "添加新宠物"快捷入口
- 首次使用引导（无宠物状态处理）
- 状态管理优化（Pinia Store）

### 4. 全局设置系统 ⚙️
**目标**：统一的单位转换和显示偏好管理

**核心功能**：
- **单位设置系统**
  - 体重单位：克(g)/千克(kg)/磅(lb)
  - 实时单位转换和显示更新
  - 全局单位转换工具函数
- **时间显示设置**
  - 日期格式：YYYY-MM-DD / MM/DD/YYYY / DD/MM/YYYY
  - 时间格式：24小时制 / 12小时制
  - 悬浮显示完整时间功能
- **主题设置**
  - 浅色/深色模式切换
  - 通知偏好设置

## 第二阶段：首页板块自定义系统

### 1. 首页板块自定义功能 🎨
**目标**：用户可以个性化定制首页显示的板块和排序

**核心功能**：
- **板块显示/隐藏控制**
  - 用户可选择首页中哪些板块显示或隐藏
  - 宠物信息卡片为必需板块（不可隐藏）
  - 其他板块均可自由控制显示状态
- **板块排序设置**
  - 支持拖拽调整首页板块的显示顺序
  - 提供上下移动按钮的替代操作方式
  - 实时预览排序效果
- **板块重置功能**
  - 提供恢复默认板块设置的选项
  - 一键重置所有自定义配置

**技术实现**：
- 使用 Pinia Store 管理板块配置状态
- localStorage 持久化存储用户偏好
- Vue 3 动态组件和 v-for 指令实现动态渲染
- 集成拖拽库（Sortable.js 或 Vue Draggable）
- Element Plus Switch 组件实现显示/隐藏控制

### 2. 批量操作功能 📋
**目标**：提高用户操作效率，支持批量管理记录

**功能范围**：
- **体重记录批量删除**
  - 多选体重记录进行批量删除
  - 批量删除确认对话框
- **健康记录批量管理**
  - 疫苗记录批量删除
  - 驱虫记录批量删除
  - 医疗日志批量删除
- **花费记录批量删除**
  - 支持按时间范围或类别批量选择
  - 批量删除统计和确认

**技术实现**：
- Element Plus Table 组件的多选功能
- 全选/反选操作支持
- 二次确认防误操作机制

### 3. 完整时间显示系统 🕐
**目标**：优化时间信息的显示和用户体验

**功能特性**：
- **默认显示优化**
  - 列表中默认显示日期（YYYY-MM-DD格式）
  - 鼠标悬浮显示完整时间（HH:mm:ss）
- **时间格式自定义**
  - 支持多种日期格式选择
  - 24小时制/12小时制切换
  - 用户偏好设置持久化

**技术实现**：
- Element Plus Tooltip 组件实现悬浮显示
- 时间格式化工具函数库
- 全局时间显示配置管理

## 第三阶段：高级功能与优化

### 1. 数据可视化增强 📈
**目标**：提供更丰富的数据分析和可视化功能

**功能扩展**：
- **高级图表功能**
  - 体重趋势多时间段对比
  - 花费统计多维度分析
  - 健康记录时间线可视化
- **数据导出功能**
  - PDF报告生成
  - Excel数据导出
  - 图表截图功能

### 2. 用户体验优化 ✨
**目标**：提升应用的整体用户体验和性能

**优化方向**：
- **性能优化**
  - 组件懒加载
  - 图片压缩和优化
  - 数据分页和虚拟滚动
- **交互优化**
  - 加载状态优化
  - 错误处理改进
  - 操作反馈增强
  - 快捷键支持
- **无障碍访问**
  - 键盘导航支持
  - 屏幕阅读器兼容
  - 高对比度模式

### 3. 高级设置功能 🔧
**目标**：提供更多个性化设置选项

**功能扩展**：
- **多语言支持**
  - 中文/英文界面切换
  - 国际化(i18n)框架集成
- **数据备份与同步**
  - 云端数据备份
  - 多设备数据同步
  - 数据导入/导出功能
- **通知系统**
  - 浏览器推送通知
  - 邮件提醒功能
  - 自定义提醒规则

## 技术栈扩展

### 核心依赖升级
- `Vue 3` + `Vite` - 继续使用现有技术栈
- `Element Plus` - 充分利用Layout、Drawer、Menu等布局组件
- `Pinia` - 状态管理，重点管理当前宠物、侧边栏状态、全局设置
- `Vue Router` - 路由管理，支持动态路由和宠物ID参数

### 新增依赖
**第一阶段**：
- `echarts` - 图表库（体重趋势、花费统计）
- `vue-echarts` - Vue ECharts 组件
- `sortablejs` - 拖拽排序功能
- `vue-draggable-next` - Vue 3 拖拽组件

**第二阶段**：
- `dayjs` - 日期处理库
- `lodash-es` - 工具函数库

**第三阶段**：
- `file-saver` - 文件下载
- `html2canvas` - 截图功能
- `jspdf` - PDF 生成
- `vue-i18n` - 国际化支持

### 代码结构重构

**组件架构**：
```
src/
├── components/
│   ├── layout/                 # 布局组件
│   │   ├── TopNavigation.vue
│   │   └── SidebarMenu.vue
│   ├── dashboard/              # 仪表盘板块组件
│   │   ├── PetInfoCard.vue
│   │   ├── WeightTrendCard.vue
│   │   ├── ExpenseOverviewCard.vue
│   │   ├── RemindersCard.vue
│   │   ├── HealthSummaryCard.vue
│   │   └── PhotoGalleryCard.vue
│   ├── settings/               # 设置相关组件
│   │   ├── DashboardBlockManager.vue
│   │   ├── UnitSettings.vue
│   │   └── TimeFormatSettings.vue
│   └── common/                 # 通用组件
│       ├── PetSwitcher.vue
│       └── BatchDeleteDialog.vue
├── stores/                     # Pinia状态管理
│   ├── pet.ts                 # 宠物状态管理
│   ├── dashboard.ts           # 仪表盘配置管理
│   ├── settings.ts            # 全局设置管理
│   └── ui.ts                  # UI状态管理（侧边栏等）
├── utils/                      # 工具函数
│   ├── settings.js            # 单位转换工具
│   ├── time.js                # 时间格式化工具
│   ├── chart.js               # 图表配置工具
│   └── export.js              # 数据导出工具
├── composables/                # 可复用逻辑
│   ├── usePetManagement.ts
│   ├── useDashboardConfig.ts
│   ├── useGlobalSettings.ts
│   └── useBatchOperations.ts
├── constants/                  # 常量定义
│   ├── dashboard.ts           # 仪表盘配置常量
│   ├── settings.ts            # 设置选项常量
│   └── units.ts               # 单位转换常量
└── types/                      # TypeScript类型定义
    ├── dashboard.ts
    ├── settings.ts
    └── pet.ts
```

**关键技术实现点**：
- **响应式侧边栏**：Element Plus Drawer + Vue 3 Transition
- **动态组件渲染**：Vue 3 动态组件 + v-for指令
- **状态持久化**：localStorage + Pinia持久化插件
- **单位转换系统**：全局工具函数 + 响应式计算属性
- **拖拽排序**：Sortable.js集成 + Vue 3组合式API

## 实施优先级

### 🔥 最高优先级（第一阶段 - 核心架构重构）
1. **布局框架重构**
   - TopNavigation.vue 顶部导航栏
   - SidebarMenu.vue 可收缩侧边栏
   - App.vue 整体框架重构

2. **仪表盘系统开发**
   - 六大核心板块组件开发
   - 数据可视化集成（ECharts）
   - 响应式布局适配

3. **宠物切换机制**
   - 宠物切换下拉菜单
   - 状态管理优化
   - 首次使用引导

4. **全局设置系统**
   - 单位转换系统
   - 时间显示设置
   - 主题设置基础

### ⚡ 高优先级（第二阶段 - 个性化功能）
1. **首页板块自定义**
   - 板块显示/隐藏控制
   - 拖拽排序功能
   - 配置持久化存储

2. **批量操作功能**
   - 多选删除机制
   - 批量操作确认
   - 用户体验优化

3. **完整时间显示**
   - 悬浮时间显示
   - 时间格式自定义
   - 全局配置管理

### 📈 中优先级（第三阶段 - 高级功能）
1. **数据可视化增强**
   - 高级图表功能
   - 数据导出功能
   - 多维度分析

2. **用户体验优化**
   - 性能优化
   - 交互优化
   - 无障碍访问

3. **高级设置功能**
   - 多语言支持
   - 数据备份同步
   - 通知系统

## 开发计划

### 第一阶段：核心架构重构（4-6周）

**Week 1-2：布局框架重构**
- 设计和实现新的布局组件
- 顶部导航栏和侧边栏开发
- 响应式设计适配
- 路由系统调整

**Week 3-4：仪表盘系统开发**
- 六大板块组件开发
- ECharts图表集成
- 数据获取和展示逻辑
- 板块间跳转功能

**Week 5-6：宠物切换与全局设置**
- 宠物切换机制实现
- 全局设置系统开发
- 单位转换功能完善
- 状态管理优化

### 第二阶段：个性化功能（3-4周）

**Week 7-8：首页板块自定义**
- 板块配置管理系统
- 拖拽排序功能实现
- 设置页面开发
- 配置持久化存储

**Week 9-10：批量操作与时间显示**
- 批量删除功能开发
- 完整时间显示系统
- 用户体验优化
- 功能测试和调试

### 第三阶段：高级功能与优化（4-5周）

**Week 11-12：数据可视化增强**
- 高级图表功能开发
- 数据导出功能实现
- 多维度数据分析

**Week 13-14：用户体验优化**
- 性能优化和代码重构
- 交互体验改进
- 无障碍访问支持

**Week 15：高级设置与发布准备**
- 多语言支持（可选）
- 最终测试和优化
- 文档更新和发布准备

## 里程碑检查点

### 🎯 里程碑1（Week 6）：核心架构完成
- ✅ 新布局框架正常运行
- ✅ 仪表盘六大板块功能完整
- ✅ 宠物切换机制流畅
- ✅ 全局设置系统可用

### 🎯 里程碑2（Week 10）：个性化功能完成
- ✅ 首页板块自定义功能完整
- ✅ 批量操作功能稳定
- ✅ 时间显示系统完善
- ✅ 用户体验显著提升

### 🎯 里程碑3（Week 15）：项目重构完成
- ✅ 所有高级功能实现
- ✅ 性能和体验优化完成
- ✅ 测试覆盖率达标
- ✅ 文档和部署就绪

## 风险评估与应对

### 技术风险
- **组件重构复杂度**：采用渐进式重构，保持功能连续性
- **状态管理复杂性**：合理设计Store结构，避免过度耦合
- **性能影响**：及时进行性能测试，优化关键路径

### 时间风险
- **功能范围控制**：优先实现核心功能，高级功能可后续迭代
- **测试时间预留**：每个阶段预留充足的测试和调试时间
- **用户反馈收集**：及时收集用户反馈，调整开发优先级