<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视图切换最终测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            margin: 0;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #303133;
            margin-bottom: 40px;
        }

        .test-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }

        .test-section h2 {
            color: #303133;
            margin-bottom: 16px;
            border-bottom: 2px solid #E4E7ED;
            padding-bottom: 8px;
        }

        .demo-area {
            background: #F5F7FA;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            display: flex;
            justify-content: center;
        }

        /* 增强的一体化视图切换样式 */
        .enhanced-view-toggle {
            position: relative;
            display: flex;
            background: #f5f7fa;
            border-radius: 8px;
            padding: 3px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .enhanced-view-toggle.small {
            padding: 2px;
        }

        .toggle-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
            border-radius: 8px;
        }

        .toggle-fill {
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
            border-radius: 5px;
            z-index: 1;
            box-shadow: 
                0 2px 8px rgba(64, 158, 255, 0.3),
                0 1px 3px rgba(64, 158, 255, 0.4);
            transform-origin: center;
        }

        .enhanced-view-toggle.small .toggle-fill {
            top: 2px;
            left: 2px;
            height: calc(100% - 4px);
        }

        .toggle-glow {
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: radial-gradient(ellipse at center, rgba(64, 158, 255, 0.4) 0%, transparent 70%);
            border-radius: 5px;
            z-index: 0;
            opacity: 0;
            filter: blur(4px);
        }

        .enhanced-view-toggle.small .toggle-glow {
            top: 2px;
            left: 2px;
            height: calc(100% - 4px);
        }

        .enhanced-toggle-btn {
            position: relative;
            z-index: 2;
            background: transparent;
            border: none;
            border-radius: 5px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            white-space: nowrap;
            font-weight: 500;
            flex: 1;
            text-align: center;
            overflow: hidden;
            transition: transform 0.2s ease;
        }

        .enhanced-view-toggle.small .enhanced-toggle-btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .enhanced-toggle-btn:hover {
            transform: translateY(-1px);
        }

        .enhanced-toggle-btn:active {
            transform: translateY(0);
        }

        .btn-content {
            position: relative;
            z-index: 3;
            color: #606266;
            transition: color 0.3s ease;
        }

        .enhanced-toggle-btn.active .btn-content {
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .btn-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            pointer-events: none;
            z-index: 1;
        }

        .enhanced-toggle-btn:hover .btn-content {
            color: #409EFF;
        }

        .enhanced-toggle-btn.active:hover .btn-content {
            color: white;
        }

        .result {
            text-align: center;
            color: #606266;
            font-size: 14px;
            margin-top: 12px;
        }

        .behavior-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .behavior-table th,
        .behavior-table td {
            border: 1px solid #E4E7ED;
            padding: 12px;
            text-align: left;
        }

        .behavior-table th {
            background: #F5F7FA;
            font-weight: 600;
        }

        .status-complete {
            color: #67C23A;
            font-weight: 600;
        }

        .status-developing {
            color: #E6A23C;
            font-weight: 600;
        }

        .mock-content {
            background: #FAFAFA;
            border-radius: 8px;
            padding: 20px;
            margin-top: 16px;
            text-align: center;
            color: #909399;
            border: 2px dashed #E4E7ED;
        }

        .mock-content.active {
            border-color: #409EFF;
            color: #409EFF;
        }

        .notification {
            background: #FDF6EC;
            border: 1px solid #FAECD8;
            border-radius: 8px;
            padding: 12px 16px;
            margin-top: 12px;
            color: #E6A23C;
            font-size: 14px;
            display: none;
        }

        .notification.show {
            display: block;
            animation: fadeInOut 3s ease-in-out;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; transform: translateY(-10px); }
            20% { opacity: 1; transform: translateY(0); }
            80% { opacity: 1; transform: translateY(0); }
            100% { opacity: 0; transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 视图切换最终测试</h1>
        
        <div class="test-section">
            <h2>期望行为说明</h2>
            <table class="behavior-table">
                <thead>
                    <tr>
                        <th>视图</th>
                        <th>切换行为</th>
                        <th>动画效果</th>
                        <th>提示消息</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>月视图</td>
                        <td>✅ 正常切换</td>
                        <td>✅ 完整动画</td>
                        <td>❌ 无提示</td>
                        <td class="status-complete">功能完整</td>
                    </tr>
                    <tr>
                        <td>周视图</td>
                        <td>✅ 允许切换</td>
                        <td>✅ 完整动画</td>
                        <td>⚠️ "功能开发中"</td>
                        <td class="status-developing">开发中</td>
                    </tr>
                    <tr>
                        <td>年视图</td>
                        <td>✅ 允许切换</td>
                        <td>✅ 完整动画</td>
                        <td>⚠️ "功能开发中"</td>
                        <td class="status-developing">开发中</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h2>日历视图切换测试</h2>
            <div class="demo-area">
                <div class="enhanced-view-toggle" id="calendarToggle" data-active="0">
                    <div class="toggle-background"></div>
                    <div class="toggle-fill" id="calendarFill"></div>
                    <div class="toggle-glow" id="calendarGlow"></div>
                    <button class="enhanced-toggle-btn active" onclick="switchCalendarView('month', 0, event)">
                        <span class="btn-content">月视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="switchCalendarView('week', 1, event)">
                        <span class="btn-content">周视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="switchCalendarView('year', 2, event)">
                        <span class="btn-content">年视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                </div>
            </div>
            <div class="result" id="calendarResult">当前选择：月视图</div>
            
            <div class="notification" id="notification">
                ⚠️ 功能开发中，敬请期待！
            </div>
            
            <!-- 模拟内容区域 -->
            <div class="mock-content" id="monthContent">
                📅 月视图内容区域 - 功能完整
            </div>
            <div class="mock-content" id="weekContent" style="display: none;">
                📊 周视图内容区域 - 功能开发中
            </div>
            <div class="mock-content" id="yearContent" style="display: none;">
                📈 年视图内容区域 - 功能开发中
            </div>
        </div>

        <div class="test-section">
            <h2>测试说明</h2>
            <ul>
                <li><strong>月视图</strong>：完全正常工作，切换时无提示消息</li>
                <li><strong>周视图</strong>：可以切换并播放动画，但会显示"功能开发中"提示</li>
                <li><strong>年视图</strong>：可以切换并播放动画，但会显示"功能开发中"提示</li>
                <li><strong>动画效果</strong>：所有视图切换都有完整的动画效果</li>
                <li><strong>用户体验</strong>：用户可以看到所有视图选项，了解功能状态</li>
            </ul>
        </div>
    </div>

    <script>
        let currentView = 'month';

        // 涟漪效果
        function createRippleEffect(button, event) {
            const ripple = button.querySelector('.btn-ripple');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;
            
            gsap.set(ripple, {
                width: size,
                height: size,
                x: x,
                y: y,
                scale: 0,
                opacity: 0.6
            });
            
            gsap.to(ripple, {
                scale: 1,
                opacity: 0,
                duration: 0.6,
                ease: "power2.out"
            });
        }

        // 更新填充位置
        function updateFillPosition(activeIndex) {
            const container = document.getElementById('calendarToggle');
            const fill = document.getElementById('calendarFill');
            const glow = document.getElementById('calendarGlow');
            
            if (!container || !fill || activeIndex < 0) return;
            
            const buttons = container.querySelectorAll('.enhanced-toggle-btn');
            if (buttons.length === 0) return;
            
            let offset = 0;
            for (let i = 0; i < activeIndex; i++) {
                offset += buttons[i].offsetWidth;
            }
            
            const activeButton = buttons[activeIndex];
            const width = activeButton.offsetWidth;
            
            // GSAP 动画：填充背景移动
            gsap.to(fill, {
                x: offset,
                width: width,
                duration: 0.4,
                ease: "power2.out"
            });
            
            // GSAP 动画：发光效果
            if (glow) {
                gsap.to(glow, {
                    x: offset,
                    width: width,
                    duration: 0.4,
                    ease: "power2.out"
                });
                
                // 发光脉冲效果
                gsap.fromTo(glow, 
                    { opacity: 0, scale: 0.8 },
                    { 
                        opacity: 0.3, 
                        scale: 1,
                        duration: 0.3,
                        ease: "power2.out",
                        yoyo: true,
                        repeat: 1
                    }
                );
            }
            
            // 按钮弹跳效果
            gsap.fromTo(activeButton,
                { scale: 0.95 },
                { 
                    scale: 1,
                    duration: 0.3,
                    ease: "back.out(1.7)"
                }
            );
            
            // 文字颜色渐变动画
            buttons.forEach((btn, index) => {
                const content = btn.querySelector('.btn-content');
                btn.classList.toggle('active', index === activeIndex);
                if (index === activeIndex) {
                    gsap.to(content, {
                        color: '#ffffff',
                        duration: 0.3,
                        ease: "power2.out"
                    });
                } else {
                    gsap.to(content, {
                        color: '#606266',
                        duration: 0.3,
                        ease: "power2.out"
                    });
                }
            });
        }

        // 显示通知
        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 切换内容显示
        function switchContent(view) {
            // 隐藏所有内容
            document.getElementById('monthContent').style.display = 'none';
            document.getElementById('weekContent').style.display = 'none';
            document.getElementById('yearContent').style.display = 'none';
            
            // 显示对应内容
            const contentElement = document.getElementById(view + 'Content');
            if (contentElement) {
                contentElement.style.display = 'block';
                
                // 为开发中的功能添加特殊样式
                if (view === 'week' || view === 'year') {
                    contentElement.classList.add('active');
                } else {
                    contentElement.classList.remove('active');
                }
            }
        }

        // 切换日历视图（修复后的逻辑）
        function switchCalendarView(newView, index, event) {
            if (newView === currentView) return;
            
            // 创建涟漪效果
            if (event && event.currentTarget) {
                createRippleEffect(event.currentTarget, event);
            }
            
            // 模拟切换动画
            setTimeout(() => {
                currentView = newView;
                
                // 检查是否为未完全实现的视图，切换后显示提示
                if (newView === 'year') {
                    showNotification('⚠️ 年视图功能开发中，敬请期待！');
                } else if (newView === 'week') {
                    showNotification('⚠️ 周视图功能开发中，敬请期待！');
                }
                
                // 更新显示
                document.getElementById('calendarResult').textContent = `当前选择：${getViewLabel(newView)}`;
                switchContent(newView);
            }, 150);
            
            // 更新填充位置
            updateFillPosition(index);
        }

        function getViewLabel(view) {
            const labels = {
                'month': '月视图',
                'week': '周视图',
                'year': '年视图'
            };
            return labels[view] || view;
        }

        // 初始化
        window.addEventListener('load', () => {
            updateFillPosition(0);
            switchContent('month');
        });
    </script>
</body>
</html>
