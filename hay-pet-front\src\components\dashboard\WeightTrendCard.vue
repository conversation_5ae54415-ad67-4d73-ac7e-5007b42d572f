<template>
  <el-card class="dashboard-card weight-trend-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">体重变化趋势</span>
        <el-button type="primary" link @click="router.push('/weight-tracking')">
          <el-icon><TrendCharts /></el-icon>
          查看详细
        </el-button>
      </div>
    </template>
    <div class="weight-trend-content">
      <div v-if="weightChartData.labels.length" class="chart-container">
        <div class="chart-container">
          <v-chart 
            class="weight-chart" 
            :option="chartOption" 
            :loading="loading"
            autoresize
          />
        </div>
        <div class="weight-stats">
          <div class="stat-item">
            <span class="stat-label">最新体重</span>
            <span class="stat-value">{{ formatWeightDisplay(latestWeight) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">变化趋势</span>
            <span class="stat-value" :class="weightTrendClass">{{ weightTrend }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">记录数量</span>
            <span class="stat-value">{{ weightRecords.length }} 条</span>
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无体重数据">
        <el-button type="primary" @click="router.push('/weight-tracking')">
          <el-icon><Plus /></el-icon>
          添加体重记录
        </el-button>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { TrendCharts, Plus } from '@element-plus/icons-vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import { usePetStore } from '@/stores/pet';
import { supabase } from '@/utils/supabase';
import { formatWeight, globalSettings } from '@/utils/settings';
import type { DashboardBlock } from '@/types/dashboard';

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
]);

interface Props {
  blockConfig: DashboardBlock;
}

defineProps<Props>();

const router = useRouter();
const petStore = usePetStore();

const weightRecords = ref<any[]>([]);
const loading = ref(false);

const currentPet = computed(() => petStore.currentPet);

const weightChartData = computed(() => {
  if (!weightRecords.value.length) {
    return { labels: [], datasets: [{ data: [] }] };
  }
  
  const sortedRecords = [...weightRecords.value]
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  return {
    labels: sortedRecords.map(record => formatDate(record.date)),
    datasets: [{
      data: sortedRecords.map(record => record.weight),
      label: `体重 (${globalSettings.weightUnit})`
    }]
  };
});

const latestWeight = computed(() => {
  if (!weightRecords.value.length) return null;
  const latest = weightRecords.value
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
  return latest?.weight_kg || null;
});

const weightTrend = computed(() => {
  if (weightRecords.value.length < 2) return '数据不足';
  
  const sorted = [...weightRecords.value]
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  const latest = sorted[sorted.length - 1]?.weight;
  const previous = sorted[sorted.length - 2]?.weight;
  
  if (!latest || !previous) return '数据不足';
  
  const diff = latest - previous;
  if (Math.abs(diff) < 0.1) return '稳定';
  return diff > 0 ? '上升' : '下降';
});

const weightTrendClass = computed(() => {
  const trend = weightTrend.value;
  if (trend === '上升') return 'trend-up';
  if (trend === '下降') return 'trend-down';
  return 'trend-stable';
});

// ECharts配置选项
const chartOption = computed(() => {
  if (!weightChartData.value.labels.length) {
    return {};
  }

  return {
    title: {
      text: '体重变化趋势',
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#409EFF',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff'
      },
      formatter: (params: any) => {
        const point = params[0];
        return `<div style="padding: 4px;">
          <div style="margin-bottom: 4px; font-weight: 600;">${point.name}</div>
          <div style="color: #409EFF;">● ${point.seriesName}: ${formatWeightDisplay(point.value)}</div>
        </div>`;
      }
    },
    grid: {
      left: '8%',
      right: '5%',
      bottom: '15%',
      top: '20%',
      containLabel: false
    },
    xAxis: {
      type: 'category',
      data: weightChartData.value.labels,
      axisLine: {
        lineStyle: {
          color: '#E4E7ED'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 12,
        color: '#909399',
        margin: 10
      }
    },
    yAxis: {
      type: 'value',
      name: `体重 (${globalSettings.weightUnit})`,
      nameLocation: 'middle',
      nameGap: 40,
      nameTextStyle: {
        fontSize: 12,
        color: '#606266',
        fontWeight: '500'
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#F2F6FC',
          type: 'dashed'
        }
      },
      axisLabel: {
        fontSize: 12,
        color: '#909399',
        formatter: (value: number) => formatWeightDisplay(value)
      }
    },
    series: [
      {
        name: `体重 (${globalSettings.weightUnit})`,
        type: 'line',
        data: weightChartData.value.datasets[0].data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 4,
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#409EFF' },
              { offset: 1, color: '#67C23A' }
            ]
          },
          shadowColor: 'rgba(64, 158, 255, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 3
        },
        itemStyle: {
          color: '#409EFF',
          borderColor: '#ffffff',
          borderWidth: 3,
          shadowColor: 'rgba(64, 158, 255, 0.4)',
          shadowBlur: 8
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(64, 158, 255, 0.25)'
              },
              {
                offset: 0.5,
                color: 'rgba(103, 194, 58, 0.15)'
              },
              {
                offset: 1,
                color: 'rgba(64, 158, 255, 0.02)'
              }
            ]
          }
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(64, 158, 255, 0.6)'
          }
        },
        animationDuration: 2000,
        animationEasing: 'cubicOut'
      }
    ]
  };
});

async function fetchWeightRecords() {
  if (!currentPet.value?.id) return;
  
  loading.value = true;
  try {
    const { data, error } = await supabase
      .from('weight_records')
      .select('*')
      .eq('pet_id', currentPet.value.id)
      .order('date', { ascending: false })
      .limit(10);
    
    if (error) throw error;
    weightRecords.value = data || [];
  } catch (error) {
    console.error('获取体重记录失败:', error);
  } finally {
    loading.value = false;
  }
}

function formatWeightDisplay(weight: number | null): string {
  if (!weight) return '未记录';
  return formatWeight(weight);
}

function formatDate(date: string): string {
  return new Date(date).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  });
}

onMounted(() => {
  fetchWeightRecords();
});

// 监听当前宠物变化
watch(currentPet, (newPet) => {
  if (newPet) {
    fetchWeightRecords();
  } else {
    weightRecords.value = [];
  }
}, { immediate: true });
</script>

<style scoped>
.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.weight-trend-content {
  padding: 0;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.weight-chart {
  height: 320px;
  width: 100%;
  border-radius: 8px;
}

.weight-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-top: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px 12px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.stat-item:hover::before {
  opacity: 1;
}

.stat-label {
  display: block;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-bottom: 6px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1.2;
}

.trend-up {
  color: var(--el-color-danger);
}

.trend-down {
  color: var(--el-color-success);
}

.trend-stable {
  color: var(--el-color-info);
}

@media (max-width: 768px) {
  .weight-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .weight-chart {
    height: 260px;
  }
  
  .stat-item {
    padding: 12px;
  }
  
  .stat-value {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .weight-chart {
    height: 220px;
  }
  
  .chart-container {
    gap: 12px;
  }
  
  .stat-item {
    padding: 10px;
  }
  
  .stat-label {
    font-size: 12px;
  }
  
  .stat-value {
    font-size: 14px;
  }
}
</style>