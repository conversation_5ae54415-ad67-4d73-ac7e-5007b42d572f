<template>
  <div class="reminder-search-panel">
    <el-card shadow="hover">
      <div class="search-panel-content">
        <!-- 搜索栏 -->
        <div class="search-section">
          <div class="search-bar">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索提醒标题或内容..."
              clearable
              class="search-input"
              @input="handleSearchChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button
              type="primary"
              link
              @click="showAdvancedFilters = !showAdvancedFilters"
              class="advanced-filter-btn"
            >
              <el-icon>
                <component :is="showAdvancedFilters ? 'ArrowUp' : 'ArrowDown'" />
              </el-icon>
              高级筛选
            </el-button>
          </div>
        </div>

        <!-- 高级筛选面板 -->
        <div v-show="showAdvancedFilters" class="advanced-filters">
          <!-- 状态筛选 -->
          <div class="filter-row">
            <span class="filter-label">状态筛选：</span>
            <el-radio-group v-model="statusFilter" size="small" @change="handleFilterChange">
              <el-radio-button value="all">全部</el-radio-button>
              <el-radio-button value="pending">待处理</el-radio-button>
              <el-radio-button value="completed">已完成</el-radio-button>
              <el-radio-button value="overdue">已逾期</el-radio-button>
              <el-radio-button value="urgent">紧急</el-radio-button>
            </el-radio-group>
          </div>

          <!-- 优先级筛选 -->
          <div class="filter-row">
            <span class="filter-label">优先级：</span>
            <el-checkbox-group v-model="selectedPriorities" @change="handleFilterChange">
              <el-checkbox value="高" border size="small">高优先级</el-checkbox>
              <el-checkbox value="中" border size="small">中优先级</el-checkbox>
              <el-checkbox value="低" border size="small">低优先级</el-checkbox>
            </el-checkbox-group>
          </div>

          <!-- 日期范围筛选 -->
          <div class="filter-row">
            <span class="filter-label">日期范围：</span>
            <el-date-picker
              v-model="dateRangeFilter"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              size="small"
              clearable
              @change="handleFilterChange"
            />
          </div>

          <!-- 快速筛选按钮 -->
          <div class="filter-row">
            <span class="filter-label">快速筛选：</span>
            <div class="quick-filters">
              <el-button size="small" @click="clearAllFilters">清除筛选</el-button>
              <el-button size="small" type="primary" @click="setTodayFilter">今天</el-button>
              <el-button size="small" type="primary" @click="setThisWeekFilter">本周</el-button>
              <el-button size="small" type="primary" @click="setThisMonthFilter">本月</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Search, ArrowUp, ArrowDown } from '@element-plus/icons-vue';

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      searchKeyword: '',
      statusFilter: 'all',
      selectedPriorities: [],
      dateRangeFilter: []
    })
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'filter-change']);

// 响应式数据
const searchKeyword = ref(props.modelValue.searchKeyword || '');
const statusFilter = ref(props.modelValue.statusFilter || 'all');
const selectedPriorities = ref(props.modelValue.selectedPriorities || []);
const dateRangeFilter = ref(props.modelValue.dateRangeFilter || []);
const showAdvancedFilters = ref(false);

// 工具函数
const getWeekStart = (date) => {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day;
  const weekStart = new Date(d.setDate(diff));
  weekStart.setHours(0, 0, 0, 0);
  return weekStart;
};

// 事件处理方法
const handleSearchChange = () => {
  emitFilterChange();
};

const handleFilterChange = () => {
  emitFilterChange();
};

const emitFilterChange = () => {
  const filterData = {
    searchKeyword: searchKeyword.value,
    statusFilter: statusFilter.value,
    selectedPriorities: selectedPriorities.value,
    dateRangeFilter: dateRangeFilter.value
  };
  
  emit('update:modelValue', filterData);
  emit('filter-change', filterData);
};

// 筛选相关方法
const clearAllFilters = () => {
  searchKeyword.value = '';
  selectedPriorities.value = [];
  dateRangeFilter.value = [];
  statusFilter.value = 'all';
  emitFilterChange();
};

const setTodayFilter = () => {
  const today = new Date().toISOString().split('T')[0];
  dateRangeFilter.value = [today, today];
  emitFilterChange();
};

const setThisWeekFilter = () => {
  const today = new Date();
  const weekStart = getWeekStart(today);
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekEnd.getDate() + 6);

  dateRangeFilter.value = [
    weekStart.toISOString().split('T')[0],
    weekEnd.toISOString().split('T')[0]
  ];
  emitFilterChange();
};

const setThisMonthFilter = () => {
  const today = new Date();
  const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
  const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  dateRangeFilter.value = [
    monthStart.toISOString().split('T')[0],
    monthEnd.toISOString().split('T')[0]
  ];
  emitFilterChange();
};

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  searchKeyword.value = newValue.searchKeyword || '';
  statusFilter.value = newValue.statusFilter || 'all';
  selectedPriorities.value = newValue.selectedPriorities || [];
  dateRangeFilter.value = newValue.dateRangeFilter || [];
}, { deep: true });
</script>

<style scoped>
.reminder-search-panel {
  margin-bottom: 24px;
}

.search-panel-content {
  padding: 20px;
}

.search-section {
  margin-bottom: 16px;
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-input {
  flex: 1;
  min-width: 300px;
}

.advanced-filter-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409EFF;
  font-weight: 500;
}

.advanced-filter-btn:hover {
  color: #66b1ff;
}

.advanced-filters {
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
  margin-top: 16px;
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  min-width: 80px;
  flex-shrink: 0;
}

.quick-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    min-width: auto;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .filter-label {
    min-width: auto;
  }
  
  .quick-filters {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
