<template>
  <div class="simple-view-toggle-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🔄 Simple ViewToggle 一体化视图切换</h1>
        <p class="page-description">
          一体化设计的视图切换组件，带有流畅的填充变形动画，简洁而优雅
        </p>
        <router-link to="/style_demo" class="back-link">
          ← 返回样式系统首页
        </router-link>
      </div>

      <!-- 基础用法 -->
      <div class="demo-section">
        <h2>📋 基础用法</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>标准视图切换</h3>
            <div class="demo-area">
              <div class="simple-view-toggle" :data-active="basicActiveIndex">
                <button
                  v-for="(option, index) in basicOptions"
                  :key="option.value"
                  @click="setBasicActive(index)"
                  :class="['simple-toggle-btn', { active: basicActiveIndex === index }]"
                >
                  {{ option.label }}
                </button>
              </div>
              <div class="result-display">
                当前选择：{{ basicOptions[basicActiveIndex]?.label }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 尺寸变体 -->
      <div class="demo-section">
        <h2>📏 尺寸变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>标准尺寸</h3>
            <div class="demo-area">
              <div class="simple-view-toggle" :data-active="standardActiveIndex">
                <button
                  v-for="(option, index) in sizeOptions"
                  :key="option.value"
                  @click="setStandardActive(index)"
                  :class="['simple-toggle-btn', { active: standardActiveIndex === index }]"
                >
                  {{ option.label }}
                </button>
              </div>
            </div>
          </div>

          <div class="showcase-item">
            <h3>小号尺寸</h3>
            <div class="demo-area">
              <div class="simple-view-toggle small" :data-active="smallActiveIndex">
                <button
                  v-for="(option, index) in sizeOptions"
                  :key="option.value"
                  @click="setSmallActive(index)"
                  :class="['simple-toggle-btn', { active: smallActiveIndex === index }]"
                >
                  {{ option.label }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实际应用示例 -->
      <div class="demo-section">
        <h2>💼 实际应用示例</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>健康记录页面 - 日历视图切换</h3>
            <div class="demo-area">
              <div class="application-demo">
                <div class="calendar-header-demo">
                  <div class="calendar-nav-demo">
                    <button class="nav-btn">‹</button>
                    <span class="date-display">2024年6月</span>
                    <button class="nav-btn">›</button>
                  </div>
                  <div class="simple-view-toggle" :data-active="calendarActiveIndex">
                    <button
                      v-for="(option, index) in calendarOptions"
                      :key="option.value"
                      @click="setCalendarActive(index)"
                      :class="['simple-toggle-btn', { active: calendarActiveIndex === index }]"
                    >
                      {{ option.label }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="showcase-item">
            <h3>记录展示 - 视图模式切换</h3>
            <div class="demo-area">
              <div class="application-demo">
                <div class="filter-header-demo">
                  <span class="filter-label">记录类型：</span>
                  <div class="simple-view-toggle small" :data-active="recordsActiveIndex">
                    <button
                      v-for="(option, index) in recordsOptions"
                      :key="option.value"
                      @click="setRecordsActive(index)"
                      :class="['simple-toggle-btn', { active: recordsActiveIndex === index }]"
                    >
                      {{ option.label }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用指南 -->
      <div class="demo-section">
        <h2>📖 使用指南</h2>
        <div class="usage-guide">
          <div class="guide-item">
            <h3>1. HTML 结构</h3>
            <pre><code>&lt;div class="simple-view-toggle" data-active="0"&gt;
  &lt;button class="simple-toggle-btn active"&gt;选项1&lt;/button&gt;
  &lt;button class="simple-toggle-btn"&gt;选项2&lt;/button&gt;
  &lt;button class="simple-toggle-btn"&gt;选项3&lt;/button&gt;
&lt;/div&gt;</code></pre>
          </div>

          <div class="guide-item">
            <h3>2. Vue 组件用法</h3>
            <pre><code>&lt;div class="simple-view-toggle" :data-active="activeIndex"&gt;
  &lt;button
    v-for="(option, index) in options"
    :key="option.value"
    @click="setActive(index)"
    :class="['simple-toggle-btn', { active: activeIndex === index }]"
  &gt;
    {{ option.label }}
  &lt;/button&gt;
&lt;/div&gt;</code></pre>
          </div>
          
          <div class="guide-item">
            <h3>3. 小号尺寸</h3>
            <pre><code>&lt;div class="simple-view-toggle small"&gt;
  &lt;!-- 按钮内容 --&gt;
&lt;/div&gt;</code></pre>
          </div>
          
          <div class="guide-item">
            <h3>4. CSS 样式</h3>
            <pre><code>.simple-view-toggle {
  position: relative;
  display: flex;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 3px;
  overflow: hidden;
}

/* 动态填充背景 */
.simple-view-toggle::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  height: calc(100% - 6px);
  background: #409EFF;
  border-radius: 5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.simple-toggle-btn {
  position: relative;
  z-index: 2;
  background: transparent;
  border: none;
  padding: 8px 16px;
  color: #606266;
  cursor: pointer;
  transition: color 0.3s ease;
  flex: 1;
  text-align: center;
}

.simple-toggle-btn.active {
  color: white;
}

/* 填充位置控制 */
.simple-view-toggle[data-active="0"]::before {
  width: 33.33%;
  transform: translateX(0);
}

.simple-view-toggle[data-active="1"]::before {
  width: 33.33%;
  transform: translateX(100%);
}

.simple-view-toggle[data-active="2"]::before {
  width: 33.33%;
  transform: translateX(200%);
}</code></pre>
          </div>
        </div>
      </div>

      <!-- 设计原则 -->
      <div class="demo-section">
        <h2>🎯 设计原则</h2>
        <div class="principles-grid">
          <div class="principle-card">
            <div class="principle-icon">🎯</div>
            <h3>一体化设计</h3>
            <p>三个按钮融为一体，通过填充背景突出当前选择</p>
          </div>
          <div class="principle-card">
            <div class="principle-icon">🌊</div>
            <h3>流畅动画</h3>
            <p>填充背景的变形切换动画，提供清晰的视觉反馈</p>
          </div>
          <div class="principle-card">
            <div class="principle-icon">⚡</div>
            <h3>性能优化</h3>
            <p>使用CSS transform实现动画，硬件加速，流畅响应</p>
          </div>
          <div class="principle-card">
            <div class="principle-icon">🎨</div>
            <h3>视觉协调</h3>
            <p>与现有页面风格完美融合，简洁而不失优雅</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 基础选项
const basicOptions = [
  { value: 'list', label: '列表' },
  { value: 'grid', label: '网格' },
  { value: 'card', label: '卡片' }
]
const basicActiveIndex = ref(0)
const setBasicActive = (index) => { basicActiveIndex.value = index }

// 尺寸选项
const sizeOptions = [
  { value: 'view1', label: '视图1' },
  { value: 'view2', label: '视图2' },
  { value: 'view3', label: '视图3' }
]
const standardActiveIndex = ref(0)
const smallActiveIndex = ref(1)
const setStandardActive = (index) => { standardActiveIndex.value = index }
const setSmallActive = (index) => { smallActiveIndex.value = index }

// 应用示例选项
const calendarOptions = [
  { value: 'month', label: '月视图' },
  { value: 'week', label: '周视图' },
  { value: 'year', label: '年视图' }
]
const calendarActiveIndex = ref(0)
const setCalendarActive = (index) => { calendarActiveIndex.value = index }

const recordsOptions = [
  { value: 'cards', label: '卡片' },
  { value: 'timeline', label: '时间线' },
  { value: 'table', label: '表格' }
]
const recordsActiveIndex = ref(0)
const setRecordsActive = (index) => { recordsActiveIndex.value = index }
</script>

<style scoped>
.simple-view-toggle-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 18px;
  color: #909399;
  max-width: 800px;
  margin: 0 auto 16px;
  line-height: 1.6;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #409EFF;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #337ECC;
}

/* 演示区块 */
.demo-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.demo-section h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 20px;
  border-bottom: 2px solid #E4E7ED;
  padding-bottom: 8px;
}

.showcase-item h3 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 16px;
}

.demo-area {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 12px;
}

.result-display {
  margin-top: 16px;
  padding: 12px;
  background: #FAFAFA;
  border-radius: 8px;
  color: #606266;
  font-size: 14px;
}

/* 一体化视图切换样式 */
.simple-view-toggle {
  position: relative;
  display: flex;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 3px;
  overflow: hidden;
}

.simple-view-toggle.small {
  padding: 2px;
}

/* 动态填充背景 */
.simple-view-toggle::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  height: calc(100% - 6px);
  background: #409EFF;
  border-radius: 5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  box-shadow: 0 1px 3px rgba(64, 158, 255, 0.3);
}

.simple-view-toggle.small::before {
  top: 2px;
  left: 2px;
  height: calc(100% - 4px);
}

.simple-toggle-btn {
  position: relative;
  z-index: 2;
  background: transparent;
  border: none;
  border-radius: 5px;
  padding: 8px 16px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  font-weight: 500;
  flex: 1;
  text-align: center;
}

.simple-view-toggle.small .simple-toggle-btn {
  padding: 6px 12px;
  font-size: 12px;
}

.simple-toggle-btn:hover {
  color: #409EFF;
}

.simple-toggle-btn.active {
  color: white;
}

.simple-toggle-btn.active:hover {
  color: white;
}

/* 动态计算填充位置 */
.simple-view-toggle[data-active="0"]::before {
  width: 33.33%;
  transform: translateX(0);
}

.simple-view-toggle[data-active="1"]::before {
  width: 33.33%;
  transform: translateX(100%);
}

.simple-view-toggle[data-active="2"]::before {
  width: 33.33%;
  transform: translateX(200%);
}

/* 应用演示 */
.application-demo {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #E4E7ED;
}

.calendar-header-demo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.calendar-nav-demo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #F5F7FA;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: #E4E7ED;
  color: #303133;
}

.date-display {
  font-weight: 500;
  color: #303133;
  min-width: 100px;
  text-align: center;
}

.filter-header-demo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.filter-label {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

/* 使用指南 */
.usage-guide {
  display: grid;
  gap: 24px;
}

.guide-item {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
}

.guide-item h3 {
  color: #303133;
  margin-bottom: 12px;
}

.guide-item pre {
  background: #FAFAFA;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 13px;
  line-height: 1.5;
}

.guide-item code {
  color: #409EFF;
}

/* 设计原则 */
.principles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.principle-card {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.principle-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.principle-card h3 {
  color: #303133;
  margin-bottom: 8px;
}

.principle-card p {
  color: #909399;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-view-toggle-demo {
    padding: 16px;
  }
  
  .calendar-header-demo {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-header-demo {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .principles-grid {
    grid-template-columns: 1fr;
  }
}
</style>
