<template>
  <div class="page-header-demo">
    <div class="demo-container">
      <!-- 演示页面标题 -->
      <div class="demo-title">
        <h1>📋 PageHeaderBar 页面标题栏组件</h1>
        <p>基于事件记录页面设计的统一页面标题栏组件，支持多种样式变体和自定义配置</p>
      </div>

      <!-- 基础用法 -->
      <div class="demo-section">
        <h2>🎯 基础用法</h2>
        <div class="demo-card">
          <div class="demo-preview">
            <PageHeaderBar
              title="事件记录"
              add-button-text="添加事件记录"
              @add-click="handleAddClick"
            />
          </div>
          <div class="demo-code">
            <h3>代码示例</h3>
            <pre><code>&lt;PageHeaderBar
  title="事件记录"
  add-button-text="添加事件记录"
  @add-click="handleAddClick"
/&gt;</code></pre>
          </div>
        </div>
      </div>

      <!-- 样式变体 -->
      <div class="demo-section">
        <h2>🎨 样式变体</h2>
        
        <!-- 默认样式 -->
        <div class="demo-card">
          <h3>默认样式 (default)</h3>
          <div class="demo-preview">
            <PageHeaderBar
              title="提醒事项"
              subtitle="管理您的宠物提醒"
              add-button-text="添加提醒"
              variant="default"
              @add-click="handleAddClick"
            />
          </div>
        </div>

        <!-- 紧凑样式 -->
        <div class="demo-card">
          <h3>紧凑样式 (compact)</h3>
          <div class="demo-preview">
            <PageHeaderBar
              title="照片相册"
              add-button-text="上传照片"
              variant="compact"
              @add-click="handleAddClick"
            />
          </div>
        </div>

        <!-- 最小化样式 -->
        <div class="demo-card">
          <h3>最小化样式 (minimal)</h3>
          <div class="demo-preview">
            <PageHeaderBar
              title="体重追踪"
              add-button-text="记录体重"
              variant="minimal"
              @add-click="handleAddClick"
            />
          </div>
        </div>
      </div>

      <!-- 主题色变体 -->
      <div class="demo-section">
        <h2>🌈 主题色变体</h2>
        
        <div class="demo-card">
          <h3>成功主题 (success)</h3>
          <div class="demo-preview">
            <PageHeaderBar
              title="健康记录"
              add-button-text="添加记录"
              header-class="page-header--success"
              @add-click="handleAddClick"
            />
          </div>
        </div>

        <div class="demo-card">
          <h3>警告主题 (warning)</h3>
          <div class="demo-preview">
            <PageHeaderBar
              title="费用追踪"
              add-button-text="添加费用"
              header-class="page-header--warning"
              @add-click="handleAddClick"
            />
          </div>
        </div>

        <div class="demo-card">
          <h3>危险主题 (danger)</h3>
          <div class="demo-preview">
            <PageHeaderBar
              title="紧急联系"
              add-button-text="添加联系人"
              header-class="page-header--danger"
              @add-click="handleAddClick"
            />
          </div>
        </div>
      </div>

      <!-- 自定义插槽 -->
      <div class="demo-section">
        <h2>🔧 自定义插槽</h2>
        
        <div class="demo-card">
          <h3>自定义标题和操作按钮</h3>
          <div class="demo-preview">
            <PageHeaderBar :show-add-button="false">
              <template #title>
                <span style="color: #E6A23C;">📊 数据统计</span>
              </template>
              <template #subtitle>
                <span style="color: #909399;">查看详细的数据分析报告</span>
              </template>
              <template #actions>
                <el-button type="success" size="large">
                  <el-icon><Download /></el-icon>
                  导出数据
                </el-button>
                <el-button type="primary" size="large">
                  <el-icon><Plus /></el-icon>
                  添加数据
                </el-button>
              </template>
            </PageHeaderBar>
          </div>
          <div class="demo-code">
            <h3>代码示例</h3>
            <pre><code>&lt;PageHeaderBar :show-add-button="false"&gt;
  &lt;template #title&gt;
    &lt;span style="color: #E6A23C;"&gt;📊 数据统计&lt;/span&gt;
  &lt;/template&gt;
  &lt;template #subtitle&gt;
    &lt;span&gt;查看详细的数据分析报告&lt;/span&gt;
  &lt;/template&gt;
  &lt;template #actions&gt;
    &lt;el-button type="success"&gt;导出数据&lt;/el-button&gt;
    &lt;el-button type="primary"&gt;添加数据&lt;/el-button&gt;
  &lt;/template&gt;
&lt;/PageHeaderBar&gt;</code></pre>
          </div>
        </div>
      </div>

      <!-- 属性说明 -->
      <div class="demo-section">
        <h2>📖 属性说明</h2>
        <div class="demo-card">
          <div class="props-table">
            <table>
              <thead>
                <tr>
                  <th>属性名</th>
                  <th>类型</th>
                  <th>默认值</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>title</td>
                  <td>String</td>
                  <td>''</td>
                  <td>页面标题文字</td>
                </tr>
                <tr>
                  <td>subtitle</td>
                  <td>String</td>
                  <td>''</td>
                  <td>页面副标题文字</td>
                </tr>
                <tr>
                  <td>showAddButton</td>
                  <td>Boolean</td>
                  <td>true</td>
                  <td>是否显示默认添加按钮</td>
                </tr>
                <tr>
                  <td>addButtonText</td>
                  <td>String</td>
                  <td>'添加'</td>
                  <td>添加按钮文字</td>
                </tr>
                <tr>
                  <td>addIcon</td>
                  <td>String|Object</td>
                  <td>Plus</td>
                  <td>添加按钮图标</td>
                </tr>
                <tr>
                  <td>variant</td>
                  <td>String</td>
                  <td>'default'</td>
                  <td>样式变体：default|compact|minimal</td>
                </tr>
                <tr>
                  <td>headerClass</td>
                  <td>String|Array|Object</td>
                  <td>''</td>
                  <td>自定义头部样式类</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 事件说明 -->
      <div class="demo-section">
        <h2>⚡ 事件说明</h2>
        <div class="demo-card">
          <div class="events-table">
            <table>
              <thead>
                <tr>
                  <th>事件名</th>
                  <th>说明</th>
                  <th>回调参数</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>add-click</td>
                  <td>点击添加按钮时触发</td>
                  <td>-</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 插槽说明 -->
      <div class="demo-section">
        <h2>🎰 插槽说明</h2>
        <div class="demo-card">
          <div class="slots-table">
            <table>
              <thead>
                <tr>
                  <th>插槽名</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>title</td>
                  <td>自定义标题内容</td>
                </tr>
                <tr>
                  <td>subtitle</td>
                  <td>自定义副标题内容</td>
                </tr>
                <tr>
                  <td>actions</td>
                  <td>自定义操作按钮区域</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { Plus, Download } from '@element-plus/icons-vue';
import PageHeaderBar from '@/components/common/PageHeaderBar.vue';

const handleAddClick = () => {
  ElMessage.success('添加按钮被点击了！');
};
</script>

<style scoped>
@import '@/styles/demo-common.css';

.demo-preview {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.props-table table,
.events-table table,
.slots-table table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
}

.props-table th,
.props-table td,
.events-table th,
.events-table td,
.slots-table th,
.slots-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #EBEEF5;
}

.props-table th,
.events-table th,
.slots-table th {
  background: #F5F7FA;
  font-weight: 600;
  color: #303133;
}

.props-table td:first-child,
.events-table td:first-child,
.slots-table td:first-child {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #E6A23C;
  font-weight: 600;
}
</style>
