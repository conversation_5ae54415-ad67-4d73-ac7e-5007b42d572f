import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { MotionPlugin } from '@vueuse/motion'
import './style.css'
import App from './App.vue'
import router from './router' // 导入路由
import { initSettings, globalSettings } from './utils/settings' // 导入设置初始化函数和全局设置
import { supabase } from './utils/supabase' // 导入统一的supabase实例

// 初始化全局设置
initSettings()

// 全局设置事件监听器为 passive，解决滚轮事件警告
if (typeof window !== 'undefined') {
  // 重写 addEventListener 以默认添加 passive 选项到滚轮事件
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'wheel' || type === 'mousewheel' || type === 'touchmove') {
      if (typeof options === 'boolean') {
        options = { capture: options, passive: true };
      } else if (typeof options === 'object' && options !== null) {
        options = { ...options, passive: true };
      } else {
        options = { passive: true };
      }
    }
    return originalAddEventListener.call(this, type, listener, options);
  };
}

const app = createApp(App)

// 创建并使用Pinia
const pinia = createPinia()
app.use(pinia)

// 注册Element Plus
app.use(ElementPlus, {
  locale: zhCn,
})

// 注册Motion插件
app.use(MotionPlugin)

// 全局注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 提供Supabase实例给所有组件
app.provide('supabase', supabase)

// 提供全局设置给所有组件
app.provide('globalSettings', globalSettings)

app.use(router) // 使用路由

app.mount('#app')