# Hay!Pet - 宠物成长记录应用

一个基于Vue 3和Supabase的宠物信息记录和成长追踪Web应用。

## 功能特性

- 🐾 宠物信息管理（添加、查看、编辑、删除）
- 📊 体重记录和追踪
- 📸 宠物头像上传
- 👤 用户认证（注册/登录）
- 📱 响应式设计，支持移动端

## 技术栈

- **前端框架**: Vue 3 + Vite
- **UI组件库**: Element Plus
- **后端服务**: Supabase（认证 + 数据库 + 存储）
- **开发语言**: JavaScript

## 快速开始

### 1. 环境要求

- Node.js 18+
- npm 9+

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

1. 复制环境变量模板文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入你的Supabase配置：
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Supabase数据库设置

在Supabase控制台中创建以下数据表：

#### pets表
```sql
CREATE TABLE pets (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(50) NOT NULL,
  species VARCHAR(50) NOT NULL,
  age INTEGER NOT NULL,
  gender VARCHAR(10) DEFAULT 'unknown',
  avatar_url TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用行级安全策略
ALTER TABLE pets ENABLE ROW LEVEL SECURITY;

-- 创建策略：用户只能访问自己的宠物
CREATE POLICY "Users can only access their own pets" ON pets
  FOR ALL USING (auth.uid() = user_id);
```

#### weights表
```sql
CREATE TABLE weights (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  pet_id UUID REFERENCES pets(id) ON DELETE CASCADE,
  weight DECIMAL(5,2) NOT NULL,
  record_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用行级安全策略
ALTER TABLE weights ENABLE ROW LEVEL SECURITY;

-- 创建策略：用户只能访问自己宠物的体重记录
CREATE POLICY "Users can only access their pets' weights" ON weights
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM pets 
      WHERE pets.id = weights.pet_id 
      AND pets.user_id = auth.uid()
    )
  );
```

### 5. 存储桶设置

在Supabase存储中创建名为 `pet-media` 的存储桶，并设置为公开访问。

### 6. 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:5173` 启动。

### 7. 构建生产版本

```bash
npm run build
```

构建文件将生成在 `dist` 目录中。

## 项目结构

```
src/
├── components/          # Vue组件
│   ├── Login.vue       # 登录组件
│   ├── PetForm.vue     # 宠物信息表单
│   └── PetList.vue     # 宠物列表
├── App.vue             # 主应用组件
└── main.js             # 应用入口
```

## 开发指南

### 添加新功能

1. 在 `src/components/` 目录下创建新的Vue组件
2. 在需要的地方导入并使用组件
3. 如需新的数据表，在Supabase中创建并设置相应的RLS策略

### 测试

```bash
# 运行单元测试
npm run test

# 运行测试并生成覆盖率报告
npm run test -- --coverage
```

## 部署

### GitHub Pages部署

1. 构建项目：
```bash
npm run build
```

2. 将 `dist` 目录内容推送到GitHub仓库的 `gh-pages` 分支

3. 在GitHub仓库设置中启用Pages功能

## 注意事项

- 确保Supabase项目的RLS（行级安全）策略正确配置
- 生产环境中请使用HTTPS
- 定期备份Supabase数据
- 图片上传有大小限制（Supabase免费版500MB存储）

## 许可证

MIT License