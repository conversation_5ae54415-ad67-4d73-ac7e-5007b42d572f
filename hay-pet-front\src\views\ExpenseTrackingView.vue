<template>
  <div class="expense-tracking-view">
    <div v-if="!currentPetId" class="no-pet-selected">
      <el-empty description="请先在左侧选择一个宠物以查看或记录花费"></el-empty>
    </div>
    <div v-else>
      <!-- 页面标题和操作按钮 - 使用预设组件 -->
      <PageHeaderBar
        title="花费追踪"
        subtitle="管理您的宠物花费记录"
        add-button-text="添加花费记录"
        header-class="page-header--warning"
        @add-click="openAddDialog"
      />



      <!-- 统计信息卡片 - 移到最上面 -->
      <div class="stats-section">
        <div class="stats-grid">
          <!-- 总花费卡片 -->
          <el-card class="stat-card total-expense" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">总花费</div>
                <div v-if="loading" class="stat-skeleton">
                  <el-skeleton animated>
                    <template #template>
                      <el-skeleton-item variant="text" style="width: 60%; height: 24px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="text" style="width: 40%; height: 16px;" />
                    </template>
                  </el-skeleton>
                </div>
                <template v-else>
                  <div class="stat-value">{{ formatCurrency(totalExpenses) }}</div>
                  <div class="stat-date">全部记录</div>
                </template>
              </div>
            </div>
          </el-card>

          <!-- 平均花费卡片 -->
          <el-card class="stat-card average-expense" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">平均花费</div>
                <div v-if="loading" class="stat-skeleton">
                  <el-skeleton animated>
                    <template #template>
                      <el-skeleton-item variant="text" style="width: 50%; height: 24px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="text" style="width: 70%; height: 16px;" />
                    </template>
                  </el-skeleton>
                </div>
                <template v-else>
                  <div class="stat-value">{{ formatCurrency(averageExpense) }}</div>
                  <div class="stat-date">每条记录</div>
                </template>
              </div>
            </div>
          </el-card>

          <!-- 最高花费卡片 -->
          <el-card class="stat-card highest-expense" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">最高花费</div>
                <div v-if="loading" class="stat-skeleton">
                  <el-skeleton animated>
                    <template #template>
                      <el-skeleton-item variant="text" style="width: 80%; height: 24px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="text" style="width: 60%; height: 16px;" />
                    </template>
                  </el-skeleton>
                </div>
                <template v-else>
                  <div class="stat-value">{{ formatCurrency(highestExpense) }}</div>
                  <div class="stat-date">单笔最高</div>
                </template>
              </div>
            </div>
          </el-card>

          <!-- 当前时间段总消费卡片 - 可点击切换时间维度 -->
          <el-card
            class="stat-card current-period-expense clickable-stat-card"
            shadow="hover"
            @click="cyclePeriodDimension"
          >
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Calendar /></el-icon>
                <div class="cycle-indicator">
                  <el-icon><RefreshRight /></el-icon>
                </div>
              </div>
              <div class="stat-info">
                <div class="stat-label">{{ currentPeriodStatTitle }}</div>
                <div v-if="loading" class="stat-skeleton">
                  <el-skeleton animated>
                    <template #template>
                      <el-skeleton-item variant="text" style="width: 70%; height: 24px; margin-bottom: 8px;" />
                      <el-skeleton-item variant="text" style="width: 50%; height: 16px;" />
                    </template>
                  </el-skeleton>
                </div>
                <template v-else>
                  <div class="stat-value">{{ formatCurrency(currentPeriodTotalExpense) }}</div>
                  <div class="stat-date">{{ currentPeriodTitle }}</div>
                  <div class="cycle-hint">点击切换时间维度</div>
                </template>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 快速记账面板 -->
      <div class="quick-expense-section">
        <el-card class="quick-expense-card" shadow="hover">
          <template #header>
            <div class="quick-expense-header">
              <span class="quick-expense-title">快速记账</span>
              <div class="quick-expense-header-right">
                <div v-if="quickForm.category" class="selected-category-hint">
                  当前选中分类：<span class="category-name">{{ quickForm.category }}</span>
                </div>
                <div v-else class="no-category-hint">
                  💡 点击分类标签选择分类，然后输入金额进行快速记账
                </div>
              </div>
            </div>
          </template>

          <div class="quick-expense-content">
            <!-- 分类标签面板 -->
            <div class="category-tags-section">
              <div class="section-label">消费分类</div>
              <div class="category-tags-container">
                <!-- 全部标签 -->
                <StandardTag
                  text="全部"
                  variant="primary"
                  :active="selectedCategory === ''"
                  :editable="false"
                  :deletable="false"
                  :draggable="false"
                  :show-actions="false"
                  :color="getAllTagColor(selectedCategory === '')"
                  :backgroundColor="getAllTagBackground(selectedCategory === '')"
                  :borderColor="getAllTagBorderColor(selectedCategory === '')"
                  class="all-category-tag"
                  @click="handleCategoryFilter('')"
                />

                <!-- 可拖拽的分类标签 -->
                <VueDraggable
                  v-model="expenseCategories"
                  v-bind="dragOptions"
                  @start="onDragStart"
                  @end="onDragEnd"
                  item-key="id"
                  class="draggable-categories"
                >
                  <template #item="{ element: category }">
                    <StandardTag
                      :text="category.name"
                      variant="primary"
                      :active="selectedCategory === category.name"
                      :editable="true"
                      :deletable="true"
                      :draggable="true"
                      :color="getTagTextColor(category.color, selectedCategory === category.name)"
                      :backgroundColor="getTagBackground(category.color, selectedCategory === category.name)"
                      :borderColor="getTagBorderColor(category.color, selectedCategory === category.name)"
                      :class="{ 'is-dragging': isDragging }"
                      @click="handleCategoryFilter(category.name)"
                      @dblclick="handleQuickAddByCategory(category.name)"
                      @edit="handleEditCategory(category)"
                      @delete="handleDeleteCategory(category)"
                    />
                  </template>
                </VueDraggable>

                <!-- 添加新分类按钮 -->
                <AddButton
                  @click="showAddCategoryDialog = true"
                  tooltip="添加新分类"
                  variant="primary"
                  size="md"
                  animation="rotate"
                />
              </div>
            </div>

            <!-- 快速输入区域 -->
            <div class="quick-input-section">
              <div class="quick-input-form">
                <el-input-number
                  v-model="quickForm.amount"
                  :precision="2"
                  :step="0.1"
                  :min="0"
                  placeholder="金额"
                  class="amount-input"
                  size="default"
                />
                <el-input
                  v-model="quickForm.description"
                  placeholder="备注（可选）"
                  class="description-input"
                  size="default"
                  clearable
                />
                <!-- 快速图片上传 -->
                <div class="quick-image-upload-container" @mouseenter="showImageTooltip" @mouseleave="hideImageTooltip">
                  <el-upload
                    ref="quickUploadRef"
                    :show-file-list="false"
                    :before-upload="handleQuickBeforeUpload"
                    :on-change="handleQuickImageChange"
                    :auto-upload="false"
                    accept="image/*"
                    class="quick-image-uploader"
                  >
                    <!-- 带进度条效果的按钮 -->
                    <el-button
                      size="default"
                      :type="quickForm.images.length > 0 ? 'success' : 'default'"
                      class="quick-image-btn"
                      :class="{ 'uploading': quickImageUploading }"
                      :disabled="quickForm.images.length >= 3"
                    >
                      <!-- 进度条填充背景 -->
                      <div
                        v-if="quickImageUploading"
                        class="button-progress-fill"
                        :style="{ width: quickUploadProgress + '%' }"
                      ></div>

                      <!-- 按钮内容 -->
                      <span class="button-content">
                        <el-icon><Camera /></el-icon>
                        <span class="button-text">
                          {{ getImageButtonText() }}
                        </span>
                      </span>
                    </el-button>
                  </el-upload>

                  <!-- 悬浮预览面板 -->
                  <div
                    v-if="quickForm.images.length > 0 && showTooltip"
                    class="image-tooltip"
                    :class="`images-${quickForm.images.length}`"
                  >
                    <div class="tooltip-content">
                      <div class="images-grid">
                        <div
                          v-for="(image, index) in quickForm.images"
                          :key="index"
                          class="image-item"
                        >
                          <img :src="image.preview" alt="预览" class="tooltip-image" />
                          <div class="image-overlay">
                            <el-button
                              type="danger"
                              size="small"
                              circle
                              class="delete-btn"
                              @click.stop="removeQuickImage(index)"
                            >
                              <el-icon><Close /></el-icon>
                            </el-button>
                          </div>
                          <div class="image-info">
                            <p class="file-name">{{ image.file.name }}</p>
                            <p class="file-size">{{ formatFileSize(image.file.size) }}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="tooltip-arrow"></div>
                  </div>
                </div>
                <div class="quick-action-buttons">
                  <el-button
                    type="primary"
                    class="quick-add-btn"
                    :disabled="!canQuickSubmit"
                    :loading="quickSubmitting"
                    @click="submitQuickExpense"
                    size="default"
                  >
                    <el-icon><Plus /></el-icon>
                    快速添加
                  </el-button>
                  <el-button
                    type="info"
                    class="detailed-entry-btn"
                    @click="openAddDialog"
                    size="default"
                    plain
                  >
                    <el-icon><Edit /></el-icon>
                    详细记录
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 数据分析图表 - 可折叠 -->
      <div class="expense-overview-section" :class="{ 'chart-collapsed': isChartCollapsed }">
        <el-card class="expense-chart-card" shadow="hover">
          <template #header>
            <div class="chart-card-header">
              <div class="chart-title-section">
                <span class="chart-title">数据分析</span>
                <span class="period-title">{{ currentPeriodTitle }}</span>
              </div>
              <div class="chart-controls">
                <div class="chart-controls-left">
                  <!-- 图表类型切换 -->
                  <div class="chart-type-selector">
                    <EnhancedViewToggle
                      v-model="chartType"
                      :options="chartTypeOptions"
                      @change="handleChartTypeChange"
                    />
                  </div>
                </div>
                <div class="chart-controls-right">
                  <!-- 折叠按钮 -->
                  <el-button
                    text
                    size="small"
                    @click="toggleChartCollapse"
                    class="collapse-btn"
                  >
                    <el-icon>
                      <component :is="isChartCollapsed ? 'ArrowDown' : 'ArrowUp'" />
                    </el-icon>
                    {{ isChartCollapsed ? '展开' : '折叠' }}
                  </el-button>
                </div>
              </div>
            </div>
          </template>
          <el-collapse-transition>
            <div v-show="!isChartCollapsed" class="chart-content">
              <!-- 图表加载状态 -->
              <div v-if="loading" class="chart-skeleton">
                <el-skeleton animated>
                  <template #template>
                    <div class="skeleton-chart">
                      <el-skeleton-item variant="text" style="width: 30%; margin: 0 auto 20px;" />
                      <div class="skeleton-chart-area">
                        <el-skeleton-item variant="rect" style="width: 100%; height: 300px;" />
                      </div>
                    </div>
                  </template>
                </el-skeleton>
              </div>
              <!-- 实际图表 -->
              <div v-else class="chart-container">
                <v-chart
                  ref="chartRef"
                  class="expense-chart"
                  :option="chartOption"
                  :key="`chart-${chartType}-${timeDimension}-${currentPeriodTitle}-${filteredExpenseRecords.length}`"
                  autoresize
                  :init-options="{ width: 'auto', height: 'auto' }"
                  @click="handleChartClick"
                />
              </div>
            </div>
          </el-collapse-transition>
        </el-card>
      </div>

      <!-- 花费记录表格 -->
      <div class="expense-records-section" :class="{ 'chart-collapsed': isChartCollapsed }">
        <el-card shadow="hover">
          <template #header>
            <div class="table-header">
              <div class="table-title-section">
                <span class="table-title">历史记录</span>
                <span v-if="selectedCategory || selectedDate" class="filter-status">
                  （筛选：
                  <span v-if="selectedCategory">{{ selectedCategory }}</span>
                  <span v-if="selectedCategory && selectedDate"> + </span>
                  <span v-if="selectedDate">{{ formatFilterDate(selectedDate, selectedDateType) }}</span>
                  ）
                </span>
                <span class="record-count">
                  共 {{ sortedExpenseRecords.length }} 条记录
                </span>
              </div>
              <div class="table-actions">
                <el-button
                  v-if="selectedCategory || selectedDate"
                  type="info"
                  size="small"
                  plain
                  @click="clearAllFilters"
                  class="clear-filter-btn"
                >
                  <el-icon><Close /></el-icon>
                  清除筛选
                </el-button>
                <el-button
                  v-if="selectedRecords.length > 0"
                  class="delete-batch-btn"
                  type="danger"
                  size="default"
                  @click="batchDeleteRecords"
                >
                  <el-icon class="delete-icon"><Delete /></el-icon>
                  <span class="btn-text">删除选中 ({{ selectedRecords.length }})</span>
                </el-button>
              </div>
            </div>
          </template>

          <!-- 表格加载状态 -->
          <div v-if="loading" class="table-skeleton">
            <el-skeleton animated>
              <template #template>
                <div class="skeleton-table">
                  <div class="skeleton-table-header">
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 35%;" />
                    <el-skeleton-item variant="text" style="width: 20%;" />
                  </div>
                  <div v-for="i in 5" :key="i" class="skeleton-table-row">
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 15%;" />
                    <el-skeleton-item variant="text" style="width: 35%;" />
                    <el-skeleton-item variant="text" style="width: 20%;" />
                  </div>
                </div>
              </template>
            </el-skeleton>
          </div>

          <!-- 实际表格 -->
          <el-table
            ref="tableRef"
            v-else-if="expenseRecords.length > 0"
            :data="sortedExpenseRecords"
            style="width: 100%"
            @selection-change="handleSelectionChange"
            :row-class-name="tableRowClassName"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="日期" width="180">
              <template #header>
                <div class="column-header">
                  <span>日期</span>
                  <div class="sort-buttons">
                    <el-button
                      link
                      size="small"
                      :type="sortConfig.field === 'date' && sortConfig.order === 'asc' ? 'primary' : 'info'"
                      @click="handleSort('date', 'asc')"
                      title="按日期升序排列（旧到新）"
                    >
                      <el-icon><ArrowUp /></el-icon>
                    </el-button>
                    <el-button
                      link
                      size="small"
                      :type="sortConfig.field === 'date' && sortConfig.order === 'desc' ? 'primary' : 'info'"
                      @click="handleSort('date', 'desc')"
                      title="按日期降序排列（新到旧）"
                    >
                      <el-icon><ArrowDown /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <div class="date-cell">
                  <el-icon class="date-icon"><Calendar /></el-icon>
                  <span :title="formatFullDateTime(row.date || row.created_at)">{{ formatDate(row.date || row.created_at) }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="类别" width="120">
              <template #default="{ row }">
                <el-tag :type="getCategoryTagType(row.category)" size="default">{{ row.category }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="金额" width="150">
              <template #header>
                <div class="column-header">
                  <span>金额</span>
                  <div class="sort-buttons">
                    <el-button
                      link
                      size="small"
                      :type="sortConfig.field === 'amount' && sortConfig.order === 'asc' ? 'primary' : 'info'"
                      @click="handleSort('amount', 'asc')"
                      title="按金额升序排列（低到高）"
                    >
                      <el-icon><ArrowUp /></el-icon>
                    </el-button>
                    <el-button
                      link
                      size="small"
                      :type="sortConfig.field === 'amount' && sortConfig.order === 'desc' ? 'primary' : 'info'"
                      @click="handleSort('amount', 'desc')"
                      title="按金额降序排列（高到低）"
                    >
                      <el-icon><ArrowDown /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <div class="amount-cell">
                  <el-tag type="danger" size="large" class="amount-tag">{{ formatCurrency(row.amount) }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="描述">
              <template #default="{ row }">
                <span class="description-text">{{ row.description || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="图片" width="100">
              <template #default="{ row }">
                <div class="image-cell">
                  <div v-if="row.image_url || row.additional_images" class="expense-images-preview">
                    <!-- 主图片 -->
                    <div v-if="row.image_url" class="expense-image-preview main-image">
                      <img
                        :src="row.image_url"
                        alt="花费图片"
                        class="expense-thumbnail"
                        @click="previewImage(row.image_url)"
                      />
                    </div>
                    <!-- 额外图片指示器 -->
                    <div v-if="getAdditionalImagesCount(row) > 0" class="additional-images-indicator">
                      <span class="image-count">+{{ getAdditionalImagesCount(row) }}</span>
                    </div>
                  </div>
                  <span v-else class="no-image">-</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <div class="action-buttons">
                  <el-button type="primary" link @click="openEditDialog(row)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button type="danger" link @click="deleteRecord(row)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-else-if="!loading && expenseRecords.length === 0" description="还没有花费记录，快去添加吧！">
            <el-button type="primary" @click="openAddDialog">
              <el-icon><Plus /></el-icon>
              添加第一条记录
            </el-button>
          </el-empty>
        </el-card>
      </div>

      <!-- 添加/编辑花费记录对话框 -->
      <el-dialog v-model="showDialog" :title="isEditing ? '编辑花费记录' : '添加花费记录'" width="500px" @closed="resetForm">
        <el-form :model="expenseForm" label-width="80px" :rules="rules" ref="formRef">
          <el-form-item label="日期" prop="date">
            <el-date-picker
              v-model="expenseForm.date"
              type="datetime"
              placeholder="选择日期和时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="类别" prop="category">
            <el-select v-model="expenseForm.category" placeholder="选择类别" style="width: 100%;">
              <el-option
                v-for="category in sortedCategories"
                :key="category.id"
                :label="category.name"
                :value="category.name"
              >
                <div style="display: flex; align-items: center; gap: 8px;">
                  <div
                    :style="{
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      backgroundColor: category.color
                    }"
                  ></div>
                  <span>{{ category.name }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="金额" prop="amount">
            <div class="amount-input-group">
              <el-button
                text
                size="small"
                @click="decreaseAmount"
                class="amount-btn"
              >
                <el-icon><Minus /></el-icon>
              </el-button>
              <el-input-number
                v-model="expenseForm.amount"
                :precision="2"
                :step="0.1"
                :min="0"
                :controls="false"
                style="width: 200px"
              />
              <el-button
                text
                size="small"
                @click="increaseAmount"
                class="amount-btn"
              >
                <el-icon><Plus /></el-icon>
              </el-button>
              <span class="unit-label">元</span>
            </div>
          </el-form-item>
          <el-form-item label="备注" prop="description">
            <el-input
              v-model="expenseForm.description"
              type="textarea"
              :rows="3"
              placeholder="输入备注信息（可选）"
            />
          </el-form-item>
          <el-form-item label="图片" prop="image">
            <div class="image-upload-section">
              <!-- 多图片上传区域 -->
              <div class="multi-image-upload">
                <!-- 已上传图片预览 -->
                <div v-if="expenseForm.images.length > 0" class="uploaded-images">
                  <div
                    v-for="(image, index) in expenseForm.images"
                    :key="index"
                    class="uploaded-image-item"
                  >
                    <img :src="image.preview" alt="预览图" class="uploaded-preview-image" />
                    <div class="image-overlay">
                      <el-button
                        type="danger"
                        size="small"
                        circle
                        class="remove-image-btn"
                        @click="removeDetailImage(index)"
                      >
                        <el-icon><Close /></el-icon>
                      </el-button>
                    </div>
                    <div class="image-info">
                      <span class="image-name">{{ image.file?.name || '已上传图片' }}</span>
                    </div>
                  </div>
                </div>

                <!-- 上传按钮 -->
                <el-upload
                  ref="uploadRef"
                  :show-file-list="false"
                  :before-upload="handleBeforeUpload"
                  :on-change="handleImageChange"
                  :auto-upload="false"
                  accept="image/*"
                  class="image-uploader"
                  :disabled="expenseForm.images.length >= 3"
                >
                  <div class="upload-placeholder" :class="{ disabled: expenseForm.images.length >= 3 }">
                    <el-icon class="upload-icon"><Plus /></el-icon>
                    <div class="upload-text">
                      {{ expenseForm.images.length >= 3 ? '已达上限' : `上传图片 (${expenseForm.images.length}/3)` }}
                    </div>
                  </div>
                </el-upload>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showDialog = false">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加/编辑分类对话框 -->
      <TypeManagementDialog
        v-model="showAddCategoryDialog"
        :is-edit-mode="isEditCategoryMode"
        type-label="分类"
        :data="categoryForm"
        :predefined-colors="predefinedCategoryColors"
        @save="handleCategorySave"
        @cancel="handleCategoryCancel"
        @closed="resetCategoryDialog"
      />

      <!-- 编辑分类对话框 -->
      <TypeManagementDialog
        v-model="showEditCategoryDialog"
        :is-edit-mode="true"
        type-label="分类"
        :data="categoryForm"
        :predefined-colors="predefinedCategoryColors"
        @save="handleCategorySave"
        @cancel="handleCategoryCancel"
        @closed="resetCategoryDialog"
      />

      <!-- 图片预览对话框 -->
      <el-dialog v-model="showImagePreview" title="图片预览" width="60%" center>
        <div class="image-preview-container">
          <img :src="previewImageUrl" alt="预览图片" class="preview-full-image" />
        </div>
      </el-dialog>

    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 消息提示辅助函数
const showMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  ElMessage({ message, type });
};
import {
  Plus, Delete, Edit, Money, TrendCharts, DataAnalysis, Calendar, ArrowUp, ArrowDown,
  Food, FirstAidKit, Basketball, ShoppingBag, Brush, MoreFilled, Close, Minus, RefreshRight, Camera
} from '@element-plus/icons-vue';
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue';
import StandardTag from '@/components/common/StandardTag.vue';
import TypeManagementDialog from '@/components/common/TypeManagementDialog.vue';
import AddButton from '@/components/common/AddButton.vue';
import PageHeaderBar from '@/components/common/PageHeaderBar.vue';
import VueDraggable from 'vuedraggable';
import StandardColorPicker from '@/components/common/StandardColorPicker.vue';
import draggable from 'vuedraggable';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart, PieChart, BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import { usePetStore } from '@/stores/pet';
import { supabase } from '@/utils/supabase';

// 注册ECharts组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DatasetComponent
]);

const petStore = usePetStore();

const expenseRecords = ref<any[]>([]);
const selectedRecords = ref<any[]>([]);
const showDialog = ref(false);
const isEditing = ref(false);
const loading = ref(true); // 初始状态为加载中
const submitting = ref(false);
const quickSubmitting = ref(false);
const formRef = ref();
const chartRef = ref();
const tableRef = ref();

// 时间维度切换相关状态
const timeDimension = ref<'month' | 'quarter' | 'year'>('month');
const currentDate = ref(new Date());

// 图表类型切换相关状态
const chartType = ref<'line' | 'pie' | 'bar' | 'doughnut'>('line');

// 时间维度选项
const timeDimensionOptions = [
  { value: 'month', label: '月视图' },
  { value: 'quarter', label: '季度视图' },
  { value: 'year', label: '年视图' }
];

// 图表类型选项
const chartTypeOptions = [
  { value: 'line', label: '趋势图' },
  { value: 'bar', label: '柱状图' },
  { value: 'pie', label: '饼图' },
  { value: 'doughnut', label: '环形图' }
];

// 排序配置
const sortConfig = ref({
  field: 'date' as 'date' | 'amount',
  order: 'desc' as 'asc' | 'desc'
});

const expenseForm = ref({
  amount: null as number | null,
  date: '',
  category: '',
  description: '',
  images: [] as Array<{
    file?: File;
    preview: string;
    url?: string;
    path?: string;
    uploading?: boolean;
    progress?: number;
  }>
});

// 图表折叠状态
const isChartCollapsed = ref(false);

// 快速记账表单
const quickForm = ref({
  amount: null as number | null,
  category: '',
  description: '',
  images: [] as Array<{
    file: File;
    preview: string;
    url?: string;
    path?: string;
    uploading?: boolean;
    progress?: number;
  }>
});

// 消费分类数据
const expenseCategories = ref<any[]>([]);

// 当前选中的分类（用于筛选）
const selectedCategory = ref('');

// 当前选中的日期（用于筛选）
const selectedDate = ref('');
// 日期筛选类型（day: 按天, month: 按月）
const selectedDateType = ref<'day' | 'month'>('day');

// 对话框状态
const showAddCategoryDialog = ref(false);
const showEditCategoryDialog = ref(false);
const isEditCategoryMode = ref(false);
const editingCategory = ref<any>(null);

// 分类表单
const categoryForm = ref({
  label: '',
  color: '#409EFF'
});

// 拖拽状态
const isDragging = ref(false);

// 拖拽配置
const dragOptions = {
  animation: 300,
  group: 'expense-categories',
  disabled: false,
  ghostClass: 'ghost'
};

// 预定义颜色
const predefinedCategoryColors = [
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
  '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
  '#909399', '#9C27B0', '#FF9800', '#4CAF50'
];

const editingRecord = ref<any>(null);

// 表单验证规则
const rules = {
  date: [{ required: true, message: '请选择日期', trigger: 'change' }],
  category: [{ required: true, message: '请选择类别', trigger: 'change' }],
  amount: [
    { required: true, message: '请输入金额', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '金额必须大于0', trigger: 'blur' }
  ]
};



const currentPetId = computed(() => {
  // 优先使用petStore中的currentPetId，如果没有则从localStorage获取
  return petStore.currentPetId || localStorage.getItem('currentPetId');
});

// 当前时间段标题
const currentPeriodTitle = computed(() => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const quarter = Math.floor(now.getMonth() / 3) + 1;

  switch (timeDimension.value) {
    case 'month':
      return `${year}年${month}月`;
    case 'quarter':
      return `${year}年第${quarter}季度`;
    case 'year':
      return `${year}年`;
    default:
      return '数据视图';
  }
});

// 当前时间段标题（用于统计卡片）
const currentPeriodStatTitle = computed(() => {
  switch (timeDimension.value) {
    case 'month':
      return '当月总消费';
    case 'quarter':
      return '当季总消费';
    case 'year':
      return '当年总消费';
    default:
      return '当前总消费';
  }
});

// 计算真正的当前时间段范围
const currentTimePeriodRange = computed(() => {
  const now = new Date();

  switch (timeDimension.value) {
    case 'month': {
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
      return { start, end };
    }
    case 'quarter': {
      const quarter = Math.floor(now.getMonth() / 3);
      const start = new Date(now.getFullYear(), quarter * 3, 1);
      const end = new Date(now.getFullYear(), quarter * 3 + 3, 0, 23, 59, 59);
      return { start, end };
    }
    case 'year': {
      const start = new Date(now.getFullYear(), 0, 1);
      const end = new Date(now.getFullYear(), 11, 31, 23, 59, 59);
      return { start, end };
    }
    default: {
      return {
        start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
        end: now
      };
    }
  }
});

// 计算时间范围 - 显示所有记录
const currentPeriodRange = computed(() => {
  const now = new Date();

  // 如果没有记录，返回当前时间
  if (expenseRecords.value.length === 0) {
    return {
      start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 默认最近7天
      end: now
    };
  }

  // 获取所有记录的时间范围
  const allDates = expenseRecords.value.map(record =>
    new Date(record.date || record.created_at)
  ).sort((a, b) => a.getTime() - b.getTime());

  const earliestDate = allDates[0];
  const latestDate = allDates[allDates.length - 1];

  // 返回全部记录的时间范围
  return {
    start: earliestDate,
    end: latestDate
  };
});

// 筛选当前时间段的数据（用于图表分析）
const filteredExpenseRecords = computed(() => {
  const { start, end } = currentTimePeriodRange.value;

  return expenseRecords.value.filter(record => {
    const recordDate = new Date(record.date || record.created_at);
    return recordDate >= start && recordDate <= end;
  });
});

// 排序后的花费记录（应用分类筛选）
const sortedExpenseRecords = computed(() => {
  if (!categoryFilteredRecords.value.length) return [];

  const records = [...categoryFilteredRecords.value];

  return records.sort((a, b) => {
    let valueA, valueB;

    if (sortConfig.value.field === 'date') {
      valueA = new Date(a.date || a.created_at).getTime();
      valueB = new Date(b.date || b.created_at).getTime();
    } else {
      valueA = a.amount;
      valueB = b.amount;
    }

    if (sortConfig.value.order === 'asc') {
      return valueA - valueB;
    } else {
      return valueB - valueA;
    }
  });
});

// 筛选当前时间段的数据（用于新的统计卡片）
const currentPeriodExpenseRecords = computed(() => {
  const { start, end } = currentTimePeriodRange.value;

  return expenseRecords.value.filter(record => {
    const recordDate = new Date(record.date || record.created_at);
    return recordDate >= start && recordDate <= end;
  });
});

// 计算属性 - 基于筛选后的数据
const totalExpenses = computed(() => {
  return filteredExpenseRecords.value.reduce((sum, record) => sum + parseFloat(record.amount || 0), 0);
});

const averageExpense = computed(() => {
  if (!filteredExpenseRecords.value.length) return 0;
  return totalExpenses.value / filteredExpenseRecords.value.length;
});

const highestExpense = computed(() => {
  if (!filteredExpenseRecords.value.length) return 0;
  return Math.max(...filteredExpenseRecords.value.map(record => parseFloat(record.amount || 0)));
});

// 当前时间段总消费
const currentPeriodTotalExpense = computed(() => {
  return currentPeriodExpenseRecords.value.reduce((sum, record) => sum + parseFloat(record.amount || 0), 0);
});

// 排序后的分类
const sortedCategories = computed(() => {
  return [...expenseCategories.value].sort((a, b) => (a.order_index || 0) - (b.order_index || 0));
});

// 筛选后的花费记录（基于分类和日期筛选）
const categoryFilteredRecords = computed(() => {
  let filtered = expenseRecords.value;

  // 分类筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(record => record.category === selectedCategory.value);
  }

  // 日期筛选
  if (selectedDate.value) {
    if (selectedDateType.value === 'day') {
      // 按天筛选：精确匹配日期
      filtered = filtered.filter(record => {
        const recordDate = formatDate(record.date || record.created_at);
        return recordDate === selectedDate.value;
      });
    } else if (selectedDateType.value === 'month') {
      // 按月筛选：匹配年月
      filtered = filtered.filter(record => {
        const recordDate = new Date(record.date || record.created_at);
        const [year, month] = selectedDate.value.split('-');
        return recordDate.getFullYear() === parseInt(year) &&
               recordDate.getMonth() === parseInt(month) - 1;
      });
    }
  }

  return filtered;
});

// 生成图表数据的函数
const generateChartData = () => {
  const { start, end } = currentTimePeriodRange.value;
  const records = filteredExpenseRecords.value;

  if (!records.length) {
    return { labels: [], data: [] };
  }

  switch (timeDimension.value) {
    case 'month':
      return generateMonthData(start, end, records);
    case 'quarter':
      return generateQuarterData(start, end, records);
    case 'year':
      return generateYearData(start, end, records);
    default:
      return { labels: [], data: [] };
  }
};

// 生成月视图数据（当月每天）
const generateMonthData = (start: Date, end: Date, records: any[]) => {
  const labels: string[] = [];
  const data: number[] = [];

  // 获取当月的天数
  const year = start.getFullYear();
  const month = start.getMonth();
  const daysInMonth = new Date(year, month + 1, 0).getDate();

  // 为当月每一天生成数据
  for (let day = 1; day <= daysInMonth; day++) {
    const currentDate = new Date(year, month, day);
    labels.push(`${day}日`);

    // 查找当天的记录
    const dayRecords = records.filter(record => {
      const recordDate = new Date(record.date || record.created_at);
      return recordDate.getDate() === day &&
             recordDate.getMonth() === month &&
             recordDate.getFullYear() === year;
    });

    const dayTotal = dayRecords.reduce((sum, record) => sum + parseFloat(record.amount || 0), 0);
    data.push(dayTotal);
  }

  return { labels, data };
};

// 生成季度视图数据（当季度各个月）
const generateQuarterData = (start: Date, end: Date, records: any[]) => {
  const labels: string[] = [];
  const data: number[] = [];
  const startMonth = start.getMonth();
  const endMonth = end.getMonth();
  const year = start.getFullYear();

  // 为当季度的每个月生成数据
  for (let month = startMonth; month <= endMonth; month++) {
    labels.push(`${month + 1}月`);

    // 查找当月的记录
    const monthRecords = records.filter(record => {
      const recordDate = new Date(record.date || record.created_at);
      return recordDate.getMonth() === month && recordDate.getFullYear() === year;
    });

    const monthTotal = monthRecords.reduce((sum, record) => sum + parseFloat(record.amount || 0), 0);
    data.push(monthTotal);
  }

  return { labels, data };
};

// 生成年视图数据（当年12个月）
const generateYearData = (start: Date, end: Date, records: any[]) => {
  const labels: string[] = [];
  const data: number[] = [];
  const year = start.getFullYear();

  // 为当年的12个月生成数据
  for (let month = 0; month < 12; month++) {
    labels.push(`${month + 1}月`);

    // 查找当月的记录
    const monthRecords = records.filter(record => {
      const recordDate = new Date(record.date || record.created_at);
      return recordDate.getMonth() === month && recordDate.getFullYear() === year;
    });

    const monthTotal = monthRecords.reduce((sum, record) => sum + parseFloat(record.amount || 0), 0);
    data.push(monthTotal);
  }

  return { labels, data };
};

// 生成类别数据（用于饼图和环形图）
const generateCategoryData = () => {
  const records = filteredExpenseRecords.value;
  if (!records.length) return [];

  const categoryMap = new Map();
  records.forEach(record => {
    const category = record.category || '其他';
    const amount = parseFloat(record.amount || 0);
    categoryMap.set(category, (categoryMap.get(category) || 0) + amount);
  });

  return Array.from(categoryMap.entries()).map(([name, value]) => ({
    name,
    value,
    itemStyle: {
      color: getCategoryColor(name)
    }
  }));
};

// 获取类别颜色
const getCategoryColor = (category: string) => {
  const categoryData = expenseCategories.value.find(cat => cat.name === category);
  return categoryData?.color || '#409EFF';
};

// 图表配置
const chartOption = computed(() => {
  if (!filteredExpenseRecords.value.length) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'middle',
        textStyle: {
          fontSize: 16,
          color: '#909399'
        }
      }
    };
  }

  const { labels, data } = generateChartData();
  const categoryData = generateCategoryData();

  const baseConfig = {
    title: {
      text: getChartTitle(),
      left: 'center',
      top: 20,
      textStyle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: chartType.value === 'line' || chartType.value === 'bar' ? 'axis' : 'item',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#E6A23C',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff'
      },
      formatter: (params: any) => {
        if (chartType.value === 'pie' || chartType.value === 'doughnut') {
          return `<div style="padding: 6px;">
            <div style="margin-bottom: 6px; font-weight: 600;">${params.name}</div>
            <div style="color: #E6A23C;">● 金额: ¥${params.value.toFixed(2)}</div>
            <div style="color: #67C23A;">● 占比: ${params.percent}%</div>
            <div style="color: #909399; font-size: 12px; margin-top: 4px;">💡 点击可筛选此分类</div>
          </div>`;
        } else {
          const point = Array.isArray(params) ? params[0] : params;
          return `<div style="padding: 6px;">
            <div style="margin-bottom: 6px; font-weight: 600;">${point.name}</div>
            <div style="color: #E6A23C;">● 花费: ¥${point.value.toFixed(2)}</div>
            <div style="color: #909399; font-size: 12px; margin-top: 4px;">💡 点击可定位到记录</div>
          </div>`;
        }
      }
    }
  };

  switch (chartType.value) {
    case 'line':
      return {
        ...baseConfig,
        grid: {
          left: '10%',
          right: '5%',
          bottom: '15%',
          top: '25%',
          containLabel: false
        },
        xAxis: {
          type: 'category',
          data: labels,
          axisLine: {
            lineStyle: {
              color: '#E4E7ED'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 12,
            color: '#909399',
            margin: 12
          }
        },
        yAxis: {
          type: 'value',
          name: '花费金额 (¥)',
          nameLocation: 'middle',
          nameGap: 50,
          nameTextStyle: {
            fontSize: 14,
            color: '#606266',
            fontWeight: '500'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#F2F6FC',
              type: 'dashed'
            }
          },
          axisLabel: {
            fontSize: 12,
            color: '#909399',
            formatter: (value: number) => `¥${value.toFixed(0)}`
          }
        },
        series: [{
          name: '花费金额',
          type: 'line',
          data: data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          connectNulls: false,
          lineStyle: {
            width: 4,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: '#E6A23C' },
                { offset: 1, color: '#F56C6C' }
              ]
            },
            shadowColor: 'rgba(230, 162, 60, 0.3)',
            shadowBlur: 10,
            shadowOffsetY: 3
          },
          itemStyle: {
            color: '#E6A23C',
            borderColor: '#ffffff',
            borderWidth: 3,
            shadowColor: 'rgba(230, 162, 60, 0.4)',
            shadowBlur: 8
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(230, 162, 60, 0.25)'
                },
                {
                  offset: 0.5,
                  color: 'rgba(245, 108, 108, 0.15)'
                },
                {
                  offset: 1,
                  color: 'rgba(230, 162, 60, 0.02)'
                }
              ]
            }
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 15,
              shadowColor: 'rgba(230, 162, 60, 0.6)'
            }
          },
          animationDuration: 2000,
          animationEasing: 'cubicOut'
        }]
      };

    case 'bar':
      return {
        ...baseConfig,
        grid: {
          left: '10%',
          right: '5%',
          bottom: '15%',
          top: '25%',
          containLabel: false
        },
        xAxis: {
          type: 'category',
          data: labels,
          axisLine: {
            lineStyle: {
              color: '#E4E7ED'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 12,
            color: '#909399',
            margin: 12
          }
        },
        yAxis: {
          type: 'value',
          name: '花费金额 (¥)',
          nameLocation: 'middle',
          nameGap: 50,
          nameTextStyle: {
            fontSize: 14,
            color: '#606266',
            fontWeight: '500'
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#F2F6FC',
              type: 'dashed'
            }
          },
          axisLabel: {
            fontSize: 12,
            color: '#909399',
            formatter: (value: number) => `¥${value.toFixed(0)}`
          }
        },
        series: [{
          name: '花费金额',
          type: 'bar',
          data: data,
          barWidth: '60%',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#409EFF' },
                { offset: 1, color: '#67C23A' }
              ]
            },
            borderRadius: [4, 4, 0, 0],
            shadowColor: 'rgba(64, 158, 255, 0.3)',
            shadowBlur: 8,
            shadowOffsetY: 2
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowColor: 'rgba(64, 158, 255, 0.6)'
            }
          },
          animationDuration: 1500,
          animationEasing: 'cubicOut'
        }]
      };

    case 'pie':
      return {
        ...baseConfig,
        color: categoryData.map(item => item.itemStyle.color), // 添加全局颜色配置
        series: [{
          type: 'pie',
          data: categoryData,
          radius: '70%',
          center: ['50%', '55%'],
          label: {
            show: true,
            fontSize: 12,
            color: '#606266',
            formatter: '{b}: ¥{c}\n({d}%)',
            lineHeight: 16
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 10,
            lineStyle: {
              color: '#E4E7ED'
            }
          },
          itemStyle: {
            borderColor: '#ffffff',
            borderWidth: 2,
            shadowBlur: 8,
            shadowColor: 'rgba(0, 0, 0, 0.1)'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
              scale: 1.05
            },
            label: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          // 修复动画：改回旋转进场动画
          animationType: 'expansion',
          animationDuration: 1500,
          animationEasing: 'cubicOut',
          animationDelay: (idx: number) => idx * 100
        }]
      };

    case 'doughnut':
      return {
        ...baseConfig,
        color: categoryData.map(item => item.itemStyle.color), // 添加全局颜色配置
        series: [{
          type: 'pie',
          data: categoryData,
          radius: ['40%', '70%'],
          center: ['50%', '55%'],
          label: {
            show: true,
            fontSize: 12,
            color: '#606266',
            formatter: '{b}: ¥{c}\n({d}%)',
            lineHeight: 16
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 10,
            lineStyle: {
              color: '#E4E7ED'
            }
          },
          itemStyle: {
            borderColor: '#ffffff',
            borderWidth: 2,
            shadowBlur: 8,
            shadowColor: 'rgba(0, 0, 0, 0.1)'
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 15,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
              scale: 1.05
            },
            label: {
              fontSize: 14,
              fontWeight: 'bold'
            }
          },
          // 修复动画：改回旋转进场动画
          animationType: 'expansion',
          animationDuration: 1500,
          animationEasing: 'cubicOut',
          animationDelay: (idx: number) => idx * 100
        }]
      };

    default:
      return baseConfig;
  }
});

// 获取图表标题
const getChartTitle = () => {
  const titleMap = {
    line: '花费趋势',
    bar: '花费对比',
    pie: '类别分布',
    doughnut: '支出占比'
  };
  return titleMap[chartType.value] || '数据分析';
};

// 工具函数
const formatDate = (date: string | Date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('zh-CN');
};

const formatFullDateTime = (datetime: string | Date) => {
  if (!datetime) return '';
  return new Date(datetime).toLocaleString('zh-CN');
};

const formatCurrency = (amount: number | string) => {
  return `¥${parseFloat(amount?.toString() || '0').toFixed(2)}`;
};

const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    '食物': 'success',
    '医疗': 'danger',
    '玩具': 'warning',
    '用品': 'info',
    '美容': 'primary',
    '训练': '',
    '其他': ''
  };
  return typeMap[category] || '';
};

// 事件处理函数
const handleTimeDimensionChange = (newDimension: string) => {
  timeDimension.value = newDimension as 'month' | 'quarter' | 'year';
};

const handleChartTypeChange = (newType: string) => {
  chartType.value = newType as 'line' | 'pie' | 'bar' | 'doughnut';
};

// 图表折叠切换
const toggleChartCollapse = () => {
  isChartCollapsed.value = !isChartCollapsed.value;
};

// 图表点击事件处理
const handleChartClick = (params: any) => {
  // 检查参数结构
  if (!params || !params.name) {
    return;
  }

  // 根据图表类型处理点击事件
  switch (chartType.value) {
    case 'pie':
    case 'doughnut':
      // 饼图和环形图：根据分类筛选记录
      handleCategoryBasedChartClick(params);
      break;
    case 'line':
    case 'bar':
      // 趋势图和柱状图：根据数据点的名称（日期/时间段）定位到对应记录
      handleTimeBasedChartClick(params);
      break;
  }
};

// 处理基于时间的图表点击（趋势图、柱状图）
const handleTimeBasedChartClick = (params: any) => {
  const clickedLabel = params.name;

  if (chartType.value === 'line') {
    // 趋势图：根据日期筛选记录
    filterByDate(clickedLabel);
  } else if (chartType.value === 'bar') {
    // 柱状图：根据时间维度筛选数据
    filterByTimePeriod(clickedLabel);
  }
};

// 处理基于分类的图表点击（饼图、环形图）
const handleCategoryBasedChartClick = (params: any) => {
  const categoryName = params.name;

  // 筛选该分类的数据
  handleCategoryFilter(categoryName);

  // 滚动到表格顶部
  scrollToTableTop();

  showMessage(`已筛选"${categoryName}"分类的记录`, 'success');
};

// 滚动到表格中指定日期的记录
const scrollToDateInTable = (targetDate: string) => {
  if (!tableRef.value) return;

  // 查找匹配日期的记录索引
  const targetIndex = sortedExpenseRecords.value.findIndex(record => {
    const recordDate = formatDate(record.date || record.created_at);
    return recordDate === targetDate;
  });

  if (targetIndex !== -1) {
    // 使用Element Plus表格的scrollTo方法
    setTimeout(() => {
      const tableElement = tableRef.value.$el.querySelector('.el-table__body-wrapper');
      if (tableElement) {
        const rowHeight = 60; // 估算的行高
        const scrollTop = targetIndex * rowHeight;
        tableElement.scrollTo({
          top: scrollTop,
          behavior: 'smooth'
        });
      }
      showMessage(`已定位到 ${targetDate} 的记录`, 'success');
    }, 100);
  } else {
    showMessage(`未找到 ${targetDate} 的记录`, 'warning');
  }
};

// 根据日期筛选数据（用于趋势图）
const filterByDate = (dateLabel: string) => {
  // 将图表标签转换为实际日期
  const targetDate = convertLabelToDate(dateLabel);

  if (selectedDate.value === targetDate) {
    // 如果已经选中了这个日期，则取消筛选
    selectedDate.value = '';
    selectedDateType.value = 'day';
    showMessage('已取消日期筛选', 'info');
  } else {
    // 否则筛选该日期
    selectedDate.value = targetDate;
    const displayDate = formatFilterDate(targetDate, selectedDateType.value);
    const filterType = selectedDateType.value === 'month' ? '月份' : '日期';
    showMessage(`已筛选 ${displayDate} 的记录`, 'success');
  }

  // 滚动到表格顶部
  scrollToTableTop();
};

// 根据时间段筛选数据（用于柱状图）
const filterByTimePeriod = (timePeriod: string) => {
  // 这里可以根据时间维度和点击的时间段来筛选数据
  // 由于当前的筛选主要基于分类，这里主要是滚动到表格顶部并提示
  scrollToTableTop();
  showMessage(`已显示 ${timePeriod} 的数据`, 'success');
};

// 将图表标签转换为实际日期格式
const convertLabelToDate = (label: string) => {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth();

  if (timeDimension.value === 'month') {
    // 月视图：标签格式为 "1日", "2日" 等，按天筛选
    selectedDateType.value = 'day';
    const day = parseInt(label.replace('日', ''));
    const date = new Date(currentYear, currentMonth, day);
    return formatDate(date);
  } else if (timeDimension.value === 'quarter' || timeDimension.value === 'year') {
    // 季度/年视图：标签格式为 "1月", "2月" 等，按月筛选
    selectedDateType.value = 'month';
    const month = parseInt(label.replace('月', ''));
    return `${currentYear}-${month.toString().padStart(2, '0')}`;
  }

  return label;
};

// 滚动到表格顶部
const scrollToTableTop = () => {
  if (!tableRef.value) return;

  setTimeout(() => {
    const tableElement = tableRef.value.$el.querySelector('.el-table__body-wrapper');
    if (tableElement) {
      tableElement.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  }, 100);
};

// 金额调整方法
const increaseAmount = () => {
  if (expenseForm.value.amount === null) {
    expenseForm.value.amount = 0.1;
  } else {
    expenseForm.value.amount = Math.round((expenseForm.value.amount + 0.1) * 100) / 100;
  }
};

const decreaseAmount = () => {
  if (expenseForm.value.amount === null || expenseForm.value.amount <= 0.1) {
    expenseForm.value.amount = 0;
  } else {
    expenseForm.value.amount = Math.round((expenseForm.value.amount - 0.1) * 100) / 100;
  }
};

// 标签样式方法
const getTagTextColor = (color: string, isActive: boolean) => {
  return isActive ? '#FFFFFF' : '#303133';
};

const getTagBackground = (color: string, isActive: boolean) => {
  if (isActive) {
    return `linear-gradient(135deg, ${color} 0%, ${adjustBrightness(color, 20)} 100%)`;
  } else {
    return `linear-gradient(135deg, ${adjustBrightness(color, 80)} 0%, ${adjustBrightness(color, 90)} 100%)`;
  }
};

const getTagBorderColor = (color: string, isActive: boolean) => {
  if (isActive) {
    return 'rgba(255, 255, 255, 0.3)';
  } else {
    return adjustBrightness(color, 60);
  }
};

// 颜色调整工具函数
const adjustColor = (color: string, amount: number) => {
  const usePound = color[0] === '#';
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;
  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
};

// 亮度调整函数（与事件记录页面保持一致）
const adjustBrightness = (color: string, percent: number) => {
  const usePound = color[0] === '#';
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);

  let r = (num >> 16);
  let g = (num >> 8 & 0x00FF);
  let b = (num & 0x0000FF);

  r = Math.round(r + (255 - r) * (percent / 100));
  g = Math.round(g + (255 - g) * (percent / 100));
  b = Math.round(b + (255 - b) * (percent / 100));

  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;

  return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
};

// "全部"标签的特殊样式方法（与事件记录页面保持一致）
const getAllTagBackground = (isActive: boolean) => {
  if (isActive) {
    // 激活状态：蓝绿渐变（与原设计一致）
    return 'linear-gradient(135deg, #409EFF 0%, #67C23A 100%)';
  } else {
    // 非激活状态：浅灰色渐变
    return 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)';
  }
};

const getAllTagColor = (isActive: boolean) => {
  if (isActive) {
    return 'white';
  } else {
    return '#495057';
  }
};

const getAllTagBorderColor = (isActive: boolean) => {
  if (isActive) {
    return 'rgba(255, 255, 255, 0.3)';
  } else {
    return '#dee2e6';
  }
};

// 分类相关方法
const handleCategoryFilter = (categoryName: string) => {
  // 如果点击的是已选中的分类，则取消选中（设置为空字符串表示显示全部）
  if (selectedCategory.value === categoryName) {
    selectedCategory.value = '';
    quickForm.value.category = ''; // 同时清空快速表单的分类
  } else {
    // 否则选中该分类
    selectedCategory.value = categoryName;
    quickForm.value.category = categoryName; // 同时设置快速表单的分类
    // 清除日期筛选，避免冲突
    selectedDate.value = '';
  }
};

// 清除所有筛选
const clearAllFilters = () => {
  selectedCategory.value = '';
  selectedDate.value = '';
  selectedDateType.value = 'day';
  quickForm.value.category = '';
  showMessage('已清除所有筛选', 'info');
};

// 格式化筛选日期显示
const formatFilterDate = (date: string, type: 'day' | 'month') => {
  if (type === 'month') {
    // 月份格式：2024-01 -> 2024年1月
    const [year, month] = date.split('-');
    return `${year}年${parseInt(month)}月`;
  } else {
    // 日期格式：直接返回
    return date;
  }
};

// 循环切换时间维度
const cyclePeriodDimension = () => {
  const dimensions = ['month', 'quarter', 'year'] as const;
  const currentIndex = dimensions.indexOf(timeDimension.value);
  const nextIndex = (currentIndex + 1) % dimensions.length;
  const nextDimension = dimensions[nextIndex];

  timeDimension.value = nextDimension;

  // 显示切换提示
  const dimensionNames = {
    month: '月',
    quarter: '季度',
    year: '年'
  };

  showMessage(`已切换到${dimensionNames[nextDimension]}视图`, 'success');
};

// 图片上传相关函数
const uploadRef = ref();
const quickUploadRef = ref();

// 图片预览相关状态
const showImagePreview = ref(false);
const previewImageUrl = ref('');

// 快速记账图片上传相关状态
const showTooltip = ref(false);
const quickImageUploading = ref(false);
const quickUploadProgress = ref(0);

// 详细记录图片上传处理
const handleBeforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isImage) {
    showMessage('只能上传图片文件！', 'error');
    return false;
  }
  if (!isLt10M) {
    showMessage('图片大小不能超过 10MB！', 'error');
    return false;
  }
  return false; // 阻止自动上传
};

const handleImageChange = (file: any) => {
  if (file.raw && expenseForm.value.images.length < 3) {
    // 创建预览URL
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageData = {
        file: file.raw,
        preview: e.target?.result as string,
        uploading: false,
        progress: 0
      };
      expenseForm.value.images.push(imageData);
    };
    reader.readAsDataURL(file.raw);
  }

  // 清除上传组件的文件列表，允许重复选择
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

const removeDetailImage = (index: number) => {
  expenseForm.value.images.splice(index, 1);
};

// 保持向后兼容的单图片删除函数
const removeImage = () => {
  expenseForm.value.images = [];
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

// 快速记账图片上传处理
const handleQuickBeforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/');
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isImage) {
    showMessage('只能上传图片文件！', 'error');
    return false;
  }
  if (!isLt10M) {
    showMessage('图片大小不能超过 10MB！', 'error');
    return false;
  }
  return false; // 阻止自动上传
};

const handleQuickImageChange = (file: any) => {
  if (file.raw && quickForm.value.images.length < 3) {
    // 创建预览URL
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageData = {
        file: file.raw,
        preview: e.target?.result as string,
        uploading: false,
        progress: 0
      };
      quickForm.value.images.push(imageData);
    };
    reader.readAsDataURL(file.raw);
  }

  // 清除上传组件的文件列表，允许重复选择
  if (quickUploadRef.value) {
    quickUploadRef.value.clearFiles();
  }
};

const removeQuickImage = (index?: number) => {
  if (typeof index === 'number') {
    // 删除指定索引的图片
    quickForm.value.images.splice(index, 1);
  } else {
    // 清空所有图片
    quickForm.value.images = [];
  }

  if (quickUploadRef.value) {
    quickUploadRef.value.clearFiles();
  }
};

// 上传单张图片到Supabase存储
const uploadSingleImageToSupabase = async (file: File, userId: string) => {
  if (!file) return null;

  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/expense-images/${Date.now()}_${Math.random().toString(36).substr(2, 9)}.${fileExt}`;

    // 上传到存储
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('pet-media')
      .upload(fileName, file, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      throw new Error(`图片上传失败: ${uploadError.message}`);
    }

    // 获取公开URL
    const { data: { publicUrl } } = supabase.storage
      .from('pet-media')
      .getPublicUrl(fileName);

    return {
      url: publicUrl,
      path: fileName
    };
  } catch (error) {
    console.error('图片上传失败:', error);
    throw error;
  }
};

// 上传多张图片到Supabase存储
const uploadMultipleImagesToSupabase = async (images: Array<any>, userId: string) => {
  if (!images || images.length === 0) return [];

  const uploadResults = [];

  // 启动进度条
  quickImageUploading.value = true;
  quickUploadProgress.value = 0;

  try {
    for (let i = 0; i < images.length; i++) {
      const image = images[i];

      // 更新进度
      const baseProgress = (i / images.length) * 100;
      quickUploadProgress.value = baseProgress;

      try {
        const result = await uploadSingleImageToSupabase(image.file, userId);
        if (result) {
          uploadResults.push(result);
        }

        // 更新完成进度
        quickUploadProgress.value = ((i + 1) / images.length) * 100;
      } catch (error) {
        console.error(`第${i + 1}张图片上传失败:`, error);
        // 继续上传其他图片
      }
    }

    // 短暂显示100%后重置
    setTimeout(() => {
      quickImageUploading.value = false;
      quickUploadProgress.value = 0;
    }, 500);

    return uploadResults;
  } catch (error) {
    console.error('批量图片上传失败:', error);
    quickImageUploading.value = false;
    quickUploadProgress.value = 0;
    throw error;
  }
};

// 保持向后兼容的单图片上传函数
const uploadImageToSupabase = async (file: File, userId: string, isQuickUpload = false) => {
  return await uploadSingleImageToSupabase(file, userId);
};

// 图片预览函数
const previewImage = (imageUrl: string) => {
  previewImageUrl.value = imageUrl;
  showImagePreview.value = true;
};

// 悬浮预览控制函数
const showImageTooltip = () => {
  if (quickForm.value.images.length > 0) {
    showTooltip.value = true;
  }
};

const hideImageTooltip = () => {
  showTooltip.value = false;
};

// 获取图片按钮文字
const getImageButtonText = () => {
  const count = quickForm.value.images.length;
  if (count === 0) {
    return '添加图片';
  } else if (count >= 3) {
    return '已满3张';
  } else {
    return `已上传${count}张`;
  }
};

// 文件大小格式化函数
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 获取额外图片数量
const getAdditionalImagesCount = (record: any) => {
  if (!record.additional_images) return 0;
  try {
    const additionalImages = JSON.parse(record.additional_images);
    return Array.isArray(additionalImages) ? additionalImages.length : 0;
  } catch (error) {
    return 0;
  }
};

const handleQuickAddByCategory = (categoryName: string) => {
  quickForm.value.category = categoryName;
  // 可以在这里打开快速添加对话框或直接进入快速输入模式
};

// 打开添加对话框
const openAddDialog = () => {
  isEditing.value = false;
  editingRecord.value = null;

  // 获取当前时间并格式化为 YYYY-MM-DD HH:mm:ss
  const now = new Date();
  const currentDateTime = now.getFullYear() + '-' +
    String(now.getMonth() + 1).padStart(2, '0') + '-' +
    String(now.getDate()).padStart(2, '0') + ' ' +
    String(now.getHours()).padStart(2, '0') + ':' +
    String(now.getMinutes()).padStart(2, '0') + ':' +
    String(now.getSeconds()).padStart(2, '0');

  expenseForm.value = {
    amount: null,
    date: currentDateTime,
    category: '',
    description: '',
    images: []
  };

  showDialog.value = true;
};

const handleEditCategory = (category: any) => {
  editingCategory.value = category;
  isEditCategoryMode.value = true;
  categoryForm.value = {
    label: category.name,
    color: category.color
  };
  showEditCategoryDialog.value = true;
};

const handleDeleteCategory = async (category: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除分类"${category.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      ElMessage({ message: '用户未登录', type: 'error' });
      return;
    }

    // 软删除：设置 is_active 为 false
    const { error } = await supabase
      .from('expense_categories')
      .update({ is_active: false })
      .eq('id', category.id)
      .eq('user_id', user.id);

    if (error) throw error;

    // 从本地数据中移除
    const index = expenseCategories.value.findIndex(c => c.id === category.id);
    if (index > -1) {
      expenseCategories.value.splice(index, 1);
    }

    // 如果当前选中的分类被删除，重置筛选
    if (selectedCategory.value === category.name) {
      selectedCategory.value = '';
    }

    showMessage('分类删除成功', 'success');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error);
      showMessage('删除分类失败: ' + error.message, 'error');
    }
  }
};

// 拖拽事件处理
const onDragStart = () => {
  isDragging.value = true;
};

const onDragEnd = async () => {
  isDragging.value = false;

  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    // 更新排序索引
    const updates = expenseCategories.value.map((category, index) => ({
      id: category.id,
      order_index: index + 1
    }));

    // 批量更新排序
    for (const update of updates) {
      const { error } = await supabase
        .from('expense_categories')
        .update({ order_index: update.order_index })
        .eq('id', update.id)
        .eq('user_id', user.id);

      if (error) throw error;
    }

    // 更新本地数据
    expenseCategories.value.forEach((category, index) => {
      category.order_index = index + 1;
    });

    ElMessage.success('分类排序已更新');
  } catch (error) {
    console.error('保存分类排序失败:', error);
    ElMessage.error('保存分类排序失败');
    // 重新加载数据以恢复原始顺序
    await fetchExpenseCategories();
  }
};

// 分类管理方法
const handleCategorySave = async (formData: any) => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      ElMessage.error('用户未登录');
      return;
    }

    if (isEditCategoryMode.value && editingCategory.value) {
      // 编辑现有分类
      const { error } = await supabase
        .from('expense_categories')
        .update({
          name: formData.label,
          color: formData.color
        })
        .eq('id', editingCategory.value.id)
        .eq('user_id', user.id);

      if (error) throw error;

      // 更新本地数据
      const index = expenseCategories.value.findIndex(c => c.id === editingCategory.value.id);
      if (index > -1) {
        expenseCategories.value[index] = {
          ...expenseCategories.value[index],
          name: formData.label,
          color: formData.color
        };
      }

      ElMessage.success('分类编辑成功');
    } else {
      // 添加新分类
      const newCategoryData = {
        user_id: user.id,
        name: formData.label,
        color: formData.color,
        order_index: expenseCategories.value.length + 1,
        is_active: true
      };

      const { data, error } = await supabase
        .from('expense_categories')
        .insert([newCategoryData])
        .select()
        .single();

      if (error) throw error;

      expenseCategories.value.push(data);
      ElMessage.success('分类添加成功');
    }

    // 关闭对话框
    showAddCategoryDialog.value = false;
    showEditCategoryDialog.value = false;
    resetCategoryDialog();
  } catch (error) {
    console.error('保存分类失败:', error);
    ElMessage.error('保存分类失败: ' + error.message);
  }
};

const handleCategoryCancel = () => {
  showAddCategoryDialog.value = false;
  showEditCategoryDialog.value = false;
  resetCategoryDialog();
};

const resetCategoryDialog = () => {
  isEditCategoryMode.value = false;
  editingCategory.value = null;
  categoryForm.value = {
    label: '',
    color: '#409EFF'
  };
};

// 快速记账相关计算属性和方法
const canQuickSubmit = computed(() => {
  return quickForm.value.amount && quickForm.value.category;
});

const submitQuickExpense = async () => {
  if (!canQuickSubmit.value || !currentPetId.value) {
    ElMessage.error('请选择金额和类别');
    return;
  }

  try {
    quickSubmitting.value = true;

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      ElMessage.error('用户未登录');
      return;
    }

    // 上传图片（如果有）
    let imageUrls = [];
    let imagePaths = [];

    if (quickForm.value.images.length > 0) {
      try {
        const uploadResults = await uploadMultipleImagesToSupabase(quickForm.value.images, user.id);
        imageUrls = uploadResults.map(result => result.url);
        imagePaths = uploadResults.map(result => result.path);
      } catch (error) {
        showMessage('部分图片上传失败，但记录将继续保存', 'warning');
        console.error('图片上传失败:', error);
      }
    }

    const recordData = {
      user_id: user.id,
      pet_id: currentPetId.value,
      category: quickForm.value.category,
      amount: quickForm.value.amount,
      date: new Date().toISOString(),
      description: quickForm.value.description || `快速记录 - ${quickForm.value.category}`,
      image_url: imageUrls.length > 0 ? imageUrls[0] : null, // 主图片
      image_path: imagePaths.length > 0 ? imagePaths[0] : null, // 主图片路径
      additional_images: imageUrls.length > 1 ? JSON.stringify(imageUrls.slice(1)) : null, // 额外图片
      additional_image_paths: imagePaths.length > 1 ? JSON.stringify(imagePaths.slice(1)) : null // 额外图片路径
    };

    const { error } = await supabase
      .from('expense_records')
      .insert([recordData]);

    if (error) throw error;

    showMessage('快速记录成功', 'success');

    // 重置快速表单
    quickForm.value = {
      amount: null,
      category: '',
      description: '',
      images: []
    };

    // 清除上传组件
    if (quickUploadRef.value) {
      quickUploadRef.value.clearFiles();
    }

    // 刷新数据
    await fetchExpenseRecords();
  } catch (error) {
    console.error('快速记录失败:', error);
    showMessage('快速记录失败: ' + (error.message || error), 'error');
  } finally {
    quickSubmitting.value = false;
  }
};



const handleSort = (field: 'date' | 'amount', order: 'asc' | 'desc') => {
  sortConfig.value.field = field;
  sortConfig.value.order = order;
};

const tableRowClassName = ({ row, rowIndex }: { row: any; rowIndex: number }) => {
  return rowIndex % 2 === 1 ? 'alternate-row' : '';
};

// 数据操作函数
const fetchExpenseRecords = async () => {
  if (!currentPetId.value) return;

  try {
    loading.value = true;
    const { data, error } = await supabase
      .from('expense_records')
      .select('*')
      .eq('pet_id', currentPetId.value)
      .order('date', { ascending: false });

    if (error) throw error;
    expenseRecords.value = data || [];
  } catch (error) {
    console.error('获取花费记录失败:', error);
    ElMessage.error('获取花费记录失败');
  } finally {
    loading.value = false;
  }
};

// 加载分类数据
const fetchExpenseCategories = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const { data, error } = await supabase
      .from('expense_categories')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('order_index', { ascending: true });

    if (error) throw error;

    if (data && data.length > 0) {
      expenseCategories.value = data;
    } else {
      // 如果没有分类，创建默认分类
      await createDefaultCategories();
    }
  } catch (error) {
    console.error('获取分类失败:', error);
    ElMessage.error('获取分类失败');
  }
};

// 创建默认分类
const createDefaultCategories = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const defaultCategories = [
      { name: '食物', color: '#FF6B6B', order_index: 1 },
      { name: '医疗', color: '#4ECDC4', order_index: 2 },
      { name: '玩具', color: '#45B7D1', order_index: 3 },
      { name: '用品', color: '#96CEB4', order_index: 4 },
      { name: '美容', color: '#FFEAA7', order_index: 5 },
      { name: '其他', color: '#F7DC6F', order_index: 6 }
    ];

    const categoriesToInsert = defaultCategories.map(cat => ({
      ...cat,
      user_id: user.id,
      is_active: true
    }));

    const { data, error } = await supabase
      .from('expense_categories')
      .insert(categoriesToInsert)
      .select();

    if (error) throw error;
    expenseCategories.value = data || [];
    ElMessage.success('已创建默认分类');
  } catch (error) {
    console.error('创建默认分类失败:', error);
    ElMessage.error('创建默认分类失败');
  }
};



const submitForm = async () => {
  if (!formRef.value || !currentPetId.value) {
    showMessage('请先选择一个宠物', 'error');
    return;
  }

  try {
    await formRef.value.validate();
    submitting.value = true;

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      showMessage('用户未登录', 'error');
      return;
    }

    // 处理图片上传
    let imageUrls = [];
    let imagePaths = [];

    // 分离需要上传的新图片和已存在的图片
    const newImages = expenseForm.value.images.filter(img => img.file);
    const existingImages = expenseForm.value.images.filter(img => !img.file && img.url);

    // 上传新图片
    if (newImages.length > 0) {
      try {
        const uploadResults = await uploadMultipleImagesToSupabase(newImages, user.id);
        imageUrls.push(...uploadResults.map(result => result.url));
        imagePaths.push(...uploadResults.map(result => result.path));
      } catch (error) {
        showMessage('部分图片上传失败，但记录将继续保存', 'warning');
        console.error('图片上传失败:', error);
      }
    }

    // 添加已存在的图片
    existingImages.forEach(img => {
      if (img.url) {
        imageUrls.push(img.url);
        imagePaths.push(img.path || '');
      }
    });

    // 处理日期格式，确保保存正确的时间
    let formattedDate = expenseForm.value.date;
    if (formattedDate) {
      // 如果是 Date 对象，转换为 ISO 字符串
      if (formattedDate instanceof Date) {
        formattedDate = formattedDate.toISOString();
      } else if (typeof formattedDate === 'string') {
        // 如果是字符串，确保格式正确并转换为 ISO 格式
        try {
          const dateObj = new Date(formattedDate);
          if (!isNaN(dateObj.getTime())) {
            formattedDate = dateObj.toISOString();
          }
        } catch (error) {
          console.error('日期格式转换失败:', error);
          formattedDate = new Date().toISOString(); // 使用当前时间作为备用
        }
      }
    } else {
      formattedDate = new Date().toISOString(); // 如果没有日期，使用当前时间
    }

    const recordData = {
      user_id: user.id,
      pet_id: currentPetId.value,
      category: expenseForm.value.category,
      amount: expenseForm.value.amount,
      date: formattedDate,
      description: expenseForm.value.description || null,
      image_url: imageUrls.length > 0 ? imageUrls[0] : null, // 主图片
      image_path: imagePaths.length > 0 ? imagePaths[0] : null, // 主图片路径
      additional_images: imageUrls.length > 1 ? JSON.stringify(imageUrls.slice(1)) : null, // 额外图片
      additional_image_paths: imagePaths.length > 1 ? JSON.stringify(imagePaths.slice(1)) : null // 额外图片路径
    };

    let result;
    if (isEditing.value && editingRecord.value) {
      // 编辑时不需要更新 user_id
      const { user_id, ...updateData } = recordData;
      result = await supabase
        .from('expense_records')
        .update(updateData)
        .eq('id', editingRecord.value.id);
    } else {
      result = await supabase
        .from('expense_records')
        .insert([recordData]);
    }

    if (result.error) throw result.error;

    showMessage(isEditing.value ? '花费记录更新成功' : '花费记录添加成功', 'success');
    showDialog.value = false;
    await fetchExpenseRecords();
  } catch (error) {
    console.error('保存花费记录失败:', error);
    showMessage('保存花费记录失败: ' + (error.message || error), 'error');
  } finally {
    submitting.value = false;
  }
};

const openEditDialog = (record: any) => {
  isEditing.value = true;
  editingRecord.value = record;

  // 处理多图片数据
  const images = [];

  // 添加主图片
  if (record.image_url) {
    images.push({
      preview: record.image_url,
      url: record.image_url,
      path: record.image_path
    });
  }

  // 添加额外图片
  if (record.additional_images) {
    try {
      const additionalUrls = JSON.parse(record.additional_images);
      const additionalPaths = record.additional_image_paths ? JSON.parse(record.additional_image_paths) : [];

      additionalUrls.forEach((url: string, index: number) => {
        images.push({
          preview: url,
          url: url,
          path: additionalPaths[index] || ''
        });
      });
    } catch (error) {
      console.error('解析额外图片数据失败:', error);
    }
  }

  // 处理日期格式，确保日期选择器能正确显示
  let recordDate = record.date || record.created_at;
  // 如果是字符串，确保格式正确
  if (typeof recordDate === 'string') {
    // 如果没有时间部分，添加默认时间
    if (!recordDate.includes(' ')) {
      recordDate = recordDate + ' 00:00:00';
    }
    // 确保格式为 YYYY-MM-DD HH:mm:ss
    recordDate = recordDate.replace('T', ' ').split('.')[0];
  }

  expenseForm.value = {
    amount: parseFloat(record.amount),
    date: recordDate,
    category: record.category,
    description: record.description || '',
    images: images
  };
  showDialog.value = true;
};

const deleteRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条花费记录吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const { error } = await supabase
      .from('expense_records')
      .delete()
      .eq('id', record.id);

    if (error) throw error;

    ElMessage.success('花费记录删除成功');
    await fetchExpenseRecords();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除花费记录失败:', error);
      ElMessage.error('删除花费记录失败');
    }
  }
};

const batchDeleteRecords = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRecords.value.length} 条花费记录吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const ids = selectedRecords.value.map(record => record.id);
    const { error } = await supabase
      .from('expense_records')
      .delete()
      .in('id', ids);

    if (error) throw error;

    ElMessage.success(`成功删除 ${selectedRecords.value.length} 条花费记录`);
    selectedRecords.value = [];
    await fetchExpenseRecords();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除花费记录失败:', error);
      ElMessage.error('批量删除花费记录失败');
    }
  }
};

const handleSelectionChange = (selection: any[]) => {
  selectedRecords.value = selection;
};

const resetForm = () => {
  // 获取当前时间并格式化为 YYYY-MM-DD HH:mm:ss
  const now = new Date();
  const currentDateTime = now.getFullYear() + '-' +
    String(now.getMonth() + 1).padStart(2, '0') + '-' +
    String(now.getDate()).padStart(2, '0') + ' ' +
    String(now.getHours()).padStart(2, '0') + ':' +
    String(now.getMinutes()).padStart(2, '0') + ':' +
    String(now.getSeconds()).padStart(2, '0');

  expenseForm.value = {
    amount: null,
    date: currentDateTime,
    category: '',
    description: '',
    images: []
  };

  // 清除上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }

  if (formRef.value) {
    formRef.value.clearValidate();
  }
};

const handleCurrentPetChanged = async (event: any) => {
  const petId = event.detail?.petId || event;
  if (petId) {
    await Promise.all([
      fetchExpenseRecords(),
      fetchExpenseCategories()
    ]);
  } else {
    expenseRecords.value = [];
  }
};

// 生命周期
onMounted(async () => {
  // 监听当前宠物变化
  window.addEventListener('currentPetChanged', handleCurrentPetChanged, { passive: true });

  // 获取当前选中的宠物ID
  const petId = localStorage.getItem('currentPetId');
  if (petId) {
    await Promise.all([
      fetchExpenseRecords(),
      fetchExpenseCategories()
    ]);
  } else {
    // 在模拟模式下设置默认宠物ID
    localStorage.setItem('currentPetId', 'mock-pet-1');
    await Promise.all([
      fetchExpenseRecords(),
      fetchExpenseCategories()
    ]);
  }


});



onBeforeUnmount(() => {
  window.removeEventListener('currentPetChanged', handleCurrentPetChanged);
});
</script>

<style scoped>
.expense-tracking-view {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.no-pet-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* 页面标题区域 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.add-expense-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.add-expense-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
}

.add-icon {
  margin-right: 8px;
  font-size: 18px;
}

.btn-text {
  font-size: 16px;
}

/* 时间控制面板 */
.time-controls-section {
  margin-bottom: 24px;
}

.time-controls-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.time-controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.controls-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.controls-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.current-period-info {
  font-size: 14px;
  color: #909399;
}

.controls-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 快速记账面板 */
.quick-expense-section {
  margin-bottom: 24px;
}

.quick-expense-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.quick-expense-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-expense-header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.quick-expense-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.detailed-entry-btn {
  flex-shrink: 0;
  background: linear-gradient(135deg, #909399, #606266);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  color: white;
}

.detailed-entry-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
  background: linear-gradient(135deg, #606266, #909399);
}

.quick-expense-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 12px;
}

/* 分类标签面板 */
.category-tags-section {
  margin-bottom: 20px;
}

.category-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.all-category-tag {
  margin-right: 8px;
}

.draggable-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

/* 拖拽状态样式 */
.is-dragging {
  opacity: 0.8;
  transform: rotate(5deg);
  transition: all 0.3s ease;
}

.ghost {
  opacity: 0.5;
  background: #f0f9ff;
  border: 2px dashed #409EFF;
}

/* AddButton 组件自带样式，无需额外定义 */

/* 快速输入区域 */
.quick-input-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.quick-input-form {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 8px;
}

.quick-action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.amount-input {
  width: 120px;
  flex-shrink: 0;
}

.description-input {
  flex: 1;
  min-width: 150px;
}

.quick-add-btn {
  flex-shrink: 0;
  background: linear-gradient(135deg, #67C23A, #409EFF);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.quick-add-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.quick-add-btn:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

.category-hint {
  text-align: center;
  margin-top: 8px;
}

.selected-category-hint {
  font-size: 12px;
  color: #909399;
}

.selected-category-hint .category-name {
  color: #409EFF;
  font-weight: 500;
}

.no-category-hint {
  font-size: 12px;
  color: #909399;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px dashed #e4e7ed;
}

/* 快速金额选择 */
.quick-amounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

.quick-amount-btn {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  background: white;
  transition: all 0.3s ease;
  min-height: 44px;
}

.quick-amount-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.quick-amount-btn.selected {
  border-color: #409EFF;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 快速类别选择 */
.quick-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.quick-category-btn {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px 8px;
  background: white;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  min-height: 64px;
  color: #606266;
}

.quick-category-btn:hover {
  border-color: var(--category-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quick-category-btn.selected {
  border-color: var(--category-color);
  background: var(--category-color);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.category-icon {
  font-size: 20px;
}

.category-name {
  font-size: 12px;
  font-weight: 500;
}

/* 快速提交区域 */
.quick-submit-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  gap: 16px;
}

.quick-form-preview {
  flex: 1;
}

.preview-text {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.preview-placeholder {
  font-size: 14px;
  color: #909399;
}

.quick-submit-btn {
  background: linear-gradient(135deg, #67C23A, #409EFF);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 120px;
}

.quick-submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4);
}

.quick-submit-btn:disabled {
  background: #c0c4cc;
  cursor: not-allowed;
}

/* 花费概览区域样式已移至上方动画部分 */

.expense-chart-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.chart-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.period-title {
  font-size: 14px;
  color: #909399;
  background: #f0f9ff;
  padding: 4px 12px;
  border-radius: 16px;
  border: 1px solid #e1f5fe;
}

.chart-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.chart-controls-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.chart-controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.collapse-btn {
  color: #909399;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: #f5f7fa;
  color: #409EFF;
}

/* 图表类型切换样式 */
.chart-type-selector {
  display: flex;
  align-items: center;
}

.chart-type-selector :deep(.enhanced-view-toggle) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.chart-type-selector :deep(.toggle-option) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-type-selector :deep(.toggle-option.active) {
  background: white;
  color: #667eea;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.chart-type-selector :deep(.toggle-option:not(.active)) {
  color: rgba(255, 255, 255, 0.8);
}

.chart-type-selector :deep(.toggle-option:not(.active):hover) {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

/* 时间维度切换样式 */
.time-dimension-selector {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.time-dimension-selector :deep(.enhanced-view-toggle) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 2px;
}

.time-dimension-selector :deep(.toggle-option) {
  border-radius: 4px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s ease;
}

.time-dimension-selector :deep(.toggle-option.active) {
  background: #409EFF;
  color: white;
  box-shadow: 0 1px 3px rgba(64, 158, 255, 0.3);
}

.time-dimension-selector :deep(.toggle-option:not(.active):hover) {
  background: #e9ecef;
  color: #495057;
}

.chart-content {
  padding: 0;
  position: relative;
  min-height: 400px;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-content > * {
  transition: all 0.3s ease-in-out;
}

/* 优化的折叠动画 */
.expense-overview-section {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 24px;
}

.expense-overview-section.chart-collapsed {
  margin-bottom: 16px;
}

.expense-records-section {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 24px;
}

.expense-records-section.chart-collapsed {
  transform: translateY(-20px);
}

/* Element Plus 折叠过渡优化 */
.el-collapse-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 图表容器动画 */
.chart-container {
  transition: all 0.3s ease-in-out;
}

/* 折叠时的图表优化 */
.expense-chart {
  transition: all 0.3s ease-in-out;
}

.chart-container {
  width: 100%;
  height: 400px;
  min-height: 300px;
  position: relative;
  transition: all 0.3s ease-in-out;
}

.expense-chart {
  height: 100% !important;
  width: 100% !important;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.expense-chart:hover {
  transform: scale(1.01);
}

/* 骨架屏样式 */
.chart-skeleton {
  padding: 20px;
}

.skeleton-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.skeleton-chart-area {
  width: 100%;
  margin-top: 20px;
}

.stat-skeleton {
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.table-skeleton {
  padding: 20px;
}

.skeleton-table {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-table-header,
.skeleton-table-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.skeleton-table-header {
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.skeleton-table-row {
  padding: 12px 0;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 24px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 24px;
  gap: 16px;
  min-height: 96px; /* 确保骨架屏和实际内容高度一致 */
}

.stat-info {
  flex: 1;
  transition: opacity 0.3s ease-in-out;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.total-expense .stat-icon {
  background: linear-gradient(135deg, #E6A23C, #F56C6C);
}

.average-expense .stat-icon {
  background: linear-gradient(135deg, #409EFF, #67C23A);
}

.highest-expense .stat-icon {
  background: linear-gradient(135deg, #909399, #606266);
}

.current-period-expense .stat-icon {
  background: linear-gradient(135deg, #67C23A, #409EFF);
  position: relative;
}

/* 可点击统计卡片样式 */
.clickable-stat-card {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
}

.clickable-stat-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 12px 32px rgba(103, 194, 58, 0.25);
}

.clickable-stat-card:active {
  transform: translateY(-4px) scale(1.01);
}

/* 循环指示器 */
.cycle-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #67C23A;
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.clickable-stat-card:hover .cycle-indicator {
  opacity: 1;
  transform: rotate(180deg);
}

/* 循环提示文字 */
.cycle-hint {
  font-size: 11px;
  color: #67C23A;
  margin-top: 4px;
  opacity: 0;
  transition: all 0.3s ease;
  font-weight: 500;
}

.clickable-stat-card:hover .cycle-hint {
  opacity: 1;
}

/* 图片上传相关样式 */
.image-upload-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.image-uploader {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-uploader:hover {
  border-color: #409EFF;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #8c939d;
}

.upload-icon {
  font-size: 28px;
}

.upload-text {
  font-size: 12px;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.overlay-icon {
  color: white;
  font-size: 20px;
}

.image-actions {
  display: flex;
  gap: 8px;
}

/* 快速记账图片上传样式 */
.quick-image-upload-container {
  position: relative;
  flex-shrink: 0;
}

.quick-image-uploader {
  flex-shrink: 0;
}

.quick-image-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100px !important;
  min-width: 100px;
  max-width: 100px;
}

/* 按钮内部进度条填充效果 */
.button-progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  transition: width 0.3s ease;
  z-index: 1;
  border-radius: inherit;
}

.button-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: button-progress-shine 1.5s infinite;
}

@keyframes button-progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.button-content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 100%;
}

/* 上传时文字变为白色 */
.quick-image-btn.uploading .button-content {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.button-text {
  white-space: nowrap;
}

/* 上传状态下的按钮样式 */
.quick-image-btn.uploading {
  pointer-events: none;
  cursor: not-allowed;
}

/* 悬浮预览面板样式 */
.image-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
  z-index: 1000;
  animation: tooltip-fade-in 0.2s ease-out;
}

@keyframes tooltip-fade-in {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.tooltip-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 12px;
  border: 1px solid #e4e7ed;
  width: 100%;
}

/* 单张图片布局 */
.image-tooltip.images-1 {
  min-width: 200px;
  max-width: 300px;
}

/* 两张图片布局 */
.image-tooltip.images-2 {
  min-width: 350px;
  max-width: 400px;
}

/* 三张图片布局 */
.image-tooltip.images-3 {
  min-width: 450px;
  max-width: 500px;
}

.images-grid {
  display: grid;
  gap: 12px;
}

/* 单张图片网格 */
.images-1 .images-grid {
  grid-template-columns: 1fr;
}

/* 两张图片网格 */
.images-2 .images-grid {
  grid-template-columns: 1fr 1fr;
}

/* 三张图片网格 */
.images-3 .images-grid {
  grid-template-columns: 1fr 1fr 1fr;
}

.image-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.tooltip-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  flex-shrink: 0;
}

.image-overlay {
  position: absolute;
  top: 2px;
  right: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.delete-btn {
  width: 20px;
  height: 20px;
  padding: 0;
  background: rgba(245, 108, 108, 0.9);
  border: none;
  color: white;
}

.delete-btn:hover {
  background: #f56c6c;
}

.image-info {
  text-align: center;
  min-width: 0;
  width: 100%;
}

.file-name {
  font-size: 12px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.file-size {
  font-size: 11px;
  color: #909399;
  margin: 0;
}

.tooltip-arrow {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid white;
}

.tooltip-arrow::before {
  content: '';
  position: absolute;
  top: -7px;
  left: -6px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #e4e7ed;
}

/* 表格图片相关样式 */
.image-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.expense-images-preview {
  display: flex;
  align-items: center;
  gap: 4px;
}

.expense-image-preview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;
  position: relative;
}

.expense-image-preview:hover {
  transform: scale(1.1);
}

.expense-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.additional-images-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.additional-images-indicator:hover {
  background: #337ecc;
  transform: scale(1.1);
}

.image-count {
  line-height: 1;
}

.no-image {
  color: #c0c4cc;
  font-size: 12px;
}

/* 多图片上传样式 */
.multi-image-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.uploaded-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  max-width: 100%;
}

.uploaded-image-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  background: #fafafa;
  transition: all 0.2s ease;
  min-height: 160px;
}

.uploaded-image-item:hover {
  border-color: #409EFF;
  background: #f0f9ff;
}

.uploaded-preview-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  flex-shrink: 0;
}

.uploaded-image-item .image-overlay {
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.uploaded-image-item:hover .image-overlay {
  opacity: 1;
}

.remove-image-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  background: rgba(245, 108, 108, 0.9);
  border: none;
  color: white;
}

.remove-image-btn:hover {
  background: #f56c6c;
}

.image-info {
  text-align: center;
  width: 100%;
}

.image-name {
  font-size: 11px;
  color: #606266;
  word-break: break-all;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: center;
  margin-top: 4px;
}

.upload-placeholder.disabled {
  background: #f5f7fa;
  color: #c0c4cc;
  cursor: not-allowed;
}

.upload-placeholder.disabled .upload-icon {
  color: #c0c4cc;
}

/* 图片预览对话框样式 */
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.preview-full-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}



.stat-label {
  display: block;
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stat-date {
  font-size: 12px;
  color: #C0C4CC;
  font-weight: 400;
}

/* 记录表格区域样式已移至上方动画部分 */

.expense-records-section .el-card__body {
  transition: opacity 0.3s ease-in-out;
  min-height: 200px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.table-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.filter-status {
  font-size: 14px;
  color: #409EFF;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.record-count {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 8px;
  border-radius: 10px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.clear-filter-btn {
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  height: 28px;
}

.delete-batch-btn {
  background: linear-gradient(135deg, #F56C6C, #E6A23C);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.delete-batch-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

.delete-icon {
  margin-right: 6px;
}

/* 表格样式 */
.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.sort-buttons {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.date-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-icon {
  color: #409EFF;
  font-size: 16px;
}

.amount-cell {
  display: flex;
  justify-content: center;
}

.amount-tag {
  font-weight: 600;
  font-size: 14px;
}

.description-text {
  color: #606266;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* 表格行样式 */
:deep(.el-table .alternate-row) {
  background-color: #fafafa;
}

:deep(.el-table .alternate-row:hover > td) {
  background-color: #f0f9ff !important;
}

/* 对话框样式 */
.amount-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.amount-btn {
  color: #409EFF;
  font-size: 16px;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-btn:hover {
  background: #e3f2fd;
  color: #1976d2;
  transform: scale(1.1);
}

.unit-label {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

/* 响应式设计 */

/* 大屏幕桌面端 (≥1200px) */
@media (min-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }

  .quick-amounts-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  .quick-categories-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  .chart-controls {
    flex-direction: row;
    gap: 20px;
  }

  .chart-controls-left,
  .chart-controls-right {
    flex-direction: row;
    gap: 16px;
  }
}

/* 平板端 (768px - 1199px) */
@media (min-width: 769px) and (max-width: 1199px) {
  .expense-tracking-view {
    padding: 18px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 18px;
  }

  .quick-amounts-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .quick-categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .chart-card-header {
    flex-wrap: wrap;
    gap: 12px;
  }

  .chart-controls {
    flex-direction: row;
    gap: 16px;
  }

  .chart-controls-left,
  .chart-controls-right {
    flex-direction: row;
    gap: 12px;
  }

  /* 平板端表格优化 */
  .expense-chart {
    height: 350px;
  }

  .stat-content {
    padding: 20px;
  }
}

/* 移动端 (≤768px) */
@media (max-width: 768px) {
  .expense-tracking-view {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .page-title {
    font-size: 26px;
    text-align: center;
  }

  .add-expense-btn {
    width: 100%;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-content {
    padding: 18px;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .table-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .table-actions {
    justify-content: flex-start;
  }

  .quick-input-form {
    flex-direction: column;
    gap: 12px;
  }

  .amount-input,
  .description-input {
    width: 100%;
  }

  .category-tags-container {
    justify-content: center;
  }

  .chart-card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .chart-controls {
    flex-direction: column;
    gap: 12px;
  }

  .chart-controls-left,
  .chart-controls-right {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .quick-amounts-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .quick-categories-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }

  .quick-submit-section {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .quick-submit-btn {
    width: 100%;
  }

  .expense-chart {
    height: 300px;
  }

  /* 移动端表格滚动优化 */
  .expense-records-section :deep(.el-table) {
    font-size: 14px;
  }

  .expense-records-section :deep(.el-table th) {
    padding: 8px 4px;
  }

  .expense-records-section :deep(.el-table td) {
    padding: 8px 4px;
  }

  /* 移动端多图片上传优化 */
  .uploaded-images {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .uploaded-image-item {
    min-height: 140px;
    padding: 10px;
  }

  .uploaded-preview-image {
    width: 70px;
    height: 70px;
  }

  .amount-tag {
    font-size: 12px;
    padding: 2px 6px;
  }

  .date-cell {
    flex-direction: column;
    gap: 2px;
    align-items: flex-start;
  }

  .date-icon {
    display: none;
  }
}

/* 小屏移动端 (≤480px) */
@media (max-width: 480px) {
  .expense-tracking-view {
    padding: 8px;
  }

  .page-title {
    font-size: 22px;
  }

  .add-expense-btn {
    padding: 12px 16px;
    font-size: 14px;
  }

  .stat-content {
    padding: 16px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
    margin: 0 auto;
  }

  .stat-value {
    font-size: 18px;
  }

  .stat-label {
    font-size: 13px;
  }

  .quick-amounts-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .quick-categories-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .quick-amount-btn,
  .quick-category-btn {
    padding: 10px 8px;
    font-size: 12px;
    min-height: 40px;
  }

  .expense-chart {
    height: 250px;
  }

  .chart-title {
    font-size: 16px;
  }

  .period-title {
    font-size: 12px;
    padding: 2px 8px;
  }

  /* 极小屏幕表格优化 */
  .expense-records-section :deep(.el-table) {
    font-size: 12px;
  }

  .column-header {
    flex-direction: column;
    gap: 4px;
  }

  .sort-buttons {
    margin-left: 0;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  /* 小屏幕多图片上传优化 */
  .uploaded-images {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .uploaded-image-item {
    min-height: 120px;
    padding: 8px;
    flex-direction: row;
    text-align: left;
  }

  .uploaded-preview-image {
    width: 60px;
    height: 60px;
  }

  .image-info {
    flex: 1;
    text-align: left;
    margin-left: 12px;
  }
}
</style>