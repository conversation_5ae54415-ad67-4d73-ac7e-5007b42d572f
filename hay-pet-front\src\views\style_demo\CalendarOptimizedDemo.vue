<template>
  <div class="calendar-optimized-demo">
    <div class="demo-header">
      <h1>📅 优化后的日历视图演示</h1>
      <p>展示优化后的日期选择样式、今天日期高亮、记录指示器等视觉效果</p>
    </div>

    <!-- 优化后的日历演示 -->
    <div class="demo-section">
      <h2>🎨 优化后的日历视图</h2>
      <div class="demo-content">
        <CalendarViewPreset
          title="优化后的日历"
          :records="demoRecords"
          :record-types="recordTypes"
          :color-mapping="colorMapping"
          :label-mapping="labelMapping"
          :show-stats="true"
          @date-click="handleDateClick"
          @view-change="handleViewChange"
        />
      </div>
    </div>

    <!-- 优化特性说明 -->
    <div class="features-section">
      <h2>✨ 优化特性</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">🎯</div>
          <h3>今天日期高亮</h3>
          <p>今天的日期有特殊的渐变边框动画和突出显示</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🔵</div>
          <h3>记录指示器优化</h3>
          <p>更大的记录点、阴影效果和悬浮动画</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🎨</div>
          <h3>悬浮效果增强</h3>
          <p>渐变背景、平滑动画和立体阴影</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">📊</div>
          <h3>统计卡片集成</h3>
          <p>自动计算统计信息并美观展示</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">📱</div>
          <h3>响应式设计</h3>
          <p>完美适配各种屏幕尺寸</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">⚡</div>
          <h3>性能优化</h3>
          <p>流畅的动画和快速的交互响应</p>
        </div>
      </div>
    </div>

    <!-- 交互日志 -->
    <div class="event-log-section">
      <h2>📋 交互日志</h2>
      <div class="event-log">
        <div v-if="eventLogs.length === 0" class="no-events">
          暂无交互记录，请点击日历进行交互
        </div>
        <div
          v-for="(log, index) in eventLogs"
          :key="index"
          class="log-item"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CalendarViewPreset from '@/components/presets/CalendarViewPreset.vue'

// 交互日志
const eventLogs = ref([])

// 丰富的演示数据
const demoRecords = ref([
  // 当前月份的记录
  { id: 1, record_type: 'vaccination', date: '2025-06-20', description: '狂犬病疫苗第一针' },
  { id: 2, record_type: 'checkup', date: '2025-06-20', description: '常规体检' },
  { id: 3, record_type: 'medication', date: '2025-06-22', description: '驱虫药' },
  { id: 4, record_type: 'illness', date: '2025-06-18', description: '轻微感冒' },
  { id: 5, record_type: 'vaccination', date: '2025-06-15', description: '疫苗接种第二针' },
  { id: 6, record_type: 'deworming', date: '2025-06-10', description: '体内驱虫' },
  { id: 7, record_type: 'checkup', date: '2025-06-05', description: '血液检查' },
  { id: 8, record_type: 'medication', date: '2025-06-03', description: '营养补充剂' },
  { id: 9, record_type: 'surgery', date: '2025-06-01', description: '绝育手术' },
  { id: 10, record_type: 'deworming', date: '2025-06-12', description: '体外驱虫' },
  { id: 11, record_type: 'medication', date: '2025-06-14', description: '维生素补充' },
  { id: 12, record_type: 'checkup', date: '2025-06-16', description: '眼部检查' },
  { id: 13, record_type: 'vaccination', date: '2025-06-08', description: '狂犬疫苗加强针' },
  { id: 14, record_type: 'illness', date: '2025-06-02', description: '皮肤过敏' },
  { id: 15, record_type: 'surgery', date: '2025-06-04', description: '牙齿清洁' },
  { id: 16, record_type: 'medication', date: '2025-06-06', description: '心脏保健药' },
  { id: 17, record_type: 'deworming', date: '2025-06-09', description: '体内驱虫' },
  { id: 18, record_type: 'checkup', date: '2025-06-25', description: '季度体检' },
  { id: 19, record_type: 'vaccination', date: '2025-06-28', description: '疫苗补种' },
  { id: 20, record_type: 'medication', date: '2025-06-30', description: '月末用药' }
])

// 记录类型配置
const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病', color: '#F56C6C' },
  { value: 'medication', label: '用药', color: '#909399' },
  { value: 'surgery', label: '手术', color: '#F56C6C' },
  { value: 'other', label: '其他', color: '#C0C4CC' }
])

// 颜色映射
const colorMapping = ref({
  vaccination: 'vaccination',
  deworming: 'deworming',
  checkup: 'checkup',
  illness: 'illness',
  medication: 'medication',
  surgery: 'surgery',
  other: 'other'
})

// 标签映射
const labelMapping = ref({
  vaccination: '疫苗接种',
  deworming: '驱虫',
  checkup: '体检',
  illness: '疾病',
  medication: '用药',
  surgery: '手术',
  other: '其他'
})

// 事件处理
const addLog = (event, data = '') => {
  const log = {
    time: new Date().toLocaleTimeString(),
    event,
    data: typeof data === 'object' ? JSON.stringify(data) : data
  }
  eventLogs.value.unshift(log)
  
  if (eventLogs.value.length > 15) {
    eventLogs.value = eventLogs.value.slice(0, 15)
  }
}

const handleDateClick = (dateStr, records) => {
  addLog('点击日期', `日期: ${dateStr}, 记录数: ${records.length}`)
}

const handleViewChange = (newView) => {
  addLog('切换视图', `新视图: ${newView}`)
}
</script>

<style scoped>
.calendar-optimized-demo {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 48px;
}

.demo-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-header p {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
}

.demo-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.demo-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 24px;
  border-bottom: 2px solid #f0f2f5;
  padding-bottom: 12px;
}

.demo-content {
  background: #fafbfc;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

/* 特性展示 */
.features-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.features-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 24px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-card {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
  border-color: #409EFF;
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 16px;
}

.feature-card h3 {
  color: #303133;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
}

.feature-card p {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
}

/* 事件日志 */
.event-log-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.event-log-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 20px;
}

.event-log {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
}

.no-events {
  text-align: center;
  color: #909399;
  font-style: italic;
  padding: 24px;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f2f5;
  font-size: 14px;
  border-radius: 6px;
  margin-bottom: 4px;
  background: #ffffff;
}

.log-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.log-time {
  color: #909399;
  font-family: monospace;
  min-width: 80px;
  flex-shrink: 0;
  font-size: 12px;
}

.log-event {
  color: #409EFF;
  font-weight: 500;
  min-width: 100px;
  flex-shrink: 0;
}

.log-data {
  color: #606266;
  flex: 1;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calendar-optimized-demo {
    padding: 16px;
  }

  .demo-header h1 {
    font-size: 24px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .log-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .log-time,
  .log-event {
    min-width: auto;
  }
}
</style>
