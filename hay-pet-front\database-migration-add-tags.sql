-- 宠物档案标签功能数据库迁移脚本
-- 请在Supabase SQL编辑器中执行此脚本

-- 创建宠物标签表
CREATE TABLE IF NOT EXISTS public.pet_tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL, -- 标签名称
    color TEXT DEFAULT '#409EFF', -- 标签颜色，默认为蓝色
    description TEXT, -- 标签描述
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, name) -- 同一用户不能创建重名标签
);

-- 创建宠物标签关联表
CREATE TABLE IF NOT EXISTS public.pet_tag_relations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES public.pet_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(pet_id, tag_id) -- 同一宠物不能重复关联同一标签
);

-- 启用行级安全策略 (RLS)
ALTER TABLE public.pet_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pet_tag_relations ENABLE ROW LEVEL SECURITY;

-- 创建宠物标签表的RLS策略
-- 用户只能查看自己的标签
DROP POLICY IF EXISTS "Users can view own pet tags" ON public.pet_tags;
CREATE POLICY "Users can view own pet tags" ON public.pet_tags
    FOR SELECT USING (auth.uid() = user_id);

-- 用户只能创建自己的标签
DROP POLICY IF EXISTS "Users can insert own pet tags" ON public.pet_tags;
CREATE POLICY "Users can insert own pet tags" ON public.pet_tags
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 用户只能更新自己的标签
DROP POLICY IF EXISTS "Users can update own pet tags" ON public.pet_tags;
CREATE POLICY "Users can update own pet tags" ON public.pet_tags
    FOR UPDATE USING (auth.uid() = user_id);

-- 用户只能删除自己的标签
DROP POLICY IF EXISTS "Users can delete own pet tags" ON public.pet_tags;
CREATE POLICY "Users can delete own pet tags" ON public.pet_tags
    FOR DELETE USING (auth.uid() = user_id);

-- 创建宠物标签关联表的RLS策略
-- 用户只能查看自己宠物的标签关联
DROP POLICY IF EXISTS "Users can view own pet tag relations" ON public.pet_tag_relations;
CREATE POLICY "Users can view own pet tag relations" ON public.pet_tag_relations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = pet_tag_relations.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能为自己的宠物创建标签关联
DROP POLICY IF EXISTS "Users can insert pet tag relations for own pets" ON public.pet_tag_relations;
CREATE POLICY "Users can insert pet tag relations for own pets" ON public.pet_tag_relations
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = pet_tag_relations.pet_id 
            AND pets.user_id = auth.uid()
        )
        AND
        EXISTS (
            SELECT 1 FROM public.pet_tags 
            WHERE pet_tags.id = pet_tag_relations.tag_id 
            AND pet_tags.user_id = auth.uid()
        )
    );

-- 用户只能更新自己宠物的标签关联
DROP POLICY IF EXISTS "Users can update own pet tag relations" ON public.pet_tag_relations;
CREATE POLICY "Users can update own pet tag relations" ON public.pet_tag_relations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = pet_tag_relations.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能删除自己宠物的标签关联
DROP POLICY IF EXISTS "Users can delete own pet tag relations" ON public.pet_tag_relations;
CREATE POLICY "Users can delete own pet tag relations" ON public.pet_tag_relations
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.pets 
            WHERE pets.id = pet_tag_relations.pet_id 
            AND pets.user_id = auth.uid()
        )
    );

-- 为pet_tags表创建更新时间戳的触发器
DROP TRIGGER IF EXISTS update_pet_tags_updated_at ON public.pet_tags;
CREATE TRIGGER update_pet_tags_updated_at
    BEFORE UPDATE ON public.pet_tags
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_pet_tags_user_id ON public.pet_tags(user_id);
CREATE INDEX IF NOT EXISTS idx_pet_tags_name ON public.pet_tags(name);
CREATE INDEX IF NOT EXISTS idx_pet_tag_relations_pet_id ON public.pet_tag_relations(pet_id);
CREATE INDEX IF NOT EXISTS idx_pet_tag_relations_tag_id ON public.pet_tag_relations(tag_id);
CREATE INDEX IF NOT EXISTS idx_pet_tag_relations_pet_tag ON public.pet_tag_relations(pet_id, tag_id);

-- 插入一些默认标签示例（可选）
INSERT INTO public.pet_tags (user_id, name, color, description) 
SELECT 
    auth.uid(),
    '活泼',
    '#67C23A',
    '性格活泼好动的宠物'
WHERE auth.uid() IS NOT NULL
ON CONFLICT (user_id, name) DO NOTHING;

INSERT INTO public.pet_tags (user_id, name, color, description) 
SELECT 
    auth.uid(),
    '温顺',
    '#409EFF',
    '性格温顺亲人的宠物'
WHERE auth.uid() IS NOT NULL
ON CONFLICT (user_id, name) DO NOTHING;

INSERT INTO public.pet_tags (user_id, name, color, description) 
SELECT 
    auth.uid(),
    '需要特殊照顾',
    '#F56C6C',
    '需要特殊照顾或有健康问题的宠物'
WHERE auth.uid() IS NOT NULL
ON CONFLICT (user_id, name) DO NOTHING;

INSERT INTO public.pet_tags (user_id, name, color, description) 
SELECT 
    auth.uid(),
    '已绝育',
    '#909399',
    '已完成绝育手术的宠物'
WHERE auth.uid() IS NOT NULL
ON CONFLICT (user_id, name) DO NOTHING;

-- 完成提示
-- 执行完成后，您的数据库将新增以下表：
-- 1. pet_tags - 宠物标签表
-- 2. pet_tag_relations - 宠物标签关联表
-- 以及相应的RLS策略确保数据安全