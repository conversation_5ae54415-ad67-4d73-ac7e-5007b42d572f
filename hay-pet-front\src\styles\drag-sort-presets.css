/* ========== 拖拽排序预设样式 ========== */

/* 拖拽容器样式 */
.draggable-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

/* 可拖拽元素基础样式 */
.draggable-tag {
  cursor: move;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
}

/* 拖拽状态样式 */
.draggable-tag.is-dragging {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  cursor: grabbing;
}

/* 拖拽时隐藏操作按钮 */
.draggable-tag.is-dragging .tag-actions {
  display: none !important;
}

/* 拖拽时的幽灵元素样式 */
.ghost {
  opacity: 0.5;
  background: linear-gradient(135deg, #c8ebfb 0%, #e3f2fd 100%) !important;
  border: 2px dashed #409EFF !important;
  transform: scale(0.95);
  color: #409EFF !important;
}

/* 拖拽时的占位符样式 */
.sortable-chosen {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  z-index: 999;
}

/* 拖拽中的元素样式 */
.sortable-drag {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

/* 拖拽悬停效果 */
.draggable-tag:hover {
  cursor: grab;
}

.draggable-tag:active {
  cursor: grabbing;
}

/* 拖拽禁用状态 */
.draggable-tag.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.draggable-tag.disabled:hover {
  cursor: not-allowed;
  transform: none;
}

/* 拖拽动画效果 */
@keyframes dragStart {
  0% {
    transform: scale(1) rotate(0deg);
  }
  100% {
    transform: scale(1.05) rotate(5deg);
  }
}

@keyframes dragEnd {
  0% {
    transform: scale(1.05) rotate(5deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}

/* 拖拽开始动画 */
.drag-start-animation {
  animation: dragStart 0.2s ease-out;
}

/* 拖拽结束动画 */
.drag-end-animation {
  animation: dragEnd 0.2s ease-out;
}

/* 移动端拖拽优化 */
@media (max-width: 768px) {
  .draggable-container {
    gap: 8px;
  }
  
  .draggable-tag {
    touch-action: none; /* 防止移动端滚动冲突 */
  }
  
  .draggable-tag.is-dragging {
    transform: rotate(3deg) scale(1.03);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
  
  .ghost {
    transform: scale(0.98);
  }
  
  .sortable-chosen {
    transform: scale(1.03);
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.25);
  }
  
  .sortable-drag {
    transform: rotate(3deg) scale(1.03);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }
}

/* 拖拽指示器 */
.drag-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 1001;
  background: rgba(64, 158, 255, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drag-indicator.show {
  opacity: 1;
}

/* 拖拽区域高亮 */
.drag-zone {
  position: relative;
  transition: all 0.3s ease;
}

.drag-zone.drag-over {
  background: rgba(64, 158, 255, 0.05);
  border: 2px dashed #409EFF;
  border-radius: 8px;
}

/* 拖拽排序列表样式 */
.sortable-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sortable-list-item {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 8px;
}

.sortable-list-item:last-child {
  margin-bottom: 0;
}

.sortable-list-item.is-dragging {
  opacity: 0.8;
  transform: rotate(2deg) scale(1.02);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 拖拽手柄样式 */
.drag-handle {
  cursor: grab;
  color: #909399;
  transition: color 0.3s ease;
  padding: 4px;
  border-radius: 4px;
}

.drag-handle:hover {
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

.drag-handle:active {
  cursor: grabbing;
}

/* 拖拽反馈提示 */
.drag-feedback {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(64, 158, 255, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  z-index: 9999;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.drag-feedback.show {
  opacity: 1;
  transform: translateY(0);
}

/* 拖拽成功提示 */
.drag-feedback.success {
  background: rgba(103, 194, 58, 0.9);
}

/* 拖拽错误提示 */
.drag-feedback.error {
  background: rgba(245, 108, 108, 0.9);
}

/* 拖拽加载状态 */
.drag-loading {
  position: relative;
  pointer-events: none;
}

.drag-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: inherit;
  z-index: 1;
}

.drag-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #409EFF;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 2;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 拖拽网格布局 */
.draggable-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.draggable-grid-item {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.draggable-grid-item.is-dragging {
  opacity: 0.8;
  transform: scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 响应式拖拽网格 */
@media (max-width: 768px) {
  .draggable-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .draggable-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}
