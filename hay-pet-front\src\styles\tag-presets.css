/**
 * 标签组件预设样式系统
 * 基于 health-records 面板标签组件的设计模式
 * 提供统一的标签样式和交互行为
 */

/* ========== 基础标签样式 ========== */

.tag-base {
  /* 基础布局 */
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  /* 尺寸和间距 */
  min-width: 80px;
  min-height: 40px;
  padding: var(--spacing-2) var(--spacing-4);
  
  /* 字体样式 */
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-normal);
  text-align: center;
  white-space: nowrap;
  
  /* 外观样式 */
  border: 2px solid transparent;
  border-radius: var(--radius-tag);
  background: var(--gradient-neutral);
  color: var(--color-text-regular);
  box-shadow: var(--shadow-base);
  backdrop-filter: blur(10px);
  
  /* 交互样式 */
  cursor: pointer;
  user-select: none;
  transition: all var(--duration-base) var(--ease-in-out);
  overflow: hidden;
  
  /* 防止文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* ========== 标签尺寸变体 ========== */

.tag-xs {
  min-width: 60px;
  min-height: 28px;
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: 14px;
}

.tag-sm {
  min-width: 70px;
  min-height: 32px;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: 16px;
}

.tag-md {
  /* 默认尺寸，使用 tag-base 的设置 */
}

.tag-lg {
  min-width: 100px;
  min-height: 48px;
  padding: var(--spacing-3) var(--spacing-5);
  font-size: var(--font-size-md);
  border-radius: var(--radius-tag);
}

.tag-xl {
  min-width: 120px;
  min-height: 56px;
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
  border-radius: 28px;
}

/* ========== 标签颜色变体 ========== */

/* 主色调标签 */
.tag-primary {
  background: linear-gradient(135deg, 
    color-mix(in srgb, var(--color-primary) 20%, white) 0%, 
    color-mix(in srgb, var(--color-primary) 10%, white) 100%);
  color: var(--color-primary);
  border-color: color-mix(in srgb, var(--color-primary) 40%, transparent);
}

.tag-primary.tag-active {
  background: var(--gradient-primary);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-primary);
  transform: translateY(-1px) scale(1.02);
}

/* 成功色标签 */
.tag-success {
  background: linear-gradient(135deg, 
    color-mix(in srgb, var(--color-success) 20%, white) 0%, 
    color-mix(in srgb, var(--color-success) 10%, white) 100%);
  color: var(--color-success);
  border-color: color-mix(in srgb, var(--color-success) 40%, transparent);
}

.tag-success.tag-active {
  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-light) 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-success);
  transform: translateY(-1px) scale(1.02);
}

/* 警告色标签 */
.tag-warning {
  background: linear-gradient(135deg, 
    color-mix(in srgb, var(--color-warning) 20%, white) 0%, 
    color-mix(in srgb, var(--color-warning) 10%, white) 100%);
  color: var(--color-warning);
  border-color: color-mix(in srgb, var(--color-warning) 40%, transparent);
}

.tag-warning.tag-active {
  background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-light) 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-warning);
  transform: translateY(-1px) scale(1.02);
}

/* 危险色标签 */
.tag-danger {
  background: linear-gradient(135deg, 
    color-mix(in srgb, var(--color-danger) 20%, white) 0%, 
    color-mix(in srgb, var(--color-danger) 10%, white) 100%);
  color: var(--color-danger);
  border-color: color-mix(in srgb, var(--color-danger) 40%, transparent);
}

.tag-danger.tag-active {
  background: linear-gradient(135deg, var(--color-danger) 0%, var(--color-danger-light) 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-danger);
  transform: translateY(-1px) scale(1.02);
}

/* 信息色标签 */
.tag-info {
  background: linear-gradient(135deg, 
    color-mix(in srgb, var(--color-info) 20%, white) 0%, 
    color-mix(in srgb, var(--color-info) 10%, white) 100%);
  color: var(--color-info);
  border-color: color-mix(in srgb, var(--color-info) 40%, transparent);
}

.tag-info.tag-active {
  background: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-light) 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
  transform: translateY(-1px) scale(1.02);
}

/* ========== 交互状态 ========== */

/* 可点击标签的悬停效果 */
.tag-clickable:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-lg);
  background: var(--gradient-hover);
}

/* 激活状态的悬停效果 */
.tag-clickable.tag-active:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-xl);
}

.tag-primary.tag-active:hover {
  background: linear-gradient(135deg, #5dade2 0%, #85ce61 100%);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.4);
}

/* 点击反馈 */
.tag-clickable:active {
  transform: translateY(-1px) scale(0.98);
  transition: all var(--duration-fast) ease;
}

/* 焦点状态 */
.tag-clickable:focus {
  outline: none;
  box-shadow: var(--shadow-focus);
}

/* 禁用状态 */
.tag-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ========== 特殊效果 ========== */

/* 激活状态的脉冲动画 */
.tag-active::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
  opacity: 0.3;
  animation: tag-pulse 2s ease-in-out infinite;
}

@keyframes tag-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.1;
  }
}

/* 拖拽状态 */
.tag-dragging {
  transform: rotate(5deg) scale(1.05);
  z-index: var(--z-index-modal);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  cursor: grabbing;
}

/* 拖拽幽灵元素 */
.tag-ghost {
  opacity: 0.5;
  background: var(--color-bg-secondary) !important;
  color: var(--color-text-placeholder) !important;
  border: 2px dashed var(--color-border-base) !important;
  box-shadow: none !important;
}

/* ========== 响应式设计 ========== */

/* 平板设备 */
@media (max-width: 992px) {
  .tag-base {
    min-width: 70px;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    min-height: 36px;
  }
}

/* 手机设备 */
@media (max-width: 768px) {
  .tag-base {
    min-width: 60px;
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-sm);
    min-height: 32px;
    border-radius: 18px;
  }
  
  .tag-lg, .tag-xl {
    min-width: 80px;
    min-height: 36px;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-base);
  }
}

/* ========== 工具类 ========== */

/* 快速应用标签样式的工具类 */
.tag { @apply tag-base tag-clickable; }
.tag-static { @apply tag-base; }

/* 组合类 */
.tag-primary-sm { @apply tag-base tag-sm tag-primary tag-clickable; }
.tag-success-md { @apply tag-base tag-md tag-success tag-clickable; }
.tag-warning-lg { @apply tag-base tag-lg tag-warning tag-clickable; }

/* ========== 添加按钮预设样式 ========== */

/* 基础添加按钮样式 */
.add-button-base {
  cursor: pointer;
  outline: none;
  background: transparent;
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: transform var(--duration-base) ease;
}

.add-button-base:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 50%;
}

/* 添加按钮尺寸变体 */
.add-button-sm {
  width: 32px;
  height: 32px;
}

.add-button-md {
  width: 40px;
  height: 40px;
}

.add-button-lg {
  width: 48px;
  height: 48px;
}

.add-button-xl {
  width: 56px;
  height: 56px;
}

/* 添加按钮动画变体 */
.add-button-rotate:hover {
  transform: rotate(90deg);
}

.add-button-scale:hover {
  transform: scale(1.1);
}

.add-button-pulse:hover {
  animation: add-button-pulse 0.6s ease-in-out;
}

@keyframes add-button-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 添加按钮图标样式 */
.add-icon-base {
  fill: none;
  transition: all var(--duration-base) ease;
}

/* 添加按钮颜色变体 */
.add-button-primary .add-icon-base {
  stroke: #6c757d;
}

.add-button-primary:hover .add-icon-base {
  stroke: var(--color-primary);
  fill: rgba(64, 158, 255, 0.1);
}

.add-button-primary:active .add-icon-base {
  stroke: var(--color-primary-dark);
  fill: rgba(64, 158, 255, 0.2);
}

.add-button-success .add-icon-base {
  stroke: #6c757d;
}

.add-button-success:hover .add-icon-base {
  stroke: var(--color-success);
  fill: rgba(103, 194, 58, 0.1);
}

.add-button-success:active .add-icon-base {
  stroke: var(--color-success-dark);
  fill: rgba(103, 194, 58, 0.2);
}

/* 添加按钮禁用状态 */
.add-button-disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.add-button-disabled:hover {
  transform: none;
}

.add-button-disabled .add-icon-base {
  stroke: #c0c4cc !important;
  fill: none !important;
}

/* 添加按钮工具类 */
.add-btn { @apply add-button-base add-button-md add-button-primary add-button-rotate; }
.add-btn-sm { @apply add-button-base add-button-sm add-button-primary add-button-rotate; }
.add-btn-lg { @apply add-button-base add-button-lg add-button-primary add-button-rotate; }
