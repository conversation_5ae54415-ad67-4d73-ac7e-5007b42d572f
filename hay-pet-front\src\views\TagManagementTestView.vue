<template>
  <div class="tag-management-test">
    <div class="test-container">
      <h1>🏷️ 标签管理面板展示</h1>
      <p class="test-description">
        优化后的标签管理面板，统一设计风格，简化颜色选择流程
      </p>

      <!-- 功能展示区域 -->
      <section class="test-section">
        <h2>📋 功能特点</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3>直观颜色选择</h3>
            <p>预设颜色 + 自定义颜色，一步到位</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">✨</div>
            <h3>统一设计风格</h3>
            <p>与现有设计语言保持一致</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3>响应式设计</h3>
            <p>移动端友好的交互体验</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>快速操作</h3>
            <p>减少点击步骤，提升效率</p>
          </div>
        </div>
      </section>

      <!-- 操作演示 -->
      <section class="test-section">
        <h2>🎮 操作演示</h2>
        <div class="demo-buttons">
          <el-button type="primary" @click="showAddDialog">
            <el-icon><Plus /></el-icon>
            添加新标签
          </el-button>
          <el-button type="success" @click="showEditDialog">
            <el-icon><Edit /></el-icon>
            编辑标签示例
          </el-button>
        </div>
      </section>

      <!-- 当前标签展示 -->
      <section class="test-section">
        <h2>🏷️ 当前标签</h2>
        <div class="current-tags">
          <div 
            v-for="tag in testTags"
            :key="tag.value"
            class="tag-display"
            :style="{ backgroundColor: tag.color }"
            @click="editTag(tag)"
          >
            <span class="tag-text">{{ tag.label }}</span>
            <el-icon class="edit-icon"><Edit /></el-icon>
          </div>
        </div>
      </section>

      <!-- 设计对比 -->
      <section class="test-section">
        <h2>📊 设计对比</h2>
        <div class="comparison-grid">
          <div class="comparison-item">
            <h3>优化前</h3>
            <ul class="comparison-list">
              <li>❌ 多步骤颜色选择</li>
              <li>❌ 界面不够直观</li>
              <li>❌ 移动端体验一般</li>
              <li>❌ 设计风格不统一</li>
            </ul>
          </div>
          <div class="comparison-item">
            <h3>优化后</h3>
            <ul class="comparison-list">
              <li>✅ 一步选择颜色</li>
              <li>✅ 直观的圆形按钮</li>
              <li>✅ 响应式友好设计</li>
              <li>✅ 统一设计语言</li>
            </ul>
          </div>
        </div>
      </section>
    </div>

    <!-- 标签管理对话框 -->
    <el-dialog
      v-model="showDialog"
      :title="isEditMode ? '编辑记录类型' : '添加记录类型'"
      width="480px"
      class="type-management-dialog"
    >
      <div class="type-form-container">
        <el-form :model="currentTag" label-width="0px">
          <!-- 类型名称 -->
          <div class="form-section">
            <div class="section-header">
              <span class="section-title">类型名称</span>
              <span class="required-mark">*</span>
            </div>
            <el-input
              v-model="currentTag.label"
              placeholder="请输入类型名称"
              maxlength="20"
              show-word-limit
              class="type-name-input"
            ></el-input>
          </div>

          <!-- 类型颜色 -->
          <div class="form-section">
            <div class="section-header">
              <span class="section-title">类型颜色</span>
              <span class="required-mark">*</span>
              <div class="color-picker-container">
                <el-color-picker
                  v-model="currentTag.color"
                  @change="handleColorPickerChange"
                  :title="'当前颜色: ' + currentTag.color"
                  size="default"
                  show-alpha
                  :predefine="predefinedColors"
                />
              </div>
            </div>

            <!-- 颜色选择区域 -->
            <div class="color-selection-area">
              <div class="unified-color-grid">
                <!-- 预设颜色按钮 -->
                <button
                  v-for="color in predefinedColors"
                  :key="color"
                  @click.prevent="selectPresetColor(color)"
                  :class="['color-button', { 'selected': currentTag.color === color }]"
                  :style="{ backgroundColor: color }"
                  :title="getColorName(color)"
                  type="button"
                >
                  <div v-if="currentTag.color === color" class="selected-indicator">
                    <el-icon class="check-icon">
                      <Check />
                    </el-icon>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer-custom">
          <el-button @click="cancelDialog" class="cancel-btn">取消</el-button>
          <el-button type="primary" @click="saveTag" class="save-btn">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Edit, Check } from '@element-plus/icons-vue'

// 响应式数据
const showDialog = ref(false)
const isEditMode = ref(false)
const currentTag = ref({
  value: '',
  label: '',
  color: '#409EFF'
})

// 移除不需要的颜色选择器变量

// 测试标签数据
const testTags = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病就诊', color: '#F56C6C' },
  { value: 'medication', label: '用药记录', color: '#909399' },
  { value: 'custom', label: '自定义标签', color: '#8A2BE2' }
])

// 预定义颜色
const predefinedColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
  '#909399', '#9C27B0', '#FF9800', '#4CAF50',
  '#2196F3', '#FF5722', '#795548', '#607D8B'
]

// 移除不需要的计算属性

// 方法
const showAddDialog = () => {
  isEditMode.value = false
  currentTag.value = {
    value: '',
    label: '',
    color: '#409EFF'
  }
  showDialog.value = true
}

const showEditDialog = () => {
  isEditMode.value = true
  currentTag.value = {
    value: 'example',
    label: '示例标签',
    color: '#9C27B0'
  }
  showDialog.value = true
}

const editTag = (tag) => {
  isEditMode.value = true
  currentTag.value = { ...tag }
  showDialog.value = true
}

const selectPresetColor = (color) => {
  currentTag.value.color = color
}

const handleColorPickerChange = (color) => {
  if (color) {
    currentTag.value.color = color
  }
}



const getColorName = (color) => {
  const colorNames = {
    '#409EFF': '蓝色',
    '#67C23A': '绿色',
    '#E6A23C': '橙色',
    '#F56C6C': '红色',
    '#909399': '灰色',
    '#9C27B0': '紫色',
    '#FF9800': '琥珀色',
    '#4CAF50': '青绿色',
    '#2196F3': '天蓝色',
    '#FF5722': '深橙色',
    '#795548': '棕色',
    '#607D8B': '蓝灰色'
  }
  return colorNames[color] || '自定义颜色'
}

const cancelDialog = () => {
  showDialog.value = false
}

const saveTag = () => {
  if (!currentTag.value.label.trim()) {
    ElMessage.warning('请输入标签名称')
    return
  }

  if (isEditMode.value) {
    const index = testTags.value.findIndex(tag => tag.value === currentTag.value.value)
    if (index !== -1) {
      testTags.value[index] = { ...currentTag.value }
    }
    ElMessage.success('标签编辑成功！')
  } else {
    const newTag = {
      ...currentTag.value,
      value: `custom_${Date.now()}`
    }
    testTags.value.push(newTag)
    ElMessage.success('标签添加成功！')
  }

  showDialog.value = false
}
</script>

<style scoped>
/* 引入设计令牌 */
@import '@/styles/design-tokens.css';

/* 测试页面样式 */
.tag-management-test {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.test-container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-3xl);
}

.test-description {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-8);
}

.test-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
}

h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

/* 功能特点网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.feature-card {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  text-align: center;
  border: 2px solid var(--color-border-light);
  transition: all 0.3s ease;
}

.feature-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-2);
}

.feature-card h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-md);
}

.feature-card p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* 演示按钮 */
.demo-buttons {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  flex-wrap: wrap;
}

/* 当前标签展示 */
.current-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
}

.tag-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-full);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tag-display:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.tag-text {
  font-size: var(--font-size-sm);
}

.edit-icon {
  font-size: 14px;
  opacity: 0.8;
}

/* 设计对比 */
.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-6);
}

.comparison-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.comparison-item h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
  text-align: center;
}

.comparison-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.comparison-list li {
  padding: var(--spacing-2) 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-regular);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-management-test {
    padding: var(--spacing-4);
  }
  
  .test-section {
    padding: var(--spacing-4);
  }
  
  .demo-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>

<!-- 引入标签管理对话框样式 -->
<style>
/* 标签管理对话框样式（从HealthRecordsView复制） */
.type-management-dialog {
  --el-dialog-border-radius: 12px;
}

.type-management-dialog .el-dialog__header {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

.type-management-dialog .el-dialog__title {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.type-management-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white;
  font-size: 20px;
}

.type-management-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: rgba(255, 255, 255, 0.8);
}

.type-management-dialog .el-dialog__body {
  padding: 24px;
  background: #fafbfc;
}

.type-form-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 24px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.required-mark {
  color: #F56C6C;
  margin-left: 4px;
  font-size: 14px;
}

.type-name-input {
  --el-input-border-radius: 8px;
}

.type-name-input .el-input__inner {
  font-size: 15px;
  padding: 12px 16px;
  border: 2px solid #E4E7ED;
  transition: all 0.3s ease;
}

.type-name-input .el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.color-selection-area {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #E4E7ED;
}

.color-picker-container {
  margin-left: auto;
}

.unified-color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
}

.color-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.color-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.color-button.selected {
  /* 移除外部蓝色描边，只保留基础样式 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selected-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9) 0%, rgba(103, 194, 58, 0.9) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation: selectedPulse 0.3s ease-out;
}

.selected-indicator .check-icon {
  color: white;
  font-size: 22px;
  font-weight: bold;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.6),
    0 0 8px rgba(255, 255, 255, 0.4);
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5));
  animation: checkIconBounce 0.4s ease-out 0.1s both;
}

@keyframes selectedPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkIconBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  60% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 移除自定义颜色按钮样式 - 现在使用Element Plus颜色选择器 */

.dialog-footer-custom {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px 24px;
  background: #fafbfc;
  border-radius: 0 0 12px 12px;
}

.cancel-btn {
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;
}

.save-btn {
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  border: none;
}

.save-btn:hover {
  background: linear-gradient(135deg, #337ECC 0%, #529B2E 100%);
}

@media (max-width: 768px) {
  .type-management-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .color-picker-container {
    margin-left: 0;
  }

  .unified-color-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .color-button {
    width: 36px;
    height: 36px;
  }

  .selected-indicator {
    border: 1.5px solid rgba(255, 255, 255, 0.8);
    box-shadow:
      0 3px 8px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .selected-indicator .check-icon {
    font-size: 18px;
  }
}
</style>
