-- 为reminders表添加priority列的迁移脚本
-- 执行日期: 2024年

-- 添加priority列到reminders表
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT '中' CHECK (priority IN ('高', '中', '低'));

-- 为现有记录设置默认优先级
UPDATE public.reminders 
SET priority = '中' 
WHERE priority IS NULL;

-- 添加注释
COMMENT ON COLUMN public.reminders.priority IS '提醒优先级：高、中、低';

-- 验证迁移结果
SELECT column_name, data_type, column_default, is_nullable
FROM information_schema.columns 
WHERE table_name = 'reminders' 
AND table_schema = 'public'
ORDER BY ordinal_position;