<template>
  <div class="page-header" :class="headerClass">
    <div class="header-content" :class="contentClass">
      <!-- 左侧标题区域 -->
      <div class="header-left">
        <h2 class="page-title" :class="titleClass">
          <slot name="title">{{ title }}</slot>
        </h2>
        <div v-if="subtitle || $slots.subtitle" class="page-subtitle">
          <slot name="subtitle">{{ subtitle }}</slot>
        </div>
      </div>

      <!-- 右侧操作区域 -->
      <div class="header-actions" :class="actionsClass">
        <slot name="actions">
          <!-- 默认添加按钮 -->
          <el-button
            v-if="showAddButton"
            class="add-btn"
            :class="addButtonClass"
            type="primary"
            size="large"
            @click="handleAddClick"
          >
            <el-icon class="add-icon">
              <component :is="addIcon" />
            </el-icon>
            <span class="btn-text">{{ addButtonText }}</span>
          </el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Plus } from '@element-plus/icons-vue';

const props = defineProps({
  // 标题相关
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  
  // 添加按钮相关
  showAddButton: {
    type: Boolean,
    default: true
  },
  addButtonText: {
    type: String,
    default: '添加'
  },
  addIcon: {
    type: [String, Object],
    default: () => Plus
  },
  
  // 样式变体
  variant: {
    type: String,
    default: 'default', // default, compact, minimal
    validator: (value) => ['default', 'compact', 'minimal'].includes(value)
  },
  
  // 自定义样式类
  headerClass: {
    type: [String, Array, Object],
    default: ''
  },
  contentClass: {
    type: [String, Array, Object],
    default: ''
  },
  titleClass: {
    type: [String, Array, Object],
    default: ''
  },
  actionsClass: {
    type: [String, Array, Object],
    default: ''
  },
  addButtonClass: {
    type: [String, Array, Object],
    default: ''
  }
});

const emit = defineEmits(['add-click']);

const handleAddClick = () => {
  emit('add-click');
};

// 计算样式类
const computedHeaderClass = computed(() => [
  'page-header',
  `page-header--${props.variant}`,
  props.headerClass
]);

const computedContentClass = computed(() => [
  'header-content',
  `header-content--${props.variant}`,
  props.contentClass
]);
</script>

<style scoped>
/* 引入页面标题栏预设样式 */
@import '@/styles/page-header-presets.css';
</style>
