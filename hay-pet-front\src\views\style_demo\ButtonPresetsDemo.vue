<template>
  <div class="button-presets-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🔘 Button Presets 按钮预设</h1>
        <p class="page-description">
          标准化的按钮样式，包含添加按钮和交互效果，基于现有项目的设计语言
        </p>
        <router-link to="/style_demo" class="back-link">
          ← 返回样式系统首页
        </router-link>
      </div>

      <!-- 添加按钮基础样式 -->
      <div class="demo-section">
        <h2>➕ 添加按钮基础样式</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>基础添加按钮</h3>
            <div class="demo-area">
              <button class="add-button-base add-button-sm add-button-primary">
                <svg class="add-icon-base" width="16" height="16" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <button class="add-button-base add-button-md add-button-primary">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <button class="add-button-base add-button-lg add-button-primary">
                <svg class="add-icon-base" width="24" height="24" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <button class="add-button-base add-button-xl add-button-primary">
                <svg class="add-icon-base" width="28" height="28" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
            </div>
            <div class="code-example">
              <pre><code>&lt;button class="add-button-base add-button-md add-button-primary"&gt;
  &lt;svg class="add-icon-base" width="20" height="20"&gt;
    &lt;path stroke="currentColor" d="M12 5v14m-7-7h14"/&gt;
  &lt;/svg&gt;
&lt;/button&gt;</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 动画效果变体 -->
      <div class="demo-section">
        <h2>🎬 动画效果变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>旋转动画</h3>
            <div class="demo-area">
              <button class="add-button-base add-button-md add-button-primary add-button-rotate">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <span class="demo-label">悬停旋转90度</span>
            </div>
          </div>

          <div class="showcase-item">
            <h3>缩放动画</h3>
            <div class="demo-area">
              <button class="add-button-base add-button-md add-button-primary add-button-scale">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <span class="demo-label">悬停缩放1.1倍</span>
            </div>
          </div>

          <div class="showcase-item">
            <h3>脉冲动画</h3>
            <div class="demo-area">
              <button class="add-button-base add-button-md add-button-primary add-button-pulse">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <span class="demo-label">悬停脉冲效果</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 颜色变体 -->
      <div class="demo-section">
        <h2>🎨 颜色变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>不同颜色主题</h3>
            <div class="demo-area">
              <button class="add-button-base add-button-md add-button-primary add-button-rotate">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <button class="add-button-base add-button-md add-button-success add-button-rotate">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <button class="add-button-base add-button-md add-button-warning add-button-rotate">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <button class="add-button-base add-button-md add-button-danger add-button-rotate">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
            </div>
            <div class="color-labels">
              <span>主色调</span>
              <span>成功色</span>
              <span>警告色</span>
              <span>危险色</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 实际应用示例 -->
      <div class="demo-section">
        <h2>💼 实际应用示例</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>标签管理界面</h3>
            <div class="demo-area tag-management-demo">
              <div class="tag-list">
                <div class="demo-tag">疫苗接种</div>
                <div class="demo-tag">驱虫</div>
                <div class="demo-tag">体检</div>
                <button class="add-button-base add-button-md add-button-primary add-button-rotate">
                  <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <div class="showcase-item">
            <h3>颜色选择器</h3>
            <div class="demo-area color-picker-demo">
              <div class="color-grid-demo">
                <div class="color-dot" style="background: #409EFF;"></div>
                <div class="color-dot" style="background: #67C23A;"></div>
                <div class="color-dot" style="background: #E6A23C;"></div>
                <div class="color-dot" style="background: #F56C6C;"></div>
                <div class="color-dot" style="background: #909399;"></div>
                <button class="add-button-base add-button-sm add-button-primary add-button-scale">
                  <svg class="add-icon-base" width="16" height="16" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <div class="showcase-item">
            <h3>工具栏按钮</h3>
            <div class="demo-area toolbar-demo">
              <div class="toolbar">
                <button class="toolbar-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-width="2" d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                    <path stroke="currentColor" stroke-width="2" d="m18.5 2.5-8 8"/>
                  </svg>
                </button>
                <button class="toolbar-btn">
                  <svg width="16" height="16" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-width="2" d="M3 6h18M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                  </svg>
                </button>
                <div class="toolbar-divider"></div>
                <button class="add-button-base add-button-sm add-button-primary add-button-rotate">
                  <svg class="add-icon-base" width="16" height="16" viewBox="0 0 24 24">
                    <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工具类使用 -->
      <div class="demo-section">
        <h2>🔧 工具类使用</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>快速应用工具类</h3>
            <div class="demo-area">
              <button class="add-btn">
                <svg class="add-icon-base" width="20" height="20" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <button class="add-btn-sm">
                <svg class="add-icon-base" width="16" height="16" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
              <button class="add-btn-lg">
                <svg class="add-icon-base" width="24" height="24" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-width="2" d="M12 5v14m-7-7h14"/>
                </svg>
              </button>
            </div>
            <div class="code-example">
              <pre><code>&lt;!-- 使用工具类 --&gt;
&lt;button class="add-btn"&gt;...&lt;/button&gt;
&lt;button class="add-btn-sm"&gt;...&lt;/button&gt;
&lt;button class="add-btn-lg"&gt;...&lt;/button&gt;</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- API 文档 -->
      <div class="demo-section">
        <h2>📖 API 文档</h2>
        <div class="api-tables">
          <div class="api-table">
            <h3>基础类</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>说明</th>
                  <th>用法</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.add-button-base</td>
                  <td>添加按钮基础样式</td>
                  <td>必须与其他类组合使用</td>
                </tr>
                <tr>
                  <td>.add-icon-base</td>
                  <td>添加图标基础样式</td>
                  <td>应用于SVG图标元素</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="api-table">
            <h3>尺寸类</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>尺寸</th>
                  <th>适用场景</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.add-button-sm</td>
                  <td>32x32px</td>
                  <td>紧凑界面、内联使用</td>
                </tr>
                <tr>
                  <td>.add-button-md</td>
                  <td>40x40px</td>
                  <td>标准界面、默认尺寸</td>
                </tr>
                <tr>
                  <td>.add-button-lg</td>
                  <td>48x48px</td>
                  <td>重要操作、突出显示</td>
                </tr>
                <tr>
                  <td>.add-button-xl</td>
                  <td>56x56px</td>
                  <td>主要操作、大屏幕</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="api-table">
            <h3>动画类</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>效果</th>
                  <th>触发条件</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.add-button-rotate</td>
                  <td>旋转90度</td>
                  <td>鼠标悬停</td>
                </tr>
                <tr>
                  <td>.add-button-scale</td>
                  <td>缩放1.1倍</td>
                  <td>鼠标悬停</td>
                </tr>
                <tr>
                  <td>.add-button-pulse</td>
                  <td>脉冲动画</td>
                  <td>鼠标悬停</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="api-table">
            <h3>颜色类</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>颜色</th>
                  <th>使用场景</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.add-button-primary</td>
                  <td>主色调</td>
                  <td>默认添加操作</td>
                </tr>
                <tr>
                  <td>.add-button-success</td>
                  <td>成功色</td>
                  <td>确认添加操作</td>
                </tr>
                <tr>
                  <td>.add-button-warning</td>
                  <td>警告色</td>
                  <td>需要注意的添加</td>
                </tr>
                <tr>
                  <td>.add-button-danger</td>
                  <td>危险色</td>
                  <td>重要的添加操作</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="api-table">
            <h3>工具类</h3>
            <table>
              <thead>
                <tr>
                  <th>类名</th>
                  <th>等价组合</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>.add-btn</td>
                  <td>.add-button-base .add-button-md .add-button-primary .add-button-rotate</td>
                  <td>标准添加按钮</td>
                </tr>
                <tr>
                  <td>.add-btn-sm</td>
                  <td>.add-button-base .add-button-sm .add-button-primary .add-button-rotate</td>
                  <td>小号添加按钮</td>
                </tr>
                <tr>
                  <td>.add-btn-lg</td>
                  <td>.add-button-base .add-button-lg .add-button-primary .add-button-rotate</td>
                  <td>大号添加按钮</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 无需特殊逻辑，纯展示页面
</script>

<style scoped>
/* 引入设计令牌和按钮预设 */
@import '@/styles/design-tokens.css';
@import '@/styles/tag-presets.css';

.button-presets-demo {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.page-header h1 {
  font-size: var(--font-size-3xl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 800px;
  margin: 0 auto var(--spacing-4);
  line-height: 1.6;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--duration-base) ease;
}

.back-link:hover {
  color: var(--color-primary-dark);
}

/* 演示区块 */
.demo-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
}

.demo-section h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

.showcase-item h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
}

.demo-area {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  align-items: center;
  padding: var(--spacing-4);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-3);
}

.demo-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.color-labels {
  display: flex;
  gap: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-2);
}

.code-example {
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-3);
}

.code-example pre {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  overflow-x: auto;
}

/* 实际应用示例 */
.tag-management-demo .tag-list {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  flex-wrap: wrap;
}

.demo-tag {
  background: var(--gradient-primary);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-tag);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.color-picker-demo .color-grid-demo {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.color-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid var(--color-border-light);
}

.toolbar-demo .toolbar {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
  padding: var(--spacing-2);
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-lg);
}

.toolbar-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--color-text-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-base) ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  background: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.toolbar-divider {
  width: 1px;
  height: 20px;
  background: var(--color-border-base);
  margin: 0 var(--spacing-1);
}

/* API 表格 */
.api-tables {
  display: grid;
  gap: var(--spacing-6);
}

.api-table h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
}

.api-table table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.api-table th,
.api-table td {
  padding: var(--spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--color-border-light);
}

.api-table th {
  background: var(--color-bg-tertiary);
  font-weight: 600;
  color: var(--color-text-primary);
}

.api-table td {
  color: var(--color-text-secondary);
}

.api-table td:first-child {
  font-family: monospace;
  color: var(--color-primary);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-presets-demo {
    padding: var(--spacing-4);
  }
  
  .demo-area {
    justify-content: center;
  }
  
  .color-labels {
    justify-content: center;
  }
  
  .api-table {
    overflow-x: auto;
  }
}
</style>
