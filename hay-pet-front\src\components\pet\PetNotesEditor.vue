<template>
  <div class="pet-notes-editor">
    <div class="notes-header">
      <span class="notes-label">宠物备注</span>
      <div class="notes-actions">
        <el-button 
          v-if="!isEditing" 
          type="primary" 
          size="small" 
          @click="startEdit"
          :icon="Edit"
        >
          编辑备注
        </el-button>
        <div v-else class="edit-actions">
          <el-button 
            type="success" 
            size="small" 
            @click="saveNotes"
            :loading="saving"
            :icon="Check"
          >
            保存
          </el-button>
          <el-button 
            size="small" 
            @click="cancelEdit"
            :icon="Close"
          >
            取消
          </el-button>
        </div>
      </div>
    </div>
    
    <div class="notes-content">
      <!-- 显示模式 -->
      <div v-if="!isEditing" class="notes-display">
        <div v-if="displayNotes" class="notes-text" v-html="formattedNotes"></div>
        <div v-else class="no-notes">
          <el-text type="info">暂无备注信息，点击编辑备注按钮添加备注</el-text>
        </div>
      </div>
      
      <!-- 编辑模式 -->
      <div v-else class="notes-edit">
        <el-input
          v-model="editingNotes"
          type="textarea"
          placeholder="请输入宠物备注信息...\n\n支持的格式：\n- 使用 **文字** 表示粗体\n- 使用 *文字* 表示斜体\n- 使用换行分段\n- 支持表情符号 😊"
          :rows="8"
          maxlength="1000"
          show-word-limit
          resize="vertical"
          class="notes-textarea"
        />
        
        <div class="edit-tips">
          <el-text type="info" size="small">
            <el-icon><InfoFilled /></el-icon>
            支持简单的 Markdown 格式：**粗体**、*斜体*
          </el-text>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, Close, InfoFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  petId: {
    type: String,
    required: true
  },
  notes: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['notesUpdated'])

// 依赖注入
const supabase = inject('supabase')

// 响应式数据
const isEditing = ref(false)
const saving = ref(false)
const editingNotes = ref('')
const originalNotes = ref('')

// 计算属性
const displayNotes = computed(() => {
  return props.notes && props.notes.trim()
})

const formattedNotes = computed(() => {
  if (!displayNotes.value) return ''
  
  let formatted = props.notes
    // 转义 HTML 特殊字符
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    // 处理 Markdown 格式
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
    .replace(/\*(.*?)\*/g, '<em>$1</em>') // 斜体
    // 处理换行
    .replace(/\n/g, '<br>')
  
  return formatted
})

// 方法
const startEdit = () => {
  isEditing.value = true
  editingNotes.value = props.notes || ''
  originalNotes.value = props.notes || ''
}

const cancelEdit = () => {
  isEditing.value = false
  editingNotes.value = originalNotes.value
}

const saveNotes = async () => {
  if (!props.petId) {
    ElMessage.error('宠物ID不能为空')
    return
  }
  
  try {
    saving.value = true
    
    // 更新数据库中的备注
    const { error } = await supabase
      .from('pets')
      .update({ notes: editingNotes.value.trim() || null })
      .eq('id', props.petId)
    
    if (error) throw error
    
    // 更新成功
    isEditing.value = false
    originalNotes.value = editingNotes.value
    
    ElMessage.success('备注保存成功')
    emit('notesUpdated', editingNotes.value)
  } catch (error) {
    console.error('保存备注失败:', error)
    ElMessage.error('保存备注失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

// 监听 props.notes 变化
watch(() => props.notes, (newNotes) => {
  if (!isEditing.value) {
    originalNotes.value = newNotes || ''
  }
}, { immediate: true })

// 监听宠物ID变化
watch(() => props.petId, () => {
  if (isEditing.value) {
    cancelEdit()
  }
})
</script>

<style scoped lang="scss">
.pet-notes-editor {
  .notes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .notes-label {
      font-weight: 500;
      color: #303133;
    }
    
    .edit-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .notes-content {
    .notes-display {
      .notes-text {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        line-height: 1.6;
        color: #303133;
        min-height: 60px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        transition: all 0.3s ease;

        &:hover {
          border-color: #409EFF;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
        }
        
        // Markdown 样式
        :deep(strong) {
          font-weight: 600;
          color: #409eff;
        }
        
        :deep(em) {
          font-style: italic;
          color: #67c23a;
        }
        
        :deep(br) {
          margin-bottom: 8px;
        }
      }
      
      .no-notes {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        background: #f8f9fa;
        border: 2px dashed #d1d5db;
        border-radius: 12px;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409EFF;
          background: #f0f9ff;
        }
      }
    }
    
    .notes-edit {
      .notes-textarea {
        :deep(.el-textarea__inner) {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          
          &::placeholder {
            color: #a8abb2;
            line-height: 1.4;
          }
        }
      }
      
      .edit-tips {
        margin-top: 8px;
        padding: 12px 16px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #409EFF;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        
        .el-text {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pet-notes-editor {
    .notes-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      
      .edit-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
    
    .notes-content {
      .notes-edit {
        .notes-textarea {
          :deep(.el-textarea__inner) {
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>