import { createClient } from '@supabase/supabase-js'

// Supabase配置 - 需要替换为实际的URL和密钥
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'placeholder_anon_key'

// 开发环境下使用代理URL来解决CORS问题
const isDev = import.meta.env.DEV

// 验证URL格式
let supabase
try {
  // 检查是否为有效的URL格式
  if (supabaseUrl.startsWith('http') && supabaseUrl.includes('supabase')) {
    // 统一创建客户端实例，避免多实例警告
    supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      }
    })
  } else {

    // 创建一个模拟的supabase对象用于开发
    const mockUser = { id: 'mock-user-id', email: '<EMAIL>' }
    const mockPets = [
      { id: 'mock-pet-1', name: '小白', species: '猫', user_id: 'mock-user-id' },
      { id: 'mock-pet-2', name: '小黑', species: '狗', user_id: 'mock-user-id' }
    ]
    
    supabase = {
      auth: {
        getUser: () => Promise.resolve({ data: { user: mockUser }, error: null }),
        signInWithPassword: () => Promise.resolve({ data: { user: mockUser }, error: null }),
        signUp: () => Promise.resolve({ data: { user: mockUser }, error: null }),
        signOut: () => Promise.resolve({ error: null }),
        onAuthStateChange: () => {}
      },
      from: (table) => ({
        select: (columns) => ({
          eq: (column, value) => ({
            order: (orderColumn, options) => {
              if (table === 'pets') {
                return Promise.resolve({ data: mockPets, error: null })
              }
              return Promise.resolve({ data: [], error: null })
            }
          }),
          data: table === 'pets' ? mockPets : [],
          error: null
        }),
        insert: (data) => {
  
          return Promise.resolve({ data: data, error: null })
        },
        update: (data) => ({
          eq: (column, value) => {

            return Promise.resolve({ data: [data], error: null })
          }
        }),
        delete: () => ({
          eq: (column, value) => {

            return Promise.resolve({ data: [], error: null })
          }
        })
      }),
      storage: {
        from: () => ({
          upload: () => Promise.resolve({ data: null, error: { message: '请配置Supabase' } }),
          getPublicUrl: () => ({ data: { publicUrl: '' } })
        })
      }
    }
  }
} catch (error) {
  console.error('Supabase初始化失败:', error)
  // 使用模拟对象
  const mockUser = { id: 'mock-user-id', email: '<EMAIL>' }
  const mockPets = [
    { id: 'mock-pet-1', name: '小白', species: '猫', user_id: 'mock-user-id' },
    { id: 'mock-pet-2', name: '小黑', species: '狗', user_id: 'mock-user-id' }
  ]
  
  supabase = {
    auth: {
      getUser: () => Promise.resolve({ data: { user: mockUser }, error: null }),
      signInWithPassword: () => Promise.resolve({ data: { user: mockUser }, error: null }),
      signUp: () => Promise.resolve({ data: { user: mockUser }, error: null }),
      signOut: () => Promise.resolve({ error: null }),
      onAuthStateChange: () => {}
    },
    from: (table) => ({
      select: (columns) => ({
        eq: (column, value) => ({
          order: (orderColumn, options) => {
            if (table === 'pets') {
              return Promise.resolve({ data: mockPets, error: null })
            }
            return Promise.resolve({ data: [], error: null })
          }
        }),
        data: table === 'pets' ? mockPets : [],
        error: null
      }),
      insert: (data) => {

        return Promise.resolve({ data: data, error: null })
      },
      update: (data) => ({
        eq: (column, value) => {

          return Promise.resolve({ data: [data], error: null })
        }
      }),
      delete: () => ({
        eq: (column, value) => {

          return Promise.resolve({ data: [], error: null })
        }
      })
    }),
    storage: {
      from: () => ({
        upload: () => Promise.resolve({ data: null, error: { message: '请配置Supabase' } }),
        getPublicUrl: () => ({ data: { publicUrl: '' } })
      })
    }
  }
}

export { supabase }