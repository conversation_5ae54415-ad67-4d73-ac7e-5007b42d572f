<template>
  <el-dialog
    v-model="visible"
    :title="isEditMode ? `编辑${typeLabel}` : `添加${typeLabel}`"
    width="420px"
    class="simple-type-dialog"
    @closed="handleClosed"
  >
    <el-form :model="formData" label-width="80px">
      <!-- 类型名称 -->
      <el-form-item :label="`${typeLabel}名称`" required>
        <el-input
          v-model="formData.label"
          :placeholder="`请输入${typeLabel}名称`"
          maxlength="20"
          show-word-limit
          clearable
        />
      </el-form-item>

      <!-- 类型颜色 -->
      <el-form-item :label="`${typeLabel}颜色`" required>
        <div class="color-selection-container">
          <!-- 颜色预览和选择器 -->
          <div class="color-picker-row">
            <div class="color-preview" :style="{ backgroundColor: formData.color }"></div>
            <el-color-picker
              v-model="formData.color"
              @change="handleColorPickerChange"
              size="default"
              :predefine="predefinedColors"
              :title="`当前颜色: ${formData.color}`"
            />
          </div>

          <!-- 快速颜色选择网格 -->
          <div class="color-grid-container">
            <div class="color-grid">
              <button
                v-for="color in predefinedColors"
                :key="color"
                @click="selectPresetColor(color)"
                :class="['color-item', { 'selected': formData.color === color }]"
                :style="{ backgroundColor: color }"
                :title="getColorName(color)"
                type="button"
              >
                <div v-if="formData.color === color" class="selected-indicator">
                  <el-icon class="check-icon">
                    <Check />
                  </el-icon>
                </div>
              </button>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="simple-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { Check } from '@element-plus/icons-vue';

const props = defineProps({
  // 对话框显示状态
  modelValue: {
    type: Boolean,
    default: false
  },
  // 是否为编辑模式
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 类型标签（如"记录类型"、"分类"等）
  typeLabel: {
    type: String,
    default: '类型'
  },
  // 表单数据
  data: {
    type: Object,
    default: () => ({
      label: '',
      color: '#409EFF'
    })
  },
  // 预定义颜色
  predefinedColors: {
    type: Array,
    default: () => [
      // 第一行：主要品牌色
      '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9C27B0',
      // 第二行：活力色彩
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
      // 第三行：专业色调
      '#6C5CE7', '#A29BFE', '#FD79A8', '#FDCB6E', '#E17055', '#74B9FF',
      // 第四行：自然色系
      '#00B894', '#00CEC9', '#E84393', '#FDCB6E', '#6C5CE7', '#A29BFE'
    ]
  }
});

const emit = defineEmits(['update:modelValue', 'save', 'cancel', 'closed']);

// 内部状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const formData = ref({ ...props.data });

// 监听外部数据变化
watch(() => props.data, (newData) => {
  formData.value = { ...newData };
}, { deep: true });

// 颜色选择相关方法
const selectPresetColor = (color) => {
  formData.value.color = color;
};

const handleColorPickerChange = (color) => {
  if (color) {
    formData.value.color = color;
  }
};

// 获取颜色名称（用于提示）
const getColorName = (color) => {
  const colorNames = {
    // 主要品牌色
    '#409EFF': '主蓝色',
    '#67C23A': '成功绿',
    '#E6A23C': '警告橙',
    '#F56C6C': '危险红',
    '#909399': '信息灰',
    '#9C27B0': '优雅紫',
    // 活力色彩
    '#FF6B6B': '活力红',
    '#4ECDC4': '薄荷绿',
    '#45B7D1': '天空蓝',
    '#96CEB4': '清新绿',
    '#FFEAA7': '柠檬黄',
    '#DDA0DD': '淡紫色',
    // 专业色调
    '#6C5CE7': '深紫色',
    '#A29BFE': '浅紫色',
    '#FD79A8': '粉红色',
    '#FDCB6E': '金黄色',
    '#E17055': '珊瑚色',
    '#74B9FF': '亮蓝色',
    // 自然色系
    '#00B894': '翡翠绿',
    '#00CEC9': '青绿色',
    '#E84393': '玫红色'
  };
  return colorNames[color] || '自定义颜色';
};

// 事件处理
const handleSave = () => {
  emit('save', { ...formData.value });
};

const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

const handleClosed = () => {
  emit('closed');
};
</script>

<style scoped>
/* 简洁的对话框样式 */
.simple-type-dialog {
  --el-dialog-border-radius: 8px;
}

/* 颜色选择容器 */
.color-selection-container {
  width: 100%;
}

/* 颜色预览行 */
.color-picker-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.color-preview {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.color-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 颜色网格容器 */
.color-grid-container {
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  padding: 20px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
  max-width: 100%;
}

/* 颜色项 */
.color-item {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  outline: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-item:hover {
  transform: scale(1.15);
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  z-index: 1;
}

.color-item.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  transform: scale(1.1);
}

.color-item.selected:hover {
  transform: scale(1.2);
}

/* 选中指示器 */
.selected-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.check-icon {
  color: #409eff;
  font-size: 12px;
  font-weight: bold;
}

.simple-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .color-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
  }

  .color-item {
    width: 36px;
    height: 36px;
  }

  .color-grid-container {
    padding: 16px;
  }
}
</style>

<style>
/* 全局样式覆盖 - 去掉花俏的装饰 */
.el-dialog.simple-type-dialog .el-dialog__header {
  background: #ffffff !important;
  color: #303133 !important;
  border-bottom: 1px solid #e4e7ed !important;
  padding: 16px 20px !important;
  text-align: center !important;
  position: relative !important;
}

.el-dialog.simple-type-dialog .el-dialog__title {
  color: #303133 !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  text-align: center !important;
  margin: 0 !important;
  width: 100% !important;
  display: block !important;
}

.el-dialog.simple-type-dialog .el-dialog__headerbtn {
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
}

.el-dialog.simple-type-dialog .el-dialog__headerbtn .el-dialog__close {
  color: #909399 !important;
  font-size: 16px !important;
}

.el-dialog.simple-type-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: #409eff !important;
}

.el-dialog.simple-type-dialog .el-dialog__body {
  padding: 20px !important;
  background: #ffffff !important;
}

.el-dialog.simple-type-dialog .el-dialog__footer {
  padding: 16px 20px !important;
  background: #ffffff !important;
  border-top: 1px solid #e4e7ed !important;
}
</style>
