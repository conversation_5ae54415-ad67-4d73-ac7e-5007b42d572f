<template>
  <el-dialog
    v-model="visible"
    :title="isEditMode ? `编辑${typeLabel}` : `添加${typeLabel}`"
    width="480px"
    class="modern-type-dialog"
    @closed="handleClosed"
  >
    <div class="dialog-content">
      <!-- 类型名称 -->
      <div class="form-section">
        <label class="form-label">
          <span class="required-star">*</span>
          {{ typeLabel }}名称
        </label>
        <el-input
          v-model="formData.label"
          :placeholder="`请输入${typeLabel}名称`"
          maxlength="20"
          show-word-limit
          clearable
          class="name-input"
        />
      </div>

      <!-- 类型颜色 -->
      <div class="form-section">
        <label class="form-label">
          <span class="required-star">*</span>
          {{ typeLabel }}颜色
        </label>

        <!-- 当前颜色预览 -->
        <div class="current-color-preview">
          <div class="color-display" :style="{ backgroundColor: formData.color }"></div>
          <span class="color-info">{{ getColorName(formData.color) }} ({{ formData.color }})</span>
          <el-color-picker
            v-model="formData.color"
            @change="handleColorPickerChange"
            size="small"
            class="custom-picker"
          />
        </div>

        <!-- 颜色选择网格 -->
        <div class="color-palette">
          <div class="palette-grid">
            <button
              v-for="color in colorPresets"
              :key="color"
              @click="selectColor(color)"
              :class="['color-button', { 'active': formData.color === color }]"
              :style="{ backgroundColor: color }"
              :title="getColorName(color)"
              type="button"
            >
              <i v-if="formData.color === color" class="check-icon">✓</i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" size="default">取消</el-button>
        <el-button type="primary" @click="handleSave" size="default">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

const props = defineProps({
  // 对话框显示状态
  modelValue: {
    type: Boolean,
    default: false
  },
  // 是否为编辑模式
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 类型标签（如"记录类型"、"分类"等）
  typeLabel: {
    type: String,
    default: '类型'
  },
  // 表单数据
  data: {
    type: Object,
    default: () => ({
      label: '',
      color: '#409EFF'
    })
  }
});

const emit = defineEmits(['update:modelValue', 'save', 'cancel', 'closed']);

// 精选颜色预设 - 6x4 网格
const colorPresets = [
  // 第一行：主要品牌色
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9C27B0',
  // 第二行：活力色彩
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
  // 第三行：专业色调
  '#6C5CE7', '#A29BFE', '#FD79A8', '#FDCB6E', '#E17055', '#74B9FF',
  // 第四行：自然色系
  '#00B894', '#00CEC9', '#E84393', '#FF7675', '#81ECEC', '#A29BFE'
];

// 内部状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const formData = ref({ ...props.data });

// 监听外部数据变化
watch(() => props.data, (newData) => {
  formData.value = { ...newData };
}, { deep: true });

// 颜色选择方法
const selectColor = (color) => {
  formData.value.color = color;
};

const handleColorPickerChange = (color) => {
  if (color) {
    formData.value.color = color;
  }
};

// 获取颜色名称（用于提示）
const getColorName = (color) => {
  const colorNames = {
    // 主要品牌色
    '#409EFF': '主蓝色',
    '#67C23A': '成功绿',
    '#E6A23C': '警告橙',
    '#F56C6C': '危险红',
    '#909399': '信息灰',
    '#9C27B0': '优雅紫',
    // 活力色彩
    '#FF6B6B': '活力红',
    '#4ECDC4': '薄荷绿',
    '#45B7D1': '天空蓝',
    '#96CEB4': '清新绿',
    '#FFEAA7': '柠檬黄',
    '#DDA0DD': '淡紫色',
    // 专业色调
    '#6C5CE7': '深紫色',
    '#A29BFE': '浅紫色',
    '#FD79A8': '粉红色',
    '#FDCB6E': '金黄色',
    '#E17055': '珊瑚色',
    '#74B9FF': '亮蓝色',
    // 自然色系
    '#00B894': '翡翠绿',
    '#00CEC9': '青绿色',
    '#E84393': '玫红色',
    '#FF7675': '浅红色',
    '#81ECEC': '浅青色',
    '#A29BFE': '浅紫色'
  };
  return colorNames[color] || '自定义颜色';
};

// 事件处理
const handleSave = () => {
  emit('save', { ...formData.value });
};

const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

const handleClosed = () => {
  emit('closed');
};
</script>

<style scoped>
/* 现代化对话框样式 */
.modern-type-dialog {
  --el-dialog-border-radius: 12px;
}

.dialog-content {
  padding: 0;
}

/* 表单区域 */
.form-section {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.required-star {
  color: #F56C6C;
  margin-right: 4px;
}

.name-input {
  width: 100%;
}

/* 当前颜色预览 */
.current-color-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.color-display {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.color-info {
  flex: 1;
  font-size: 13px;
  color: #606266;
}

.custom-picker {
  flex-shrink: 0;
}

/* 颜色调色板 */
.color-palette {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  padding: 16px;
}

.palette-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
  justify-items: center;
}

/* 颜色按钮 */
.color-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  outline: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.color-button:hover {
  transform: scale(1.1);
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.color-button.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  transform: scale(1.05);
}

.color-button.active:hover {
  transform: scale(1.15);
}

/* 勾选图标 */
.check-icon {
  color: white;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 8px;
}

/* 响应式设计 */
@media (max-width: 520px) {
  .palette-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 6px;
  }

  .color-button {
    width: 32px;
    height: 32px;
  }

  .current-color-preview {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>

<style>
/* 全局样式覆盖 - 现代化对话框 */
.el-dialog.modern-type-dialog .el-dialog__header {
  background: #ffffff !important;
  color: #303133 !important;
  border-bottom: 1px solid #e4e7ed !important;
  padding: 20px 24px !important;
  text-align: center !important;
  position: relative !important;
}

.el-dialog.modern-type-dialog .el-dialog__title {
  color: #303133 !important;
  font-weight: 600 !important;
  font-size: 18px !important;
  text-align: center !important;
  margin: 0 !important;
  width: 100% !important;
  display: block !important;
}

.el-dialog.modern-type-dialog .el-dialog__headerbtn {
  position: absolute !important;
  right: 20px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
}

.el-dialog.modern-type-dialog .el-dialog__headerbtn .el-dialog__close {
  color: #909399 !important;
  font-size: 18px !important;
  transition: color 0.2s ease !important;
}

.el-dialog.modern-type-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: #409eff !important;
}

.el-dialog.modern-type-dialog .el-dialog__body {
  padding: 24px !important;
  background: #ffffff !important;
}

.el-dialog.modern-type-dialog .el-dialog__footer {
  padding: 20px 24px !important;
  background: #ffffff !important;
  border-top: 1px solid #e4e7ed !important;
}
</style>
