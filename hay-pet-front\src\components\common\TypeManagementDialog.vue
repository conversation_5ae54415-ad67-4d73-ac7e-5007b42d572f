<template>
  <el-dialog
    v-model="visible"
    :title="isEditMode ? `编辑${typeLabel}` : `添加${typeLabel}`"
    width="420px"
    class="simple-type-dialog"
    @closed="handleClosed"
  >
    <el-form :model="formData" label-width="80px">
      <!-- 类型名称 -->
      <el-form-item :label="`${typeLabel}名称`" required>
        <el-input
          v-model="formData.label"
          :placeholder="`请输入${typeLabel}名称`"
          maxlength="20"
          show-word-limit
          clearable
        />
      </el-form-item>

      <!-- 类型颜色 -->
      <el-form-item :label="`${typeLabel}颜色`" required>
        <div class="color-selection-wrapper">
          <!-- 颜色预览和选择器 -->
          <div class="color-preview-row">
            <div class="current-color" :style="{ backgroundColor: formData.color }"></div>
            <el-color-picker
              v-model="formData.color"
              @change="handleColorPickerChange"
              size="default"
              :predefine="predefinedColors"
            />
          </div>

          <!-- 快速颜色选择 -->
          <div class="quick-colors">
            <button
              v-for="color in predefinedColors"
              :key="color"
              @click.prevent="selectPresetColor(color)"
              :class="['quick-color-btn', { 'active': formData.color === color }]"
              :style="{ backgroundColor: color }"
              :title="getColorName(color)"
              type="button"
            >
              <el-icon v-if="formData.color === color" class="check-mark">
                <Check />
              </el-icon>
            </button>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="simple-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { Check } from '@element-plus/icons-vue';

const props = defineProps({
  // 对话框显示状态
  modelValue: {
    type: Boolean,
    default: false
  },
  // 是否为编辑模式
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 类型标签（如"记录类型"、"分类"等）
  typeLabel: {
    type: String,
    default: '类型'
  },
  // 表单数据
  data: {
    type: Object,
    default: () => ({
      label: '',
      color: '#409EFF'
    })
  },
  // 预定义颜色
  predefinedColors: {
    type: Array,
    default: () => [
      // 第一行：主要颜色
      '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#9C27B0',
      // 第二行：扩展颜色
      '#FF9800', '#4CAF50', '#2196F3', '#FF5722', '#795548', '#607D8B',
      // 第三行：柔和颜色
      '#87CEEB', '#98FB98', '#FFB6C1', '#DDA0DD', '#F0E68C', '#FFA07A'
    ]
  }
});

const emit = defineEmits(['update:modelValue', 'save', 'cancel', 'closed']);

// 内部状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const formData = ref({ ...props.data });

// 监听外部数据变化
watch(() => props.data, (newData) => {
  formData.value = { ...newData };
}, { deep: true });

// 颜色选择相关方法
const selectPresetColor = (color) => {
  formData.value.color = color;
};

const handleColorPickerChange = (color) => {
  if (color) {
    formData.value.color = color;
  }
};

// 获取颜色名称（用于提示）
const getColorName = (color) => {
  const colorNames = {
    // 常用颜色
    '#409EFF': '蓝色',
    '#67C23A': '绿色',
    '#E6A23C': '橙色',
    '#F56C6C': '红色',
    '#909399': '灰色',
    '#9C27B0': '紫色',
    // 扩展颜色
    '#FF9800': '琥珀色',
    '#4CAF50': '青绿色',
    '#2196F3': '天蓝色',
    '#FF5722': '深橙色',
    '#795548': '棕色',
    '#607D8B': '蓝灰色',
    // 柔和颜色
    '#87CEEB': '天空蓝',
    '#98FB98': '浅绿色',
    '#FFB6C1': '浅粉色',
    '#DDA0DD': '梅花色',
    '#F0E68C': '卡其色',
    '#FFA07A': '浅橙色'
  };
  return colorNames[color] || '自定义颜色';
};

// 事件处理
const handleSave = () => {
  emit('save', { ...formData.value });
};

const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

const handleClosed = () => {
  emit('closed');
};
</script>

<style scoped>
/* 简洁的对话框样式 */
.simple-type-dialog {
  --el-dialog-border-radius: 8px;
}

.color-selection-wrapper {
  width: 100%;
}

.color-preview-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.current-color {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 2px solid #e4e7ed;
  flex-shrink: 0;
}

.quick-colors {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  width: 100%;
  box-sizing: border-box;
}

.quick-color-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  outline: none;
}

.quick-color-btn:hover {
  transform: scale(1.1);
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.quick-color-btn.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.check-mark {
  color: white;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.simple-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>

<style>
/* 全局样式覆盖 - 去掉花俏的装饰 */
.el-dialog.simple-type-dialog .el-dialog__header {
  background: #ffffff !important;
  color: #303133 !important;
  border-bottom: 1px solid #e4e7ed !important;
  padding: 16px 20px !important;
  text-align: center !important;
  position: relative !important;
}

.el-dialog.simple-type-dialog .el-dialog__title {
  color: #303133 !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  text-align: center !important;
  margin: 0 !important;
  width: 100% !important;
  display: block !important;
}

.el-dialog.simple-type-dialog .el-dialog__headerbtn {
  position: absolute !important;
  right: 16px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin: 0 !important;
}

.el-dialog.simple-type-dialog .el-dialog__headerbtn .el-dialog__close {
  color: #909399 !important;
  font-size: 16px !important;
}

.el-dialog.simple-type-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: #409eff !important;
}

.el-dialog.simple-type-dialog .el-dialog__body {
  padding: 20px !important;
  background: #ffffff !important;
}

.el-dialog.simple-type-dialog .el-dialog__footer {
  padding: 16px 20px !important;
  background: #ffffff !important;
  border-top: 1px solid #e4e7ed !important;
}
</style>
