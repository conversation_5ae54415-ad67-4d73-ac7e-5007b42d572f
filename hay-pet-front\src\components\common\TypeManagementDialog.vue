<template>
  <el-dialog
    v-model="visible"
    :title="isEditMode ? `编辑${typeLabel}` : `添加${typeLabel}`"
    width="480px"
    class="type-management-dialog"
    @closed="handleClosed"
  >
    <div class="type-form-container">
      <el-form :model="formData" label-width="0px">
        <!-- 类型名称 -->
        <div class="form-section">
          <div class="section-header">
            <span class="section-title">{{ typeLabel }}名称</span>
            <span class="required-mark">*</span>
          </div>
          <el-input
            v-model="formData.label"
            :placeholder="`请输入${typeLabel}名称`"
            maxlength="20"
            show-word-limit
            class="type-name-input"
          ></el-input>
        </div>

        <!-- 类型颜色 -->
        <div class="form-section">
          <div class="section-header">
            <span class="section-title">{{ typeLabel }}颜色</span>
            <span class="required-mark">*</span>
            <div class="color-picker-container">
              <el-color-picker
                v-model="formData.color"
                @change="handleColorPickerChange"
                :title="'当前颜色: ' + formData.color"
                size="default"
                show-alpha
                :predefine="predefinedColors"
              />
            </div>
          </div>

          <!-- 颜色选择区域 -->
          <div class="color-selection-area">
            <div class="unified-color-grid">
              <!-- 预设颜色按钮 -->
              <button
                v-for="color in predefinedColors"
                :key="color"
                @click.prevent="selectPresetColor(color)"
                :class="['color-button', { 'selected': formData.color === color }]"
                :style="{ backgroundColor: color }"
                :title="getColorName(color)"
                type="button"
              >
                <div v-if="formData.color === color" class="selected-indicator">
                  <el-icon class="check-icon">
                    <Check />
                  </el-icon>
                </div>
              </button>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer-custom">
        <el-button @click="handleCancel" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="handleSave" class="save-btn">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { Check } from '@element-plus/icons-vue';

const props = defineProps({
  // 对话框显示状态
  modelValue: {
    type: Boolean,
    default: false
  },
  // 是否为编辑模式
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 类型标签（如"记录类型"、"分类"等）
  typeLabel: {
    type: String,
    default: '类型'
  },
  // 表单数据
  data: {
    type: Object,
    default: () => ({
      label: '',
      color: '#409EFF'
    })
  },
  // 预定义颜色
  predefinedColors: {
    type: Array,
    default: () => [
      '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
      '#909399', '#9C27B0', '#FF9800', '#4CAF50',
      '#2196F3', '#FF5722', '#795548', '#607D8B'
    ]
  }
});

const emit = defineEmits(['update:modelValue', 'save', 'cancel', 'closed']);

// 内部状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const formData = ref({ ...props.data });

// 监听外部数据变化
watch(() => props.data, (newData) => {
  formData.value = { ...newData };
}, { deep: true });

// 颜色选择相关方法
const selectPresetColor = (color) => {
  formData.value.color = color;
};

const handleColorPickerChange = (color) => {
  if (color) {
    formData.value.color = color;
  }
};

// 获取颜色名称（用于提示）
const getColorName = (color) => {
  const colorNames = {
    '#409EFF': '蓝色',
    '#67C23A': '绿色',
    '#E6A23C': '橙色',
    '#F56C6C': '红色',
    '#909399': '灰色',
    '#9C27B0': '紫色',
    '#FF9800': '琥珀色',
    '#4CAF50': '青绿色',
    '#2196F3': '天蓝色',
    '#FF5722': '深橙色',
    '#795548': '棕色',
    '#607D8B': '蓝灰色'
  };
  return colorNames[color] || '自定义颜色';
};

// 事件处理
const handleSave = () => {
  emit('save', { ...formData.value });
};

const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

const handleClosed = () => {
  emit('closed');
};
</script>

<style>
/* 类型管理对话框样式 - 确保优先级 */
.el-dialog.type-management-dialog .el-dialog__header {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%) !important;
  color: white !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 20px 24px !important;
}

.el-dialog.type-management-dialog .el-dialog__title {
  color: white !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.el-dialog.type-management-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white !important;
  font-size: 20px !important;
}

.el-dialog.type-management-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

.el-dialog.type-management-dialog .el-dialog__body {
  padding: 24px !important;
  background: #fafbfc !important;
}
</style>
