{"name": "hay-pet-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "@types/sortablejs": "^1.15.8", "@vueuse/integrations": "^13.3.0", "@vueuse/motion": "^3.0.3", "animate.css": "^4.1.1", "chart.js": "^4.4.9", "cropperjs": "^2.0.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.4.4", "exif-js": "^2.3.0", "file-saver": "^2.0.5", "gsap": "^3.13.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.3.1", "playwright": "^1.53.1", "sass": "^1.89.2", "sortablejs": "^1.15.6", "v-viewer": "^3.0.21", "vue": "^3.3.11", "vue-chartjs": "^5.3.2", "vue-echarts": "^7.0.3", "vue-picture-cropper": "^0.7.0", "vue-router": "^4.5.1", "vue-virtual-scroll-list": "^2.3.5", "vue-waterfall-plugin-next": "^2.6.7", "vue3-photo-preview": "^0.3.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^4.5.2", "@vue/test-utils": "^2.4.3", "jsdom": "^23.0.1", "vite": "^5.0.8", "vitest": "^1.0.4"}}