<template>
  <el-card class="dashboard-card health-timeline-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">健康记录时间线</span>
        <el-button type="primary" link @click="router.push('/records')">
          <el-icon><Calendar /></el-icon>
          查看详细
        </el-button>
      </div>
    </template>
    <div class="health-timeline-content">
      <div v-if="healthRecords.length" class="timeline-content">
        <div class="timeline-chart">
          <v-chart 
            class="health-chart" 
            :option="timelineOption" 
            :loading="loading"
            autoresize
          />
        </div>
        <div class="health-stats">
          <div class="stat-item">
            <span class="stat-label">总记录数</span>
            <span class="stat-value">{{ healthRecords.length }} 条</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最近记录</span>
            <span class="stat-value">{{ latestRecord?.type || '无' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">下次提醒</span>
            <span class="stat-value">{{ nextReminder || '无' }}</span>
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无健康记录">
        <el-button type="primary" @click="router.push('/records')">
          <el-icon><Plus /></el-icon>
          添加健康记录
        </el-button>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Calendar, Plus } from '@element-plus/icons-vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { ScatterChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import { usePetStore } from '@/stores/pet';
import { supabase } from '@/utils/supabase';
import type { DashboardBlock } from '@/types/dashboard';

// 注册ECharts组件
use([
  CanvasRenderer,
  ScatterChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
]);

interface Props {
  blockConfig: DashboardBlock;
}

defineProps<Props>();

const router = useRouter();
const petStore = usePetStore();

const healthRecords = ref<any[]>([]);
const loading = ref(false);

const currentPet = computed(() => petStore.currentPet);

// 健康记录类型映射
const recordTypeMap = {
  'vaccination': { name: '疫苗接种', color: '#67C23A', symbol: 'circle' },
  'deworming': { name: '驱虫', color: '#E6A23C', symbol: 'rect' },
  'medical': { name: '医疗', color: '#F56C6C', symbol: 'triangle' },
  'checkup': { name: '体检', color: '#409EFF', symbol: 'diamond' },
  'illness': { name: '疾病就诊', color: '#F56C6C', symbol: 'triangle' },
  'medication': { name: '用药记录', color: '#909399', symbol: 'roundRect' },
  'allergy': { name: '过敏记录', color: '#FF5722', symbol: 'pin' },
  'surgery': { name: '手术记录', color: '#9C27B0', symbol: 'rect' },
  'other': { name: '其他', color: '#607D8B', symbol: 'circle' }
};

const latestRecord = computed(() => {
  if (!healthRecords.value.length) return null;
  return healthRecords.value
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
});

const nextReminder = computed(() => {
  const now = new Date();
  const futureRecords = healthRecords.value
    .filter(record => record.next_due_date && new Date(record.next_due_date) > now)
    .sort((a, b) => new Date(a.next_due_date).getTime() - new Date(b.next_due_date).getTime());
  
  if (futureRecords.length === 0) return null;
  
  const next = futureRecords[0];
  const nextDate = new Date(next.next_due_date);
  const diffDays = Math.ceil((nextDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  return `${diffDays}天后`;
});

// 时间线图表配置
const timelineOption = computed(() => {
  if (!healthRecords.value.length) {
    return {};
  }

  const series = Object.entries(recordTypeMap).map(([type, config]) => {
    const typeRecords = healthRecords.value.filter(record => record.type === type);
    
    return {
      name: config.name,
      type: 'scatter',
      symbol: config.symbol,
      symbolSize: 12,
      itemStyle: {
        color: config.color
      },
      data: typeRecords.map(record => [
        new Date(record.date).getTime(),
        1,
        record
      ])
    };
  }).filter(series => series.data.length > 0);

  return {
    title: {
      text: '健康记录时间线',
      left: 'center',
      textStyle: {
        fontSize: 14,
        fontWeight: 'normal',
        color: '#606266'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const record = params.data[2];
        const date = new Date(record.date).toLocaleDateString('zh-CN');
        return `${params.seriesName}<br/>${date}<br/>${record.description || '无描述'}`;
      }
    },
    legend: {
      bottom: '5%',
      left: 'center',
      textStyle: {
        fontSize: 12,
        color: '#606266'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'time',
      axisLabel: {
        fontSize: 12,
        color: '#909399',
        formatter: (value: number) => {
          return new Date(value).toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric'
          });
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E4E7ED',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      show: false,
      min: 0.5,
      max: 1.5
    },
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: 0,
        filterMode: 'none'
      }
    ],
    series
  };
});

async function fetchHealthRecords() {
  if (!currentPet.value?.id) return;
  
  loading.value = true;
  try {
    // 从统一的健康记录表获取数据
    const { data, error } = await supabase
      .from('health_records')
      .select('*')
      .eq('pet_id', currentPet.value.id)
      .order('date', { ascending: false })
      .limit(20);
    
    if (error) {
      console.error('获取健康记录失败:', error);
      healthRecords.value = [];
      return;
    }
    
    if (data) {
      healthRecords.value = data.map(record => ({
        ...record,
        type: record.record_type || 'checkup', // 使用record_type字段
        description: record.description || ''
      }));
    } else {
      healthRecords.value = [];
    }
  } catch (error) {
    console.error('获取健康记录失败:', error);
    healthRecords.value = [];
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  fetchHealthRecords();
});

// 监听当前宠物变化
watch(currentPet, (newPet) => {
  if (newPet) {
    fetchHealthRecords();
  } else {
    healthRecords.value = [];
  }
}, { immediate: true });
</script>

<style scoped>
.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.health-timeline-content {
  padding: 0;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.health-chart {
  height: 280px;
  width: 100%;
}

.health-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

@media (max-width: 768px) {
  .health-stats {
    grid-template-columns: 1fr;
  }
  
  .health-chart {
    height: 240px;
  }
}
</style>