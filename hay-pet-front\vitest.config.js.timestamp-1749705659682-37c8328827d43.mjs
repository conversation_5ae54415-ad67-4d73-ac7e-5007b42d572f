// vitest.config.js
import { defineConfig } from "file:///F:/%E6%8F%92%E4%BB%B6%E8%84%9A%E6%9C%AC%E5%BC%80%E5%8F%91/hay!pet/hay-pet-front/node_modules/vitest/dist/config.js";
import vue from "file:///F:/%E6%8F%92%E4%BB%B6%E8%84%9A%E6%9C%AC%E5%BC%80%E5%8F%91/hay!pet/hay-pet-front/node_modules/@vitejs/plugin-vue/dist/index.mjs";
var vitest_config_default = defineConfig({
  plugins: [vue()],
  test: {
    environment: "jsdom",
    globals: true,
    setupFiles: ["./src/test-setup.js"]
  }
});
export {
  vitest_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZXN0LmNvbmZpZy5qcyJdLAogICJzb3VyY2VzQ29udGVudCI6IFsiY29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2Rpcm5hbWUgPSBcIkY6XFxcXFx1NjNEMlx1NEVGNlx1ODExQVx1NjcyQ1x1NUYwMFx1NTNEMVxcXFxoYXkhcGV0XFxcXGhheS1wZXQtZnJvbnRcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkY6XFxcXFx1NjNEMlx1NEVGNlx1ODExQVx1NjcyQ1x1NUYwMFx1NTNEMVxcXFxoYXkhcGV0XFxcXGhheS1wZXQtZnJvbnRcXFxcdml0ZXN0LmNvbmZpZy5qc1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRjovJUU2JThGJTkyJUU0JUJCJUI2JUU4JTg0JTlBJUU2JTlDJUFDJUU1JUJDJTgwJUU1JThGJTkxL2hheSFwZXQvaGF5LXBldC1mcm9udC92aXRlc3QuY29uZmlnLmpzXCI7aW1wb3J0IHsgZGVmaW5lQ29uZmlnIH0gZnJvbSAndml0ZXN0L2NvbmZpZydcbmltcG9ydCB2dWUgZnJvbSAnQHZpdGVqcy9wbHVnaW4tdnVlJ1xuXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xuICBwbHVnaW5zOiBbdnVlKCldLFxuICB0ZXN0OiB7XG4gICAgZW52aXJvbm1lbnQ6ICdqc2RvbScsXG4gICAgZ2xvYmFsczogdHJ1ZSxcbiAgICBzZXR1cEZpbGVzOiBbJy4vc3JjL3Rlc3Qtc2V0dXAuanMnXVxuICB9XG59KSJdLAogICJtYXBwaW5ncyI6ICI7QUFBMlUsU0FBUyxvQkFBb0I7QUFDeFcsT0FBTyxTQUFTO0FBRWhCLElBQU8sd0JBQVEsYUFBYTtBQUFBLEVBQzFCLFNBQVMsQ0FBQyxJQUFJLENBQUM7QUFBQSxFQUNmLE1BQU07QUFBQSxJQUNKLGFBQWE7QUFBQSxJQUNiLFNBQVM7QUFBQSxJQUNULFlBQVksQ0FBQyxxQkFBcUI7QUFBQSxFQUNwQztBQUNGLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
