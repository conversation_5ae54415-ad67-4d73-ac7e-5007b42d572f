import { createRouter, createWebHistory } from 'vue-router';
import HomeView from '../views/HomeView.vue';
import PetProfileView from '../views/PetProfileView.vue';
import HealthRecordsView from '../views/HealthRecordsView.vue';
import PhotoAlbumView from '../views/PhotoAlbumView.vue';
import RemindersView from '../views/RemindersView.vue';
import WeightTrackingView from '../views/WeightTrackingView.vue'; // 新增
// import ExpenseTrackingView from '../views/ExpenseTrackingView.vue'; // 暂时移除
import SettingsView from '../views/SettingsView.vue';

// Style Demo 相关页面
import StyleDemoIndex from '../views/style_demo/StyleDemoIndex.vue';
import TestDemo from '../views/style_demo/TestDemo.vue';
import StandardTagDemo from '../views/style_demo/StandardTagDemo.vue';
import ColorPickerDemo from '../views/style_demo/ColorPickerDemo.vue';
import DesignTokensDemo from '../views/style_demo/DesignTokensDemo.vue';
import ButtonPresetsDemo from '../views/style_demo/ButtonPresetsDemo.vue';
import ViewToggleDemo from '../views/StyleDemo/ViewToggleDemo.vue';
import SimpleViewToggleDemo from '../views/style_demo/SimpleViewToggleDemo.vue';
import EnhancedViewToggleDemo from '../views/style_demo/EnhancedViewToggleDemo.vue';
import DragSortDemo from '../views/style_demo/DragSortDemo.vue';
import PageHeaderDemo from '../views/style_demo/PageHeaderDemo.vue';
import CalendarViewDemo from '../views/style_demo/CalendarViewDemo.vue';
import EventRecordsDemo from '../views/style_demo/EventRecordsDemo.vue';
import CalendarOptimizedDemo from '../views/style_demo/CalendarOptimizedDemo.vue';
import StyleDemoView from '../views/StyleDemoView.vue';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomeView,
  },
  {
    path: '/pet-profile/:petId',
    name: 'PetProfile',
    component: PetProfileView,
  },
  {
    path: '/records',
    name: 'HealthRecords',
    component: HealthRecordsView,
  },
  {
    path: '/photo-album',
    name: 'PhotoAlbum',
    component: PhotoAlbumView,
  },
  {
    path: '/reminders',
    name: 'Reminders',
    component: RemindersView,
  },
  {
    path: '/weight-tracking', // 新增路由
    name: 'WeightTracking',
    component: WeightTrackingView,
  },
  {
    path: '/expense-tracking',
    name: 'ExpenseTracking',
    component: () => import('../views/ExpenseTrackingView.vue')
  },
  {
    path: '/settings',
    name: 'Settings',
    component: SettingsView,
  },
  // Style Demo 路由 - 无需登录验证
  {
    path: '/style_demo',
    name: 'StyleDemo',
    component: StyleDemoIndex,
    meta: { requiresAuth: false }
  },
  {
    path: '/test_demo',
    name: 'TestDemo',
    component: TestDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/standard-tag',
    name: 'StandardTagDemo',
    component: StandardTagDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/color-picker',
    name: 'ColorPickerDemo',
    component: ColorPickerDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/design-tokens',
    name: 'DesignTokensDemo',
    component: DesignTokensDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/button-presets',
    name: 'ButtonPresetsDemo',
    component: ButtonPresetsDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/view-toggle',
    name: 'ViewToggleDemo',
    component: ViewToggleDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/simple-view-toggle',
    name: 'SimpleViewToggleDemo',
    component: SimpleViewToggleDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/enhanced-view-toggle',
    name: 'EnhancedViewToggleDemo',
    component: EnhancedViewToggleDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/drag-sort',
    name: 'DragSortDemo',
    component: DragSortDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/page-header',
    name: 'PageHeaderDemo',
    component: PageHeaderDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/calendar-view',
    name: 'CalendarViewDemo',
    component: CalendarViewDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/event-records',
    name: 'EventRecordsDemo',
    component: EventRecordsDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/calendar-optimized',
    name: 'CalendarOptimizedDemo',
    component: CalendarOptimizedDemo,
    meta: { requiresAuth: false }
  },
  {
    path: '/style_demo/record-views-preset',
    name: 'RecordViewsPresetDemo',
    component: StyleDemoView,
    meta: { requiresAuth: false }
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  // 确保URL参数正确编码
  parseQuery(query) {
    const result = {}
    for (const [key, value] of new URLSearchParams(query)) {
      try {
        // 尝试解码URL编码的中文字符
        result[key] = decodeURIComponent(value)
      } catch (e) {
        // 如果解码失败，使用原始值
        result[key] = value
      }
    }
    return result
  },
  stringifyQuery(query) {
    const params = new URLSearchParams()
    for (const [key, value] of Object.entries(query)) {
      if (value != null) {
        // 确保中文字符正确编码
        params.append(key, encodeURIComponent(String(value)))
      }
    }
    return params.toString()
  }
});

// 路由守卫：处理路径参数中的中文字符
router.beforeEach((to, from, next) => {
  // 对于宠物档案路由，现在支持名称参数，不需要强制重定向
  if (to.params.petId && to.name === 'PetProfile') {
    try {
      const decoded = decodeURIComponent(to.params.petId);

    } catch (error) {

    }
  }
  next();
});

export default router;