// 测试分类显示的脚本
// 在浏览器控制台中运行此脚本来测试分类颜色显示

console.log('开始测试分类颜色显示...');

// 测试获取分类数据
async function testCategoriesDisplay() {
  try {
    // 检查是否在花费追踪页面
    if (!window.location.pathname.includes('expense')) {
      console.log('请先导航到花费追踪页面: http://localhost:5208/expense-tracking');
      return;
    }

    // 等待 Vue 应用加载
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 查找分类标签元素
    const categoryTags = document.querySelectorAll('.draggable-categories .tag-base');
    console.log(`找到 ${categoryTags.length} 个分类标签`);

    if (categoryTags.length === 0) {
      console.log('❌ 没有找到分类标签，可能数据还在加载中');
      return;
    }

    // 检查每个标签的样式
    categoryTags.forEach((tag, index) => {
      const text = tag.querySelector('.tag-text')?.textContent || '未知';
      const computedStyle = window.getComputedStyle(tag);
      const background = computedStyle.background || computedStyle.backgroundColor;
      const color = computedStyle.color;
      const borderColor = computedStyle.borderColor;

      console.log(`标签 ${index + 1}: "${text}"`);
      console.log(`  - 背景: ${background}`);
      console.log(`  - 文字颜色: ${color}`);
      console.log(`  - 边框颜色: ${borderColor}`);
      console.log('---');
    });

    // 检查是否有内联样式
    const tagsWithInlineStyle = Array.from(categoryTags).filter(tag => tag.style.length > 0);
    console.log(`有内联样式的标签数量: ${tagsWithInlineStyle.length}`);

    tagsWithInlineStyle.forEach((tag, index) => {
      const text = tag.querySelector('.tag-text')?.textContent || '未知';
      console.log(`内联样式标签 "${text}":`, tag.style.cssText);
    });

    return {
      totalTags: categoryTags.length,
      tagsWithStyle: tagsWithInlineStyle.length,
      success: tagsWithInlineStyle.length > 0
    };

  } catch (error) {
    console.error('测试分类显示失败:', error);
    return { success: false, error: error.message };
  }
}

// 测试 Vue 组件数据
async function testVueComponentData() {
  try {
    // 查找 Vue 应用实例
    const app = document.querySelector('#app').__vue_app__;
    if (!app) {
      console.log('❌ 找不到 Vue 应用实例');
      return;
    }

    console.log('✅ 找到 Vue 应用实例');

    // 尝试获取组件数据（这需要 Vue DevTools 或特殊方法）
    console.log('提示: 请在 Vue DevTools 中检查 ExpenseTrackingView 组件的 expenseCategories 数据');
    
    return { success: true };
  } catch (error) {
    console.error('测试 Vue 组件数据失败:', error);
    return { success: false, error: error.message };
  }
}

// 测试 Supabase 连接
async function testSupabaseConnection() {
  try {
    if (typeof supabase === 'undefined') {
      console.log('❌ Supabase 客户端未找到');
      return { success: false };
    }

    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;

    if (!user) {
      console.log('❌ 用户未登录');
      return { success: false, message: '用户未登录' };
    }

    console.log('✅ 用户已登录:', user.email);

    // 测试获取分类数据
    const { data: categories, error: categoriesError } = await supabase
      .from('expense_categories')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('order_index', { ascending: true });

    if (categoriesError) throw categoriesError;

    console.log('✅ 成功获取分类数据:', categories);
    
    if (categories && categories.length > 0) {
      categories.forEach((cat, index) => {
        console.log(`分类 ${index + 1}: ${cat.name} (颜色: ${cat.color})`);
      });
    } else {
      console.log('⚠️ 没有找到分类数据，可能需要创建默认分类');
    }

    return { success: true, categories };
  } catch (error) {
    console.error('测试 Supabase 连接失败:', error);
    return { success: false, error: error.message };
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('=== 开始分类显示测试 ===');
  
  const supabaseResult = await testSupabaseConnection();
  if (!supabaseResult.success) {
    console.log('❌ Supabase 连接测试失败');
    return;
  }

  const vueResult = await testVueComponentData();
  const displayResult = await testCategoriesDisplay();

  console.log('=== 测试结果汇总 ===');
  console.log('Supabase 连接:', supabaseResult.success ? '✅ 成功' : '❌ 失败');
  console.log('Vue 组件:', vueResult.success ? '✅ 成功' : '❌ 失败');
  console.log('分类显示:', displayResult?.success ? '✅ 成功' : '❌ 失败');

  if (displayResult?.success) {
    console.log(`找到 ${displayResult.totalTags} 个分类标签，其中 ${displayResult.tagsWithStyle} 个有自定义样式`);
  }

  console.log('=== 测试完成 ===');
}

// 导出测试函数
window.testCategoriesDisplay = {
  runAllTests,
  testCategoriesDisplay,
  testVueComponentData,
  testSupabaseConnection
};

console.log('测试脚本已加载，请运行: testCategoriesDisplay.runAllTests()');
console.log('或者单独运行: testCategoriesDisplay.testCategoriesDisplay()');
