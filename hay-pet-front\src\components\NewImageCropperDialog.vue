<template>
  <el-dialog
    v-model="visible"
    title="图片裁切"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="image-cropper-dialog"
  >
    <div class="image-cropper-container">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧：裁切区域 -->
        <div class="cropper-section">
          <div class="cropper-header">
            <h3 class="cropper-title">图片编辑</h3>
            <div class="cropper-info">
              <span class="aspect-ratio-badge">1:1</span>
              <span class="format-badge">PNG</span>
            </div>
          </div>
          <div class="cropper-wrapper">
            <VuePictureCropper
              v-if="imageSrc"
              ref="cropperRef"
              :boxStyle="{
                width: '100%',
                height: '100%',
                backgroundColor: '#fafbfc',
                margin: 'auto',
                borderRadius: '12px'
              }"
              :img="imageSrc"
              :options="cropperOptions"
              @ready="onCropperReady"
              :auto-crop-area="0.8"
              :preview="false"
            />
            <div v-else class="no-image-placeholder">
              <div class="placeholder-icon">
                <el-icon size="64" color="#d1d5db">
                  <Picture />
                </el-icon>
              </div>
              <p class="placeholder-text">等待图片加载...</p>
              <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：预览和控制区域 -->
        <div class="sidebar-section">
          <!-- 预览区域 -->
          <div class="preview-section">
            <div class="preview-header">
              <h3 class="preview-title">预览效果</h3>
            </div>
            <div class="preview-container">
              <div class="preview-item">
                <div class="preview-label">头像预览</div>
                <div class="preview-avatar">
                  <img v-if="previewDataURL" :src="previewDataURL" alt="预览" />
                  <div v-else class="preview-placeholder">
                    <el-icon size="32" color="#d1d5db">
                      <Picture />
                    </el-icon>
                    <span class="preview-placeholder-text">等待裁切</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作工具区域 -->
          <div class="tools-section">
            <div class="tools-header">
              <h3 class="tools-title">操作工具</h3>
            </div>
            <div class="tools-content">
              <div class="tool-buttons">
                <el-button
                  size="small"
                  class="tool-btn"
                  @click="rotate(-90)"
                >
                  <el-icon><RefreshLeft /></el-icon>
                  左转
                </el-button>
                <el-button
                  size="small"
                  class="tool-btn"
                  @click="rotate(90)"
                >
                  <el-icon><RefreshRight /></el-icon>
                  右转
                </el-button>
                <el-button
                  size="small"
                  class="tool-btn"
                  @click="reset"
                >
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传进度区域 -->
      <div v-if="uploadStatus.isUploading" class="upload-progress-section">
        <div class="progress-content">
          <el-icon class="progress-icon" :class="{ 'rotating': uploadStatus.isUploading }">
            <Loading />
          </el-icon>
          <div class="progress-text">
            <div class="progress-title">{{ uploadStatus.message }}</div>
            <div class="progress-subtitle">{{ uploadStatus.detail }}</div>
          </div>
        </div>
        <el-progress
          :percentage="uploadStatus.progress"
          :status="uploadStatus.status"
          :stroke-width="6"
          class="upload-progress-bar"
        />
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-buttons-section">
        <div class="action-buttons">
          <el-button
            @click="handleClose"
            :disabled="uploadStatus.isUploading && uploadStatus.progress < 100"
            class="action-btn cancel-btn"
          >
            {{ getCloseButtonText() }}
          </el-button>
          <el-button
            type="primary"
            @click="handleConfirm"
            :loading="processing"
            :disabled="uploadStatus.isUploading"
            v-if="uploadStatus.progress < 100"
            class="action-btn confirm-btn"
          >
            {{ uploadStatus.isUploading ? '上传中...' : '确认裁切' }}
          </el-button>
        </div>
      </div>

    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import VuePictureCropper, { cropper } from 'vue-picture-cropper'
import {
  RefreshLeft, RefreshRight, Refresh, Picture, Loading
} from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  imageSrc: {
    type: String,
    default: ''
  },
  aspectRatio: {
    type: Number,
    default: 1
  }
})

// 组件事件
const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'upload-progress', 'upload-complete', 'upload-error'])

// 响应式数据
const visible = ref(false)
const cropperRef = ref()
const processing = ref(false)
const previewDataURL = ref('')

// 上传状态管理
const uploadStatus = ref({
  isUploading: false,
  progress: 0,
  message: '',
  detail: '',
  status: '' // 'success', 'exception', ''
})

// 裁切设置 - 固定配置
const aspectRatio = ref(1) // 固定 1:1 比例
const outputQuality = ref(0.8) // 固定 80% 质量
const outputFormat = ref('image/png') // 固定 PNG 格式

// 裁切器选项
const cropperOptions = computed(() => ({
  viewMode: 1,
  dragMode: 'crop',
  aspectRatio: aspectRatio.value === 0 ? NaN : aspectRatio.value,
  autoCropArea: 0.8,
  restore: false,
  guides: true,
  center: true,
  highlight: false,
  cropBoxMovable: true,
  cropBoxResizable: true,
  toggleDragModeOnDblclick: false,
  crop: updatePreview, // 使用防抖优化的预览更新
  // 优化性能配置
  responsive: true,
  checkCrossOrigin: false,
  checkOrientation: false
}))

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.imageSrc) {
    nextTick(() => {
      updatePreview()
    })
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 监听图片源变化
watch(() => props.imageSrc, (newSrc) => {
  if (newSrc && visible.value) {
    nextTick(() => {
      updatePreview()
    })
  }
})

// 裁切器准备就绪
const onCropperReady = () => {
  updatePreview()
}

// 旋转图片
const rotate = (degree) => {
  if (cropper && typeof cropper.rotate === 'function') {
    cropper.rotate(degree)
  } else {
    console.warn('Cropper rotate method not available')
  }
}

// 重置
const reset = () => {
  if (cropper && typeof cropper.reset === 'function') {
    cropper.reset()
  } else {
    console.warn('Cropper reset method not available')
  }
}

// 更新预览（防抖优化）
const updatePreview = debounce(() => {
  if (!props.imageSrc) {
    console.warn('No image source available for preview')
    return
  }

  if (cropper && typeof cropper.getDataURL === 'function') {
    try {
      previewDataURL.value = cropper.getDataURL({
        width: 100,
        height: 100,
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high'
      })
    } catch (error) {
      console.warn('预览更新失败:', error)
    }
  }
}, 100) // 100ms 防抖延迟



// 重置上传状态
const resetUploadStatus = () => {
  uploadStatus.value = {
    isUploading: false,
    progress: 0,
    message: '',
    detail: '',
    status: ''
  }
}

// 更新上传进度
const updateUploadProgress = (progress, message, detail = '', status = '') => {
  uploadStatus.value = {
    isUploading: true,
    progress,
    message,
    detail,
    status
  }
  emit('upload-progress', { progress, message, detail, status })
}

// 确认裁切
const handleConfirm = async () => {
  if (!props.imageSrc) {
    ElMessage.error('没有可裁切的图片')
    return
  }

  if (!cropper) {
    ElMessage.error('裁切器未初始化')
    return
  }

  if (typeof cropper.getFile !== 'function') {
    ElMessage.error('裁切器方法不可用')
    return
  }

  processing.value = true
  resetUploadStatus()

  try {
    // 步骤1: 裁切图片
    updateUploadProgress(20, '正在裁切图片...', '处理图片数据')

    const file = await cropper.getFile({
      width: 400,
      height: 400,
      imageSmoothingEnabled: true,
      imageSmoothingQuality: 'high',
      type: outputFormat.value,
      quality: outputQuality.value
    })

    if (!file) {
      throw new Error('获取裁切结果失败')
    }

    // 步骤2: 准备上传
    updateUploadProgress(40, '准备上传...', '生成文件信息')

    // 重命名文件
    const renamedFile = new File([file], `cropped_image.${getFileExtension()}`, {
      type: outputFormat.value
    })

    // 步骤3: 开始上传
    updateUploadProgress(60, '正在上传头像...', '连接服务器')

    // 发送确认事件，但不立即关闭对话框
    emit('confirm', renamedFile, {
      onProgress: (progress, message, detail) => {
        updateUploadProgress(60 + progress * 0.4, message, detail)
      },
      onSuccess: () => {
        updateUploadProgress(100, '上传成功！', '头像已更新，可以关闭面板', 'success')
        processing.value = false
        emit('upload-complete')
        // 不自动关闭，让用户手动关闭
      },
      onError: (error) => {
        uploadStatus.value = {
          isUploading: false,
          progress: 0,
          message: '上传失败',
          detail: error.message,
          status: 'exception'
        }
        emit('upload-error', error)
        processing.value = false
      }
    })

  } catch (error) {
    console.error('图片裁切失败:', error)
    uploadStatus.value = {
      isUploading: false,
      progress: 0,
      message: '裁切失败',
      detail: error.message,
      status: 'exception'
    }
    ElMessage.error('图片裁切失败: ' + error.message)
    processing.value = false
  }
}

// 获取文件扩展名 - 固定为 PNG
const getFileExtension = () => {
  return 'png'
}

// 获取关闭按钮文本
const getCloseButtonText = () => {
  if (uploadStatus.value.isUploading && uploadStatus.value.progress < 100) {
    return '上传中...'
  } else if (uploadStatus.value.status === 'success') {
    return '关闭'
  } else if (uploadStatus.value.status === 'exception') {
    return '关闭'
  } else {
    return '取消'
  }
}

// 关闭对话框
const handleClose = () => {
  // 只有在正在上传时才询问用户是否确认关闭
  // 如果上传已完成（progress = 100 且 status = 'success'），直接关闭
  if (uploadStatus.value.isUploading && uploadStatus.value.progress < 100) {
    ElMessageBox.confirm(
      '正在上传中，确定要关闭吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      forceClose()
    }).catch(() => {
      // 用户取消关闭
    })
  } else {
    forceClose()
  }
}

// 强制关闭对话框
const forceClose = () => {
  visible.value = false
  processing.value = false
  resetUploadStatus()

  // 清理预览数据URL（如果是blob URL）
  if (previewDataURL.value && previewDataURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(previewDataURL.value)
  }
  previewDataURL.value = ''

  // 如果上传成功，不发送 cancel 事件
  if (uploadStatus.value.status !== 'success') {
    emit('cancel')
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  // 取消防抖函数
  updatePreview.cancel()
  
  // 清理 blob URL
  if (previewDataURL.value && previewDataURL.value.startsWith('blob:')) {
    URL.revokeObjectURL(previewDataURL.value)
  }
})
</script>

<style scoped lang="scss">
// 对话框样式
:deep(.image-cropper-dialog) {
  .el-dialog {
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: dialogFadeIn 0.3s ease-out;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 28px 28px 24px;
    border-bottom: none;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
      pointer-events: none;
    }

    .el-dialog__title {
      font-size: 20px;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 1;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 22px;
        transition: all 0.3s ease;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
          background: rgba(255, 255, 255, 0.1);
          transform: scale(1.1);
        }
      }
    }
  }

  .el-dialog__body {
    padding: 28px;
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
      pointer-events: none;
    }
  }

  .el-dialog__footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(233, 236, 239, 0.5);
    padding: 24px 28px;
  }
}

.image-cropper-container {
  display: flex;
  flex-direction: column;
  gap: 0;
  min-height: 520px;
  animation: slideInUp 0.4s ease-out;
}

.main-content {
  display: flex;
  gap: 32px;
  flex: 1;
  align-items: flex-start; // 改为顶部对齐，让内容自然流动
  min-height: 480px; // 设置最小高度确保左右区域有足够空间
}

.cropper-section {
  flex: 2.2;
  display: flex;
  flex-direction: column;
  gap: 16px;
  // 移除固定高度，让内容自然决定高度

  .cropper-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 4px;

    .cropper-title {
      font-size: 18px;
      font-weight: 600;
      color: #495057;
      margin: 0;
    }

    .cropper-info {
      display: flex;
      gap: 8px;

      .aspect-ratio-badge,
      .format-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .format-badge {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      }
    }
  }

  .cropper-wrapper {
    width: 100%;
    height: 420px; // 设置固定高度，确保裁切器有稳定的尺寸
    border: 2px solid #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;

    &:hover {
      border-color: #667eea;
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
    }

    .no-image-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      color: #6c757d;
      position: relative;

      .placeholder-icon {
        margin-bottom: 16px;
        opacity: 0.7;
      }

      .placeholder-text {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 500;
      }

      .loading-dots {
        display: flex;
        gap: 4px;

        span {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #667eea;
          animation: loading-bounce 1.4s ease-in-out infinite both;

          &:nth-child(1) { animation-delay: -0.32s; }
          &:nth-child(2) { animation-delay: -0.16s; }
          &:nth-child(3) { animation-delay: 0s; }
        }
      }
    }
  }

  .cropper-controls {
    .control-group {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .control-label {
        font-size: 14px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
      }

      .control-buttons {
        display: flex;
        gap: 8px;

        .control-btn {
          border-radius: 8px;
          border: 1px solid #e9ecef;
          background: white;
          color: #495057;
          transition: all 0.3s ease;

          &:hover {
            background: #667eea;
            border-color: #667eea;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

.tools-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  animation: slideInUp 0.5s ease-out 0.2s both;
  flex-shrink: 0; // 防止工具区域被压缩
  height: 140px; // 简化后减少高度

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 100%, rgba(102, 126, 234, 0.03) 0%, transparent 50%);
    border-radius: 16px;
    pointer-events: none;
  }

  .tools-header {
    margin-bottom: 20px;
    position: relative;
    z-index: 1;

    .tools-title {
      font-size: 16px;
      font-weight: 600;
      color: #495057;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        width: 3px;
        height: 16px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border-radius: 2px;
      }
    }
  }

  .tools-content {
    position: relative;
    z-index: 1;

    .tool-buttons {
      display: flex;
      gap: 8px;
      flex-wrap: nowrap;
      justify-content: center;

      .tool-btn {
        border-radius: 10px;
        border: 1px solid #e9ecef;
        background: white;
        color: #495057;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        min-width: 70px;
        padding: 8px 12px;
        flex: 1;
        max-width: 90px;

        &:hover {
          border-color: #28a745;
          color: #28a745;
          background: rgba(40, 167, 69, 0.05);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(40, 167, 69, 0.15);
        }

        &:active {
          transform: translateY(0);
        }

        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }
}

.toolbar-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 16px 20px;
  border: 1px solid rgba(233, 236, 239, 0.5);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .toolbar-content {
    .tool-group {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .tool-label {
        font-size: 14px;
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '';
          width: 4px;
          height: 16px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 2px;
        }
      }

      .tool-buttons {
        display: flex;
        gap: 12px;

        .tool-btn {
          border-radius: 8px;
          border: 1px solid #e9ecef;
          background: white;
          color: #495057;
          font-weight: 500;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

          &:hover {
            border-color: #667eea;
            color: #667eea;
            background: rgba(102, 126, 234, 0.05);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.15);
          }

          &:active {
            transform: translateY(0);
          }

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
  }
}

.sidebar-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  // 计算总高度：cropper-header(约50px) + gap(16px) + cropper-wrapper(420px) = 486px
  height: 486px; // 设置固定高度与左侧总高度匹配
}

.preview-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  animation: slideInUp 0.5s ease-out 0.1s both;
  flex: 1; // 让预览区域占据可用空间
  min-height: 260px; // 调整最小高度，为工具区域留出空间

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 0%, rgba(102, 126, 234, 0.03) 0%, transparent 50%);
    border-radius: 16px;
    pointer-events: none;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;

    .preview-title {
      font-size: 16px;
      font-weight: 600;
      color: #495057;
      margin: 0;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 0;
        height: 2px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: width 0.3s ease;
      }

      &:hover::after {
        width: 100%;
      }
    }

    .preview-badge {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 6px 14px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        animation: shimmer 2s infinite;
      }
    }
  }

  .preview-container {
    position: relative;
    z-index: 1;

    .preview-item {
      text-align: center;

      .preview-label {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 16px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 2px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 1px;
        }
      }

      .preview-avatar {
        width: 140px;
        height: 140px;
        border: 4px solid #e9ecef;
        border-radius: 50%;
        overflow: hidden;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);

        &::before {
          content: '';
          position: absolute;
          top: -4px;
          left: -4px;
          right: -4px;
          bottom: -4px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          z-index: -1;
        }

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 0;
          height: 0;
          border-radius: 50%;
          background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          pointer-events: none;
        }

        &:hover {
          transform: scale(1.08) rotate(2deg);
          box-shadow: 0 16px 40px rgba(102, 126, 234, 0.2);

          &::before {
            opacity: 1;
          }

          &::after {
            width: 120%;
            height: 120%;
          }
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
          transition: all 0.3s ease;

          &:hover {
            filter: brightness(1.1) contrast(1.05);
          }
        }

        .preview-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          color: #adb5bd;
          animation: pulse 2s infinite;

          .el-icon {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
          }

          .preview-placeholder-text {
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
    }
  }
}

/* 上传进度区域 */
.upload-progress-section {
  margin: 20px 0 16px 0;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  .progress-content {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .progress-icon {
      font-size: 28px;
      color: #667eea;
      margin-right: 16px;

      &.rotating {
        animation: rotate 2s linear infinite;
      }
    }

    .progress-text {
      flex: 1;

      .progress-title {
        font-size: 16px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 4px;
      }

      .progress-subtitle {
        font-size: 14px;
        color: #6c757d;
      }
    }
  }

  .upload-progress-bar {
    :deep(.el-progress-bar__outer) {
      background-color: #e9ecef;
      border-radius: 10px;
      height: 8px;
    }

    :deep(.el-progress-bar__inner) {
      border-radius: 10px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
      transition: width 0.3s ease;
    }
  }
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .el-button {
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:not(.el-button--primary) {
      background: #f8f9fa;
      border-color: #e9ecef;
      color: #495057;

      &:hover {
        background: #e9ecef;
        border-color: #dee2e6;
        transform: translateY(-1px);
      }
    }

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.image-cropper-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 0 auto;
    }
  }

  .image-cropper-container {
    gap: 16px;
    min-height: auto;
  }

  .main-content {
    flex-direction: column;
    gap: 20px;
  }

  .cropper-section {
    .cropper-header {
      .cropper-title {
        font-size: 16px;
      }

      .cropper-info {
        .aspect-ratio-badge,
        .format-badge {
          padding: 3px 8px;
          font-size: 11px;
        }
      }
    }

    .cropper-wrapper {
      height: 280px;
    }
  }

  .sidebar-section {
    gap: 16px;
  }

  .preview-section {
    padding: 20px;

    .preview-container {
      .preview-item {
        .preview-avatar {
          width: 100px !important;
          height: 100px !important;
        }
      }
    }
  }

  .tools-section {
    padding: 20px;

    .tools-content {
      .tool-group {
        .tool-buttons {
          .tool-btn {
            font-size: 12px;
            padding: 8px 12px;

            .el-icon {
              margin-right: 2px;
            }
          }
        }
      }
    }
  }

  /* 操作按钮区域响应式 */
  .action-buttons-section {
    margin-top: 20px;
    padding: 20px 0;

    .action-buttons {
      gap: 12px;

      .action-btn {
        padding: 12px 24px;
        min-width: 100px;
        font-size: 13px;
      }
    }
  }
}

/* 操作按钮区域 */
.action-buttons-section {
  margin-top: 24px;
  padding: 20px 0;
  border-top: 1px solid rgba(233, 236, 239, 0.5);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  animation: slideInUp 0.5s ease-out 0.3s both;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;

  .action-btn {
    border-radius: 12px;
    padding: 14px 32px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    min-width: 120px;

    &.cancel-btn {
      background: #f8f9fa;
      border-color: #e9ecef;
      color: #495057;

      &:hover {
        background: #e9ecef;
        border-color: #dee2e6;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    &.confirm-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      color: white;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
      box-shadow: none !important;
    }
  }
}
</style>