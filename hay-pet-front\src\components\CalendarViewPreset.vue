<template>
  <div class="calendar-section">
    <el-card class="calendar-card" shadow="hover">
      <template #header>
        <div class="calendar-header">
          <div class="calendar-title-section">
            <span class="calendar-title">{{ title }}</span>
            <div class="current-date-info">
              {{ formatCurrentDate(selectedDate) }}
            </div>
          </div>
          <div class="calendar-controls">
            <!-- 导航按钮组 -->
            <div class="navigation-buttons">
              <el-button
                @click="previousPeriod"
                size="small"
                circle
                class="nav-btn"
                :icon="ArrowLeft"
                title="上一月/上一周"
              ></el-button>
              <el-button
                @click="goToToday"
                size="small"
                class="today-btn"
                title="今天"
              >
                <el-icon><Calendar /></el-icon>
                <span class="today-text">今天</span>
              </el-button>
              <el-button
                @click="nextPeriod"
                size="small"
                circle
                class="nav-btn"
                :icon="ArrowRight"
                title="下一月/下一周"
              ></el-button>
            </div>

            <!-- 日期选择器组 -->
            <div class="date-selectors">
              <!-- 年份选择器 -->
              <el-select
                v-model="currentYear"
                @change="handleYearChange"
                size="small"
                class="year-selector"
                placeholder="年份"
              >
                <el-option
                  v-for="year in yearOptions"
                  :key="year"
                  :label="`${year}年`"
                  :value="year"
                />
              </el-select>

              <!-- 月份选择器 -->
              <el-select
                v-model="currentMonth"
                @change="handleMonthChange"
                size="small"
                class="month-selector"
                placeholder="月份"
              >
                <el-option
                  v-for="(month, index) in monthOptions"
                  :key="index"
                  :label="month"
                  :value="index"
                />
              </el-select>
            </div>

            <!-- 视图切换按钮 -->
            <EnhancedViewToggle
              v-model="calendarView"
              :options="calendarViewOptions"
              @change="handleCalendarViewChange"
            />
          </div>
        </div>
      </template>

      <div class="calendar-content">
        <!-- 月视图 -->
        <el-calendar
          v-if="calendarView === 'month'"
          v-model="selectedDate"
          class="health-calendar"
          ref="calendarRef"
        >
          <template #date-cell="{ data }">
            <div
              class="calendar-cell"
              :class="{
                'has-records': getRecordsForDate(data.day).length > 0,
                'selected-date': isSelectedDate(data.day)
              }"
              @click="handleDateClick(data.day)"
              @mouseenter="handleDateHover(data.day, $event)"
              @mouseleave="handleDateLeave"
            >
              <div class="date-number">{{ data.day.split('-').pop() }}</div>
              <div class="record-indicators" v-if="getRecordsForDate(data.day).length > 0">
                <div
                  v-for="record in getRecordsForDate(data.day).slice(0, 3)"
                  :key="record.id"
                  :class="['record-dot', getRecordTypeClass(record)]"
                ></div>
                <div
                  v-if="getRecordsForDate(data.day).length > 3"
                  class="more-indicator"
                >+{{ getRecordsForDate(data.day).length - 3 }}</div>
              </div>
            </div>
          </template>
        </el-calendar>

        <!-- 周视图 -->
        <div v-else-if="calendarView === 'week'" class="week-calendar">
          <div class="week-header">
            <div class="week-navigation">
              <el-button
                @click="previousWeek"
                size="small"
                circle
                :icon="ArrowLeft"
              ></el-button>
              <div class="week-info">
                <span class="week-range">{{ currentWeekRange }}</span>
                <span class="week-number">第{{ currentWeekNumber }}周</span>
              </div>
              <el-button
                @click="nextWeek"
                size="small"
                circle
                :icon="ArrowRight"
              ></el-button>
            </div>
          </div>
          <div class="week-grid">
            <div class="week-days-header">
              <div
                v-for="day in weekDays"
                :key="day"
                class="week-day-header"
              >
                {{ day }}
              </div>
            </div>
            <div class="week-dates">
              <div
                v-for="date in currentWeekDates"
                :key="date.dateStr"
                class="week-date-cell"
                :class="{
                  'has-records': getRecordsForDate(date.dateStr).length > 0,
                  'selected-date': isSelectedDate(date.dateStr),
                  'today': date.isToday
                }"
                @click="handleDateClick(date.dateStr)"
                @mouseenter="handleDateHover(date.dateStr, $event)"
                @mouseleave="handleDateLeave"
              >
                <div class="week-date-number">{{ date.day }}</div>
                <div class="week-record-indicators" v-if="getRecordsForDate(date.dateStr).length > 0">
                  <div
                    v-for="record in getRecordsForDate(date.dateStr).slice(0, 2)"
                    :key="record.id"
                    :class="['record-dot', getRecordTypeClass(record)]"
                  ></div>
                  <div
                    v-if="getRecordsForDate(date.dateStr).length > 2"
                    class="more-indicator"
                  >+{{ getRecordsForDate(date.dateStr).length - 2 }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 年视图 -->
        <div v-else-if="calendarView === 'year'" class="year-calendar">
          <div class="year-header">
            <div class="year-navigation">
              <el-button
                @click="previousYear"
                size="small"
                circle
                :icon="ArrowLeft"
              ></el-button>
              <span class="year-title">{{ currentYear }}年</span>
              <el-button
                @click="nextYear"
                size="small"
                circle
                :icon="ArrowRight"
              ></el-button>
            </div>
          </div>
          <div class="year-grid">
            <div
              v-for="(month, index) in yearMonths"
              :key="index"
              class="year-month-cell"
              :class="{
                'has-records': getMonthRecordsCount(index) > 0,
                'current-month': isCurrentMonth(index)
              }"
              @click="goToMonth(index)"
            >
              <div class="month-name">{{ month }}</div>
              <div class="month-stats">
                <div class="records-count">{{ getMonthRecordsCount(index) }}条{{ recordTypeName }}</div>
                <div class="records-density" :class="getMonthDensityClass(index)">
                  <div
                    v-for="type in getMonthRecordTypes(index)"
                    :key="type"
                    :class="['density-dot', getRecordTypeClass({ [recordTypeField]: type })]"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 悬浮提示框 -->
    <div
      v-if="showTooltip && tooltipRecords.length > 0"
      class="records-tooltip"
      :style="tooltipStyle"
    >
      <div class="tooltip-header">
        <el-icon><Calendar /></el-icon>
        {{ formatTooltipDate(tooltipDate) }}
      </div>
      <div class="tooltip-content">
        <div
          v-for="(record, index) in tooltipRecords.slice(0, 5)"
          :key="record.id"
          class="tooltip-record"
        >
          <el-tag
            :type="getRecordTypeTag(record)"
            size="small"
            class="record-type-tag"
          >
            {{ getRecordTypeLabel(record) }}
          </el-tag>
          <span class="record-desc">
            {{ truncateDescription(record) }}
          </span>
        </div>
        <div
          v-if="tooltipRecords.length > 5"
          class="more-records-hint"
        >
          还有 {{ tooltipRecords.length - 5 }} 条{{ recordTypeName }}...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { Calendar, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import EnhancedViewToggle from './common/EnhancedViewToggle.vue';

// Props
const props = defineProps({
  title: {
    type: String,
    default: '日历视图'
  },
  records: {
    type: Array,
    default: () => []
  },
  recordTypeName: {
    type: String,
    default: '记录'
  },
  recordTypeField: {
    type: String,
    default: 'record_type'
  },
  dateField: {
    type: String,
    default: 'date'
  },
  getRecordTypeClass: {
    type: Function,
    default: (record) => 'other'
  },
  getRecordTypeTag: {
    type: Function,
    default: (record) => 'info'
  },
  getRecordTypeLabel: {
    type: Function,
    default: (record) => record.record_type || record.category || '未知'
  },
  truncateDescription: {
    type: Function,
    default: (record) => {
      const desc = record.description || record.title || record.notes;
      if (!desc) return '无描述';
      return desc.length > 20 ? desc.substring(0, 20) + '...' : desc;
    }
  }
});

// Emits
const emit = defineEmits(['date-click', 'calendar-view-change']);

// 响应式数据
const selectedDate = ref(new Date());
const calendarView = ref('month');
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const calendarRef = ref(null);

// 悬浮提示相关
const showTooltip = ref(false);
const tooltipDate = ref('');
const tooltipRecords = ref([]);
const tooltipStyle = ref({});
const tooltipTimer = ref(null);

// 周视图相关
const currentWeekStart = ref(new Date());
const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
const yearMonths = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

// 计算属性
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear - 5; i <= currentYear + 5; i++) {
    years.push(i);
  }
  return years;
});

const monthOptions = computed(() => {
  return [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ];
});

const calendarViewOptions = [
  { value: 'month', label: '月视图' },
  { value: 'week', label: '周视图' },
  { value: 'year', label: '年视图' }
];

// 周视图计算属性
const currentWeekRange = computed(() => {
  const start = new Date(currentWeekStart.value);
  const end = new Date(start);
  end.setDate(end.getDate() + 6);

  return `${start.getMonth() + 1}/${start.getDate()} - ${end.getMonth() + 1}/${end.getDate()}`;
});

const currentWeekNumber = computed(() => {
  const start = new Date(currentWeekStart.value);
  const yearStart = new Date(start.getFullYear(), 0, 1);
  const days = Math.floor((start - yearStart) / (24 * 60 * 60 * 1000));
  return Math.ceil((days + yearStart.getDay() + 1) / 7);
});

const currentWeekDates = computed(() => {
  const dates = [];
  const start = new Date(currentWeekStart.value);

  for (let i = 0; i < 7; i++) {
    const date = new Date(start);
    date.setDate(date.getDate() + i);

    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();

    dates.push({
      dateStr: date.toISOString().split('T')[0],
      day: date.getDate(),
      isToday
    });
  }

  return dates;
});

// 工具函数
const initializeWeekStart = (date) => {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day;
  const weekStart = new Date(d.setDate(diff));
  weekStart.setHours(0, 0, 0, 0);
  return weekStart;
};

const formatCurrentDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  if (calendarView.value === 'week') {
    return currentWeekRange.value;
  } else if (calendarView.value === 'year') {
    return `${d.getFullYear()}年`;
  } else {
    return d.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long'
    });
  }
};

const formatTooltipDate = (dateStr) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  });
};

// 记录相关方法
const getRecordsForDate = (dateStr) => {
  if (!dateStr || !props.records) return [];
  const targetDate = new Date(dateStr);
  targetDate.setHours(0, 0, 0, 0);

  return props.records.filter(record => {
    const recordDate = new Date(record[props.dateField] || record.created_at);
    recordDate.setHours(0, 0, 0, 0);
    return recordDate.getTime() === targetDate.getTime();
  });
};

const getMonthRecordsCount = (monthIndex) => {
  if (!props.records) return 0;
  return props.records.filter(record => {
    const recordDate = new Date(record[props.dateField]);
    return recordDate.getFullYear() === currentYear.value &&
           recordDate.getMonth() === monthIndex;
  }).length;
};

const getMonthRecordTypes = (monthIndex) => {
  if (!props.records) return [];
  const monthRecords = props.records.filter(record => {
    const recordDate = new Date(record[props.dateField]);
    return recordDate.getFullYear() === currentYear.value &&
           recordDate.getMonth() === monthIndex;
  });

  const types = [...new Set(monthRecords.map(record => record.record_type || record.category || '未知'))];
  return types.slice(0, 5); // 最多显示5种类型
};

const getMonthDensityClass = (monthIndex) => {
  const count = getMonthRecordsCount(monthIndex);
  if (count === 0) return 'density-none';
  if (count <= 2) return 'density-low';
  if (count <= 5) return 'density-medium';
  return 'density-high';
};

const isSelectedDate = (dateStr) => {
  if (!dateStr || !selectedDate.value) return false;
  const targetDate = new Date(dateStr).toDateString();
  const currentDate = new Date(selectedDate.value).toDateString();
  return targetDate === currentDate;
};

const isCurrentMonth = (monthIndex) => {
  const today = new Date();
  return today.getFullYear() === currentYear.value && today.getMonth() === monthIndex;
};

// 事件处理方法
const handleDateClick = (dateStr) => {
  selectedDate.value = new Date(dateStr);
  const records = getRecordsForDate(dateStr);
  emit('date-click', { date: dateStr, records });
};

const handleDateHover = (dateStr, event) => {
  const records = getRecordsForDate(dateStr);
  if (records.length === 0) return;

  // 清除之前的定时器
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value);
  }

  // 延迟显示提示框
  tooltipTimer.value = setTimeout(() => {
    tooltipDate.value = dateStr;
    tooltipRecords.value = records;

    // 计算提示框位置
    const rect = event.target.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    tooltipStyle.value = {
      position: 'absolute',
      left: `${rect.left + scrollLeft + rect.width / 2}px`,
      top: `${rect.top + scrollTop - 10}px`,
      transform: 'translateX(-50%) translateY(-100%)',
      zIndex: 9999
    };

    showTooltip.value = true;
  }, 300);
};

const handleDateLeave = () => {
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value);
  }

  // 延迟隐藏提示框
  setTimeout(() => {
    showTooltip.value = false;
  }, 200);
};

// 导航方法
const previousPeriod = () => {
  if (calendarView.value === 'week') {
    previousWeek();
  } else if (calendarView.value === 'year') {
    previousYear();
  } else {
    // 月视图：上一月
    const newDate = new Date(selectedDate.value);
    newDate.setMonth(newDate.getMonth() - 1);
    selectedDate.value = newDate;
  }
};

const nextPeriod = () => {
  if (calendarView.value === 'week') {
    nextWeek();
  } else if (calendarView.value === 'year') {
    nextYear();
  } else {
    // 月视图：下一月
    const newDate = new Date(selectedDate.value);
    newDate.setMonth(newDate.getMonth() + 1);
    selectedDate.value = newDate;
  }
};

const goToToday = () => {
  const today = new Date();
  selectedDate.value = today;
  currentYear.value = today.getFullYear();
  currentMonth.value = today.getMonth();

  if (calendarView.value === 'week') {
    currentWeekStart.value = initializeWeekStart(today);
  }
};

const previousWeek = () => {
  const newStart = new Date(currentWeekStart.value);
  newStart.setDate(newStart.getDate() - 7);
  currentWeekStart.value = newStart;
};

const nextWeek = () => {
  const newStart = new Date(currentWeekStart.value);
  newStart.setDate(newStart.getDate() + 7);
  currentWeekStart.value = newStart;
};

const previousYear = () => {
  currentYear.value--;
  const newDate = new Date(selectedDate.value);
  newDate.setFullYear(currentYear.value);
  selectedDate.value = newDate;
};

const nextYear = () => {
  currentYear.value++;
  const newDate = new Date(selectedDate.value);
  newDate.setFullYear(currentYear.value);
  selectedDate.value = newDate;
};

const handleYearChange = (year) => {
  const newDate = new Date(selectedDate.value);
  newDate.setFullYear(year);
  selectedDate.value = newDate;
  currentYear.value = year;
};

const handleMonthChange = (month) => {
  const newDate = new Date(selectedDate.value);
  newDate.setMonth(month);
  selectedDate.value = newDate;
  currentMonth.value = month;
};

const goToMonth = (monthIndex) => {
  const newDate = new Date(selectedDate.value);
  newDate.setMonth(monthIndex);
  selectedDate.value = newDate;
  calendarView.value = 'month';
};

const handleCalendarViewChange = (newView) => {
  if (newView === 'week') {
    currentWeekStart.value = initializeWeekStart(selectedDate.value);
  }
  emit('calendar-view-change', newView);
};

// 监听器
watch(selectedDate, (newDate) => {
  currentYear.value = newDate.getFullYear();
  currentMonth.value = newDate.getMonth();

  if (calendarView.value === 'week') {
    currentWeekStart.value = initializeWeekStart(newDate);
  }
}, { immediate: true });

watch(calendarView, (newView) => {
  if (newView === 'week') {
    currentWeekStart.value = initializeWeekStart(selectedDate.value);
  }
});

// 生命周期
onMounted(() => {
  // 初始化周视图
  if (calendarView.value === 'week') {
    currentWeekStart.value = initializeWeekStart(selectedDate.value);
  }
});

onUnmounted(() => {
  // 清理定时器
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value);
  }
});
</script>

<style scoped>
/* 日历面板 */
.calendar-section {
  margin-bottom: 24px;
}

.calendar-card {
  border-radius: 12px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.calendar-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.calendar-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.current-date-info {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.calendar-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.navigation-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #606266;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background: #ecf5ff;
  border-color: #409EFF;
  color: #409EFF;
}

.date-selectors {
  display: flex;
  align-items: center;
  gap: 8px;
}

.year-selector, .month-selector {
  width: 80px;
}

.today-btn {
  background: #67C23A;
  border: 1px solid #67C23A;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
}

.today-btn:hover {
  background: #85CE61;
  border-color: #85CE61;
}

.today-text {
  margin-left: 4px;
}

/* 日历内容切换动画 */
.calendar-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.calendar-content.view-switching {
  opacity: 0;
  transform: translateY(10px);
}

.health-calendar {
  margin-top: 16px;
}

/* 隐藏Element Plus日历的默认导航按钮 */
.health-calendar :deep(.el-calendar__header) {
  display: none;
}

.health-calendar :deep(.el-calendar__body) {
  padding: 0;
}

.calendar-cell {
  position: relative;
  height: 100%;
  padding: 4px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.calendar-cell:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.calendar-cell.has-records {
  background-color: rgba(64, 158, 255, 0.05);
}

.calendar-cell.has-records:hover {
  background-color: rgba(64, 158, 255, 0.15);
  transform: scale(1.02);
}

.calendar-cell.selected-date {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2), rgba(103, 194, 58, 0.2));
  border: 2px solid #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.date-number {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.record-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  margin-top: 4px;
}

.record-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.more-indicator {
  font-size: 10px;
  color: #909399;
  font-weight: 500;
}
</style>
