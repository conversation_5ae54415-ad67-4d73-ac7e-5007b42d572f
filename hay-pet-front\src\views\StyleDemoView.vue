<template>
  <div class="style-demo-view">
    <div class="demo-header">
      <h1>组件样式演示</h1>
      <p>展示项目中可复用的预设组件和设计系统</p>
    </div>

    <div class="demo-content">
      <!-- RecordViewsPreset 演示 -->
      <div class="demo-section">
        <div class="section-header">
          <h2>RecordViewsPreset - 记录视图预设组件</h2>
          <p>统一的三视图样式组件，提供卡片、时间线、表格三种视图样式</p>
        </div>

        <div class="demo-controls">
          <div class="control-group">
            <el-button @click="addSampleRecord" type="primary">
              <el-icon><Plus /></el-icon>
              添加示例记录
            </el-button>
            <el-button @click="clearRecords" type="danger">
              <el-icon><Delete /></el-icon>
              清空记录
            </el-button>
          </div>
        </div>

        <!-- 三种视图样式展示 -->
        <div class="views-showcase">
          <!-- 卡片视图 -->
          <div class="view-demo">
            <div class="view-header">
              <h3>卡片视图</h3>
              <p>网格布局，适合展示详细信息</p>
            </div>
            <div class="view-content">
              <RecordViewsPreset
                :records="sampleRecords"
                view-mode="cards"
                :color-mapping="colorMapping"
                :label-mapping="labelMapping"
                date-field="date"
                category-field="category"
                description-field="description"
                completed-field="is_completed"
                @edit="handleEdit"
                @delete="handleDelete"
                @toggle-completion="handleToggleCompletion"
              >
                <template #empty-action>
                  <el-button type="primary" @click="addSampleRecord">
                    <el-icon><Plus /></el-icon>
                    添加示例记录
                  </el-button>
                </template>
              </RecordViewsPreset>
            </div>
          </div>

          <!-- 时间线视图 -->
          <div class="view-demo">
            <div class="view-header">
              <h3>时间线视图</h3>
              <p>按日期分组，适合时间序列展示</p>
            </div>
            <div class="view-content">
              <RecordViewsPreset
                :records="sampleRecords"
                view-mode="timeline"
                :color-mapping="colorMapping"
                :label-mapping="labelMapping"
                date-field="date"
                category-field="category"
                description-field="description"
                completed-field="is_completed"
                @edit="handleEdit"
                @delete="handleDelete"
                @toggle-completion="handleToggleCompletion"
              >
                <template #empty-action>
                  <el-button type="primary" @click="addSampleRecord">
                    <el-icon><Plus /></el-icon>
                    添加示例记录
                  </el-button>
                </template>
              </RecordViewsPreset>
            </div>
          </div>

          <!-- 表格视图 -->
          <div class="view-demo">
            <div class="view-header">
              <h3>表格视图</h3>
              <p>表格形式，适合数据对比和批量操作</p>
            </div>
            <div class="view-content">
              <RecordViewsPreset
                :records="sampleRecords"
                view-mode="table"
                :color-mapping="colorMapping"
                :label-mapping="labelMapping"
                date-field="date"
                category-field="category"
                description-field="description"
                completed-field="is_completed"
                @edit="handleEdit"
                @delete="handleDelete"
                @toggle-completion="handleToggleCompletion"
              >
                <template #empty-action>
                  <el-button type="primary" @click="addSampleRecord">
                    <el-icon><Plus /></el-icon>
                    添加示例记录
                  </el-button>
                </template>
              </RecordViewsPreset>
            </div>
          </div>
        </div>

        <div class="demo-info">
          <h3>组件特性</h3>
          <ul>
            <li><strong>三种视图样式：</strong>卡片视图、时间线视图、表格视图</li>
            <li><strong>统一设计语言：</strong>24x24px圆形按钮、类别颜色边框、完成状态样式</li>
            <li><strong>交互功能：</strong>双击编辑、完成状态切换</li>
            <li><strong>响应式设计：</strong>适配不同屏幕尺寸</li>
            <li><strong>可配置字段：</strong>支持自定义数据字段映射</li>
            <li><strong>纯样式组件：</strong>专注于视图展示，不包含业务逻辑</li>
          </ul>

          <h3>API 说明</h3>
          <div class="api-table">
            <h4>Props</h4>
            <table>
              <thead>
                <tr>
                  <th>属性名</th>
                  <th>类型</th>
                  <th>默认值</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>records</td>
                  <td>Array</td>
                  <td>[]</td>
                  <td>记录数据数组</td>
                </tr>
                <tr>
                  <td>viewMode</td>
                  <td>String</td>
                  <td>'cards'</td>
                  <td>当前视图模式：'cards' | 'timeline' | 'table'</td>
                </tr>

                <tr>
                  <td>colorMapping</td>
                  <td>Object</td>
                  <td>{}</td>
                  <td>类别颜色映射</td>
                </tr>
                <tr>
                  <td>labelMapping</td>
                  <td>Object</td>
                  <td>{}</td>
                  <td>类别标签映射</td>
                </tr>
                <tr>
                  <td>dateField</td>
                  <td>String</td>
                  <td>'date'</td>
                  <td>日期字段名</td>
                </tr>
                <tr>
                  <td>categoryField</td>
                  <td>String</td>
                  <td>'category'</td>
                  <td>类别字段名</td>
                </tr>
                <tr>
                  <td>descriptionField</td>
                  <td>String</td>
                  <td>'description'</td>
                  <td>描述字段名</td>
                </tr>
                <tr>
                  <td>completedField</td>
                  <td>String</td>
                  <td>'is_completed'</td>
                  <td>完成状态字段名</td>
                </tr>
              </tbody>
            </table>

            <h4>Events</h4>
            <table>
              <thead>
                <tr>
                  <th>事件名</th>
                  <th>参数</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>edit</td>
                  <td>record</td>
                  <td>编辑记录事件</td>
                </tr>
                <tr>
                  <td>delete</td>
                  <td>record</td>
                  <td>删除记录事件</td>
                </tr>
                <tr>
                  <td>toggle-completion</td>
                  <td>record</td>
                  <td>切换完成状态事件</td>
                </tr>
              </tbody>
            </table>
          </div>

          <h3>使用示例</h3>
          <div class="code-example">
            <pre><code>&lt;RecordViewsPreset
  :records="records"
  :view-mode="viewMode"
  :color-mapping="colorMapping"
  :label-mapping="labelMapping"
  @edit="handleEdit"
  @delete="handleDelete"
  @toggle-completion="handleToggleCompletion"
&gt;
  &lt;template #empty-action&gt;
    &lt;el-button type="primary" @click="addRecord"&gt;
      添加记录
    &lt;/el-button&gt;
  &lt;/template&gt;
&lt;/RecordViewsPreset&gt;</code></pre>
          </div>
        </div>
      </div>

      <!-- TypeManagementDialog 演示 -->
      <div class="demo-section">
        <div class="section-header">
          <h2>TypeManagementDialog - 类型管理对话框预设组件</h2>
          <p>简洁优雅的类型管理对话框，支持名称编辑和颜色选择，标题居中对齐，颜色选择区域宽度优化</p>
        </div>

        <div class="demo-controls">
          <div class="control-group">
            <el-button @click="showAddDialog" type="primary">
              <el-icon><Plus /></el-icon>
              添加新类型
            </el-button>
            <el-button @click="showEditDialog" type="success">
              <el-icon><Edit /></el-icon>
              编辑现有类型
            </el-button>
          </div>
        </div>

        <div class="demo-component">
          <div class="type-list-demo">
            <h3>当前类型列表</h3>
            <div class="type-items">
              <div
                v-for="type in typeList"
                :key="type.id"
                class="type-item"
                :style="{ borderColor: type.color }"
                @click="editType(type)"
              >
                <div class="type-color" :style="{ backgroundColor: type.color }"></div>
                <span class="type-label">{{ type.label }}</span>
                <el-button
                  type="danger"
                  size="small"
                  circle
                  @click.stop="deleteType(type)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <div class="demo-info">
          <h3>组件特性</h3>
          <ul>
            <li><strong>标题居中对齐：</strong>对话框标题完美居中显示，视觉更加平衡</li>
            <li><strong>颜色区域优化：</strong>颜色选择网格充分利用可用空间，视觉更饱满</li>
            <li><strong>简洁设计：</strong>去掉花俏装饰，使用简洁的白色标题和清爽布局</li>
            <li><strong>颜色预览：</strong>实时显示当前选中颜色，提供直观的视觉反馈</li>
            <li><strong>丰富色彩：</strong>提供18种预设颜色，包括常用色、扩展色和柔和色</li>
            <li><strong>响应式设计：</strong>适配不同屏幕尺寸，移动端友好</li>
          </ul>

          <h3>API 说明</h3>
          <div class="api-table">
            <h4>Props</h4>
            <table>
              <thead>
                <tr>
                  <th>属性名</th>
                  <th>类型</th>
                  <th>默认值</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>modelValue</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>对话框显示状态</td>
                </tr>
                <tr>
                  <td>isEditMode</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>是否为编辑模式</td>
                </tr>
                <tr>
                  <td>typeLabel</td>
                  <td>String</td>
                  <td>'类型'</td>
                  <td>类型标签（如"分类"、"标签"等）</td>
                </tr>
                <tr>
                  <td>data</td>
                  <td>Object</td>
                  <td>{ label: '', color: '#409EFF' }</td>
                  <td>表单数据</td>
                </tr>
                <tr>
                  <td>predefinedColors</td>
                  <td>Array</td>
                  <td>18种预设颜色</td>
                  <td>预定义颜色数组</td>
                </tr>
              </tbody>
            </table>

            <h4>Events</h4>
            <table>
              <thead>
                <tr>
                  <th>事件名</th>
                  <th>参数</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>save</td>
                  <td>formData</td>
                  <td>保存事件，返回表单数据</td>
                </tr>
                <tr>
                  <td>cancel</td>
                  <td>-</td>
                  <td>取消事件</td>
                </tr>
                <tr>
                  <td>closed</td>
                  <td>-</td>
                  <td>对话框关闭事件</td>
                </tr>
              </tbody>
            </table>
          </div>

          <h3>使用示例</h3>
          <div class="code-example">
            <pre><code>&lt;TypeManagementDialog
  v-model="dialogVisible"
  :is-edit-mode="isEditMode"
  type-label="分类"
  :data="formData"
  @save="handleSave"
  @cancel="handleCancel"
/&gt;</code></pre>
          </div>
        </div>
      </div>
    </div>

    <!-- TypeManagementDialog 组件实例 -->
    <TypeManagementDialog
      v-model="dialogVisible"
      :is-edit-mode="isEditMode"
      :type-label="dialogTypeLabel"
      :data="dialogData"
      @save="handleDialogSave"
      @cancel="handleDialogCancel"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Edit } from '@element-plus/icons-vue';
import RecordViewsPreset from '@/components/presets/RecordViewsPreset.vue';
import TypeManagementDialog from '@/components/common/TypeManagementDialog.vue';

// 响应式数据

// 示例数据
const sampleRecords = ref([
  {
    id: 1,
    date: '2024-01-15',
    category: 'health',
    description: '定期体检，身体状况良好',
    is_completed: false
  },
  {
    id: 2,
    date: '2024-01-14',
    category: 'exercise',
    description: '晨跑30分钟，感觉很棒',
    is_completed: true
  },
  {
    id: 3,
    date: '2024-01-13',
    category: 'diet',
    description: '尝试新的健康食谱',
    is_completed: false
  },
  {
    id: 4,
    date: '2024-01-12',
    category: 'work',
    description: '完成项目里程碑',
    is_completed: true
  },
  {
    id: 5,
    date: '2024-01-11',
    category: 'study',
    description: '学习新的编程技术',
    is_completed: false
  }
]);

// 类别配置
const colorMapping = {
  health: '#67C23A',
  exercise: '#409EFF',
  diet: '#E6A23C',
  work: '#F56C6C',
  study: '#9C27B0'
};

const labelMapping = {
  health: '健康',
  exercise: '运动',
  diet: '饮食',
  work: '工作',
  study: '学习'
};

// 事件处理函数

const handleEdit = (record) => {
  ElMessage.info(`编辑记录: ${record.description}`);
};

const handleDelete = (record) => {
  ElMessageBox.confirm(`确定要删除记录"${record.description}"吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = sampleRecords.value.findIndex(r => r.id === record.id);
    if (index > -1) {
      sampleRecords.value.splice(index, 1);
      ElMessage.success('删除成功');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const handleToggleCompletion = (record) => {
  const index = sampleRecords.value.findIndex(r => r.id === record.id);
  if (index > -1) {
    sampleRecords.value[index].is_completed = !sampleRecords.value[index].is_completed;
    ElMessage.success(sampleRecords.value[index].is_completed ? '标记为已完成' : '标记为未完成');
  }
};



const addSampleRecord = () => {
  const categories = Object.keys(colorMapping);
  const randomCategory = categories[Math.floor(Math.random() * categories.length)];
  const newRecord = {
    id: Date.now(),
    date: new Date().toISOString().split('T')[0],
    category: randomCategory,
    description: `新的${labelMapping[randomCategory]}记录`,
    is_completed: false
  };
  sampleRecords.value.unshift(newRecord);
  ElMessage.success('添加示例记录成功');
};

const clearRecords = () => {
  ElMessageBox.confirm('确定要清空所有记录吗？', '清空记录', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    sampleRecords.value = [];
    ElMessage.success('已清空所有记录');
  });
};

// TypeManagementDialog 相关数据和方法
const dialogVisible = ref(false);
const isEditMode = ref(false);
const dialogTypeLabel = ref('分类');
const dialogData = ref({
  label: '',
  color: '#409EFF'
});

// 类型列表数据
const typeList = ref([
  { id: 1, label: '健康', color: '#67C23A' },
  { id: 2, label: '运动', color: '#409EFF' },
  { id: 3, label: '饮食', color: '#E6A23C' },
  { id: 4, label: '工作', color: '#F56C6C' },
  { id: 5, label: '学习', color: '#9C27B0' }
]);

const showAddDialog = () => {
  isEditMode.value = false;
  dialogTypeLabel.value = '分类';
  dialogData.value = {
    label: '',
    color: '#409EFF'
  };
  dialogVisible.value = true;
};

const showEditDialog = () => {
  if (typeList.value.length === 0) {
    ElMessage.warning('请先添加一些类型');
    return;
  }

  const firstType = typeList.value[0];
  editType(firstType);
};

const editType = (type) => {
  isEditMode.value = true;
  dialogTypeLabel.value = '分类';
  dialogData.value = {
    id: type.id,
    label: type.label,
    color: type.color
  };
  dialogVisible.value = true;
};

const deleteType = (type) => {
  ElMessageBox.confirm(`确定要删除分类"${type.label}"吗？`, '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = typeList.value.findIndex(t => t.id === type.id);
    if (index > -1) {
      typeList.value.splice(index, 1);
      ElMessage.success('删除成功');
    }
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

const handleDialogSave = (formData) => {
  if (isEditMode.value) {
    // 编辑模式
    const index = typeList.value.findIndex(t => t.id === formData.id);
    if (index > -1) {
      typeList.value[index] = { ...formData };
      ElMessage.success('编辑成功');
    }
  } else {
    // 添加模式
    const newType = {
      id: Date.now(),
      label: formData.label,
      color: formData.color
    };
    typeList.value.push(newType);
    ElMessage.success('添加成功');
  }
  dialogVisible.value = false;
};

const handleDialogCancel = () => {
  dialogVisible.value = false;
  ElMessage.info('已取消操作');
};
</script>

<style scoped>
.style-demo-view {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.demo-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.demo-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.demo-section {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.section-header {
  padding: 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.section-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.section-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.demo-controls {
  padding: 20px 24px;
  background: #fafbfc;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-group label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.demo-component {
  padding: 24px;
}

/* 三视图展示样式 */
.views-showcase {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 24px;
}

.view-demo {
  background: #fafbfc;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.view-header {
  padding: 20px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.view-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.view-header p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.view-content {
  padding: 24px;
  background: white;
}

.demo-info {
  padding: 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.demo-info h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 16px 0;
}

.demo-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 24px 0 12px 0;
}

.demo-info ul {
  margin: 0 0 24px 0;
  padding-left: 20px;
}

.demo-info li {
  margin-bottom: 8px;
  color: #4b5563;
  line-height: 1.5;
}

.api-table {
  margin-top: 24px;
}

.api-table table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.api-table th,
.api-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.api-table th {
  background: #f3f4f6;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.api-table td {
  color: #4b5563;
  font-size: 13px;
}

.api-table td:first-child {
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-weight: 500;
  color: #7c3aed;
}

.api-table td:nth-child(2) {
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  color: #059669;
}

.code-example {
  margin-top: 16px;
  background: #1f2937;
  border-radius: 8px;
  overflow: hidden;
}

.code-example pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
}

.code-example code {
  color: #e5e7eb;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .style-demo-view {
    padding: 16px;
  }

  .demo-header h1 {
    font-size: 24px;
  }

  .demo-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .control-group {
    justify-content: space-between;
  }

  .views-showcase {
    gap: 24px;
    padding: 16px;
  }

  .view-content {
    padding: 16px;
  }

  .view-header {
    padding: 16px 20px;
  }

  .api-table {
    overflow-x: auto;
  }

  .api-table table {
    min-width: 600px;
  }
}
</style>
