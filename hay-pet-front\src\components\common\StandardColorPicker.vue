<template>
  <div class="color-picker-wrapper">
    <!-- 颜色选择器标题区域 -->
    <div class="section-header" v-if="showHeader">
      <span class="section-title">{{ title }}</span>
      <span v-if="required" class="required-mark">*</span>
      <div class="color-picker-container">
        <el-color-picker
          v-model="currentColor"
          @change="handleColorPickerChange"
          :title="'当前颜色: ' + currentColor"
          :size="pickerSize"
          :show-alpha="showAlpha"
          :predefine="predefinedColors"
          :disabled="disabled"
        />
      </div>
    </div>

    <!-- 颜色选择区域 -->
    <div class="color-selection-area" :class="areaClasses">
      <div class="unified-color-grid" :class="gridClasses">
        <!-- 预设颜色按钮 -->
        <button
          v-for="color in predefinedColors"
          :key="color"
          @click.prevent="selectPresetColor(color)"
          :class="['color-button', sizeClass, { 'selected': currentColor === color }]"
          :style="{ backgroundColor: color }"
          :title="getColorName(color)"
          :disabled="disabled"
          type="button"
        >
          <div v-if="currentColor === color" class="selected-indicator">
            <el-icon class="check-icon">
              <Check />
            </el-icon>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Check } from '@element-plus/icons-vue'

// Props 定义
const props = defineProps({
  // 基础属性
  modelValue: {
    type: String,
    default: '#409EFF'
  },
  
  title: {
    type: String,
    default: '颜色选择'
  },
  
  // 显示选项
  showHeader: {
    type: Boolean,
    default: true
  },
  
  showAlpha: {
    type: Boolean,
    default: false
  },
  
  required: {
    type: Boolean,
    default: false
  },
  
  disabled: {
    type: Boolean,
    default: false
  },
  
  // 尺寸选项
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
  },
  
  pickerSize: {
    type: String,
    default: 'default',
    validator: (value) => ['large', 'default', 'small'].includes(value)
  },
  
  // 布局选项
  columns: {
    type: Number,
    default: 6,
    validator: (value) => [4, 5, 6, 8].includes(value)
  },
  
  variant: {
    type: String,
    default: 'standard',
    validator: (value) => ['standard', 'compact', 'minimal'].includes(value)
  },
  
  // 颜色配置
  predefinedColors: {
    type: Array,
    default: () => [
      '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
      '#909399', '#9C27B0', '#FF9800', '#4CAF50',
      '#2196F3', '#FF5722', '#795548', '#607D8B'
    ]
  }
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'change', 'select'])

// 响应式数据
const currentColor = ref(props.modelValue)

// 计算属性
const sizeClass = computed(() => {
  return `color-button-${props.size}`
})

const gridClasses = computed(() => {
  const classes = []
  if (props.columns !== 6) {
    classes.push(`color-grid-${props.columns}`)
  }
  return classes
})

const areaClasses = computed(() => {
  const classes = []
  if (props.variant !== 'standard') {
    classes.push(`color-picker-${props.variant}`)
  }
  return classes
})

// 监听器
watch(() => props.modelValue, (newValue) => {
  currentColor.value = newValue
})

watch(currentColor, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
})

// 方法
const selectPresetColor = (color) => {
  if (props.disabled) return
  currentColor.value = color
  emit('select', color)
}

const handleColorPickerChange = (color) => {
  if (color && !props.disabled) {
    currentColor.value = color
  }
}

const getColorName = (color) => {
  const colorNames = {
    '#409EFF': '主蓝色',
    '#67C23A': '成功绿',
    '#E6A23C': '警告橙',
    '#F56C6C': '危险红',
    '#909399': '信息灰',
    '#9C27B0': '紫色',
    '#FF9800': '橙色',
    '#4CAF50': '绿色',
    '#2196F3': '蓝色',
    '#FF5722': '深橙',
    '#795548': '棕色',
    '#607D8B': '蓝灰'
  }
  return colorNames[color] || color
}

// 暴露方法
defineExpose({
  selectColor: selectPresetColor,
  getCurrentColor: () => currentColor.value,
  resetColor: () => {
    currentColor.value = props.predefinedColors[0] || '#409EFF'
  }
})
</script>

<style scoped>
/* 引入颜色选择器预设样式 */
@import '@/styles/design-tokens.css';
@import '@/styles/color-picker-presets.css';

/* 组件特定样式 */
.color-picker-wrapper {
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-3);
  gap: var(--spacing-2);
}

.section-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--color-text-primary);
}

.required-mark {
  color: var(--color-danger);
  margin-left: var(--spacing-1);
}

/* 禁用状态 */
.color-picker-wrapper[disabled] .color-button {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.color-picker-wrapper[disabled] .color-selection-area {
  opacity: 0.6;
}
</style>
