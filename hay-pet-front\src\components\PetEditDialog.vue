<template>
  <el-dialog
    v-model="dialogVisible"
    title="编辑宠物信息"
    width="600px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="宠物姓名" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入宠物姓名"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="宠物品种" prop="species">
        <el-input
          v-model="form.species"
          placeholder="请输入宠物品种，如：金毛、英短、布偶猫等"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="年龄" prop="age">
        <el-input-number
          v-model="form.age"
          :min="0"
          :max="30"
          placeholder="请输入年龄"
          style="width: 200px"
        />
        <span style="margin-left: 10px; color: #909399;">岁</span>
      </el-form-item>
      
      <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="form.gender">
          <el-radio value="male">公</el-radio>
          <el-radio value="female">母</el-radio>
          <el-radio value="unknown">未知</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="当前头像">
        <div class="current-avatar">
          <el-avatar
            :size="80"
            :src="form.avatar_url"
            :icon="UserFilled"
            shape="square"
          />
        </div>
      </el-form-item>
      
      <el-form-item label="更换头像">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :on-change="handleAvatarChange"
          :show-file-list="false"
          accept="image/*"
          :limit="1"
        >
          <el-button type="primary" :icon="Upload">选择新头像</el-button>
        </el-upload>
        <div v-if="avatarPreview" class="avatar-preview">
          <p style="margin: 10px 0 5px 0; color: #909399;">新头像预览：</p>
          <img :src="avatarPreview" alt="新头像预览" />
        </div>
      </el-form-item>
      
      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="可以记录一些特殊信息，如健康状况、喜好等"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, inject, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, UserFilled } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  pet: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'pet-updated'])

const supabase = inject('supabase')
const formRef = ref()
const uploadRef = ref()
const loading = ref(false)
const avatarFile = ref(null)
const avatarPreview = ref('')
const dialogVisible = ref(false)

const form = ref({
  name: '',
  species: '',
  age: null,
  gender: 'unknown',
  avatar_url: '',
  notes: ''
})

const rules = {
  name: [
    { required: true, message: '请输入宠物姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '姓名长度在1到50个字符', trigger: 'blur' }
  ],
  species: [
    { required: true, message: '请输入宠物品种', trigger: 'blur' },
    { min: 1, max: 50, message: '品种长度在1到50个字符', trigger: 'blur' }
  ],
  age: [
    { required: true, message: '请输入宠物年龄', trigger: 'blur' },
    { type: 'number', min: 0, max: 30, message: '年龄必须在0到30之间', trigger: 'blur' }
  ]
}

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.pet) {
    // 初始化表单数据
    form.value = {
      name: props.pet.name || '',
      species: props.pet.species || '',
      age: props.pet.age || null,
      gender: props.pet.gender || 'unknown',
      avatar_url: props.pet.avatar_url || '',
      notes: props.pet.notes || ''
    }
    // 清空预览
    avatarPreview.value = ''
    avatarFile.value = null
  }
})

// 监听对话框关闭
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 处理头像选择
const handleAvatarChange = (file) => {
  avatarFile.value = file.raw
  
  // 创建预览
  const reader = new FileReader()
  reader.onload = (e) => {
    avatarPreview.value = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

// 上传头像到Supabase存储
const uploadAvatar = async (userId) => {
  if (!avatarFile.value) return null
  
  const fileExt = avatarFile.value.name.split('.').pop()
  const fileName = `${userId}/${Date.now()}.${fileExt}`
  
  const { data, error } = await supabase.storage
    .from('pet-media')
    .upload(fileName, avatarFile.value, {
      contentType: avatarFile.value.type,
      cacheControl: '3600',
      upsert: false
    })
  
  if (error) {
    console.error('头像上传失败:', error)
    return null
  }
  
  // 获取公开URL
  const { data: { publicUrl } } = supabase.storage
    .from('pet-media')
    .getPublicUrl(fileName)
  
  return publicUrl
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  loading.value = true
  
  try {
    // 获取当前用户
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      ElMessage.error('请先登录')
      return
    }
    
    // 准备更新数据
    const updateData = {
      name: form.value.name,
      species: form.value.species,
      age: form.value.age,
      gender: form.value.gender,
      notes: form.value.notes,
      updated_at: new Date().toISOString()
    }
    
    // 如果有新头像，先上传
    if (avatarFile.value) {
      const avatarUrl = await uploadAvatar(user.id)
      if (avatarUrl) {
        updateData.avatar_url = avatarUrl
      }
    }
    
    // 更新宠物数据
    const { error } = await supabase
      .from('pets')
      .update(updateData)
      .eq('id', props.pet.id)
      .eq('user_id', user.id)
    
    if (error) {
      ElMessage.error(`更新宠物信息失败: ${error.message}`)
      console.error('更新宠物错误:', error)
    } else {
      ElMessage.success('宠物信息更新成功！')
      emit('pet-updated')
      handleClose()
    }
  } catch (err) {
    ElMessage.error('更新宠物信息过程中发生错误')
    console.error('更新错误:', err)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  // 重置表单
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  avatarPreview.value = ''
  avatarFile.value = null
}
</script>

<style scoped>
.current-avatar {
  margin-bottom: 10px;
}

.avatar-preview {
  margin-top: 10px;
}

.avatar-preview img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.dialog-footer {
  text-align: right;
}
</style>