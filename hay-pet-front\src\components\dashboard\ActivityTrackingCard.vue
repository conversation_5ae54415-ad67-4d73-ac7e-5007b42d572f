<template>
  <div class="activity-tracking-card">
    <div class="card-header">
      <div class="header-left">
        <el-icon class="header-icon"><Timer /></el-icon>
        <h3>活动记录</h3>
      </div>
      <div class="header-right">
        <el-button type="primary" size="small" @click="addActivity">
          <el-icon><Plus /></el-icon>
          添加记录
        </el-button>
      </div>
    </div>

    <div class="card-content">
      <!-- 统计信息 -->
      <div class="stats-row">
        <div class="stat-item">
          <div class="stat-value">{{ todaySteps }}</div>
          <div class="stat-label">今日步数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ weeklyAverage }}</div>
          <div class="stat-label">周平均</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ totalActivities }}</div>
          <div class="stat-label">总记录</div>
        </div>
      </div>

      <!-- 活动趋势图表 -->
      <div class="chart-container">
        <div ref="chartRef" class="activity-chart"></div>
      </div>

      <!-- 最近活动 -->
      <div class="recent-activities">
        <h4>最近活动</h4>
        <div v-if="recentActivities.length" class="activity-list">
          <div 
            v-for="activity in recentActivities" 
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="activity-info">
              <div class="activity-type">{{ activity.type }}</div>
              <div class="activity-details">
                {{ activity.duration }}分钟 · {{ activity.distance }}km
              </div>
            </div>
            <div class="activity-time">
              {{ formatTime(activity.createdAt) }}
            </div>
          </div>
        </div>
        <div v-else class="empty-activities">
          <p>暂无活动记录</p>
        </div>
      </div>

      <!-- 操作链接 -->
      <div class="card-actions">
        <!-- 活动记录功能暂未实现 -->
        <span class="disabled-link">
          查看详细活动记录
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElIcon, ElButton } from 'element-plus';
import { Timer, Plus, User, ArrowRight } from '@element-plus/icons-vue';
import * as echarts from 'echarts';

// 响应式数据
const chartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 数据
const todaySteps = ref(0);
const weeklyAverage = ref(0);
const totalActivities = ref(0);

const recentActivities = ref([]);

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chartInstance = echarts.init(chartRef.value);
  
  // 生成最近7天的日期
  const dates = [];
  const steps = [];
  const activities = [];
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    dates.push(date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' }));
    steps.push(0);
    activities.push(0);
  }
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['步数', '活动次数'],
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        fontSize: 11
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '步数',
        position: 'left',
        axisLabel: {
          formatter: '{value}',
          fontSize: 11
        }
      },
      {
        type: 'value',
        name: '次数',
        position: 'right',
        axisLabel: {
          formatter: '{value}',
          fontSize: 11
        }
      }
    ],
    series: [
      {
        name: '步数',
        type: 'line',
        data: steps,
        smooth: true,
        itemStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      },
      {
        name: '活动次数',
        type: 'bar',
        yAxisIndex: 1,
        data: activities,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  };
  
  chartInstance.setOption(option);
};

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  
  if (hours < 1) {
    const minutes = Math.floor(diff / (1000 * 60));
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else {
    const days = Math.floor(hours / 24);
    return `${days}天前`;
  }
};

// 添加活动记录
const addActivity = () => {
  // 这里可以打开添加活动的对话框或跳转到添加页面

};

// 窗口大小变化时重新调整图表
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize, { passive: true });
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.activity-tracking-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 20px;
  color: #409EFF;
}

.header-left h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-content {
  padding: 20px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #409EFF;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.chart-container {
  margin-bottom: 24px;
}

.activity-chart {
  width: 100%;
  height: 200px;
}

.recent-activities h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.activity-item:hover {
  background: #ecf5ff;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.activity-info {
  flex: 1;
}

.activity-type {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.activity-details {
  font-size: 12px;
  color: #909399;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

.card-actions {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.action-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #409EFF;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
}

.action-link:hover {
  color: #66b1ff;
}

.disabled-link {
  color: var(--el-text-color-disabled);
  font-size: 14px;
  cursor: not-allowed;
}

.empty-activities {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .card-header {
    padding: 16px;
  }
  
  .card-content {
    padding: 16px;
  }
  
  .stats-row {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  
  .stat-item {
    padding: 12px 8px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .stat-label {
    font-size: 11px;
  }
  
  .activity-chart {
    height: 160px;
  }
  
  .activity-item {
    padding: 10px;
  }
  
  .activity-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}
</style>