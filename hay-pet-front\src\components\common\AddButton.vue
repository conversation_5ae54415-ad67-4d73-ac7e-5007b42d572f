<template>
  <button
    @click="handleClick"
    :title="tooltip"
    :class="buttonClasses"
    :disabled="disabled"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      :width="iconSize"
      :height="iconSize"
      viewBox="0 0 24 24"
      :class="iconClasses"
    >
      <path
        d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
        stroke-width="1.5"
      ></path>
      <path d="M8 12H16" stroke-width="1.5"></path>
      <path d="M12 16V8" stroke-width="1.5"></path>
    </svg>
  </button>
</template>

<script setup>
import { computed } from 'vue'

// Props 定义
const props = defineProps({
  // 基础属性
  tooltip: {
    type: String,
    default: '添加'
  },
  
  disabled: {
    type: Boolean,
    default: false
  },
  
  // 尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
  },
  
  // 颜色变体
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'success', 'warning', 'danger', 'info', 'neutral'].includes(value)
  },
  
  // 动画类型
  animation: {
    type: String,
    default: 'rotate',
    validator: (value) => ['rotate', 'scale', 'pulse', 'none'].includes(value)
  }
})

// Emits 定义
const emit = defineEmits(['click'])

// 计算属性
const iconSize = computed(() => {
  const sizeMap = {
    sm: '32px',
    md: '40px',
    lg: '48px',
    xl: '56px'
  }
  return sizeMap[props.size]
})

const buttonClasses = computed(() => {
  const classes = ['add-button-base']
  
  // 尺寸类
  classes.push(`add-button-${props.size}`)
  
  // 变体类
  classes.push(`add-button-${props.variant}`)
  
  // 动画类
  if (props.animation !== 'none') {
    classes.push(`add-button-${props.animation}`)
  }
  
  // 禁用状态
  if (props.disabled) {
    classes.push('add-button-disabled')
  }
  
  return classes
})

const iconClasses = computed(() => {
  const classes = ['add-icon-base']
  
  // 变体类
  classes.push(`add-icon-${props.variant}`)
  
  return classes
})

// 事件处理
const handleClick = (event) => {
  if (props.disabled) return
  emit('click', event)
}
</script>

<style scoped>
/* 引入设计令牌 */
@import '@/styles/design-tokens.css';

/* 基础按钮样式 */
.add-button-base {
  cursor: pointer;
  outline: none;
  background: transparent;
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: transform var(--duration-base) ease;
}

.add-button-base:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: 50%;
}

/* 尺寸变体 */
.add-button-sm {
  width: 32px;
  height: 32px;
}

.add-button-md {
  width: 40px;
  height: 40px;
}

.add-button-lg {
  width: 48px;
  height: 48px;
}

.add-button-xl {
  width: 56px;
  height: 56px;
}

/* 动画变体 */
.add-button-rotate:hover {
  transform: rotate(90deg);
}

.add-button-scale:hover {
  transform: scale(1.1);
}

.add-button-pulse:hover {
  animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 基础图标样式 */
.add-icon-base {
  fill: none;
  transition: all var(--duration-base) ease;
}

/* 颜色变体 */
.add-button-primary .add-icon-primary {
  stroke: #6c757d;
}

.add-button-primary:hover .add-icon-primary {
  stroke: var(--color-primary);
  fill: rgba(64, 158, 255, 0.1);
}

.add-button-primary:active .add-icon-primary {
  stroke: var(--color-primary-dark);
  fill: rgba(64, 158, 255, 0.2);
}

.add-button-success .add-icon-success {
  stroke: #6c757d;
}

.add-button-success:hover .add-icon-success {
  stroke: var(--color-success);
  fill: rgba(103, 194, 58, 0.1);
}

.add-button-success:active .add-icon-success {
  stroke: var(--color-success-dark);
  fill: rgba(103, 194, 58, 0.2);
}

.add-button-warning .add-icon-warning {
  stroke: #6c757d;
}

.add-button-warning:hover .add-icon-warning {
  stroke: var(--color-warning);
  fill: rgba(230, 162, 60, 0.1);
}

.add-button-warning:active .add-icon-warning {
  stroke: var(--color-warning-dark);
  fill: rgba(230, 162, 60, 0.2);
}

.add-button-danger .add-icon-danger {
  stroke: #6c757d;
}

.add-button-danger:hover .add-icon-danger {
  stroke: var(--color-danger);
  fill: rgba(245, 108, 108, 0.1);
}

.add-button-danger:active .add-icon-danger {
  stroke: var(--color-danger-dark);
  fill: rgba(245, 108, 108, 0.2);
}

.add-button-info .add-icon-info {
  stroke: #6c757d;
}

.add-button-info:hover .add-icon-info {
  stroke: var(--color-info);
  fill: rgba(144, 147, 153, 0.1);
}

.add-button-info:active .add-icon-info {
  stroke: var(--color-info-dark);
  fill: rgba(144, 147, 153, 0.2);
}

.add-button-neutral .add-icon-neutral {
  stroke: #6c757d;
}

.add-button-neutral:hover .add-icon-neutral {
  stroke: #495057;
  fill: rgba(108, 117, 125, 0.1);
}

.add-button-neutral:active .add-icon-neutral {
  stroke: #343a40;
  fill: rgba(108, 117, 125, 0.2);
}

/* 禁用状态 */
.add-button-disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.add-button-disabled:hover {
  transform: none;
}

.add-button-disabled .add-icon-base {
  stroke: #c0c4cc !important;
  fill: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-button-lg {
    width: 44px;
    height: 44px;
  }
  
  .add-button-xl {
    width: 48px;
    height: 48px;
  }
}
</style>
