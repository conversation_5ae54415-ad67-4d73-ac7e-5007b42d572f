<template>
  <div class="component-showcase">
    <div class="showcase-container">
      <h1 class="showcase-title">🎨 组件样式预设系统展示</h1>
      <p class="showcase-description">
        展示 Hay!Pet 项目的标准化标签组件和样式预设系统
      </p>

      <!-- 基础变体展示 -->
      <section class="showcase-section">
        <h2 class="section-title">🎯 颜色变体</h2>
        <div class="tag-group">
          <StandardTag text="主色调" variant="primary" />
          <StandardTag text="成功" variant="success" />
          <StandardTag text="警告" variant="warning" />
          <StandardTag text="危险" variant="danger" />
          <StandardTag text="信息" variant="info" />
        </div>
      </section>

      <!-- 尺寸展示 -->
      <section class="showcase-section">
        <h2 class="section-title">📏 尺寸变体</h2>
        <div class="tag-group">
          <StandardTag text="超小" size="xs" variant="primary" />
          <StandardTag text="小" size="sm" variant="primary" />
          <StandardTag text="中等" size="md" variant="primary" />
          <StandardTag text="大" size="lg" variant="primary" />
          <StandardTag text="超大" size="xl" variant="primary" />
        </div>
      </section>

      <!-- 激活状态展示 -->
      <section class="showcase-section">
        <h2 class="section-title">✨ 激活状态</h2>
        <div class="tag-group">
          <StandardTag text="普通状态" variant="primary" />
          <StandardTag text="激活状态" variant="primary" :active="true" />
          <StandardTag text="成功激活" variant="success" :active="true" />
          <StandardTag text="警告激活" variant="warning" :active="true" />
          <StandardTag text="危险激活" variant="danger" :active="true" />
        </div>
      </section>

      <!-- 交互功能展示 -->
      <section class="showcase-section">
        <h2 class="section-title">🔧 交互功能</h2>
        <div class="tag-group">
          <StandardTag 
            text="可编辑" 
            variant="primary" 
            :editable="true"
            @edit="handleEdit"
          />
          <StandardTag 
            text="可删除" 
            variant="success" 
            :deletable="true"
            @delete="handleDelete"
          />
          <StandardTag 
            text="编辑+删除" 
            variant="warning" 
            :editable="true"
            :deletable="true"
            @edit="handleEdit"
            @delete="handleDelete"
          />
          <StandardTag 
            text="禁用状态" 
            variant="info" 
            :disabled="true"
          />
          <StandardTag 
            text="加载中" 
            variant="primary" 
            :loading="isLoading"
            @click="toggleLoading"
          />
        </div>
      </section>

      <!-- 图标展示 -->
      <section class="showcase-section">
        <h2 class="section-title">🎭 图标支持</h2>
        <div class="tag-group">
          <StandardTag 
            text="左图标" 
            variant="primary" 
            left-icon="Star"
          />
          <StandardTag 
            text="右图标" 
            variant="success" 
            right-icon="ArrowRight"
          />
          <StandardTag 
            text="双图标" 
            variant="warning" 
            left-icon="User"
            right-icon="Check"
          />
        </div>
      </section>

      <!-- 自定义样式展示 -->
      <section class="showcase-section">
        <h2 class="section-title">🎨 自定义样式</h2>
        <div class="tag-group">
          <StandardTag 
            text="自定义颜色" 
            color="#ff6b6b"
            background-color="rgba(255, 107, 107, 0.1)"
            border-color="rgba(255, 107, 107, 0.3)"
          />
          <StandardTag 
            text="渐变背景" 
            color="white"
            background-color="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
          />
          <StandardTag 
            text="紫色主题" 
            color="#8b5cf6"
            background-color="rgba(139, 92, 246, 0.1)"
            border-color="rgba(139, 92, 246, 0.3)"
          />
        </div>
      </section>

      <!-- 实际应用场景 -->
      <section class="showcase-section">
        <h2 class="section-title">🏥 实际应用场景</h2>
        
        <div class="scenario-group">
          <h3 class="scenario-title">健康记录类型</h3>
          <div class="tag-group">
            <StandardTag 
              text="疫苗接种" 
              variant="success" 
              :active="selectedType === 'vaccination'"
              @click="selectType('vaccination')"
            />
            <StandardTag 
              text="驱虫" 
              variant="warning" 
              :active="selectedType === 'deworming'"
              @click="selectType('deworming')"
            />
            <StandardTag 
              text="体检" 
              variant="info" 
              :active="selectedType === 'checkup'"
              @click="selectType('checkup')"
            />
            <StandardTag 
              text="过敏记录" 
              variant="danger" 
              :active="selectedType === 'allergy'"
              @click="selectType('allergy')"
            />
          </div>
        </div>

        <div class="scenario-group">
          <h3 class="scenario-title">宠物标签管理</h3>
          <div class="tag-group">
            <StandardTag 
              v-for="tag in petTags"
              :key="tag.id"
              :text="tag.name"
              :variant="tag.variant"
              :editable="true"
              :deletable="true"
              @edit="editPetTag(tag)"
              @delete="deletePetTag(tag)"
            />
            <StandardTag 
              text="添加标签" 
              variant="info" 
              left-icon="Plus"
              @click="addPetTag"
            />
          </div>
        </div>
      </section>

      <!-- 响应式展示 -->
      <section class="showcase-section">
        <h2 class="section-title">📱 响应式设计</h2>
        <p class="section-description">
          调整浏览器窗口大小查看标签组件的响应式效果
        </p>
        <div class="responsive-demo">
          <div class="tag-group">
            <StandardTag 
              v-for="i in 8"
              :key="i"
              :text="`标签 ${i}`"
              variant="primary"
            />
          </div>
        </div>
      </section>

      <!-- 操作日志 -->
      <section class="showcase-section" v-if="actionLogs.length > 0">
        <h2 class="section-title">📋 操作日志</h2>
        <div class="action-logs">
          <div 
            v-for="(log, index) in actionLogs"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action">{{ log.action }}</span>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import StandardTag from '@/components/common/StandardTag.vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const isLoading = ref(false)
const selectedType = ref('')
const actionLogs = ref([])

// 示例宠物标签数据
const petTags = ref([
  { id: 1, name: '活泼', variant: 'success' },
  { id: 2, name: '温顺', variant: 'primary' },
  { id: 3, name: '需要关注', variant: 'warning' }
])

// 事件处理函数
const handleEdit = () => {
  addLog('点击了编辑按钮')
  ElMessage.success('编辑功能触发')
}

const handleDelete = () => {
  addLog('点击了删除按钮')
  ElMessage.warning('删除功能触发')
}

const toggleLoading = () => {
  isLoading.value = !isLoading.value
  addLog(`切换加载状态: ${isLoading.value ? '开始' : '结束'}`)
}

const selectType = (type) => {
  selectedType.value = selectedType.value === type ? '' : type
  addLog(`选择记录类型: ${type}`)
}

const editPetTag = (tag) => {
  addLog(`编辑宠物标签: ${tag.name}`)
  ElMessage.info(`编辑标签: ${tag.name}`)
}

const deletePetTag = (tag) => {
  addLog(`删除宠物标签: ${tag.name}`)
  petTags.value = petTags.value.filter(t => t.id !== tag.id)
  ElMessage.success(`已删除标签: ${tag.name}`)
}

const addPetTag = () => {
  const newTag = {
    id: Date.now(),
    name: `新标签 ${petTags.value.length + 1}`,
    variant: 'info'
  }
  petTags.value.push(newTag)
  addLog(`添加新标签: ${newTag.name}`)
  ElMessage.success(`已添加标签: ${newTag.name}`)
}

const addLog = (action) => {
  actionLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    action
  })
  // 限制日志数量
  if (actionLogs.value.length > 10) {
    actionLogs.value = actionLogs.value.slice(0, 10)
  }
}
</script>

<style scoped>
.component-showcase {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.showcase-container {
  max-width: 1200px;
  margin: 0 auto;
}

.showcase-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  text-align: center;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.showcase-description {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-8);
}

.showcase-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
  transition: all var(--duration-base) var(--ease-in-out);
}

.showcase-section:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.section-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

.section-description {
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-4);
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  align-items: center;
}

.scenario-group {
  margin-bottom: var(--spacing-6);
}

.scenario-group:last-child {
  margin-bottom: 0;
}

.scenario-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-regular);
  margin-bottom: var(--spacing-3);
}

.responsive-demo {
  border: 2px dashed var(--color-border-base);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  background: var(--color-bg-secondary);
}

.action-logs {
  max-height: 200px;
  overflow-y: auto;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.log-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-1) 0;
  border-bottom: 1px solid var(--color-border-light);
  font-size: var(--font-size-sm);
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: var(--color-text-secondary);
  font-family: monospace;
}

.log-action {
  color: var(--color-text-regular);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .component-showcase {
    padding: var(--spacing-4);
  }
  
  .showcase-title {
    font-size: var(--font-size-2xl);
  }
  
  .showcase-section {
    padding: var(--spacing-4);
  }
  
  .tag-group {
    gap: var(--spacing-2);
  }
}
</style>
