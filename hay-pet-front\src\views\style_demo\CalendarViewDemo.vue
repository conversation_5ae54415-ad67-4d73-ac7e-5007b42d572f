<template>
  <div class="calendar-view-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>📅 CalendarViewPreset 日历视图预设</h1>
        <p class="page-description">
          完整的日历视图组件，支持月视图、周视图、年视图，包含记录指示器、悬浮提示和响应式设计
        </p>
      </div>

      <!-- 组件信息 -->
      <div class="component-info">
        <div class="info-grid">
          <div class="info-item">
            <h3>📦 组件名称</h3>
            <p>CalendarViewPreset</p>
          </div>
          <div class="info-item">
            <h3>🏷️ 版本</h3>
            <p>v1.6.0</p>
          </div>
          <div class="info-item">
            <h3>📁 文件位置</h3>
            <p>@/components/presets/CalendarViewPreset.vue</p>
          </div>
          <div class="info-item">
            <h3>🎯 状态</h3>
            <p class="status stable">稳定</p>
          </div>
        </div>
      </div>

      <!-- 基础演示 -->
      <div class="demo-section">
        <h2>🎨 基础演示</h2>
        <div class="demo-content">
          <CalendarViewPreset
            title="事件记录日历"
            :records="eventRecords"
            :record-types="recordTypes"
            :color-mapping="colorMapping"
            :label-mapping="labelMapping"
            record-id-field="id"
            record-type-field="record_type"
            record-date-field="date"
            record-desc-field="description"
            @date-click="handleDateClick"
            @view-change="handleCalendarViewChange"
            @date-change="handleDateChange"
          />
        </div>
      </div>

      <!-- 提醒事项演示 -->
      <div class="demo-section">
        <h2>📋 提醒事项演示</h2>
        <div class="demo-content">
          <CalendarViewPreset
            title="提醒日历"
            :records="reminderRecords"
            :record-types="reminderTypes"
            :color-mapping="reminderColorMapping"
            :label-mapping="reminderLabelMapping"
            record-id-field="id"
            record-type-field="category"
            record-date-field="due_date"
            record-desc-field="description"
            initial-view="week"
            @date-click="handleReminderDateClick"
            @view-change="handleReminderCalendarViewChange"
            @date-change="handleReminderDateChange"
          />
        </div>
      </div>

      <!-- Props 配置说明 -->
      <div class="props-section">
        <h2>⚙️ Props 配置</h2>
        <div class="props-table">
          <table>
            <thead>
              <tr>
                <th>属性名</th>
                <th>类型</th>
                <th>默认值</th>
                <th>说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>title</code></td>
                <td>String</td>
                <td>'日历视图'</td>
                <td>日历标题</td>
              </tr>
              <tr>
                <td><code>records</code></td>
                <td>Array</td>
                <td>[]</td>
                <td>记录数据数组</td>
              </tr>
              <tr>
                <td><code>recordIdField</code></td>
                <td>String</td>
                <td>'id'</td>
                <td>记录ID字段名</td>
              </tr>
              <tr>
                <td><code>recordTypeField</code></td>
                <td>String</td>
                <td>'record_type'</td>
                <td>记录类型字段名</td>
              </tr>
              <tr>
                <td><code>recordDateField</code></td>
                <td>String</td>
                <td>'date'</td>
                <td>记录日期字段名</td>
              </tr>
              <tr>
                <td><code>recordDescField</code></td>
                <td>String</td>
                <td>'description'</td>
                <td>记录描述字段名</td>
              </tr>
              <tr>
                <td><code>recordTypes</code></td>
                <td>Array</td>
                <td>[]</td>
                <td>记录类型配置数组</td>
              </tr>
              <tr>
                <td><code>colorMapping</code></td>
                <td>Object</td>
                <td>{}</td>
                <td>颜色映射配置</td>
              </tr>
              <tr>
                <td><code>labelMapping</code></td>
                <td>Object</td>
                <td>{}</td>
                <td>标签映射配置</td>
              </tr>
              <tr>
                <td><code>initialView</code></td>
                <td>String</td>
                <td>'month'</td>
                <td>初始视图模式</td>
              </tr>
              <tr>
                <td><code>initialDate</code></td>
                <td>Date</td>
                <td>new Date()</td>
                <td>初始日期</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 事件说明 -->
      <div class="events-section">
        <h2>📡 事件</h2>
        <div class="events-table">
          <table>
            <thead>
              <tr>
                <th>事件名</th>
                <th>参数</th>
                <th>说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><code>date-click</code></td>
                <td>(dateStr: string, records: Array)</td>
                <td>点击日期时触发，返回日期字符串和当日记录</td>
              </tr>
              <tr>
                <td><code>view-change</code></td>
                <td>(newView: string)</td>
                <td>日历视图切换时触发</td>
              </tr>
              <tr>
                <td><code>date-change</code></td>
                <td>(newDate: Date)</td>
                <td>选中日期变化时触发</td>
              </tr>
              <tr>
                <td><code>month-change</code></td>
                <td>(month: number)</td>
                <td>月份变化时触发</td>
              </tr>
              <tr>
                <td><code>year-change</code></td>
                <td>(year: number)</td>
                <td>年份变化时触发</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 使用示例 -->
      <div class="usage-section">
        <h2>💻 使用示例</h2>
        <div class="code-example">
          <h3>基础用法</h3>
          <pre><code>&lt;template&gt;
  &lt;CalendarViewPreset
    title="提醒日历"
    :records="reminders"
    :record-types="reminderTypes"
    :color-mapping="colorMapping"
    :label-mapping="labelMapping"
    record-id-field="id"
    record-type-field="category"
    record-date-field="due_date"
    record-desc-field="description"
    initial-view="month"
    @date-click="handleDateClick"
    @view-change="handleViewChange"
    @date-change="handleDateChange"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, reactive } from 'vue'
import CalendarViewPreset from '@/components/presets/CalendarViewPreset.vue'

const reminders = ref([
  {
    id: 1,
    title: '疫苗接种提醒',
    category: 'vaccination',
    due_date: '2024-06-25',
    description: '狂犬病疫苗第二针'
  }
])

const reminderTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'medication', label: '用药提醒', color: '#E6A23C' },
  { value: 'checkup', label: '体检提醒', color: '#409EFF' }
])

const colorMapping = reactive({
  vaccination: 'vaccination',
  medication: 'medication',
  checkup: 'checkup'
})

const labelMapping = reactive({
  vaccination: '疫苗接种',
  medication: '用药提醒',
  checkup: '体检提醒'
})

const handleDateClick = (dateStr, records) => {
  console.log('点击日期:', dateStr, '记录:', records)
}

const handleViewChange = (newView) => {
  console.log('视图切换:', newView)
}

const handleDateChange = (newDate) => {
  console.log('日期变化:', newDate)
}
&lt;/script&gt;</code></pre>
        </div>
      </div>

      <!-- 特性说明 -->
      <div class="features-section">
        <h2>✨ 特性</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">📅</div>
            <h3>三种视图模式</h3>
            <p>支持月视图、周视图、年视图，满足不同场景需求</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>记录指示器</h3>
            <p>在日历上显示彩色圆点，直观展示当日记录数量和类型</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💬</div>
            <h3>悬浮提示</h3>
            <p>鼠标悬浮显示当日记录详情，提供快速预览功能</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3>响应式设计</h3>
            <p>自动适配桌面、平板、手机等不同设备屏幕</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔧</div>
            <h3>高度可配置</h3>
            <p>支持自定义字段映射、样式函数和事件处理</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎨</div>
            <h3>统一样式</h3>
            <p>基于事件记录页面设计，保持整体视觉一致性</p>
          </div>
        </div>
      </div>

      <!-- 样式定制 -->
      <div class="customization-section">
        <h2>🎨 样式定制</h2>
        <div class="customization-content">
          <h3>记录类型样式映射</h3>
          <div class="style-mapping">
            <div class="mapping-item">
              <div class="dot vaccination"></div>
              <span>疫苗接种 (vaccination)</span>
            </div>
            <div class="mapping-item">
              <div class="dot medication"></div>
              <span>用药记录 (medication)</span>
            </div>
            <div class="mapping-item">
              <div class="dot checkup"></div>
              <span>体检记录 (checkup)</span>
            </div>
            <div class="mapping-item">
              <div class="dot illness"></div>
              <span>疾病就诊 (illness)</span>
            </div>
            <div class="mapping-item">
              <div class="dot other"></div>
              <span>其他记录 (other)</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 事件日志 -->
      <div class="event-log-section">
        <h2>📋 事件日志</h2>
        <div class="event-log">
          <div v-if="eventLogs.length === 0" class="no-events">
            暂无事件记录，请与日历组件交互
          </div>
          <div
            v-for="(log, index) in eventLogs"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
            <span class="log-data">{{ log.data }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import CalendarViewPreset from '@/components/presets/CalendarViewPreset.vue'

// 事件日志
const eventLogs = ref([])

// 模拟事件记录数据 - 添加更多当前月份的数据
const eventRecords = ref([
  // 2025年6月的记录
  {
    id: 1,
    record_type: 'vaccination',
    date: '2025-06-20',
    description: '狂犬病疫苗第一针',
    notes: '接种正常，无不良反应',
    next_due_date: '2025-07-20'
  },
  {
    id: 2,
    record_type: 'checkup',
    date: '2025-06-22',
    description: '常规体检',
    notes: '身体状况良好'
  },
  {
    id: 3,
    record_type: 'medication',
    date: '2025-06-25',
    description: '驱虫药',
    notes: '口服驱虫药一粒'
  },
  {
    id: 4,
    record_type: 'illness',
    date: '2025-06-18',
    description: '轻微感冒',
    notes: '已康复'
  },
  {
    id: 5,
    record_type: 'vaccination',
    date: '2025-06-15',
    description: '疫苗接种第二针',
    notes: '接种顺利',
    next_due_date: '2025-07-15'
  },
  {
    id: 6,
    record_type: 'deworming',
    date: '2025-06-10',
    description: '体内驱虫',
    notes: '定期驱虫治疗'
  },
  {
    id: 7,
    record_type: 'checkup',
    date: '2025-06-05',
    description: '血液检查',
    notes: '各项指标正常'
  },
  {
    id: 8,
    record_type: 'medication',
    date: '2025-06-03',
    description: '营养补充剂',
    notes: '维生素补充'
  },
  {
    id: 9,
    record_type: 'surgery',
    date: '2025-06-01',
    description: '绝育手术',
    notes: '手术成功，恢复良好'
  },
  // 其他月份的记录
  {
    id: 10,
    record_type: 'vaccination',
    date: '2025-05-20',
    description: '疫苗接种',
    notes: '第一针疫苗'
  },
  {
    id: 11,
    record_type: 'checkup',
    date: '2025-05-15',
    description: '季度体检',
    notes: '健康状况良好'
  },
  {
    id: 12,
    record_type: 'illness',
    date: '2025-04-28',
    description: '肠胃不适',
    notes: '已治愈'
  },
  // 添加更多6月的记录让日历更丰富
  {
    id: 13,
    record_type: 'deworming',
    date: '2025-06-12',
    description: '体外驱虫',
    notes: '滴剂驱虫'
  },
  {
    id: 14,
    record_type: 'medication',
    date: '2025-06-14',
    description: '维生素补充',
    notes: '每日维生素'
  },
  {
    id: 15,
    record_type: 'checkup',
    date: '2025-06-16',
    description: '眼部检查',
    notes: '眼部健康'
  },
  {
    id: 16,
    record_type: 'vaccination',
    date: '2025-06-08',
    description: '狂犬疫苗加强针',
    notes: '年度加强'
  },
  {
    id: 17,
    record_type: 'illness',
    date: '2025-06-02',
    description: '皮肤过敏',
    notes: '已用药治疗'
  },
  {
    id: 18,
    record_type: 'surgery',
    date: '2025-06-04',
    description: '牙齿清洁',
    notes: '口腔护理'
  },
  {
    id: 19,
    record_type: 'medication',
    date: '2025-06-06',
    description: '心脏保健药',
    notes: '预防性用药'
  },
  {
    id: 20,
    record_type: 'deworming',
    date: '2025-06-09',
    description: '体内驱虫',
    notes: '季度驱虫'
  }
])

// 记录类型配置
const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病', color: '#F56C6C' },
  { value: 'medication', label: '用药', color: '#909399' },
  { value: 'surgery', label: '手术', color: '#F56C6C' },
  { value: 'other', label: '其他', color: '#C0C4CC' }
])

// 颜色映射
const colorMapping = reactive({
  vaccination: 'vaccination',
  deworming: 'deworming',
  checkup: 'checkup',
  illness: 'illness',
  medication: 'medication',
  surgery: 'surgery',
  other: 'other'
})

// 标签映射
const labelMapping = reactive({
  vaccination: '疫苗接种',
  deworming: '驱虫',
  checkup: '体检',
  illness: '疾病',
  medication: '用药',
  surgery: '手术',
  other: '其他'
})

// 模拟提醒事项数据
const reminderRecords = ref([
  {
    id: 1,
    category: 'vaccination',
    due_date: '2024-06-28',
    title: '疫苗接种提醒',
    description: '狂犬病疫苗第二针'
  },
  {
    id: 2,
    category: 'medication',
    due_date: '2024-06-30',
    title: '用药提醒',
    description: '心脏病药物'
  },
  {
    id: 3,
    category: 'checkup',
    due_date: '2024-07-05',
    title: '体检提醒',
    description: '季度常规体检'
  },
  {
    id: 4,
    category: 'grooming',
    due_date: '2024-07-01',
    title: '美容护理',
    description: '洗澡和修剪指甲'
  }
])

// 提醒类型配置
const reminderTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'medication', label: '用药提醒', color: '#E6A23C' },
  { value: 'checkup', label: '体检提醒', color: '#409EFF' },
  { value: 'grooming', label: '美容护理', color: '#9C27B0' },
  { value: 'feeding', label: '喂食提醒', color: '#FF9800' },
  { value: 'exercise', label: '运动提醒', color: '#4CAF50' },
  { value: 'other', label: '其他', color: '#C0C4CC' }
])

// 提醒颜色映射
const reminderColorMapping = reactive({
  vaccination: 'vaccination',
  medication: 'medication',
  checkup: 'checkup',
  grooming: 'other',
  feeding: 'deworming',
  exercise: 'vaccination',
  other: 'other'
})

// 提醒标签映射
const reminderLabelMapping = reactive({
  vaccination: '疫苗接种',
  medication: '用药提醒',
  checkup: '体检提醒',
  grooming: '美容护理',
  feeding: '喂食提醒',
  exercise: '运动提醒',
  other: '其他'
})

// 事件处理函数
const addEventLog = (event, data) => {
  const now = new Date()
  eventLogs.value.unshift({
    time: now.toLocaleTimeString(),
    event,
    data: JSON.stringify(data)
  })

  // 限制日志数量
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10)
  }
}

const handleDateClick = (dateStr, records) => {
  addEventLog('事件记录日期点击', { date: dateStr, recordCount: records.length })
}

const handleCalendarViewChange = (newView) => {
  addEventLog('事件记录视图切换', { view: newView })
}

const handleDateChange = (newDate) => {
  addEventLog('事件记录日期变化', { date: newDate.toISOString().split('T')[0] })
}

const handleReminderDateClick = (dateStr, records) => {
  addEventLog('提醒事项日期点击', { date: dateStr, recordCount: records.length })
}

const handleReminderCalendarViewChange = (newView) => {
  addEventLog('提醒事项视图切换', { view: newView })
}

const handleReminderDateChange = (newDate) => {
  addEventLog('提醒事项日期变化', { date: newDate.toISOString().split('T')[0] })
}
</script>

<style scoped>
.calendar-view-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 18px;
  color: #909399;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 组件信息 */
.component-info {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  text-align: center;
  padding: 16px;
  background: #F5F7FA;
  border-radius: 12px;
}

.info-item h3 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item p {
  color: #606266;
  font-weight: 500;
}

.status.stable {
  color: #67C23A;
  background: #F0F9FF;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
}

/* 演示区域 */
.demo-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.demo-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 20px;
  border-bottom: 2px solid #F5F7FA;
  padding-bottom: 12px;
}

.demo-content {
  background: #FAFBFC;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #E5E7EB;
}

/* Props 表格 */
.props-section,
.events-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.props-section h2,
.events-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 20px;
}

.props-table table,
.events-table table {
  width: 100%;
  border-collapse: collapse;
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.props-table th,
.events-table th {
  background: #F5F7FA;
  color: #303133;
  font-weight: 600;
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #E5E7EB;
}

.props-table td,
.events-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #F5F7FA;
  color: #606266;
}

.props-table code,
.events-table code {
  background: #F5F7FA;
  color: #409EFF;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;
}

/* 使用示例 */
.usage-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.usage-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 20px;
}

.code-example {
  background: #F8F9FA;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #E5E7EB;
}

.code-example h3 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 16px;
}

.code-example pre {
  background: #2D3748;
  color: #E2E8F0;
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 14px;
  line-height: 1.5;
}

.code-example code {
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
}

/* 特性网格 */
.features-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.features-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 20px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.feature-card {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.feature-card h3 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 16px;
}

.feature-card p {
  color: #909399;
  line-height: 1.5;
  font-size: 14px;
}

/* 样式定制 */
.customization-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.customization-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 20px;
}

.customization-content h3 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 16px;
}

.style-mapping {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.mapping-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #F5F7FA;
  border-radius: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.dot.vaccination { background-color: #67C23A; }
.dot.medication { background-color: #E6A23C; }
.dot.checkup { background-color: #409EFF; }
.dot.illness { background-color: #F56C6C; }
.dot.other { background-color: #C0C4CC; }

.mapping-item span {
  color: #606266;
  font-size: 14px;
}

/* 事件日志 */
.event-log-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.event-log-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 20px;
}

.event-log {
  background: #F8F9FA;
  border-radius: 12px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #E5E7EB;
}

.no-events {
  text-align: center;
  color: #909399;
  font-style: italic;
  padding: 20px;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: #FFFFFF;
  border-radius: 8px;
  border: 1px solid #F0F2F5;
  font-size: 13px;
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-time {
  color: #909399;
  font-weight: 500;
  min-width: 80px;
}

.log-event {
  color: #409EFF;
  font-weight: 600;
  min-width: 120px;
}

.log-data {
  color: #606266;
  font-family: 'Monaco', 'Consolas', monospace;
  background: #F5F7FA;
  padding: 2px 6px;
  border-radius: 4px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calendar-view-demo {
    padding: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .style-mapping {
    grid-template-columns: 1fr;
  }

  .props-table,
  .events-table {
    overflow-x: auto;
  }

  .log-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .log-time,
  .log-event {
    min-width: auto;
  }

  .log-data {
    width: 100%;
    white-space: normal;
    word-break: break-all;
  }
}

@media (max-width: 480px) {
  .page-header h1 {
    font-size: 24px;
  }

  .page-description {
    font-size: 16px;
  }

  .demo-section,
  .props-section,
  .events-section,
  .usage-section,
  .features-section,
  .customization-section,
  .event-log-section {
    padding: 16px;
  }

  .demo-content {
    padding: 16px;
  }

  .code-example pre {
    padding: 12px;
    font-size: 12px;
  }
}
</style>
