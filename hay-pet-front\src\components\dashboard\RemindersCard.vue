<template>
  <el-card v-if="hasReminders" class="dashboard-card reminders-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">提醒事项</span>
        <el-button type="primary" link @click="router.push('/reminders')">
          <el-icon><Bell /></el-icon>
          查看全部
        </el-button>
      </div>
    </template>
    <div class="reminders-content">
      <div v-if="reminders.length" class="reminders-list">
        <div v-for="reminder in upcomingReminders" :key="reminder.id" class="reminder-item" :class="getReminderClass(reminder)">
          <div class="reminder-info">
            <div class="reminder-header">
              <span class="reminder-title">{{ reminder.title }}</span>
              <el-tag :type="getReminderTagType(reminder)" size="small">
                {{ getReminderStatus(reminder) }}
              </el-tag>
            </div>
            <p class="reminder-notes" v-if="reminder.notes">{{ reminder.notes }}</p>
            <div class="reminder-meta">
              <span class="reminder-date">
                <el-icon><Calendar /></el-icon>
                {{ formatReminderDate(reminder.due_date) }}
              </span>
              <span class="reminder-type">{{ reminder.reminder_type }}</span>
            </div>
          </div>
          <div class="reminder-actions">
            <el-button 
              v-if="!reminder.is_completed" 
              type="success" 
              size="small" 
              @click="markAsCompleted(reminder.id)"
              :loading="completingReminder === reminder.id"
            >
              <el-icon><Check /></el-icon>
              完成
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              link
              @click="editReminder(reminder)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </div>
        </div>
        
        <div v-if="completedCount > 0" class="completed-summary">
          <el-divider>
            <span class="completed-text">已完成 {{ completedCount }} 项提醒</span>
          </el-divider>
        </div>
      </div>
      <el-empty v-else description="暂无提醒事项">
        <el-button type="primary" @click="router.push('/reminders')">
          <el-icon><Plus /></el-icon>
          添加提醒
        </el-button>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Bell, Calendar, Check, Edit, Plus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { usePetStore } from '@/stores/pet';
import { supabase } from '@/utils/supabase';
import type { DashboardBlock } from '@/types/dashboard';

interface Props {
  blockConfig: DashboardBlock;
}

defineProps<Props>();

const router = useRouter();
const petStore = usePetStore();

const reminders = ref<any[]>([]);
const loading = ref(false);
const completingReminder = ref<string | null>(null);

const currentPet = computed(() => petStore.currentPet);

const upcomingReminders = computed(() => {
  return reminders.value
    .filter(reminder => !reminder.is_completed)
    .sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime())
    .slice(0, 5);
});

// 判断是否有待办事项（未完成的提醒）
const hasReminders = computed(() => {
  return upcomingReminders.value.length > 0;
});

const completedCount = computed(() => {
  return reminders.value.filter(reminder => reminder.is_completed).length;
});

async function fetchReminders() {
  if (!currentPet.value?.id) return;
  
  loading.value = true;
  try {
    const { data, error } = await supabase
      .from('reminders')
      .select('*')
      .eq('pet_id', currentPet.value.id)
      .order('due_date', { ascending: true })
      .limit(10);
    
    if (error) throw error;
    reminders.value = data || [];
  } catch (error) {
    console.error('获取提醒事项失败:', error);
  } finally {
    loading.value = false;
  }
}

async function markAsCompleted(reminderId: string) {
  completingReminder.value = reminderId;
  try {
    const { error } = await supabase
      .from('reminders')
      .update({ is_completed: true })
      .eq('id', reminderId);
    
    if (error) throw error;
    
    // 更新本地数据
    const reminder = reminders.value.find(r => r.id === reminderId);
    if (reminder) {
      reminder.is_completed = true;
    }
    
    ElMessage.success('提醒已标记为完成');
  } catch (error) {
    console.error('标记提醒完成失败:', error);
    ElMessage.error('操作失败，请重试');
  } finally {
    completingReminder.value = null;
  }
}

function editReminder(reminder: any) {
  router.push(`/reminders?edit=${reminder.id}`);
}

function getReminderClass(reminder: any): string {
  const now = new Date();
  const reminderDate = new Date(reminder.due_date);
  const diffDays = Math.ceil((reminderDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) return 'overdue';
  if (diffDays === 0) return 'today';
  if (diffDays <= 3) return 'upcoming';
  return 'future';
}

function getReminderTagType(reminder: any): string {
  const reminderClass = getReminderClass(reminder);
  switch (reminderClass) {
    case 'overdue': return 'danger';
    case 'today': return 'warning';
    case 'upcoming': return 'primary';
    default: return 'info';
  }
}

function getReminderStatus(reminder: any): string {
  const now = new Date();
  const reminderDate = new Date(reminder.due_date);
  const diffDays = Math.ceil((reminderDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) return '已逾期';
  if (diffDays === 0) return '今天';
  if (diffDays === 1) return '明天';
  if (diffDays <= 7) return `${diffDays}天后`;
  return '未来';
}

function formatReminderDate(date: string): string {
  return new Date(date).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

onMounted(() => {
  fetchReminders();
});

// 监听当前宠物变化
watch(currentPet, (newPet) => {
  if (newPet) {
    fetchReminders();
  } else {
    reminders.value = [];
  }
}, { immediate: true });
</script>

<style scoped>
.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.reminders-content {
  padding: 0;
}

.reminders-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reminder-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
  transition: all 0.3s ease;
}

.reminder-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.reminder-item.overdue {
  border-color: var(--el-color-danger-light-7);
  background: var(--el-color-danger-light-9);
}

.reminder-item.today {
  border-color: var(--el-color-warning-light-7);
  background: var(--el-color-warning-light-9);
}

.reminder-item.upcoming {
  border-color: var(--el-color-primary-light-7);
  background: var(--el-color-primary-light-9);
}

.reminder-info {
  flex: 1;
  margin-right: 12px;
}

.reminder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reminder-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.reminder-notes {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.reminder-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.reminder-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.reminder-type {
  padding: 2px 6px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  font-size: 11px;
}

.reminder-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.completed-summary {
  margin-top: 8px;
}

.completed-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

@media (max-width: 768px) {
  .reminder-item {
    flex-direction: column;
    gap: 12px;
  }
  
  .reminder-actions {
    flex-direction: row;
    width: 100%;
    justify-content: flex-end;
  }
  
  .reminder-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>