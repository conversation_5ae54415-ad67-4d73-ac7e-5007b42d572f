<template>
  <div id="app">
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header class="top-navigation" style="background: transparent; height: auto; padding: 10px 24px; box-shadow: none;">
        <!-- 左侧：菜单按钮和Logo -->
        <div class="nav-left">
          <el-button 
            v-if="user"
            class="menu-button" 
            @click="toggleSidebar" 
            link
          >
            <el-icon size="24"><Menu /></el-icon>
          </el-button>
          <div class="logo-container" @click="refreshPage" style="cursor: pointer; user-select: none;">
            <img src="./public/logo.png" alt="Hay!Pet Logo" class="logo-image" />
            <h1 class="app-title" style="font-size: 24px; color: #333; margin: 0 0 0 10px;">Hay!Pet</h1>
          </div>
        </div>
        
        <!-- 中间：当前宠物信息 -->
        <div class="nav-center" v-if="user && currentPet">
          <div class="current-pet-info" @click="$router.push('/')">
            <el-avatar class="pet-avatar" :size="32" :src="currentPet.avatar_url">
              {{ currentPet.avatar_url ? '' : currentPet.name.charAt(0) }}
            </el-avatar>
            <span class="pet-name">{{ currentPet.name }}</span>
          </div>
        </div>
        
        <!-- 右侧：用户账户 -->
        <div class="nav-right" v-if="user">
          <div class="user-dropdown" @click="signOut">
            <el-icon><Setting /></el-icon>
            <span style="margin-left: 5px;">退出</span>
          </div>
        </div>
        
        <!-- 登录按钮 -->
        <div class="nav-right" v-if="!user && !showWelcome">
          <el-button @click="showLogin = true" link>登录</el-button>
        </div>
      </el-header>
      
      <!-- 主要内容区域 -->
      <el-container class="main-container">
        <!-- 可收缩侧边栏 -->
        <el-drawer
          v-model="sidebarVisible"
          :with-header="false"
          direction="ltr"
          size="280px"
          class="sidebar-drawer"
        >
          <SidebarMenu v-if="user" @pet-changed="handlePetChanged" />
        </el-drawer>
        
        <!-- 主内容区 -->
        <el-main class="main-content" :class="{ 'full-width': !sidebarVisible }">
          <!-- 登录对话框 -->
          <Login v-if="showLogin && !user" @login-success="handleLoginSuccess" @close="showLogin = false" />
          
          <!-- 主要内容区域 -->
          <div v-if="user">
            <router-view></router-view> <!-- 路由出口 -->
          </div>
          
          <!-- 未登录欢迎页面 -->
          <div v-if="showWelcome" class="welcome-container" v-motion="{ initial: { opacity: 0 }, enter: { opacity: 1, transition: { duration: 500 } } }">
            <div class="welcome-card" v-motion="{ initial: { opacity: 0, scale: 0.9 }, enter: { opacity: 1, scale: 1, transition: { duration: 500, ease: 'back.out(1.7)' } } }">
              <!-- 品牌标识 -->
              <div class="brand-section" v-motion="brandSectionMotion">
                <div class="brand-icon" @mouseenter="animatePawIcon">
                  <div class="paw-icon" ref="pawIconRef" v-motion="{ initial: { opacity: 0, scale: 0, rotate: -30 }, enter: { opacity: 1, scale: 1, rotate: 0, transition: { duration: 600, ease: 'elastic.out(1, 0.3)' } } }">🐾</div>
                </div>
                <h1 class="brand-title animate__animated animate__fadeIn animate__faster">Hay!Pet</h1>
                <p class="brand-subtitle animate__animated animate__fadeIn animate__faster">记录您宠物的成长点滴</p>
              </div>
              
              <!-- 主要内容 -->
              <div class="welcome-content" v-motion="contentMotion">
                <h2 class="welcome-title animate__animated animate__fadeInUp animate__faster">欢迎回来！</h2>
                <p class="welcome-description animate__animated animate__fadeInUp animate__faster">
                  开始记录您毛茸茸朋友的美好时光<br>
                  健康追踪 · 成长记录 · 美好回忆
                </p>
                
                <!-- 配置提示（如果需要） -->
                <div v-if="!isSupabaseConfigured" class="config-notice" v-motion="{ initial: { opacity: 0, y: 20 }, enter: { opacity: 1, y: 0, transition: { delay: 300, duration: 400 } } }">
                  <div class="notice-icon animate__animated animate__pulse animate__infinite">⚠️</div>
                  <div class="notice-content">
                    <h4 class="animate__animated animate__fadeIn animate__faster">配置提示</h4>
                    <p class="animate__animated animate__fadeIn animate__faster">请先配置Supabase连接信息以保存数据</p>
                    <details class="config-details animate__animated animate__fadeIn animate__faster">
                      <summary>查看配置步骤</summary>
                      <ol>
                        <li>复制 <code>.env.example</code> 文件为 <code>.env</code></li>
                        <li>在 <code>.env</code> 文件中填入您的Supabase项目信息</li>
                      </ol>
                    </details>
                  </div>
                </div>
                
                <!-- 行动按钮 -->
                <div class="action-section" v-motion="buttonMotion">
                  <el-button 
                    @click="showLogin = true" 
                    @mouseenter="animateButton"
                    type="primary" 
                    size="large"
                    class="start-button animate__animated animate__bounceIn animate__faster"
                    ref="startButtonRef"
                  >
                    <span>开始使用</span>
                    <el-icon class="button-icon"><ArrowRight /></el-icon>
                  </el-button>
                </div>
              </div>
              
              <!-- 添加背景动画元素 -->
              <div class="animated-background">
                <div class="bg-circle circle-1" v-motion="{ initial: { opacity: 0 }, enter: { opacity: 0.5, transition: { delay: 100, duration: 500 } } }"></div>
                <div class="bg-circle circle-2" v-motion="{ initial: { opacity: 0 }, enter: { opacity: 0.5, transition: { delay: 200, duration: 500 } } }"></div>
                <div class="bg-circle circle-3" v-motion="{ initial: { opacity: 0 }, enter: { opacity: 0.5, transition: { delay: 300, duration: 500 } } }"></div>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 宠物切换对话框 -->
    <el-dialog v-model="showPetSwitcher" title="切换宠物" width="400px">
      <div class="pet-switcher-dialog">
        <el-scrollbar max-height="300px">
          <div 
            v-for="pet in pets" 
            :key="pet.id" 
            class="pet-option" 
            :class="{ active: currentPetId === pet.id.toString() }"
            @click="selectPet(pet.id)"
          >
            <el-avatar :size="40">{{ pet.name.charAt(0) }}</el-avatar>
            <div class="pet-info">
              <div class="pet-name">{{ pet.name }}</div>
              <div class="pet-type">{{ pet.type || '未设置类型' }}</div>
            </div>
            <el-icon v-if="currentPetId === pet.id.toString()" class="check-icon"><Check /></el-icon>
          </div>
        </el-scrollbar>
        <el-divider />
        <el-button type="primary" @click="openAddPetDialog" style="width: 100%;">
          <el-icon><Plus /></el-icon>
          添加新宠物
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, computed, inject } from 'vue'
import { useRouter } from 'vue-router'
import Login from './components/Login.vue'
import SidebarMenu from './components/SidebarMenu.vue'
import { useMotion } from '@vueuse/motion'
import { gsap } from 'gsap'
import { CSSPlugin } from 'gsap/CSSPlugin'
import 'animate.css'
import { 
  Menu, 
  ArrowDown, 
  Setting, 
  Switch, 
  Check, 
  Plus,
  ArrowRight,
  House 
} from '@element-plus/icons-vue'

// 注册GSAP插件 - 确保在所有import之后注册
gsap.registerPlugin(CSSPlugin)

export default {
  name: 'App',
  components: {
    SidebarMenu,
    Login
  },
  setup() {
    const router = useRouter()
    const supabase = inject('supabase')
    const user = ref(null)
    const showLogin = ref(false)
    const isSupabaseConfigured = ref(true)
    
    // 新布局相关状态
    const sidebarVisible = ref(false)
    const showPetSwitcher = ref(false)
    const pets = ref([])
    const currentPetId = ref(localStorage.getItem('currentPetId') || '')
    
    // 计算当前宠物信息
    const currentPet = computed(() => {
      if (!currentPetId.value || !pets.value.length) return null
      return pets.value.find(pet => pet.id.toString() === currentPetId.value)
    })

    // 计算是否显示欢迎页面
    const showWelcome = computed(() => {
      // 只有在用户未登录且没有显示登录框时才显示欢迎页面
      return !user.value && !showLogin.value
    })

    // 检查Supabase是否正确配置
    const checkSupabaseConfig = () => {
      const url = import.meta.env.VITE_SUPABASE_URL
      const key = import.meta.env.VITE_SUPABASE_ANON_KEY
      isSupabaseConfigured.value = url && key && 
        url !== 'https://placeholder.supabase.co' && 
        key !== 'placeholder_anon_key' &&
        url.startsWith('https://') && 
        url.includes('.supabase.co')
    }

    // 检查用户登录状态
    const checkUser = async () => {
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      user.value = currentUser
      if (currentUser) {
        await fetchPets()
      }
    }
    
    // 获取宠物列表
    const fetchPets = async () => {
      if (!isSupabaseConfigured.value || !user.value) {
        return
      }
      
      try {
        const { data, error } = await supabase
          .from('pets')
          .select('*')
          .eq('user_id', user.value.id)
          .order('created_at', { ascending: true })
        
        if (error) throw error
        pets.value = data || []
        
        // 如果没有选中的宠物且有宠物列表，选择第一个
        if (!currentPetId.value && pets.value.length > 0) {
          currentPetId.value = pets.value[0].id.toString()
          localStorage.setItem('currentPetId', currentPetId.value)
        }
        
        // 触发宠物数据更新事件，通知其他组件
        window.dispatchEvent(new CustomEvent('petsUpdated'))
      } catch (error) {
        console.error('获取宠物列表失败:', error)
      }
    }

    // 切换侧边栏显示状态
    const toggleSidebar = () => {
      sidebarVisible.value = !sidebarVisible.value
    }
    
    // 选择宠物
    const selectPet = (petId) => {
      currentPetId.value = petId.toString()
      localStorage.setItem('currentPetId', currentPetId.value)
      showPetSwitcher.value = false
      // 触发全局事件，通知其他组件宠物已切换
      window.dispatchEvent(new CustomEvent('petChanged', { detail: petId }))
    }
    
    // 处理宠物变更
    const handlePetChanged = (petId) => {
      selectPet(petId)
    }
    
    // 打开添加宠物对话框
    const openAddPetDialog = () => {
      showPetSwitcher.value = false
      // 触发侧边栏的添加宠物功能
      window.dispatchEvent(new CustomEvent('openAddPetDialog'))
      sidebarVisible.value = true
    }

    // 处理登录成功
    const handleLoginSuccess = (loggedInUser) => {
      user.value = loggedInUser
      showLogin.value = false
      fetchPets()
    }

    // 退出登录
    const signOut = async () => {
      await supabase.auth.signOut()
      user.value = null
      pets.value = []
      currentPetId.value = ''
      localStorage.removeItem('currentPetId')
      sidebarVisible.value = false
    }

    // 监听认证状态变化
    supabase.auth.onAuthStateChange((event, session) => {
      user.value = session?.user || null
      if (session?.user) {
        fetchPets()
      } else {
        pets.value = []
        currentPetId.value = ''
        localStorage.removeItem('currentPetId')
      }
    })

    onMounted(() => {
      checkSupabaseConfig()
      checkUser()
      
      // 监听宠物列表更新事件
      window.addEventListener('petsUpdated', fetchPets, { passive: true })
    })
    
    // 刷新页面方法
    const refreshPage = () => {
      window.location.reload()
    }

    // 动画相关引用和方法
    const pawIconRef = ref(null)
    const startButtonRef = ref(null)

    // 品牌区域动画配置
    const brandSectionMotion = {
      initial: { opacity: 0, y: -20 },
      enter: { 
        opacity: 1, 
        y: 0,
        transition: { 
          delay: 300,
          duration: 800,
          ease: 'ease-out'
        }
      }
    }

    // 内容区域动画配置
    const contentMotion = {
      initial: { opacity: 0, y: 20 },
      enter: { 
        opacity: 1, 
        y: 0,
        transition: { 
          delay: 600,
          duration: 800,
          ease: 'ease-out'
        }
      }
    }

    // 按钮动画配置
    const buttonMotion = {
      initial: { opacity: 0, scale: 0.9 },
      enter: { 
        opacity: 1, 
        scale: 1,
        transition: { 
          delay: 900,
          duration: 600,
          ease: 'back.out(1.7)'
        }
      }
    }

    // 爪子图标动画
    const animatePawIcon = () => {
      if (pawIconRef.value) {
        gsap.to(pawIconRef.value, {
          rotation: 15,
          scale: 1.2,
          duration: 0.5,
          ease: 'elastic.out(1.2, 0.3)',
          onComplete: () => {
            gsap.to(pawIconRef.value, {
              rotation: 0,
              scale: 1,
              duration: 0.5,
              ease: 'elastic.out(1, 0.3)'
            })
          }
        })
      }
    }

    // 按钮悬停动画
    const animateButton = () => {
      if (startButtonRef.value) {
        gsap.to(startButtonRef.value, {
          scale: 1.05,
          duration: 0.3,
          ease: 'power2.out'
        })

        // 按钮恢复原状
        try {
          // 检查startButtonRef.value是否为DOM元素且有addEventListener方法
          if (startButtonRef.value instanceof Element && typeof startButtonRef.value.addEventListener === 'function') {
            startButtonRef.value.addEventListener('mouseleave', () => {
              if (startButtonRef.value) {
                gsap.to(startButtonRef.value, {
                  scale: 1,
                  duration: 0.3,
                  ease: 'power2.out'
                })
              }
            }, { once: true })
          } else {
            // 如果不是DOM元素，使用gsap的onComplete回调
            setTimeout(() => {
              if (startButtonRef.value) {
                gsap.to(startButtonRef.value, {
                  scale: 1,
                  duration: 0.3,
                  ease: 'power2.out'
                })
              }
            }, 2000) // 2秒后自动恢复，模拟鼠标离开效果
          }
        } catch (error) {
          console.warn('无法添加mouseleave事件监听器:', error)
        }
      }
    }

    // 初始化背景圆圈动画
    onMounted(() => {
      // 背景圆圈动画
      const circles = document.querySelectorAll('.bg-circle')
      if (circles.length) {
        gsap.to(circles, {
          x: 'random(-20, 20)',
          y: 'random(-20, 20)',
          scale: 'random(0.8, 1.2)',
          duration: 'random(10, 20)',
          ease: 'sine.inOut',
          repeat: -1,
          yoyo: true,
          stagger: 2,
        })
      }
    })

    return {
      user,
      showLogin,
      isSupabaseConfigured,
      sidebarVisible,
      showPetSwitcher,
      pets,
      currentPetId,
      currentPet,
      showWelcome,
      toggleSidebar,
      selectPet,
      handlePetChanged,
      openAddPetDialog,
      signOut,
      handleLoginSuccess,
      refreshPage,
      // 动画相关
      pawIconRef,
      startButtonRef,
      brandSectionMotion,
      contentMotion,
      buttonMotion,
      animatePawIcon,
      animateButton,
      // 图标
      Menu,
      ArrowDown,
      Setting,
      Switch,
      Check,
      Plus
    }
  }
}
</script>

<style scoped>
/* 顶部导航栏样式 */
.top-navigation {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  z-index: 1000;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 移除装饰性伪元素 */

.nav-left {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.menu-button {
  background: transparent;
  border: none;
  color: #333;
  transition: all 0.3s ease;
}

.menu-button:hover {
  transform: scale(1.05);
}

.app-title {
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  color: #333;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.current-pet-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 18px;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.current-pet-info:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.pet-avatar {
  background: rgba(148, 163, 184, 0.2);
  color: #64748b;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.current-pet-info:hover .pet-avatar {
  background: rgba(148, 163, 184, 0.3);
  color: #475569;
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
}

.pet-name {
  font-weight: 600;
  font-size: 15px;
  color: #1e293b;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.nav-right {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  color: #333;
}

.user-dropdown:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

/* 登录按钮样式简化 */
.nav-right .el-button {
  background: transparent;
  border: 1px solid #333;
  color: #333;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.nav-right .el-button:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

/* 主容器样式 */
.main-container {
  height: calc(100vh - 60px);
  position: relative;
}

/* 侧边栏抽屉样式 */
.sidebar-drawer {
  z-index: 999;
}

.sidebar-drawer :deep(.el-drawer__body) {
  padding: 0;
}

/* 主内容区样式 */
.main-content {
  padding: 0;
  padding-top: 60px;
  background-color: transparent;
  transition: all 0.3s ease;
  overflow-y: auto;
}

.main-content.full-width {
  width: 100%;
}

/* 简约清新欢迎页面样式 */
.welcome-container {
  min-height: calc(100vh - 60px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, 
    #f8fafc 0%, 
    #e2e8f0 25%, 
    #cbd5e1 50%, 
    #94a3b8 75%, 
    #64748b 100%);
  background-size: 200% 200%;
  animation: gentleShift 20s ease infinite;
  position: relative;
  overflow: hidden;
}

@keyframes gentleShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.welcome-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 48px 40px;
  max-width: 480px;
  width: 100%;
  text-align: center;
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.05),
    0 4px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.5);
  position: relative;
  transform-style: preserve-3d;
  overflow: hidden;
}

/* 背景动画元素 */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1), rgba(6, 182, 212, 0.1));
  opacity: 0.5;
  filter: blur(40px);
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -50px;
  left: -50px;
}

.circle-2 {
  width: 300px;
  height: 300px;
  bottom: -100px;
  right: -50px;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
}

/* 品牌标识区域 */
.brand-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.brand-icon {
  margin-bottom: 16px;
  cursor: pointer;
  display: inline-block;
}

.paw-icon {
  font-size: 48px;
  display: inline-block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
  transform-origin: center center;
  will-change: transform;
}

.brand-title {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #0ea5e9, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
  position: relative;
}

.brand-subtitle {
  color: #64748b;
  font-size: 16px;
  margin: 0;
  font-weight: 500;
  position: relative;
}

/* 欢迎内容区域 */
.welcome-content {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
  letter-spacing: -0.3px;
  position: relative;
}

.welcome-description {
  color: #64748b;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 32px 0;
  font-weight: 400;
  position: relative;
}

/* 配置提示样式 */
.config-notice {
  background: linear-gradient(135deg, #fef7ed, #fed7aa);
  border: 1px solid #fb923c;
  border-radius: 12px;
  padding: 20px;
  margin: 24px 0;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  text-align: left;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease;
}

.config-notice:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(14, 165, 233, 0.15);
  background: linear-gradient(135deg, #e0f2fe, #bae6fd);
  border-color: #0ea5e9;
}

.notice-icon {
  font-size: 20px;
  margin-right: 12px;
  display: inline-block;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.config-details summary {
  cursor: pointer;
  color: #0ea5e9;
  font-weight: 500;
  margin-top: 8px;
  transition: all 0.3s ease;
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
}

.config-details summary:hover {
  color: #0284c7;
  background-color: rgba(14, 165, 233, 0.1);
  text-decoration: underline;
}

.notice-icon {
  font-size: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.notice-content h4 {
  margin: 0 0 8px 0;
  color: #0c4a6e;
  font-size: 16px;
  font-weight: 600;
}

.notice-content p {
  margin: 0 0 12px 0;
  color: #0c4a6e;
  font-size: 14px;
}

.config-details {
  margin-top: 8px;
}

.config-details summary {
  color: #0c4a6e;
  font-size: 14px;
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 8px;
}

.config-details ol {
  margin: 8px 0 0 0;
  padding-left: 20px;
  color: #0c4a6e;
  font-size: 13px;
}

.config-details code {
  background: rgba(14, 165, 233, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
}

/* 行动按钮区域 */
.action-section {
  margin-top: 32px;
}

.start-button {
  background: linear-gradient(135deg, #0ea5e9, #06b6d4);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.start-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 20px rgba(14, 165, 233, 0.3);
  background: linear-gradient(135deg, #0284c7, #0ea5e9);
}

.start-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(14, 165, 233, 0.2);
}

.start-button::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.start-button:hover::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

.button-icon {
  margin-left: 8px;
  font-size: 18px;
  transition: transform 0.3s ease;
}

.start-button:hover .button-icon {
  transform: translateX(4px);
}

.start-button:hover .button-icon {
  transform: translateX(4px);
}

.start-button:hover .button-icon {
  transform: translateX(4px);
}

/* Logo样式 */
.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  height: 32px;
  width: auto;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.logo-container:hover .logo-image {
  transform: scale(1.05);
}

/* 宠物切换对话框样式 */
.pet-switcher-dialog {
  padding: 10px 0;
}

.pet-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.pet-option:hover {
  background-color: #f5f7fa;
}

.pet-option.active {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.pet-info {
  flex: 1;
}

.pet-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.pet-type {
  font-size: 12px;
  color: #999;
}

.check-icon {
  color: #1890ff;
  font-size: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

#app {
  min-height: 100vh;
}

.el-container {
  height: 100vh;
}

.el-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #409eff;
  color: white;
  flex-shrink: 0;
}

.el-header h1 {
  margin: 0;
  font-size: 24px;
}

.sidebar {
  border-right: 1px solid #e6e6e6;
  background-color: #f4f6f8;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-navigation {
    padding: 0 15px;
  }
  
  .nav-center {
    display: none;
  }
  
  .app-title {
    font-size: 18px;
  }
  
  .main-content {
    padding: 15px;
    padding-top: 75px;
  }
  
  /* 欢迎页面移动端适配 */
  .welcome-container {
    padding: 15px;
    min-height: calc(100vh - 60px);
  }
  
  .welcome-card {
    padding: 32px 24px;
    border-radius: 20px;
    max-width: 100%;
  }
  
  .brand-title {
    font-size: 28px;
  }
  
  .welcome-title {
    font-size: 24px;
  }
  
  .paw-icon {
    font-size: 40px;
  }
  
  .start-button {
    padding: 14px 28px;
    font-size: 15px;
  }
  
  .config-notice {
    padding: 16px;
    margin: 20px 0;
  }
}

@media (max-width: 480px) {
  .nav-left {
    gap: 10px;
  }
  
  .app-title {
    font-size: 16px;
  }
  
  .main-content {
    padding: 10px;
    padding-top: 70px;
  }
  
  /* 小屏幕欢迎页面优化 */
  .welcome-container {
    padding: 10px;
  }
  
  .welcome-card {
    padding: 24px 20px;
    border-radius: 16px;
  }
  
  .brand-title {
    font-size: 24px;
  }
  
  .welcome-title {
    font-size: 20px;
  }
  
  .welcome-description {
    font-size: 14px;
  }
  
  .brand-subtitle {
    font-size: 14px;
  }
  
  .paw-icon {
    font-size: 36px;
  }
  
  .start-button {
    padding: 12px 24px;
    font-size: 14px;
    width: 100%;
  }
  
  .floating-element {
    font-size: 20px;
  }
}
</style>