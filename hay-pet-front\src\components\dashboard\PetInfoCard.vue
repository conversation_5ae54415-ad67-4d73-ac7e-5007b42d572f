<template>
  <el-card class="dashboard-card pet-info-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">宠物档案</span>
        <el-button type="primary" link @click="goToPetProfile">
          <el-icon><Edit /></el-icon>
          编辑档案
        </el-button>
      </div>
    </template>
    <div v-if="currentPet" class="pet-info-content">
      <div class="pet-avatar-section">
        <el-avatar :size="80" :src="currentPet.avatar_url" class="pet-avatar">
          <el-icon><User /></el-icon>
        </el-avatar>
        <div class="pet-basic-info">
          <h3 class="pet-name">{{ currentPet.name }}</h3>
          <p class="pet-species">{{ currentPet.type }}</p>
        </div>
      </div>
      <div class="pet-details">
        <div class="detail-item">
          <span class="detail-label">年龄</span>
          <span class="detail-value">{{ calculateAge(currentPet.birth_date) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">性别</span>
          <span class="detail-value">{{ currentPet.gender || '未知' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">当前体重</span>
          <span class="detail-value">{{ formatWeightDisplay(currentPet.latest_weight_kg) }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">品种</span>
          <span class="detail-value">{{ currentPet.species || '未知' }}</span>
        </div>
      </div>
    </div>
    <div v-else class="no-pet-placeholder">
      <el-empty description="暂无宠物信息">
        <el-button type="primary" @click="router.push('/pets')">
          <el-icon><Plus /></el-icon>
          添加第一只宠物
        </el-button>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { Edit, User, Plus } from '@element-plus/icons-vue';
import { usePetStore } from '@/stores/pet';
import { formatWeight } from '@/utils/settings';
import type { DashboardBlock } from '@/types/dashboard';

interface Props {
  blockConfig: DashboardBlock;
}

defineProps<Props>();

const router = useRouter();
const petStore = usePetStore();

const currentPet = computed(() => petStore.currentPet);

function goToPetProfile() {
  router.push('/pet-profile');
}

function calculateAge(birthDate: string | null): string {
  if (!birthDate) return '未知';
  
  const birth = new Date(birthDate);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - birth.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 30) {
    return `${diffDays}天`;
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months}个月`;
  } else {
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    return months > 0 ? `${years}岁${months}个月` : `${years}岁`;
  }
}

function formatWeightDisplay(weight: number | null): string {
  if (!weight) return '未记录';
  return formatWeight(weight);
}
</script>

<style scoped>
/* 卡片整体样式 */
.dashboard-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border: none;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

/* 卡片头部样式 */
:deep(.el-card__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 20px 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card-header .el-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 10px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.card-header .el-button:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 卡片内容样式 */
:deep(.el-card__body) {
  padding: 24px;
}

.pet-info-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pet-avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.pet-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 32px;
  font-weight: bold;
  border: 3px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.pet-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 24px rgba(102, 126, 234, 0.4);
}

.pet-basic-info {
  flex: 1;
}

.pet-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.pet-species {
  margin: 0;
  color: #718096;
  font-size: 16px;
  font-weight: 500;
}

.pet-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.detail-item:hover {
  background: linear-gradient(135deg, #e6f0ff 0%, #d6e8ff 100%);
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.detail-label {
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.detail-value {
  font-weight: 500;
  color: #2d3748;
  font-size: 15px;
}

/* 空状态样式 */
.no-pet-placeholder {
  padding: 40px 20px;
  text-align: center;
}

.no-pet-placeholder .el-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.no-pet-placeholder .el-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pet-avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .pet-details {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}
</style>