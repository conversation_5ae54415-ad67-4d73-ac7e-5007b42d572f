<template>
  <el-card class="pet-form-card">
    <template #header>
      <div class="card-header">
        <span>添加新宠物</span>
      </div>
    </template>
    
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="宠物姓名" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入宠物姓名"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="宠物品种" prop="species">
        <el-input
          v-model="form.species"
          placeholder="请输入宠物品种，如：金毛、英短、布偶猫等"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="年龄" prop="age">
        <el-input-number
          v-model="form.age"
          :min="0"
          :max="30"
          placeholder="请输入年龄"
          style="width: 200px"
        />
        <span style="margin-left: 10px; color: #909399;">岁</span>
      </el-form-item>
      
      <el-form-item label="性别" prop="gender">
        <el-radio-group v-model="form.gender">
          <el-radio value="male">公</el-radio>
          <el-radio value="female">母</el-radio>
          <el-radio value="unknown">未知</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="头像上传">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :on-change="handleAvatarChange"
          :show-file-list="false"
          accept="image/*"
          :limit="1"
        >
          <el-button type="primary" :icon="Upload">选择头像</el-button>
        </el-upload>
        <div v-if="avatarPreview" class="avatar-preview">
          <img :src="avatarPreview" alt="头像预览" />
        </div>
      </el-form-item>
      
      <el-form-item label="当前体重">
        <el-input-number
          v-model="form.weight"
          :min="0"
          :max="200"
          :precision="2"
          placeholder="请输入体重"
          style="width: 200px"
        />
        <span style="margin-left: 10px; color: #909399;">kg</span>
      </el-form-item>
      
      <el-form-item label="备注">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="3"
          placeholder="可以记录一些特殊信息，如健康状况、喜好等"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm" :loading="loading">
          添加宠物
        </el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup>
import { ref, inject } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'

const emit = defineEmits(['pet-added'])

const supabase = inject('supabase')
const formRef = ref()
const uploadRef = ref()
const loading = ref(false)
const avatarFile = ref(null)
const avatarPreview = ref('')

const form = ref({
  name: '',
  species: '',
  age: null,
  gender: 'unknown',
  weight: null,
  notes: ''
})

const rules = {
  name: [
    { required: true, message: '请输入宠物姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '姓名长度在1到50个字符', trigger: 'blur' }
  ],
  species: [
    { required: true, message: '请输入宠物品种', trigger: 'blur' },
    { min: 1, max: 50, message: '品种长度在1到50个字符', trigger: 'blur' }
  ],
  age: [
    { required: true, message: '请输入宠物年龄', trigger: 'blur' },
    { type: 'number', min: 0, max: 30, message: '年龄必须在0到30之间', trigger: 'blur' }
  ]
}

// 处理头像选择
const handleAvatarChange = (file) => {
  avatarFile.value = file.raw
  
  // 创建预览
  const reader = new FileReader()
  reader.onload = (e) => {
    avatarPreview.value = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

// 上传头像到Supabase存储
const uploadAvatar = async (userId) => {
  if (!avatarFile.value) return null
  
  const fileExt = avatarFile.value.name.split('.').pop()
  const fileName = `${userId}/${Date.now()}.${fileExt}`
  
  const { data, error } = await supabase.storage
    .from('pet-media')
    .upload(fileName, avatarFile.value, {
      contentType: avatarFile.value.type,
      cacheControl: '3600',
      upsert: false
    })
  
  if (error) {
    console.error('头像上传失败:', error)
    return null
  }
  
  // 获取公开URL
  const { data: { publicUrl } } = supabase.storage
    .from('pet-media')
    .getPublicUrl(fileName)
  
  return publicUrl
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  loading.value = true
  
  try {
    // 获取当前用户
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      ElMessage.error('请先登录')
      return
    }
    
    // 上传头像（如果有）
    let avatarUrl = null
    if (avatarFile.value) {
      avatarUrl = await uploadAvatar(user.id)
    }
    
    // 准备宠物数据
    const petData = {
      user_id: user.id,
      name: form.value.name,
      species: form.value.species,
      age: form.value.age,
      gender: form.value.gender,
      avatar_url: avatarUrl,
      notes: form.value.notes,
      created_at: new Date().toISOString()
    }
    
    // 插入宠物数据
    const { data, error } = await supabase
      .from('pets')
      .insert([petData])
      .select()
    
    if (error) {
      ElMessage.error(`添加宠物失败: ${error.message}`)
      console.error('添加宠物错误:', error)
    } else {
      ElMessage.success('宠物添加成功！')
      
      // 如果有体重数据，同时添加到体重记录表
      if (form.value.weight && data && data[0]) {
        const weightData = {
          pet_id: data[0].id,
          weight: form.value.weight,
          record_date: new Date().toISOString().split('T')[0]
        }
        
        await supabase
          .from('weight_records')
          .insert([weightData])
      }
      
      resetForm()
      emit('pet-added')
    }
  } catch (err) {
    ElMessage.error('添加宠物过程中发生错误')
    console.error('提交错误:', err)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  avatarFile.value = null
  avatarPreview.value = ''
  form.value = {
    name: '',
    species: '',
    age: null,
    gender: 'unknown',
    weight: null,
    notes: ''
  }
}
</script>

<style scoped>
.pet-form-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.avatar-preview {
  margin-top: 10px;
}

.avatar-preview img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}
</style>