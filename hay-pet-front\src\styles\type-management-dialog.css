/* ========== 类型管理对话框样式 ========== */

/* 对话框整体样式 */
.el-dialog.type-management-dialog {
  --el-dialog-border-radius: 12px;
}

.el-dialog.type-management-dialog .el-dialog__header {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%) !important;
  color: white !important;
  border-radius: 12px 12px 0 0 !important;
  padding: 20px 24px !important;
}

.el-dialog.type-management-dialog .el-dialog__title {
  color: white !important;
  font-weight: 600 !important;
  font-size: 18px !important;
}

.el-dialog.type-management-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white !important;
  font-size: 20px !important;
}

.el-dialog.type-management-dialog .el-dialog__headerbtn .el-dialog__close:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}

.el-dialog.type-management-dialog .el-dialog__body {
  padding: 24px !important;
  background: #fafbfc !important;
}

/* 表单容器 */
.type-form-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 表单区域 */
.form-section {
  margin-bottom: 24px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.required-mark {
  color: #F56C6C;
  margin-left: 4px;
  font-size: 14px;
}

/* 类型名称输入框 */
.type-name-input {
  --el-input-border-radius: 8px;
}

.type-name-input .el-input__inner {
  font-size: 15px;
  padding: 12px 16px;
  border: 2px solid #E4E7ED;
  transition: all 0.3s ease;
}

.type-name-input .el-input__inner:focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 颜色选择区域 */
.color-selection-area {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #E4E7ED;
}

/* 颜色选择器容器 */
.color-picker-container {
  margin-left: auto;
}

/* 统一颜色网格 */
.unified-color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
}

.color-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.color-button:hover {
  transform: scale(1.1);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.25),
    0 0 0 3px rgba(64, 158, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  filter: brightness(1.1) saturate(1.1);
}

.color-button:active {
  transform: scale(1.05);
  transition: transform 0.1s ease;
}

.color-button.selected {
  /* 移除外部蓝色描边，只保留基础样式 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selected-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9) 0%, rgba(103, 194, 58, 0.9) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation: selectedPulse 0.3s ease-out;
}

.selected-indicator .check-icon {
  color: white;
  font-size: 22px;
  font-weight: bold;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.6),
    0 0 8px rgba(255, 255, 255, 0.4);
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5));
  animation: checkIconBounce 0.4s ease-out 0.1s both;
}

@keyframes selectedPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkIconBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  60% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 对话框底部 */
.dialog-footer-custom {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px 24px;
  background: #fafbfc;
  border-radius: 0 0 12px 12px;
}

.cancel-btn {
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;
}

.save-btn {
  padding: 10px 24px;
  border-radius: 8px;
  font-weight: 500;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  border: none;
}

.save-btn:hover {
  background: linear-gradient(135deg, #337ECC 0%, #529B2E 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .type-management-dialog {
    width: 90% !important;
    margin: 5vh auto !important;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .color-picker-container {
    margin-left: 0;
  }

  .unified-color-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
  }

  .color-button {
    width: 36px;
    height: 36px;
  }

  .selected-indicator {
    border: 1.5px solid rgba(255, 255, 255, 0.8);
    box-shadow:
      0 3px 8px rgba(0, 0, 0, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .selected-indicator .check-icon {
    font-size: 18px;
  }
}
