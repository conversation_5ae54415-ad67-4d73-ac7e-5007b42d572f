<!--
  RecordViewsPreset - 记录视图预设组件

  功能特性：
  - 三种视图样式：卡片视图、时间线视图、表格视图
  - 统一的设计语言：24x24px圆形按钮、类别颜色边框、完成状态样式
  - 双击编辑功能
  - 完成状态切换逻辑
  - 响应式设计

  Props:
  - records: Array - 记录数据数组
  - viewMode: String - 当前视图模式 ('cards', 'timeline', 'table')
  - colorMapping: Object - 类别颜色映射
  - labelMapping: Object - 类别标签映射
  - dateField: String - 日期字段名 (默认: 'date')
  - categoryField: String - 类别字段名 (默认: 'category')
  - descriptionField: String - 描述字段名 (默认: 'description')
  - completedField: String - 完成状态字段名 (默认: 'is_completed')

  Events:
  - edit: 编辑记录事件
  - delete: 删除记录事件
  - toggle-completion: 切换完成状态事件
-->

<template>
  <div class="record-views-preset">
    <!-- 视图内容 -->
    <div class="view-content">
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'cards'" class="cards-view">
        <div v-if="records.length > 0" class="records-grid">
          <el-card
            v-for="record in records"
            :key="record.id"
            class="record-card"
            :class="{
              'is-completed': getFieldValue(record, completedField)
            }"
            :style="{ borderColor: getRecordBorderColor(record) }"
            @dblclick="handleEdit(record)"
          >
            <div class="record-content">
              <div class="record-header">
                <div class="record-type-badge">
                  <el-tag size="large">
                    {{ getCategoryLabel(record) }}
                  </el-tag>
                </div>
                <div class="record-date">
                  <el-icon><Calendar /></el-icon>
                  {{ formatDate(getFieldValue(record, dateField)) }}
                </div>
              </div>

              <div class="record-body">
                <div class="record-description">
                  {{ getFieldValue(record, descriptionField) || '无描述' }}
                </div>
              </div>

              <div class="record-actions">
                <div
                  class="completion-button"
                  :class="{ 'is-completed': getFieldValue(record, completedField) }"
                  @click.stop="handleToggleCompletion(record)"
                  :title="getFieldValue(record, completedField) ? '标记为未完成' : '标记为已完成'"
                >
                  <el-icon v-if="getFieldValue(record, completedField)" class="check-icon">
                    <Check />
                  </el-icon>
                </div>
                <div
                  class="delete-button"
                  @click.stop="handleDelete(record)"
                  :title="'删除记录'"
                >
                  <el-icon><Delete /></el-icon>
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <div v-else class="empty-state">
          <el-empty description="暂无记录">
            <slot name="empty-action"></slot>
          </el-empty>
        </div>
      </div>

      <!-- 时间线视图 -->
      <div v-else-if="viewMode === 'timeline'" class="timeline-view">
        <div class="simple-timeline">
          <div v-if="records.length > 0" class="timeline-container">
            <div
              v-for="(group, date) in groupedRecords"
              :key="date"
              class="timeline-group"
            >
              <!-- 日期分组头部 -->
              <div class="group-header">
                <div class="date-label">
                  <span class="date-text">{{ formatGroupDate(date) }}</span>
                  <span class="date-sub">{{ formatGroupDay(date) }}</span>
                </div>
              </div>

              <!-- 该日期的记录列表 -->
              <div class="group-items">
                <div
                  v-for="record in group"
                  :key="record.id"
                  class="timeline-item"
                  :class="{ 'is-completed': getFieldValue(record, completedField) }"
                  :style="{ borderColor: getRecordBorderColor(record) }"
                  @dblclick="handleEdit(record)"
                >
                  <!-- 左侧完成状态按钮 -->
                  <div class="item-completion-left">
                    <div
                      class="completion-button"
                      :class="{ 'is-completed': getFieldValue(record, completedField) }"
                      @click.stop="handleToggleCompletion(record)"
                      :title="getFieldValue(record, completedField) ? '标记为未完成' : '标记为已完成'"
                    >
                      <el-icon v-if="getFieldValue(record, completedField)" class="check-icon">
                        <Check />
                      </el-icon>
                    </div>
                  </div>

                  <!-- 主要内容区域 -->
                  <div class="item-content">
                    <div class="content-header">
                      <div class="item-category">
                        <span
                          class="category-dot"
                          :style="{ backgroundColor: getCategoryColor(record) }"
                        ></span>
                        <span class="category-name">{{ getCategoryLabel(record) }}</span>
                      </div>
                      <div class="item-time">{{ formatTime(getFieldValue(record, dateField)) }}</div>
                    </div>

                    <div v-if="getFieldValue(record, descriptionField)" class="item-description">
                      {{ getFieldValue(record, descriptionField) }}
                    </div>
                  </div>

                  <!-- 右侧删除按钮 -->
                  <div class="item-actions-right">
                    <div
                      class="delete-button"
                      @click.stop="handleDelete(record)"
                      :title="'删除记录'"
                    >
                      <el-icon><Delete /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div v-else class="simple-empty">
            <div class="empty-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="empty-text">
              <h3>暂无记录</h3>
              <p>开始记录您的数据</p>
            </div>
            <slot name="empty-action"></slot>
          </div>
        </div>
      </div>

      <!-- 表格视图 - 完全重构 -->
      <div v-else-if="viewMode === 'table'" class="table-view-new">
        <div class="table-container">
          <div class="table-header-new">
            <h3 class="table-title-new">记录列表</h3>
          </div>

          <div v-if="records.length > 0" class="custom-table">
            <!-- 表格头部 -->
            <div class="table-head">
              <div class="table-row table-header-row">
                <div class="table-cell date-column">日期</div>
                <div class="table-cell category-column">类别</div>
                <div class="table-cell description-column">描述</div>
                <div class="table-cell actions-column">操作</div>
              </div>
            </div>

            <!-- 表格主体 -->
            <div class="table-body">
              <div
                v-for="(record, index) in records"
                :key="record.id"
                class="table-row table-data-row"
                :class="{
                  'is-completed': getFieldValue(record, completedField),
                  'even-row': index % 2 === 0,
                  'odd-row': index % 2 === 1
                }"
                @dblclick="handleEdit(record)"
              >
                <!-- 日期列 -->
                <div class="table-cell date-column">
                  <div class="date-cell-content">
                    <el-icon class="date-icon"><Calendar /></el-icon>
                    <span>{{ formatDate(getFieldValue(record, dateField)) }}</span>
                  </div>
                </div>

                <!-- 类别列 -->
                <div class="table-cell category-column">
                  <el-tag
                    size="large"
                    effect="light"
                    :style="{
                      borderColor: getCategoryColor(record),
                      color: getCategoryColor(record)
                    }"
                  >
                    {{ getCategoryLabel(record) }}
                  </el-tag>
                </div>

                <!-- 描述列 -->
                <div class="table-cell description-column">
                  <span class="description-text-new">
                    {{ getFieldValue(record, descriptionField) || '无描述' }}
                  </span>
                </div>

                <!-- 操作列 -->
                <div class="table-cell actions-column">
                  <div class="action-buttons-container">
                    <!-- 完成状态按钮 -->
                    <button
                      class="action-button completion-btn"
                      :class="{ 'completed': getFieldValue(record, completedField) }"
                      @click.stop="handleToggleCompletion(record)"
                      :title="getFieldValue(record, completedField) ? '标记为未完成' : '标记为已完成'"
                    >
                      <el-icon v-if="getFieldValue(record, completedField)" class="check-icon-new">
                        <Check />
                      </el-icon>
                    </button>

                    <!-- 删除按钮 -->
                    <button
                      class="action-button delete-btn"
                      @click.stop="handleDelete(record)"
                      title="删除记录"
                    >
                      <el-icon><Delete /></el-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="empty-state-new">
            <div class="empty-icon-new">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="empty-text-new">
              <h3>暂无记录</h3>
              <p>开始记录您的数据</p>
            </div>
            <slot name="empty-action"></slot>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Calendar, Delete, Check, TrendCharts } from '@element-plus/icons-vue';

// Props 定义
const props = defineProps({
  // 数据源
  records: {
    type: Array,
    default: () => []
  },
  // 当前视图模式
  viewMode: {
    type: String,
    default: 'cards',
    validator: (value) => ['cards', 'timeline', 'table'].includes(value)
  },
  // 类别颜色映射
  colorMapping: {
    type: Object,
    default: () => ({})
  },
  // 类别标签映射
  labelMapping: {
    type: Object,
    default: () => ({})
  },
  // 字段映射配置
  dateField: {
    type: String,
    default: 'date'
  },
  categoryField: {
    type: String,
    default: 'category'
  },
  descriptionField: {
    type: String,
    default: 'description'
  },
  completedField: {
    type: String,
    default: 'is_completed'
  }
});

// Events 定义
const emit = defineEmits([
  'edit',
  'delete',
  'toggle-completion'
]);

// 计算属性
const groupedRecords = computed(() => {
  const groups = {};
  props.records.forEach(record => {
    const date = getFieldValue(record, props.dateField);
    const dateKey = formatDateKey(date);
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(record);
  });
  return groups;
});

// 工具函数
const getFieldValue = (record, field) => {
  return record[field];
};

const getCategoryColor = (record) => {
  const category = getFieldValue(record, props.categoryField);
  return props.colorMapping[category] || '#409EFF';
};

const getCategoryLabel = (record) => {
  const category = getFieldValue(record, props.categoryField);
  return props.labelMapping[category] || category;
};

const getRecordBorderColor = (record) => {
  const isCompleted = getFieldValue(record, props.completedField);
  return isCompleted ? '#d1d5db' : getCategoryColor(record);
};



const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

const formatTime = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatDateKey = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

const formatGroupDate = (dateKey) => {
  const d = new Date(dateKey);
  return d.toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric'
  });
};

const formatGroupDay = (dateKey) => {
  const d = new Date(dateKey);
  const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return days[d.getDay()];
};

const getTableRowClassName = ({ row, rowIndex }) => {
  let className = rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
  if (getFieldValue(row, props.completedField)) {
    className += ' is-completed';
  }
  return className;
};

// 事件处理函数
const handleEdit = (record) => {
  emit('edit', record);
};

const handleDelete = (record) => {
  emit('delete', record);
};

const handleToggleCompletion = (record) => {
  emit('toggle-completion', record);
};

</script>

<style scoped>
.record-views-preset {
  width: 100%;
}



/* 卡片视图 */
.cards-view {
  margin-bottom: 24px;
}

.records-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.record-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  border: 2px solid;
}

.record-card:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.record-card.selected {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.record-card.is-completed {
  background: #f8f9fa;
  opacity: 0.7;
}

.record-card.is-completed .record-description,
.record-card.is-completed .record-type-badge {
  color: #999999;
}

.record-content {
  padding: 20px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.record-type-badge .el-tag {
  font-size: 13px;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 8px;
}

.record-date {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.record-date .el-icon {
  font-size: 16px;
}

.record-body {
  margin-bottom: 16px;
}

.record-description {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 12px;
  min-height: 44px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.record-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 时间线视图 */
.timeline-view {
  margin-bottom: 24px;
}

.simple-timeline {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.timeline-container {
  padding: 24px;
}

.timeline-group {
  margin-bottom: 32px;
}

.timeline-group:last-child {
  margin-bottom: 0;
}

.group-header {
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.date-label {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.date-text {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.date-sub {
  font-size: 13px;
  color: #6b7280;
}

.group-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeline-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fafbfc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 60px;
}

.timeline-item:hover {
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.timeline-item.is-completed {
  background: #f8f9fa;
  opacity: 0.7;
}

.timeline-item.is-completed .item-description,
.timeline-item.is-completed .category-name {
  color: #999999;
}

.item-completion-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 12px;
}

.item-content {
  flex: 1;
  min-width: 0;
  padding: 0 8px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-category {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-time {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.item-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.5;
}

.item-actions-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: 12px;
}

/* 表格视图 - 完全重构的样式 */
.table-view-new {
  margin-bottom: 24px;
}

.table-container {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.table-header-new {
  padding: 20px 24px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.table-title-new {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.custom-table {
  width: 100%;
}

.table-head {
  background: #f3f4f6;
  border-bottom: 2px solid #e5e7eb;
}

.table-row {
  display: flex;
  align-items: center;
  min-height: 56px;
}

.table-header-row {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  background: #f3f4f6;
}

.table-data-row {
  border-bottom: 1px solid #f3f4f6;
  transition: all 0.2s ease;
  cursor: pointer;
}

.table-data-row:hover {
  background: #f9fafb;
}

.table-data-row.even-row {
  background: #ffffff;
}

.table-data-row.odd-row {
  background: #fafbfc;
}

.table-data-row.is-completed {
  background: #f8f9fa !important;
  opacity: 0.7;
  border-left: 4px solid #d1d5db;
}

.table-data-row.is-completed .table-cell {
  color: #6b7280;
}

.table-cell {
  padding: 16px 20px;
  display: flex;
  align-items: center;
}

.date-column {
  width: 160px;
  flex-shrink: 0;
}

.category-column {
  width: 140px;
  flex-shrink: 0;
}

.description-column {
  flex: 1;
  min-width: 200px;
}

.actions-column {
  width: 140px;
  flex-shrink: 0;
  justify-content: center;
}



.date-cell-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-icon {
  font-size: 16px;
  color: #6b7280;
}

.description-text-new {
  display: block;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #374151;
  font-size: 14px;
}

/* 新的操作按钮样式 */
.action-buttons-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.action-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  outline: none;
}

/* 完成状态按钮 */
.completion-btn {
  border-color: #d1d5db;
  color: #6b7280;
}

.completion-btn:hover {
  border-color: #10b981;
  background: #f0fdf4;
  transform: scale(1.1);
}

.completion-btn.completed {
  border-color: #10b981;
  background: #10b981;
  color: #ffffff;
}

.completion-btn.completed:hover {
  background: #059669;
  border-color: #059669;
  transform: scale(1.1);
}

.check-icon-new {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  animation: checkmark-appear 0.3s ease-in-out;
}

/* 删除按钮 */
.delete-btn {
  border-color: #d1d5db;
  color: #6b7280;
}

.delete-btn:hover {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
  transform: scale(1.1);
}

/* 空状态样式 */
.empty-state-new {
  padding: 60px 24px;
  text-align: center;
  background: #fafbfc;
}

.empty-icon-new {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-text-new h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-text-new p {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
}

/* 旧的表格样式已移除，使用新的重构样式 */

/* 通用按钮样式 */
.completion-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid #d1d5db;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.completion-button:hover {
  border-color: #10b981;
  background: #f0fdf4;
  transform: scale(1.1);
}

.completion-button.is-completed {
  border-color: #10b981;
  background: #10b981;
  color: #ffffff;
}

.completion-button.is-completed:hover {
  background: #059669;
  border-color: #059669;
  transform: scale(1.1);
}

.check-icon {
  font-size: 14px;
  font-weight: bold;
  color: #ffffff;
  animation: checkmark-appear 0.3s ease-in-out;
}

@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.delete-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: transparent;
  border: 2px solid #d1d5db;
  color: #6b7280;
  cursor: pointer;
}

.delete-button:hover {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
}

/* 空状态 */
.empty-state {
  padding: 60px 24px;
  text-align: center;
}

.simple-empty {
  padding: 60px 24px;
  text-align: center;
  background: #fafbfc;
}

.empty-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-text h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-text p {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
}

/* 表格行样式 */
:deep(.el-table .even-row) {
  background-color: #fafafa;
}

:deep(.el-table .odd-row) {
  background-color: #ffffff;
}

:deep(.el-table .is-completed) {
  background-color: #f8f9fa !important;
  opacity: 0.7;
  border-left: 3px solid #d1d5db;
}

:deep(.el-table .is-completed td) {
  color: #999999;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
  vertical-align: middle;
}

/* 操作列的特殊样式 */
:deep(.el-table td:last-child .cell) {
  padding: 0 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 40px !important;
  width: 100% !important;
}



/* 其他列保持默认样式 */
:deep(.el-table td:not(:last-child) .cell) {
  padding: 0 12px;
  min-height: 40px;
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .records-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .timeline-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .item-completion-left,
  .item-actions-right {
    align-self: flex-end;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .record-content {
    padding: 16px;
  }

  .timeline-container {
    padding: 16px;
  }
}
</style>
