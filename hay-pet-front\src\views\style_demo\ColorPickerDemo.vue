<template>
  <div class="color-picker-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🎨 StandardColorPicker 颜色选择器</h1>
        <p class="page-description">
          优化的颜色选择器组件，包含选中动画和渐变效果，提供统一的颜色选择交互体验
        </p>
        <router-link to="/style_demo" class="back-link">
          ← 返回样式系统首页
        </router-link>
      </div>

      <!-- 基础用法 -->
      <div class="demo-section">
        <h2>📋 基础用法</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>标准颜色选择器</h3>
            <StandardColorPicker 
              v-model="basicColor"
              title="选择颜色"
              @change="handleColorChange"
            />
            <div class="result-display">
              <span>当前选择：</span>
              <div class="color-preview" :style="{ backgroundColor: basicColor }"></div>
              <code>{{ basicColor }}</code>
            </div>
          </div>
        </div>
      </div>

      <!-- 尺寸变体 -->
      <div class="demo-section">
        <h2>📏 尺寸变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>不同尺寸</h3>
            <div class="size-demos">
              <div class="size-demo">
                <h4>小号 (sm)</h4>
                <StandardColorPicker 
                  v-model="sizeColors.sm"
                  size="sm"
                  title="小号选择器"
                  :show-header="false"
                />
              </div>
              <div class="size-demo">
                <h4>中号 (md)</h4>
                <StandardColorPicker 
                  v-model="sizeColors.md"
                  size="md"
                  title="中号选择器"
                  :show-header="false"
                />
              </div>
              <div class="size-demo">
                <h4>大号 (lg)</h4>
                <StandardColorPicker 
                  v-model="sizeColors.lg"
                  size="lg"
                  title="大号选择器"
                  :show-header="false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 布局变体 -->
      <div class="demo-section">
        <h2>📐 布局变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>不同列数布局</h3>
            <div class="layout-demos">
              <div class="layout-demo">
                <h4>4列布局</h4>
                <StandardColorPicker 
                  v-model="layoutColors.col4"
                  :columns="4"
                  title="4列布局"
                  :show-header="false"
                />
              </div>
              <div class="layout-demo">
                <h4>6列布局（默认）</h4>
                <StandardColorPicker 
                  v-model="layoutColors.col6"
                  :columns="6"
                  title="6列布局"
                  :show-header="false"
                />
              </div>
              <div class="layout-demo">
                <h4>8列布局</h4>
                <StandardColorPicker 
                  v-model="layoutColors.col8"
                  :columns="8"
                  title="8列布局"
                  :show-header="false"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主题变体 -->
      <div class="demo-section">
        <h2>🎭 主题变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>不同主题样式</h3>
            <div class="theme-demos">
              <div class="theme-demo">
                <h4>标准主题</h4>
                <StandardColorPicker 
                  v-model="themeColors.standard"
                  variant="standard"
                  title="标准主题"
                />
              </div>
              <div class="theme-demo">
                <h4>紧凑主题</h4>
                <StandardColorPicker 
                  v-model="themeColors.compact"
                  variant="compact"
                  title="紧凑主题"
                />
              </div>
              <div class="theme-demo">
                <h4>极简主题</h4>
                <StandardColorPicker 
                  v-model="themeColors.minimal"
                  variant="minimal"
                  title="极简主题"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 自定义配置 -->
      <div class="demo-section">
        <h2>⚙️ 自定义配置</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>自定义颜色集合</h3>
            <StandardColorPicker 
              v-model="customColor"
              title="自定义颜色"
              :predefined-colors="customColors"
              :show-alpha="true"
            />
            <div class="custom-controls">
              <el-button @click="addCustomColor" size="small">
                <el-icon><Plus /></el-icon>
                添加颜色
              </el-button>
              <el-button @click="resetCustomColors" size="small">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 实际应用示例 -->
      <div class="demo-section">
        <h2>💼 实际应用示例</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>标签管理对话框</h3>
            <div class="application-demo">
              <el-button type="primary" @click="showTagDialog = true">
                打开标签管理
              </el-button>
              
              <!-- 模拟对话框 -->
              <el-dialog
                v-model="showTagDialog"
                title="添加记录类型"
                width="480px"
                class="tag-management-dialog"
              >
                <div class="dialog-content">
                  <div class="form-section">
                    <div class="section-header">
                      <span class="section-title">类型名称</span>
                      <span class="required-mark">*</span>
                    </div>
                    <el-input
                      v-model="newTagName"
                      placeholder="请输入类型名称"
                      maxlength="20"
                      show-word-limit
                    />
                  </div>
                  
                  <div class="form-section">
                    <StandardColorPicker 
                      v-model="newTagColor"
                      title="类型颜色"
                      :required="true"
                    />
                  </div>
                </div>
                
                <template #footer>
                  <div class="dialog-footer">
                    <el-button @click="showTagDialog = false">取消</el-button>
                    <el-button type="primary" @click="saveTag">保存</el-button>
                  </div>
                </template>
              </el-dialog>
            </div>
          </div>
        </div>
      </div>

      <!-- 动画效果展示 -->
      <div class="demo-section">
        <h2>🎬 动画效果展示</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>选中状态动画</h3>
            <div class="animation-demo">
              <p>点击颜色按钮查看选中动画效果：</p>
              <StandardColorPicker 
                v-model="animationColor"
                title="动画演示"
                :show-header="false"
                size="lg"
              />
              <div class="animation-description">
                <ul>
                  <li>✨ 脉冲动画：选中时0.3秒缩放脉冲效果</li>
                  <li>🎯 弹跳动画：勾选图标0.4秒弹跳进入动画</li>
                  <li>🌈 渐变背景：蓝绿渐变遮罩 + 白色边框</li>
                  <li>💫 毛玻璃效果：backdrop-filter模糊背景</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- API 文档 -->
      <div class="demo-section">
        <h2>📖 API 文档</h2>
        <div class="api-tables">
          <div class="api-table">
            <h3>Props</h3>
            <table>
              <thead>
                <tr>
                  <th>属性名</th>
                  <th>类型</th>
                  <th>默认值</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>modelValue</td>
                  <td>String</td>
                  <td>'#409EFF'</td>
                  <td>当前选中的颜色值</td>
                </tr>
                <tr>
                  <td>title</td>
                  <td>String</td>
                  <td>'颜色选择'</td>
                  <td>选择器标题</td>
                </tr>
                <tr>
                  <td>size</td>
                  <td>String</td>
                  <td>'md'</td>
                  <td>按钮尺寸：sm/md/lg/xl</td>
                </tr>
                <tr>
                  <td>columns</td>
                  <td>Number</td>
                  <td>6</td>
                  <td>网格列数：4/5/6/8</td>
                </tr>
                <tr>
                  <td>variant</td>
                  <td>String</td>
                  <td>'standard'</td>
                  <td>主题变体：standard/compact/minimal</td>
                </tr>
                <tr>
                  <td>showHeader</td>
                  <td>Boolean</td>
                  <td>true</td>
                  <td>是否显示标题区域</td>
                </tr>
                <tr>
                  <td>showAlpha</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>是否支持透明度</td>
                </tr>
                <tr>
                  <td>required</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>是否显示必填标记</td>
                </tr>
                <tr>
                  <td>disabled</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>是否禁用</td>
                </tr>
                <tr>
                  <td>predefinedColors</td>
                  <td>Array</td>
                  <td>默认色板</td>
                  <td>预设颜色数组</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="api-table">
            <h3>Events</h3>
            <table>
              <thead>
                <tr>
                  <th>事件名</th>
                  <th>参数</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>update:modelValue</td>
                  <td>color: string</td>
                  <td>颜色值变化时触发</td>
                </tr>
                <tr>
                  <td>change</td>
                  <td>color: string</td>
                  <td>颜色改变时触发</td>
                </tr>
                <tr>
                  <td>select</td>
                  <td>color: string</td>
                  <td>选择预设颜色时触发</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="api-table">
            <h3>Methods</h3>
            <table>
              <thead>
                <tr>
                  <th>方法名</th>
                  <th>参数</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>selectColor</td>
                  <td>color: string</td>
                  <td>程序化选择颜色</td>
                </tr>
                <tr>
                  <td>getCurrentColor</td>
                  <td>-</td>
                  <td>获取当前颜色值</td>
                </tr>
                <tr>
                  <td>resetColor</td>
                  <td>-</td>
                  <td>重置为默认颜色</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Refresh } from '@element-plus/icons-vue'
import StandardColorPicker from '@/components/common/StandardColorPicker.vue'

// 响应式数据
const basicColor = ref('#409EFF')
const animationColor = ref('#67C23A')
const customColor = ref('#FF6B6B')

const sizeColors = ref({
  sm: '#E6A23C',
  md: '#409EFF',
  lg: '#67C23A'
})

const layoutColors = ref({
  col4: '#F56C6C',
  col6: '#409EFF',
  col8: '#9C27B0'
})

const themeColors = ref({
  standard: '#409EFF',
  compact: '#67C23A',
  minimal: '#E6A23C'
})

// 对话框相关
const showTagDialog = ref(false)
const newTagName = ref('')
const newTagColor = ref('#409EFF')

// 自定义颜色
const customColors = ref([
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
  '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
  '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA'
])

// 方法
const handleColorChange = (color) => {
  ElMessage.success(`颜色已更改为: ${color}`)
}

const addCustomColor = () => {
  const randomColor = '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')
  customColors.value.push(randomColor)
  ElMessage.success(`已添加颜色: ${randomColor}`)
}

const resetCustomColors = () => {
  customColors.value = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
    '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
  ]
  ElMessage.info('已重置为默认颜色')
}

const saveTag = () => {
  if (!newTagName.value.trim()) {
    ElMessage.warning('请输入类型名称')
    return
  }
  ElMessage.success(`已保存标签: ${newTagName.value} (${newTagColor.value})`)
  showTagDialog.value = false
  newTagName.value = ''
  newTagColor.value = '#409EFF'
}
</script>

<style scoped>
/* 引入设计令牌 */
@import '@/styles/design-tokens.css';

.color-picker-demo {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.page-header h1 {
  font-size: var(--font-size-3xl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 800px;
  margin: 0 auto var(--spacing-4);
  line-height: 1.6;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--duration-base) ease;
}

.back-link:hover {
  color: var(--color-primary-dark);
}

/* 演示区块 */
.demo-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
}

.demo-section h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

.showcase-item h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
}

/* 结果显示 */
.result-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-top: var(--spacing-4);
  padding: var(--spacing-3);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
}

.color-preview {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid var(--color-border-base);
  box-shadow: var(--shadow-sm);
}

.result-display code {
  background: var(--color-bg-tertiary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-family: monospace;
  color: var(--color-primary);
}

/* 尺寸演示 */
.size-demos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.size-demo {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.size-demo h4 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  text-align: center;
}

/* 布局演示 */
.layout-demos {
  display: grid;
  gap: var(--spacing-6);
}

.layout-demo {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.layout-demo h4 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  text-align: center;
}

/* 主题演示 */
.theme-demos {
  display: grid;
  gap: var(--spacing-6);
}

.theme-demo {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.theme-demo h4 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  text-align: center;
}

/* 自定义控制 */
.custom-controls {
  display: flex;
  gap: var(--spacing-3);
  margin-top: var(--spacing-4);
  justify-content: center;
}

/* 应用演示 */
.application-demo {
  text-align: center;
  padding: var(--spacing-4);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
}

/* 对话框样式 */
.tag-management-dialog {
  --el-dialog-border-radius: 12px;
}

.tag-management-dialog .el-dialog__header {
  background: var(--gradient-primary);
  color: white;
  border-radius: 12px 12px 0 0;
}

.dialog-content {
  padding: var(--spacing-4);
}

.form-section {
  margin-bottom: var(--spacing-4);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-2);
}

.section-title {
  font-weight: 600;
  color: var(--color-text-primary);
}

.required-mark {
  color: var(--color-danger);
  margin-left: var(--spacing-1);
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: var(--spacing-3);
}

/* 动画演示 */
.animation-demo {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  text-align: center;
}

.animation-description {
  margin-top: var(--spacing-4);
  text-align: left;
}

.animation-description ul {
  list-style: none;
  padding: 0;
}

.animation-description li {
  padding: var(--spacing-1) 0;
  color: var(--color-text-secondary);
}

/* API 表格 */
.api-tables {
  display: grid;
  gap: var(--spacing-6);
}

.api-table h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
}

.api-table table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.api-table th,
.api-table td {
  padding: var(--spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--color-border-light);
}

.api-table th {
  background: var(--color-bg-tertiary);
  font-weight: 600;
  color: var(--color-text-primary);
}

.api-table td {
  color: var(--color-text-secondary);
}

.api-table td:first-child {
  font-family: monospace;
  color: var(--color-primary);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .color-picker-demo {
    padding: var(--spacing-4);
  }
  
  .size-demos {
    grid-template-columns: 1fr;
  }
  
  .custom-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .api-table {
    overflow-x: auto;
  }
}
</style>
