<template>
  <div class="enhanced-view-toggle-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🚀 Enhanced ViewToggle 增强动画视图切换</h1>
        <p class="page-description">
          使用 GSAP 和 VueUse Motion 打造的高级动画效果，包含涟漪、发光、弹跳等丰富交互
        </p>
        <router-link to="/style_demo" class="back-link">
          ← 返回样式系统首页
        </router-link>
      </div>

      <!-- 动画库介绍 -->
      <div class="demo-section">
        <h2>🎨 使用的动画库</h2>
        <div class="libraries-grid">
          <div class="library-card">
            <div class="library-icon">⚡</div>
            <h3>GSAP</h3>
            <p>业界最强大的JavaScript动画库，提供高性能的动画引擎</p>
            <div class="features">
              <span class="feature-tag">硬件加速</span>
              <span class="feature-tag">时间轴控制</span>
              <span class="feature-tag">缓动函数</span>
            </div>
          </div>
          <div class="library-card">
            <div class="library-icon">🎯</div>
            <h3>VueUse Motion</h3>
            <p>Vue3专用的动画库，提供声明式的动画API</p>
            <div class="features">
              <span class="feature-tag">Vue3集成</span>
              <span class="feature-tag">响应式动画</span>
              <span class="feature-tag">手势支持</span>
            </div>
          </div>
          <div class="library-card">
            <div class="library-icon">💫</div>
            <h3>Animate.css</h3>
            <p>经典的CSS动画库，提供即用的动画类</p>
            <div class="features">
              <span class="feature-tag">轻量级</span>
              <span class="feature-tag">即插即用</span>
              <span class="feature-tag">兼容性好</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 增强动画效果展示 -->
      <div class="demo-section">
        <h2>✨ 增强动画效果</h2>
        <div class="effects-showcase">
          <div class="effect-item">
            <h3>🌊 涟漪点击效果</h3>
            <p>点击按钮时产生的水波纹扩散动画</p>
            <div class="demo-area">
              <div class="enhanced-view-toggle" :data-active="rippleActiveIndex" ref="rippleToggleRef">
                <div class="toggle-background"></div>
                <div class="toggle-fill" ref="rippleFillRef"></div>
                <div class="toggle-glow" ref="rippleGlowRef"></div>
                <button
                  v-for="(option, index) in basicOptions"
                  :key="option.value"
                  @click="setRippleActive(index, $event)"
                  :class="['enhanced-toggle-btn', { active: rippleActiveIndex === index }]"
                >
                  <span class="btn-content">{{ option.label }}</span>
                  <div class="btn-ripple"></div>
                </button>
              </div>
            </div>
          </div>

          <div class="effect-item">
            <h3>💫 发光脉冲效果</h3>
            <p>切换时的发光背景脉冲动画</p>
            <div class="demo-area">
              <div class="enhanced-view-toggle" :data-active="glowActiveIndex" ref="glowToggleRef">
                <div class="toggle-background"></div>
                <div class="toggle-fill" ref="glowFillRef"></div>
                <div class="toggle-glow" ref="glowGlowRef"></div>
                <button
                  v-for="(option, index) in glowOptions"
                  :key="option.value"
                  @click="setGlowActive(index, $event)"
                  :class="['enhanced-toggle-btn', { active: glowActiveIndex === index }]"
                >
                  <span class="btn-content">{{ option.label }}</span>
                  <div class="btn-ripple"></div>
                </button>
              </div>
            </div>
          </div>

          <div class="effect-item">
            <h3>🎯 弹跳变形效果</h3>
            <p>填充背景的弹性移动和按钮弹跳</p>
            <div class="demo-area">
              <div class="enhanced-view-toggle small" :data-active="bounceActiveIndex" ref="bounceToggleRef">
                <div class="toggle-background"></div>
                <div class="toggle-fill" ref="bounceFillRef"></div>
                <div class="toggle-glow" ref="bounceGlowRef"></div>
                <button
                  v-for="(option, index) in bounceOptions"
                  :key="option.value"
                  @click="setBounceActive(index, $event)"
                  :class="['enhanced-toggle-btn', { active: bounceActiveIndex === index }]"
                >
                  <span class="btn-content">{{ option.label }}</span>
                  <div class="btn-ripple"></div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 技术实现 -->
      <div class="demo-section">
        <h2>🔧 技术实现</h2>
        <div class="tech-grid">
          <div class="tech-item">
            <h3>GSAP 动画控制</h3>
            <pre><code>// 填充背景移动
gsap.to(fill, {
  x: offset,
  width: width,
  duration: 0.4,
  ease: "power2.out"
});

// 发光脉冲效果
gsap.fromTo(glow, 
  { opacity: 0, scale: 0.8 },
  { 
    opacity: 0.3, 
    scale: 1,
    duration: 0.3,
    ease: "power2.out",
    yoyo: true,
    repeat: 1
  }
);</code></pre>
          </div>
          
          <div class="tech-item">
            <h3>涟漪效果实现</h3>
            <pre><code>const createRippleEffect = (button, event) => {
  const ripple = button.querySelector('.btn-ripple');
  const rect = button.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);
  const x = event.clientX - rect.left - size / 2;
  const y = event.clientY - rect.top - size / 2;
  
  gsap.set(ripple, {
    width: size,
    height: size,
    x: x,
    y: y,
    scale: 0,
    opacity: 0.6
  });
  
  gsap.to(ripple, {
    scale: 1,
    opacity: 0,
    duration: 0.6,
    ease: "power2.out"
  });
};</code></pre>
          </div>
        </div>
      </div>

      <!-- 性能优化 -->
      <div class="demo-section">
        <h2>⚡ 性能优化</h2>
        <div class="optimization-grid">
          <div class="optimization-card">
            <div class="optimization-icon">🚀</div>
            <h3>硬件加速</h3>
            <p>使用 transform 和 opacity 属性，触发 GPU 加速</p>
          </div>
          <div class="optimization-card">
            <div class="optimization-icon">🎯</div>
            <h3>避免重排重绘</h3>
            <p>动画不影响文档流，减少浏览器重新计算</p>
          </div>
          <div class="optimization-card">
            <div class="optimization-icon">⏱️</div>
            <h3>时间轴优化</h3>
            <p>GSAP 的时间轴管理，确保动画流畅执行</p>
          </div>
          <div class="optimization-card">
            <div class="optimization-icon">🔧</div>
            <h3>内存管理</h3>
            <p>动画完成后自动清理，避免内存泄漏</p>
          </div>
        </div>
      </div>

      <!-- 使用指南 -->
      <div class="demo-section">
        <h2>📖 使用指南</h2>
        <div class="usage-steps">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>安装依赖</h3>
              <pre><code>npm install gsap @vueuse/motion animate.css</code></pre>
            </div>
          </div>
          
          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>导入动画库</h3>
              <pre><code>import { gsap } from 'gsap';
import { useMotion } from '@vueuse/motion';
import 'animate.css';</code></pre>
            </div>
          </div>
          
          <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>应用到组件</h3>
              <pre><code>&lt;div class="enhanced-view-toggle" ref="toggleRef"&gt;
  &lt;div class="toggle-fill" ref="fillRef"&gt;&lt;/div&gt;
  &lt;div class="toggle-glow" ref="glowRef"&gt;&lt;/div&gt;
  &lt;!-- 按钮内容 --&gt;
&lt;/div&gt;</code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { gsap } from 'gsap'

// 基础选项
const basicOptions = [
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' }
]

const glowOptions = [
  { value: 'glow1', label: '发光1' },
  { value: 'glow2', label: '发光2' },
  { value: 'glow3', label: '发光3' }
]

const bounceOptions = [
  { value: 'bounce1', label: '弹跳1' },
  { value: 'bounce2', label: '弹跳2' },
  { value: 'bounce3', label: '弹跳3' }
]

// 状态管理
const rippleActiveIndex = ref(0)
const glowActiveIndex = ref(1)
const bounceActiveIndex = ref(2)

// 元素引用
const rippleToggleRef = ref(null)
const rippleFillRef = ref(null)
const rippleGlowRef = ref(null)

const glowToggleRef = ref(null)
const glowFillRef = ref(null)
const glowGlowRef = ref(null)

const bounceToggleRef = ref(null)
const bounceFillRef = ref(null)
const bounceGlowRef = ref(null)

// 涟漪效果
const createRippleEffect = (button, event) => {
  const ripple = button.querySelector('.btn-ripple')
  const rect = button.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  const x = event.clientX - rect.left - size / 2
  const y = event.clientY - rect.top - size / 2
  
  gsap.set(ripple, {
    width: size,
    height: size,
    x: x,
    y: y,
    scale: 0,
    opacity: 0.6
  })
  
  gsap.to(ripple, {
    scale: 1,
    opacity: 0,
    duration: 0.6,
    ease: "power2.out"
  })
}

// 更新填充位置
const updateFillPosition = (container, fill, glow, activeIndex) => {
  if (!container || !fill || activeIndex < 0) return
  
  const buttons = container.querySelectorAll('.enhanced-toggle-btn')
  if (buttons.length === 0) return
  
  let offset = 0
  for (let i = 0; i < activeIndex; i++) {
    offset += buttons[i].offsetWidth
  }
  
  const activeButton = buttons[activeIndex]
  const width = activeButton.offsetWidth
  
  // GSAP 动画：填充背景移动
  gsap.to(fill, {
    x: offset,
    width: width,
    duration: 0.4,
    ease: "power2.out"
  })
  
  // GSAP 动画：发光效果
  if (glow) {
    gsap.to(glow, {
      x: offset,
      width: width,
      duration: 0.4,
      ease: "power2.out"
    })
    
    // 发光脉冲效果
    gsap.fromTo(glow, 
      { opacity: 0, scale: 0.8 },
      { 
        opacity: 0.3, 
        scale: 1,
        duration: 0.3,
        ease: "power2.out",
        yoyo: true,
        repeat: 1
      }
    )
  }
  
  // 按钮弹跳效果
  gsap.fromTo(activeButton,
    { scale: 0.95 },
    { 
      scale: 1,
      duration: 0.3,
      ease: "back.out(1.7)"
    }
  )
  
  // 文字颜色渐变动画
  buttons.forEach((btn, index) => {
    const content = btn.querySelector('.btn-content')
    if (index === activeIndex) {
      gsap.to(content, {
        color: '#ffffff',
        duration: 0.3,
        ease: "power2.out"
      })
    } else {
      gsap.to(content, {
        color: '#606266',
        duration: 0.3,
        ease: "power2.out"
      })
    }
  })
}

// 设置激活状态
const setRippleActive = (index, event) => {
  rippleActiveIndex.value = index
  if (event && event.currentTarget) {
    createRippleEffect(event.currentTarget, event)
  }
  nextTick(() => {
    updateFillPosition(rippleToggleRef.value, rippleFillRef.value, rippleGlowRef.value, index)
  })
}

const setGlowActive = (index, event) => {
  glowActiveIndex.value = index
  if (event && event.currentTarget) {
    createRippleEffect(event.currentTarget, event)
  }
  nextTick(() => {
    updateFillPosition(glowToggleRef.value, glowFillRef.value, glowGlowRef.value, index)
  })
}

const setBounceActive = (index, event) => {
  bounceActiveIndex.value = index
  if (event && event.currentTarget) {
    createRippleEffect(event.currentTarget, event)
  }
  nextTick(() => {
    updateFillPosition(bounceToggleRef.value, bounceFillRef.value, bounceGlowRef.value, index)
  })
}
</script>

<style scoped>
/* 页面基础样式 */
.enhanced-view-toggle-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 18px;
  color: #909399;
  max-width: 800px;
  margin: 0 auto 16px;
  line-height: 1.6;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: #409EFF;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #337ECC;
}

/* 演示区块 */
.demo-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.demo-section h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 20px;
  border-bottom: 2px solid #E4E7ED;
  padding-bottom: 8px;
}

/* 动画库网格 */
.libraries-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.library-card {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.library-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.library-card h3 {
  color: #303133;
  margin-bottom: 8px;
}

.library-card p {
  color: #909399;
  line-height: 1.5;
  margin-bottom: 12px;
}

.features {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.feature-tag {
  background: #409EFF;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 效果展示 */
.effects-showcase {
  display: grid;
  gap: 24px;
}

.effect-item h3 {
  color: #303133;
  margin-bottom: 8px;
}

.effect-item p {
  color: #909399;
  margin-bottom: 16px;
}

.demo-area {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: center;
}

/* 增强的视图切换样式 */
.enhanced-view-toggle {
  position: relative;
  display: flex;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 3px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.enhanced-view-toggle.small {
  padding: 2px;
}

.toggle-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
  border-radius: 8px;
}

.toggle-fill {
  position: absolute;
  top: 3px;
  left: 3px;
  height: calc(100% - 6px);
  background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
  border-radius: 5px;
  z-index: 1;
  box-shadow: 
    0 2px 8px rgba(64, 158, 255, 0.3),
    0 1px 3px rgba(64, 158, 255, 0.4);
  transform-origin: center;
}

.enhanced-view-toggle.small .toggle-fill {
  top: 2px;
  left: 2px;
  height: calc(100% - 4px);
}

.toggle-glow {
  position: absolute;
  top: 3px;
  left: 3px;
  height: calc(100% - 6px);
  background: radial-gradient(ellipse at center, rgba(64, 158, 255, 0.4) 0%, transparent 70%);
  border-radius: 5px;
  z-index: 0;
  opacity: 0;
  filter: blur(4px);
}

.enhanced-view-toggle.small .toggle-glow {
  top: 2px;
  left: 2px;
  height: calc(100% - 4px);
}

.enhanced-toggle-btn {
  position: relative;
  z-index: 2;
  background: transparent;
  border: none;
  border-radius: 5px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  font-weight: 500;
  flex: 1;
  text-align: center;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.enhanced-view-toggle.small .enhanced-toggle-btn {
  padding: 6px 12px;
  font-size: 12px;
}

.enhanced-toggle-btn:hover {
  transform: translateY(-1px);
}

.enhanced-toggle-btn:active {
  transform: translateY(0);
}

.btn-content {
  position: relative;
  z-index: 3;
  color: #606266;
  transition: color 0.3s ease;
}

.enhanced-toggle-btn.active .btn-content {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  pointer-events: none;
  z-index: 1;
}

.enhanced-toggle-btn:hover .btn-content {
  color: #409EFF;
}

.enhanced-toggle-btn.active:hover .btn-content {
  color: white;
}

/* 技术实现网格 */
.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 16px;
}

.tech-item {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
}

.tech-item h3 {
  color: #303133;
  margin-bottom: 12px;
}

.tech-item pre {
  background: #FAFAFA;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 13px;
  line-height: 1.5;
}

.tech-item code {
  color: #409EFF;
}

/* 性能优化网格 */
.optimization-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.optimization-card {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.optimization-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.optimization-card h3 {
  color: #303133;
  margin-bottom: 8px;
}

.optimization-card p {
  color: #909399;
  line-height: 1.5;
}

/* 使用步骤 */
.usage-steps {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.step-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.step-number {
  width: 32px;
  height: 32px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  color: #303133;
  margin-bottom: 8px;
}

.step-content pre {
  background: #F5F7FA;
  padding: 12px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 13px;
}

.step-content code {
  color: #409EFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-view-toggle-demo {
    padding: 16px;
  }
  
  .libraries-grid,
  .tech-grid,
  .optimization-grid {
    grid-template-columns: 1fr;
  }
  
  .step-item {
    flex-direction: column;
  }
}
</style>
