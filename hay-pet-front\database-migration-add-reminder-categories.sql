-- 为提醒事项表添加分类系统的迁移脚本
-- 执行日期: 2024年

-- 添加分类字段到reminders表
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS category TEXT DEFAULT 'other';

-- 添加标签字段（支持多标签）
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';

-- 添加预计耗时字段（分钟）
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS estimated_duration INTEGER DEFAULT 0;

-- 添加提前通知字段（分钟）
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS advance_notice INTEGER DEFAULT 0;

-- 添加重复类型字段
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS repeat_type TEXT DEFAULT 'none' CHECK (repeat_type IN ('none', 'daily', 'weekly', 'monthly', 'yearly', 'custom'));

-- 添加重复间隔字段
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS repeat_interval INTEGER DEFAULT 1;

-- 添加父提醒ID字段（用于重复提醒的关联）
ALTER TABLE public.reminders 
ADD COLUMN IF NOT EXISTS parent_reminder_id UUID REFERENCES public.reminders(id) ON DELETE SET NULL;

-- 创建提醒分类表
CREATE TABLE IF NOT EXISTS public.reminder_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    value TEXT UNIQUE NOT NULL,
    label TEXT NOT NULL,
    color TEXT NOT NULL DEFAULT '#409EFF',
    icon TEXT DEFAULT 'Bell',
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 插入默认的系统分类
INSERT INTO public.reminder_categories (value, label, color, icon, description, is_system, sort_order) VALUES
    ('medical', '医疗健康', '#F56C6C', 'FirstAidKit', '疫苗、体检、用药等医疗相关提醒', TRUE, 1),
    ('grooming', '美容护理', '#E6A23C', 'Brush', '洗澡、美容、修剪等护理提醒', TRUE, 2),
    ('feeding', '喂食营养', '#67C23A', 'Food', '喂食、营养补充等饮食提醒', TRUE, 3),
    ('exercise', '运动娱乐', '#409EFF', 'Trophy', '散步、运动、游戏等活动提醒', TRUE, 4),
    ('training', '训练教育', '#9C27B0', 'School', '训练、教育、行为矫正等提醒', TRUE, 5),
    ('social', '社交活动', '#FF5722', 'Users', '聚会、社交、外出等活动提醒', TRUE, 6),
    ('other', '其他事项', '#909399', 'More', '其他未分类的提醒事项', TRUE, 7)
ON CONFLICT (value) DO NOTHING;

-- 为现有记录设置默认分类
UPDATE public.reminders 
SET category = 'other' 
WHERE category IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_reminders_category ON public.reminders(category);
CREATE INDEX IF NOT EXISTS idx_reminders_due_date ON public.reminders(due_date);
CREATE INDEX IF NOT EXISTS idx_reminders_priority ON public.reminders(priority);
CREATE INDEX IF NOT EXISTS idx_reminders_is_completed ON public.reminders(is_completed);
CREATE INDEX IF NOT EXISTS idx_reminders_repeat_type ON public.reminders(repeat_type);
CREATE INDEX IF NOT EXISTS idx_reminders_parent_id ON public.reminders(parent_reminder_id);
CREATE INDEX IF NOT EXISTS idx_reminder_categories_value ON public.reminder_categories(value);
CREATE INDEX IF NOT EXISTS idx_reminder_categories_sort_order ON public.reminder_categories(sort_order);

-- 启用行级安全策略
ALTER TABLE public.reminder_categories ENABLE ROW LEVEL SECURITY;

-- 创建分类表的RLS策略 - 所有用户都可以查看系统分类
CREATE POLICY "Everyone can view reminder categories" ON public.reminder_categories
    FOR SELECT USING (true);

-- 用户可以创建自定义分类（非系统分类）
CREATE POLICY "Users can insert custom reminder categories" ON public.reminder_categories
    FOR INSERT WITH CHECK (is_system = FALSE);

-- 用户可以更新自定义分类
CREATE POLICY "Users can update custom reminder categories" ON public.reminder_categories
    FOR UPDATE USING (is_system = FALSE);

-- 用户可以删除自定义分类
CREATE POLICY "Users can delete custom reminder categories" ON public.reminder_categories
    FOR DELETE USING (is_system = FALSE);

-- 添加注释
COMMENT ON COLUMN public.reminders.category IS '提醒分类：medical, grooming, feeding, exercise, training, social, other';
COMMENT ON COLUMN public.reminders.tags IS '提醒标签数组，支持多标签';
COMMENT ON COLUMN public.reminders.estimated_duration IS '预计耗时（分钟）';
COMMENT ON COLUMN public.reminders.advance_notice IS '提前通知时间（分钟）';
COMMENT ON COLUMN public.reminders.repeat_type IS '重复类型：none, daily, weekly, monthly, yearly, custom';
COMMENT ON COLUMN public.reminders.repeat_interval IS '重复间隔（与repeat_type配合使用）';
COMMENT ON COLUMN public.reminders.parent_reminder_id IS '父提醒ID，用于重复提醒的关联';

COMMENT ON TABLE public.reminder_categories IS '提醒分类管理表';
COMMENT ON COLUMN public.reminder_categories.value IS '分类值（唯一标识）';
COMMENT ON COLUMN public.reminder_categories.label IS '分类显示名称';
COMMENT ON COLUMN public.reminder_categories.color IS '分类颜色代码';
COMMENT ON COLUMN public.reminder_categories.icon IS '分类图标名称';
COMMENT ON COLUMN public.reminder_categories.is_system IS '是否为系统预设分类';
COMMENT ON COLUMN public.reminder_categories.sort_order IS '排序顺序';

-- 验证迁移结果
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'reminders' 
AND table_schema = 'public'
AND column_name IN ('category', 'tags', 'estimated_duration', 'advance_notice', 'repeat_type', 'repeat_interval', 'parent_reminder_id')
ORDER BY ordinal_position;

-- 验证分类数据
SELECT value, label, color, icon, is_system, sort_order 
FROM public.reminder_categories 
ORDER BY sort_order;
