/**
 * 统计卡片组件预设样式系统
 * 基于健康记录页面统计信息区域的自适应填充布局设计
 * 提供统一的统计卡片样式和响应式布局
 */

/* ========== 基础统计卡片样式 ========== */

.stats-section {
  margin-bottom: var(--spacing-6);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-5);
}

.stat-card {
  border-radius: var(--radius-card);
  overflow: hidden;
  transition: all var(--duration-base) ease;
  position: relative;
  background: #FFFFFF !important; /* 强制使用白色背景，不受暗色主题影响 */
  border: 1px solid var(--color-border-light);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--duration-base) ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-elevated);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: var(--spacing-6);
  gap: var(--spacing-4);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-card);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-1);
  font-weight: var(--font-weight-medium);
}

.stat-value {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-tight);
}

.stat-date {
  font-size: var(--font-size-xs);
  color: var(--color-text-tertiary);
}

/* ========== 统计卡片主题变体 ========== */

/* 总记录数 */
.stat-card.total-records .stat-icon {
  background: linear-gradient(135deg, var(--color-primary), var(--color-success));
}

/* 本月记录 */
.stat-card.month-records .stat-icon {
  background: linear-gradient(135deg, var(--color-warning), var(--color-danger));
}

/* 待处理提醒 */
.stat-card.pending-reminders .stat-icon {
  background: linear-gradient(135deg, var(--color-danger), #FF6B9D);
}

/* 最近活动 */
.stat-card.recent-activity .stat-icon {
  background: linear-gradient(135deg, var(--color-text-secondary), var(--color-text-regular));
}

/* 活动频率分析 */
.stat-card.activity-frequency .stat-icon {
  background: linear-gradient(135deg, #9C27B0, #673AB7);
}

/* 自定义主题 */
.stat-card.custom .stat-icon {
  background: var(--custom-gradient, var(--gradient-primary));
}

/* ========== 统计卡片尺寸变体 ========== */

/* 紧凑尺寸 */
.stat-card.compact .stat-content {
  padding: var(--spacing-4);
  gap: var(--spacing-3);
}

.stat-card.compact .stat-icon {
  width: 40px;
  height: 40px;
  font-size: 20px;
}

.stat-card.compact .stat-value {
  font-size: var(--font-size-lg);
}

/* 大尺寸 */
.stat-card.large .stat-content {
  padding: var(--spacing-8);
  gap: var(--spacing-5);
}

.stat-card.large .stat-icon {
  width: 56px;
  height: 56px;
  font-size: 28px;
}

.stat-card.large .stat-value {
  font-size: var(--font-size-2xl);
}

/* ========== 响应式设计 ========== */

/* 桌面端大屏幕 (≥1200px) */
@media (min-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: var(--spacing-5);
  }
}

/* 平板端 (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--spacing-4);
  }
  
  .stat-content {
    padding: var(--spacing-5);
    gap: var(--spacing-3);
  }
  
  .stat-icon {
    width: 44px;
    height: 44px;
    font-size: 22px;
  }
  
  .stat-value {
    font-size: var(--font-size-lg);
  }
  
  .stat-label {
    font-size: var(--font-size-xs);
  }
  
  .stat-date {
    font-size: 11px;
  }
}

/* 移动端 (<768px) */
@media (max-width: 767px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }
  
  .stat-content {
    padding: var(--spacing-4);
    gap: var(--spacing-2);
  }
  
  .stat-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
  
  .stat-value {
    font-size: var(--font-size-base);
  }
  
  .stat-label {
    font-size: var(--font-size-xs);
  }
  
  .stat-date {
    font-size: 10px;
  }
}

/* ========== 工具类 ========== */

/* 快速应用统计卡片样式的工具类 */
.stats { @apply stats-section; }
.stats-row { @apply stats-grid; }
.stat { @apply stat-card; }

/* 组合类 */
.stats-compact .stat-card { @apply compact; }
.stats-large .stat-card { @apply large; }

/* 主题组合类 */
.stat-primary .stat-icon { background: var(--gradient-primary); }
.stat-success .stat-icon { background: var(--gradient-success); }
.stat-warning .stat-icon { background: var(--gradient-warning); }
.stat-danger .stat-icon { background: var(--gradient-danger); }

/* ========== 动画增强 ========== */

/* 加载状态 */
.stat-card.loading .stat-value {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  color: transparent;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 数值变化动画 */
.stat-value.updating {
  animation: valueUpdate 0.3s ease-in-out;
}

@keyframes valueUpdate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
    color: var(--color-primary);
  }
  100% {
    transform: scale(1);
  }
}

/* 错误状态 */
.stat-card.error {
  border-color: var(--color-danger);
  background: rgba(245, 108, 108, 0.05);
}

.stat-card.error .stat-value {
  color: var(--color-danger);
}
