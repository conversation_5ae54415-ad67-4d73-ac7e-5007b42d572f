-- Database migration to add 'is_active' column to pet_tags table

-- Add is_active column with a default value of TRUE
ALTER TABLE public.pet_tags
ADD COLUMN is_active BOOLEAN DEFAULT TRUE;

-- Set existing tags to active (if not already set)
UPDATE public.pet_tags
SET is_active = TRUE
WHERE is_active IS NULL;

-- Create a policy for users to update their own tag's active status
DROP POLICY IF EXISTS "Users can update own pet tag active status" ON public.pet_tags;
CREATE POLICY "Users can update own pet tag active status" ON public.pet_tags
    FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Optional: Add an index for performance if you expect frequent queries based on active status
-- CREATE INDEX IF NOT EXISTS idx_pet_tags_is_active ON public.pet_tags(is_active); 