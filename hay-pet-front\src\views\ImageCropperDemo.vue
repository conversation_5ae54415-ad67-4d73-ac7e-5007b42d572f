<template>
  <div class="image-cropper-demo">
    <div class="demo-header">
      <h1 class="demo-title">图片裁切面板演示</h1>
      <p class="demo-description">体验全新设计的图片裁切功能，支持拖拽上传、实时预览和一键裁切</p>
    </div>

    <div class="demo-content">
      <!-- 图片上传面板 -->
      <div class="upload-section">
        <h2 class="section-title">
          <el-icon><Upload /></el-icon>
          图片上传
        </h2>
        <ImageUploadPanel
          :max-size="10"
          :aspect-ratio="1"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
          @file-selected="handleFileSelected"
        />
      </div>

      <!-- 功能特性 -->
      <div class="features-section">
        <h2 class="section-title">
          <el-icon><Star /></el-icon>
          设计特性
        </h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Picture /></el-icon>
            </div>
            <h3>现代化设计</h3>
            <p>采用渐变背景、毛玻璃效果和微妙动画，提供现代化的视觉体验</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Crop /></el-icon>
            </div>
            <h3>智能裁切</h3>
            <p>固定1:1比例，80%输出质量，PNG格式，专为头像优化</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><View /></el-icon>
            </div>
            <h3>实时预览</h3>
            <p>实时显示裁切效果，支持旋转和重置操作</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Upload /></el-icon>
            </div>
            <h3>进度反馈</h3>
            <p>详细的上传进度显示，支持手动关闭控制</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Star, Picture, Crop, View } from '@element-plus/icons-vue'
import ImageUploadPanel from '@/components/ImageUploadPanel.vue'

// 处理上传成功
const handleUploadSuccess = (file) => {
  console.log('上传成功:', file)
  ElMessage.success('图片上传成功！')
}

// 处理上传错误
const handleUploadError = (error) => {
  console.error('上传失败:', error)
  ElMessage.error('图片上传失败：' + error.message)
}

// 处理文件选择
const handleFileSelected = (file) => {
  console.log('文件已选择:', file)
}
</script>

<style scoped lang="scss">
.image-cropper-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;

  .demo-header {
    text-align: center;
    margin-bottom: 60px;
    color: white;

    .demo-title {
      font-size: 48px;
      font-weight: 700;
      margin: 0 0 20px 0;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      background: linear-gradient(45deg, #ffffff, #f8f9fa);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .demo-description {
      font-size: 18px;
      margin: 0;
      opacity: 0.9;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .demo-content {
    max-width: 1200px;
    margin: 0 auto;

    .upload-section {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 40px;
      margin-bottom: 40px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .features-section {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .section-title {
      font-size: 24px;
      font-weight: 600;
      color: #495057;
      margin: 0 0 30px 0;
      display: flex;
      align-items: center;
      gap: 12px;

      .el-icon {
        font-size: 28px;
        color: #667eea;
      }
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;

      .feature-card {
        background: white;
        border-radius: 16px;
        padding: 32px 24px;
        text-align: center;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 1px solid #f1f3f4;

        &:hover {
          transform: translateY(-8px);
          box-shadow: 0 16px 40px rgba(102, 126, 234, 0.15);
        }

        .feature-icon {
          width: 64px;
          height: 64px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 20px;
          box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);

          .el-icon {
            font-size: 28px;
            color: white;
          }
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #495057;
          margin: 0 0 12px 0;
        }

        p {
          font-size: 14px;
          color: #6c757d;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-cropper-demo {
    padding: 20px 16px;

    .demo-header {
      margin-bottom: 40px;

      .demo-title {
        font-size: 32px;
      }

      .demo-description {
        font-size: 16px;
      }
    }

    .demo-content {
      .upload-section,
      .features-section {
        padding: 24px;
        margin-bottom: 24px;
      }

      .section-title {
        font-size: 20px;
      }

      .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        .feature-card {
          padding: 24px 20px;
        }
      }
    }
  }
}
</style>
