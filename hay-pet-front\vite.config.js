import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 5195,
    proxy: {
      '/supabase-proxy': {
        target: 'https://eqcelbwpdtmwdnbpbcqy.supabase.co',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/supabase-proxy/, ''),
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
            // 确保请求头中包含正确的Content-Type和字符编码
            if (req.method === 'POST' || req.method === 'PATCH') {
              proxyReq.setHeader('Content-Type', 'application/json; charset=utf-8');
              proxyReq.setHeader('Accept-Charset', 'utf-8');
              // 打印请求体以便调试
              console.log('Request body:', req.body);
            }
            // 打印所有请求头
            console.log('Request headers:', req.headers);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
            // 确保响应头包含正确的字符编码
            if (proxyRes.headers['content-type'] && proxyRes.headers['content-type'].includes('application/json')) {
              proxyRes.headers['content-type'] = 'application/json; charset=utf-8';
            }
            // 打印响应头
            console.log('Response headers:', proxyRes.headers);
          });
        }
      }
    }
  },
  test: {
    environment: 'jsdom'
  }
})
