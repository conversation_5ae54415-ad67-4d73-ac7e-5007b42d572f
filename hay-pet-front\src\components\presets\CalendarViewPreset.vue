<template>
  <div class="calendar-preset">
    <el-card class="calendar-card" shadow="hover">
      <template #header>
        <div class="calendar-header">
          <div class="calendar-title-section">
            <span class="calendar-title">{{ title }}</span>
            <div class="current-date-info">
              {{ formatCurrentDate(selectedDate) }}
            </div>
          </div>
          <div class="calendar-controls">
            <!-- 视图切换按钮 -->
            <EnhancedViewToggle
              v-model="calendarView"
              :options="calendarViewOptions"
              @change="handleCalendarViewChange"
            />
          </div>
        </div>
      </template>

      <div class="calendar-content">
        <!-- 月视图 -->
        <div v-if="calendarView === 'month'" class="month-calendar">
          <div class="month-header">
            <div class="month-navigation">
              <el-button
                @click="previousMonth"
                size="small"
                circle
                :icon="ArrowLeft"
              ></el-button>
              <span
                class="month-title clickable-title"
                @dblclick="goToToday"
                :title="'双击跳转到今天'"
              >{{ currentYear }}年{{ currentMonth + 1 }}月</span>
              <el-button
                @click="nextMonth"
                size="small"
                circle
                :icon="ArrowRight"
              ></el-button>
            </div>
          </div>
          <el-calendar
            v-model="selectedDate"
            class="health-calendar"
            ref="calendarRef"
          >
            <template #date-cell="{ data }">
              <div
                class="calendar-cell"
                :class="{
                  'has-records': getRecordsForDate(data.day).length > 0,
                  'selected-date': isSelectedDate(data.day),
                  'today': isToday(data.day),
                  'other-month': isOtherMonth(data.day)
                }"
                @click="handleDateClick(data.day)"
                @dblclick="handleDateDoubleClick(data.day)"
                @mouseenter="handleDateHover(data.day, $event)"
                @mouseleave="handleDateLeave"
              >
                <div class="date-number">{{ data.day.split('-').pop() }}</div>
                <div class="record-indicators" v-if="getRecordsForDate(data.day).length > 0">
                  <div
                    v-for="record in getRecordsForDate(data.day).slice(0, 3)"
                    :key="record[recordIdField]"
                    :class="['record-dot', getRecordTypeClass(record[recordTypeField])]"
                  ></div>
                  <div
                    v-if="getRecordsForDate(data.day).length > 3"
                    class="more-indicator"
                  >+{{ getRecordsForDate(data.day).length - 3 }}</div>
                </div>
              </div>
            </template>
          </el-calendar>
        </div>

        <!-- 周视图 -->
        <div v-else-if="calendarView === 'week'" class="week-calendar">
          <div class="week-header">
            <div class="week-navigation">
              <el-button
                @click="previousWeek"
                size="small"
                circle
                :icon="ArrowLeft"
              ></el-button>
              <div
                class="week-info clickable-title"
                @dblclick="goToToday"
                :title="'双击跳转到本周'"
              >
                <span class="week-range">{{ currentWeekRange }}</span>
                <span class="week-number">第{{ currentWeekNumber }}周</span>
              </div>
              <el-button
                @click="nextWeek"
                size="small"
                circle
                :icon="ArrowRight"
              ></el-button>
            </div>
          </div>
          <div class="week-grid">
            <div class="week-days-header">
              <div
                v-for="day in weekDays"
                :key="day"
                class="week-day-header"
              >
                {{ day }}
              </div>
            </div>
            <div class="week-dates">
              <div
                v-for="date in currentWeekDates"
                :key="date.dateStr"
                class="week-date-cell"
                :class="{
                  'has-records': getRecordsForDate(date.dateStr).length > 0,
                  'selected-date': isSelectedDate(date.dateStr),
                  'today': date.isToday
                }"
                @click="handleDateClick(date.dateStr)"
                @dblclick="handleDateDoubleClick(date.dateStr)"
                @mouseenter="handleDateHover(date.dateStr, $event)"
                @mouseleave="handleDateLeave"
              >
                <div class="week-date-number">{{ date.day }}</div>
                <div class="week-record-indicators" v-if="getRecordsForDate(date.dateStr).length > 0">
                  <div
                    v-for="record in getRecordsForDate(date.dateStr).slice(0, 2)"
                    :key="record[recordIdField]"
                    :class="['record-dot', getRecordTypeClass(record[recordTypeField])]"
                  ></div>
                  <div
                    v-if="getRecordsForDate(date.dateStr).length > 2"
                    class="more-indicator"
                  >+{{ getRecordsForDate(date.dateStr).length - 2 }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 年视图 -->
        <div v-else-if="calendarView === 'year'" class="year-calendar">
          <div class="year-header">
            <div class="year-navigation">
              <el-button
                @click="previousYear"
                size="small"
                circle
                :icon="ArrowLeft"
              ></el-button>
              <span
                class="year-title clickable-title"
                @dblclick="goToToday"
                :title="'双击跳转到今年'"
              >{{ currentYear }}年</span>
              <el-button
                @click="nextYear"
                size="small"
                circle
                :icon="ArrowRight"
              ></el-button>
            </div>
          </div>
          <div class="year-grid">
            <div
              v-for="(month, index) in yearMonths"
              :key="index"
              class="year-month-cell"
              :class="{
                'has-records': getMonthRecordsCount(index) > 0,
                'current-month': isCurrentMonth(index)
              }"
              @click="goToMonth(index)"
              @dblclick="handleMonthDoubleClick(index)"
            >
              <div class="month-name">{{ month }}</div>
              <div class="month-stats">
                <div class="records-count">{{ getMonthRecordsCount(index) }}条记录</div>
                <div class="records-density" :class="getMonthDensityClass(index)">
                  <div
                    v-for="type in getMonthRecordTypes(index)"
                    :key="type"
                    :class="['density-dot', getRecordTypeClass(type)]"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 悬浮提示框 -->
    <div
      v-if="showTooltip && tooltipRecords.length > 0"
      class="records-tooltip"
      :style="tooltipStyle"
      @mouseenter="handleTooltipMouseEnter"
      @mouseleave="handleTooltipMouseLeave"
    >
      <div class="tooltip-header">
        <el-icon><Calendar /></el-icon>
        {{ formatTooltipDate(tooltipDate) }}
      </div>
      <div class="tooltip-content">
        <div
          v-for="(record, index) in tooltipRecords.slice(0, 5)"
          :key="record[recordIdField]"
          class="tooltip-record"
          @click="handleRecordClick(record)"
          @dblclick="handleRecordDoubleClick(record)"
        >
          <el-tag
            :type="getRecordTypeTag(record[recordTypeField])"
            size="small"
            class="record-type-tag"
          >
            {{ getRecordTypeLabel(record[recordTypeField]) }}
          </el-tag>
          <span class="record-desc">
            {{ truncateDescription(record[recordDescField]) }}
          </span>
        </div>
        <div
          v-if="tooltipRecords.length > 5"
          class="more-records-hint"
        >
          还有 {{ tooltipRecords.length - 5 }} 条记录...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import { ArrowLeft, ArrowRight, Calendar, Document, Bell, Clock, TrendCharts } from '@element-plus/icons-vue'
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue'

// Props
const props = defineProps({
  // 基础配置
  title: {
    type: String,
    default: '日历视图'
  },

  // 数据相关
  records: {
    type: Array,
    default: () => []
  },

  // 字段映射配置
  recordIdField: {
    type: String,
    default: 'id'
  },
  recordTypeField: {
    type: String,
    default: 'record_type'
  },
  recordDateField: {
    type: String,
    default: 'date'
  },
  recordDescField: {
    type: String,
    default: 'description'
  },

  // 记录类型配置
  recordTypes: {
    type: Array,
    default: () => []
  },

  // 颜色映射
  colorMapping: {
    type: Object,
    default: () => ({})
  },

  // 标签映射
  labelMapping: {
    type: Object,
    default: () => ({})
  },

  // 初始视图
  initialView: {
    type: String,
    default: 'month',
    validator: (value) => ['month', 'week', 'year'].includes(value)
  },

  // 初始日期
  initialDate: {
    type: Date,
    default: () => new Date()
  },

  // 统计卡片配置
  showStats: {
    type: Boolean,
    default: true
  },

  // 自定义统计项
  customStatsItems: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'date-click',
  'view-change',
  'date-change',
  'month-change',
  'year-change',
  'record-click',
  'record-edit'
])

// 响应式数据
const selectedDate = ref(new Date(props.initialDate))
const calendarView = ref(props.initialView)
const currentYear = ref(props.initialDate.getFullYear())
const currentMonth = ref(props.initialDate.getMonth())
const calendarRef = ref(null)

// 悬浮提示相关
const showTooltip = ref(false)
const tooltipDate = ref('')
const tooltipRecords = ref([])
const tooltipStyle = ref({})
const tooltipTimer = ref(null)

// 周视图相关
const currentWeekStart = ref(new Date())

// 视图切换选项
const calendarViewOptions = [
  { value: 'month', label: '月视图' },
  { value: 'week', label: '周视图' },
  { value: 'year', label: '年视图' }
]

// 年份选项
const yearOptions = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  // 扩大年份范围：从1990年到未来20年
  for (let i = 1990; i <= currentYear + 20; i++) {
    years.push(i)
  }
  return years
})

// 月份选项
const monthOptions = computed(() => {
  return [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ]
})

// 周视图相关计算属性
const weekDays = computed(() => {
  return ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
})

// 当前周的日期范围显示
const currentWeekRange = computed(() => {
  const start = new Date(currentWeekStart.value)
  const end = new Date(start)
  end.setDate(start.getDate() + 6)

  return `${start.getMonth() + 1}月${start.getDate()}日 - ${end.getMonth() + 1}月${end.getDate()}日`
})

// 当前周数
const currentWeekNumber = computed(() => {
  const start = new Date(currentWeekStart.value)
  const year = start.getFullYear()
  const firstDayOfYear = new Date(year, 0, 1)

  // 计算第一周的开始日期（第一个周日）
  const firstSunday = new Date(firstDayOfYear)
  const dayOfWeek = firstDayOfYear.getDay()
  if (dayOfWeek !== 0) {
    firstSunday.setDate(firstDayOfYear.getDate() - dayOfWeek)
  }

  // 计算当前周是第几周
  const diffTime = start.getTime() - firstSunday.getTime()
  const diffWeeks = Math.floor(diffTime / (7 * 24 * 60 * 60 * 1000)) + 1

  return diffWeeks
})

// 当前周的所有日期
const currentWeekDates = computed(() => {
  const dates = []
  const start = new Date(currentWeekStart.value)
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  for (let i = 0; i < 7; i++) {
    const date = new Date(start)
    date.setDate(start.getDate() + i)
    date.setHours(0, 0, 0, 0)

    dates.push({
      dateStr: date.toISOString().split('T')[0],
      day: date.getDate(),
      isToday: date.getTime() === today.getTime()
    })
  }

  return dates
})

// 年视图相关计算属性
const yearMonths = computed(() => {
  return [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ]
})

// 统计卡片相关计算属性
// 本月记录数
const monthlyRecords = computed(() => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth()

  return props.records.filter(record => {
    const recordDate = new Date(record[props.recordDateField] || record.created_at)
    return recordDate.getFullYear() === currentYear && recordDate.getMonth() === currentMonth
  }).length
})

// 当前月份显示
const currentMonthDisplay = computed(() => {
  const now = new Date()
  return `${now.getFullYear()}年${now.getMonth() + 1}月`
})

// 待处理提醒数量
const pendingReminders = computed(() => {
  const now = new Date()
  return props.records.filter(record => {
    const nextDueDate = record.next_due_date
    if (!nextDueDate) return false
    const dueDate = new Date(nextDueDate)
    return dueDate >= now
  }).length
})

// 最近活动
const recentActivity = computed(() => {
  if (props.records.length === 0) return '暂无记录'

  // 按日期排序，获取最新记录
  const sortedRecords = [...props.records].sort((a, b) => {
    const dateA = new Date(a[props.recordDateField] || a.created_at)
    const dateB = new Date(b[props.recordDateField] || b.created_at)
    return dateB - dateA
  })

  const latestRecord = sortedRecords[0]
  return getRecordTypeLabel(latestRecord[props.recordTypeField])
})

// 最新记录日期
const latestRecordDate = computed(() => {
  if (props.records.length === 0) return '暂无记录'

  const sortedRecords = [...props.records].sort((a, b) => {
    const dateA = new Date(a[props.recordDateField] || a.created_at)
    const dateB = new Date(b[props.recordDateField] || b.created_at)
    return dateB - dateA
  })

  const latestRecord = sortedRecords[0]
  const recordDate = new Date(latestRecord[props.recordDateField] || latestRecord.created_at)
  const now = new Date()
  const diffTime = now - recordDate
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60))
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffHours < 1) return '刚刚'
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  return formatDate(latestRecord[props.recordDateField])
})

// 活动频率分析
const activityFrequency = computed(() => {
  if (props.records.length === 0) return { type: '暂无数据', count: 0 }

  // 统计各类型记录的数量
  const typeCount = {}
  props.records.forEach(record => {
    const type = record[props.recordTypeField] || 'other'
    typeCount[type] = (typeCount[type] || 0) + 1
  })

  // 找出最频繁的类型
  let maxCount = 0
  let mostFrequentType = 'other'

  Object.entries(typeCount).forEach(([type, count]) => {
    if (count > maxCount) {
      maxCount = count
      mostFrequentType = type
    }
  })

  return {
    type: getRecordTypeLabel(mostFrequentType),
    count: maxCount
  }
})

// 统计卡片数据
const statsItems = computed(() => {
  // 如果有自定义统计项，使用自定义的
  if (props.customStatsItems.length > 0) {
    return props.customStatsItems
  }

  // 默认统计项
  const items = [
    {
      key: 'total-records',
      type: 'total-records',
      icon: Document,
      label: '总记录数',
      value: props.records.length,
      date: '全部记录'
    },
    {
      key: 'month-records',
      type: 'month-records',
      icon: Calendar,
      label: '本月记录',
      value: monthlyRecords.value,
      date: currentMonthDisplay.value
    },
    {
      key: 'pending-reminders',
      type: 'pending-reminders',
      icon: Bell,
      label: '待处理提醒',
      value: pendingReminders.value,
      date: '需要关注'
    },
    {
      key: 'recent-activity',
      type: 'recent-activity',
      icon: Clock,
      label: '最近活动',
      value: recentActivity.value,
      date: latestRecordDate.value
    },
    {
      key: 'activity-frequency',
      type: 'activity-frequency',
      icon: TrendCharts,
      label: '活动频率分析',
      value: activityFrequency.value.type,
      date: activityFrequency.value.count > 0 ? `${activityFrequency.value.count} 次记录` : '暂无数据'
    }
  ]

  return items
})

// 方法
// 获取指定日期的记录
const getRecordsForDate = (dateStr) => {
  return props.records.filter(record => {
    const recordDate = new Date(record[props.recordDateField] || record.created_at)
    const targetDate = new Date(dateStr)
    return recordDate.toDateString() === targetDate.toDateString()
  })
}

// 判断是否为选中日期
const isSelectedDate = (dateStr) => {
  const targetDate = new Date(dateStr)
  return targetDate.toDateString() === selectedDate.value.toDateString()
}

// 判断是否为今天
const isToday = (dateStr) => {
  const today = new Date()
  const targetDate = new Date(dateStr)
  return targetDate.toDateString() === today.toDateString()
}

// 判断是否为其他月份的日期
const isOtherMonth = (dateStr) => {
  const date = new Date(dateStr)
  return date.getMonth() !== currentMonth.value || date.getFullYear() !== currentYear.value
}

// 获取记录类型样式类
const getRecordTypeClass = (recordType) => {
  // 使用传入的颜色映射，或默认颜色
  const defaultColors = {
    vaccination: 'vaccination',
    deworming: 'deworming',
    checkup: 'checkup',
    illness: 'illness',
    medication: 'medication',
    allergy: 'allergy',
    surgery: 'surgery',
    other: 'other'
  }

  return props.colorMapping[recordType] || defaultColors[recordType] || 'other'
}

// 获取记录类型标签
const getRecordTypeTag = (recordType) => {
  const tagMapping = {
    vaccination: 'success',
    deworming: 'warning',
    checkup: 'primary',
    illness: 'danger',
    medication: 'info',
    allergy: 'danger',
    surgery: 'danger',
    other: 'info'
  }
  return tagMapping[recordType] || 'info'
}

// 获取记录类型标签文本
const getRecordTypeLabel = (recordType) => {
  const defaultLabels = {
    vaccination: '疫苗接种',
    deworming: '驱虫',
    checkup: '体检',
    illness: '疾病',
    medication: '用药',
    allergy: '过敏',
    surgery: '手术',
    other: '其他'
  }

  return props.labelMapping[recordType] || defaultLabels[recordType] || '其他'
}

// 处理日期点击
const handleDateClick = (dateStr) => {
  const targetDate = new Date(dateStr)
  selectedDate.value = targetDate
  emit('date-click', dateStr, getRecordsForDate(dateStr))
  emit('date-change', targetDate)
}

// 处理日期双击 - 新建功能
const handleDateDoubleClick = (dateStr) => {
  const targetDate = new Date(dateStr)
  emit('date-dblclick', {
    date: dateStr,
    dateObject: targetDate,
    records: getRecordsForDate(dateStr)
  })
}

// 处理月份双击 - 新建当月第一天的记录
const handleMonthDoubleClick = (monthIndex) => {
  const targetDate = new Date(currentYear.value, monthIndex, 1)
  const dateStr = targetDate.toISOString().split('T')[0]
  emit('date-dblclick', {
    date: dateStr,
    dateObject: targetDate,
    records: []
  })
}

// 处理日期悬浮
const handleDateHover = (dateStr, event) => {
  const records = getRecordsForDate(dateStr)
  if (records.length === 0) return

  // 清除之前的定时器
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value)
  }

  tooltipDate.value = dateStr
  tooltipRecords.value = records

  // 延迟显示提示框
  tooltipTimer.value = setTimeout(() => {
    // 获取目标元素
    const target = event.currentTarget || event.target.closest('.calendar-cell')
    if (!target) return

    const rect = target.getBoundingClientRect()

    // 使用fixed定位，相对于视窗
    tooltipStyle.value = {
      position: 'fixed',
      left: `${rect.left + rect.width / 2}px`,
      top: `${rect.top - 10}px`,
      transform: 'translateX(-50%) translateY(-100%)',
      zIndex: 9999,
      pointerEvents: 'auto'
    }

    showTooltip.value = true
  }, 100)
}

// 处理日期离开
const handleDateLeave = () => {
  // 清除定时器
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value)
  }

  // 延迟隐藏提示框
  tooltipTimer.value = setTimeout(() => {
    showTooltip.value = false
  }, 200)
}

// 处理悬浮提示框鼠标进入
const handleTooltipMouseEnter = () => {
  // 清除隐藏定时器，保持提示框显示
  if (tooltipTimer.value) {
    clearTimeout(tooltipTimer.value)
  }
}

// 处理悬浮提示框鼠标离开
const handleTooltipMouseLeave = () => {
  // 延迟隐藏提示框
  tooltipTimer.value = setTimeout(() => {
    showTooltip.value = false
  }, 200)
}

// 处理记录单击事件
const handleRecordClick = (record) => {
  emit('record-click', record)
}

// 处理记录双击事件
const handleRecordDoubleClick = (record) => {
  emit('record-edit', record)
}

// 格式化当前日期
const formatCurrentDate = (date) => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long'
  })
}

// 格式化提示框日期
const formatTooltipDate = (dateStr) => {
  return new Date(dateStr).toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

// 截断描述文本
const truncateDescription = (description) => {
  if (!description) return '无描述'
  return description.length > 20 ? description.substring(0, 20) + '...' : description
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 导航方法
// 获取导航按钮标题
const getNavigationTitle = (direction) => {
  const viewTitles = {
    month: direction === 'prev' ? '上一月' : '下一月',
    week: direction === 'prev' ? '上一周' : '下一周',
    year: direction === 'prev' ? '上一年' : '下一年'
  }
  return viewTitles[calendarView.value] || ''
}

// 统一的导航方法
const previousPeriod = () => {
  if (calendarView.value === 'month') {
    const newDate = new Date(selectedDate.value)
    newDate.setMonth(newDate.getMonth() - 1)
    selectedDate.value = newDate
    currentYear.value = newDate.getFullYear()
    currentMonth.value = newDate.getMonth()
  } else if (calendarView.value === 'week') {
    previousWeek()
  } else if (calendarView.value === 'year') {
    previousYear()
  }
}

const nextPeriod = () => {
  if (calendarView.value === 'month') {
    const newDate = new Date(selectedDate.value)
    newDate.setMonth(newDate.getMonth() + 1)
    selectedDate.value = newDate
    currentYear.value = newDate.getFullYear()
    currentMonth.value = newDate.getMonth()
  } else if (calendarView.value === 'week') {
    nextWeek()
  } else if (calendarView.value === 'year') {
    nextYear()
  }
}

// 回到今天/今周/今年
const goToToday = () => {
  const today = new Date()
  selectedDate.value = today
  currentYear.value = today.getFullYear()
  currentMonth.value = today.getMonth()

  if (calendarView.value === 'week') {
    currentWeekStart.value = initializeWeekStart(today)
  }

  // 添加视觉反馈
  const titleElement = document.querySelector('.clickable-title')
  if (titleElement) {
    titleElement.style.transform = 'scale(0.95)'
    titleElement.style.background = 'rgba(103, 194, 58, 0.2)'
    setTimeout(() => {
      titleElement.style.transform = ''
      titleElement.style.background = ''
    }, 200)
  }

  emit('date-change', today)
}



// 处理日历视图切换
const handleCalendarViewChange = (newView) => {
  // 添加切换动画类
  const calendarContainer = document.querySelector('.calendar-content')
  if (calendarContainer) {
    calendarContainer.classList.add('view-switching')
    setTimeout(() => {
      calendarContainer.classList.remove('view-switching')
    }, 200)
  }

  if (newView === 'week') {
    currentWeekStart.value = initializeWeekStart(selectedDate.value)
  }

  emit('view-change', newView)
}

// 月视图导航方法
const previousMonth = () => {
  const newDate = new Date(selectedDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  selectedDate.value = newDate
  currentYear.value = newDate.getFullYear()
  currentMonth.value = newDate.getMonth()
  emit('date-change', newDate)
}

const nextMonth = () => {
  const newDate = new Date(selectedDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  selectedDate.value = newDate
  currentYear.value = newDate.getFullYear()
  currentMonth.value = newDate.getMonth()
  emit('date-change', newDate)
}

// 处理年份变化
const handleYearChange = (year) => {
  const newDate = new Date(selectedDate.value)
  newDate.setFullYear(year)
  selectedDate.value = newDate
  currentYear.value = year
  emit('date-change', newDate)
}

// 处理月份变化
const handleMonthChange = (month) => {
  const newDate = new Date(selectedDate.value)
  newDate.setMonth(month)
  selectedDate.value = newDate
  currentMonth.value = month
  emit('date-change', newDate)
}

// 周视图导航方法
const previousWeek = () => {
  const newStart = new Date(currentWeekStart.value)
  newStart.setDate(newStart.getDate() - 7)
  currentWeekStart.value = newStart
}

const nextWeek = () => {
  const newStart = new Date(currentWeekStart.value)
  newStart.setDate(newStart.getDate() + 7)
  currentWeekStart.value = newStart
}

// 初始化当前周的开始日期（周日开始）
const initializeWeekStart = (date) => {
  const d = new Date(date)
  const day = d.getDay()
  const diff = d.getDate() - day
  const weekStart = new Date(d.setDate(diff))
  weekStart.setHours(0, 0, 0, 0)
  return weekStart
}

// 年视图导航方法
const previousYear = () => {
  const newDate = new Date(selectedDate.value)
  newDate.setFullYear(newDate.getFullYear() - 1)
  selectedDate.value = newDate
  currentYear.value = newDate.getFullYear()
}

const nextYear = () => {
  const newDate = new Date(selectedDate.value)
  newDate.setFullYear(newDate.getFullYear() + 1)
  selectedDate.value = newDate
  currentYear.value = newDate.getFullYear()
}

// 年视图相关方法
// 获取指定月份的记录数量
const getMonthRecordsCount = (monthIndex) => {
  const year = currentYear.value
  return props.records.filter(record => {
    const recordDate = new Date(record[props.recordDateField] || record.created_at)
    return recordDate.getFullYear() === year && recordDate.getMonth() === monthIndex
  }).length
}

// 获取指定月份的记录类型
const getMonthRecordTypes = (monthIndex) => {
  const year = currentYear.value
  const monthRecords = props.records.filter(record => {
    const recordDate = new Date(record[props.recordDateField] || record.created_at)
    return recordDate.getFullYear() === year && recordDate.getMonth() === monthIndex
  })

  const types = [...new Set(monthRecords.map(record => record[props.recordTypeField]))]
  return types.slice(0, 3) // 最多显示3种类型
}

// 获取月份密度样式类
const getMonthDensityClass = (monthIndex) => {
  const count = getMonthRecordsCount(monthIndex)
  if (count === 0) return 'density-none'
  if (count <= 2) return 'density-low'
  if (count <= 5) return 'density-medium'
  return 'density-high'
}

// 判断是否为当前月
const isCurrentMonth = (monthIndex) => {
  const now = new Date()
  return now.getFullYear() === currentYear.value && now.getMonth() === monthIndex
}

// 跳转到指定月份
const goToMonth = (monthIndex) => {
  const newDate = new Date(currentYear.value, monthIndex, 1)
  selectedDate.value = newDate
  currentMonth.value = monthIndex
  calendarView.value = 'month'
  emit('month-change', monthIndex)
  emit('view-change', 'month')
}

// 生命周期
onMounted(() => {
  // 初始化周视图的开始日期
  currentWeekStart.value = initializeWeekStart(selectedDate.value)
})

// 监听props变化
watch(() => props.initialDate, (newDate) => {
  selectedDate.value = new Date(newDate)
  currentYear.value = newDate.getFullYear()
  currentMonth.value = newDate.getMonth()
  currentWeekStart.value = initializeWeekStart(newDate)
})

watch(() => props.initialView, (newView) => {
  calendarView.value = newView
})
</script>

<style scoped>
/* 日历预设组件样式 */
.calendar-preset {
  position: relative;
}

/* 统计卡片面板 */
.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  background: #FFFFFF !important;
  border: 1px solid #e5e7eb;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 24px;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1.2;
}

.stat-date {
  font-size: 12px;
  color: #C0C4CC;
}

/* 统计卡片主题变体 */
.stat-card.total-records .stat-icon {
  background: linear-gradient(135deg, #409EFF, #67C23A);
}

.stat-card.month-records .stat-icon {
  background: linear-gradient(135deg, #E6A23C, #F56C6C);
}

.stat-card.pending-reminders .stat-icon {
  background: linear-gradient(135deg, #F56C6C, #FF6B9D);
}

.stat-card.recent-activity .stat-icon {
  background: linear-gradient(135deg, #909399, #606266);
}

.stat-card.activity-frequency .stat-icon {
  background: linear-gradient(135deg, #9C27B0, #673AB7);
}

/* 日历面板 */
.calendar-card {
  border-radius: 12px;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.calendar-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.calendar-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.current-date-info {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.calendar-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  flex-wrap: wrap;
}

.navigation-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #606266;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: #409EFF;
  border-color: #409EFF;
  color: white;
  transform: scale(1.05);
}

.date-selectors {
  display: flex;
  align-items: center;
  gap: 8px;
}

.year-selector, .month-selector {
  width: 80px;
}

.today-btn {
  background: #67C23A;
  border-color: #67C23A;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.today-btn:hover {
  background: #5daf34;
  border-color: #5daf34;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.today-btn .el-icon {
  margin-right: 4px;
}

/* 日历内容切换动画 */
.calendar-content {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.calendar-content.view-switching {
  opacity: 0;
  transform: translateY(10px);
}

.health-calendar {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.health-calendar :deep(.el-calendar__body) {
  padding: 0;
}

.health-calendar :deep(.el-calendar__header) {
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid #e5e7eb;
}

.health-calendar :deep(.el-calendar-table) {
  background: #ffffff;
}

.health-calendar :deep(.el-calendar-table thead th) {
  background: #f5f7fa;
  color: #606266;
  font-weight: 600;
  padding: 12px 8px;
  border-bottom: 1px solid #e5e7eb;
}

.health-calendar :deep(.el-calendar-table .el-calendar-day) {
  padding: 0;
  border: none;
}

.health-calendar :deep(.el-calendar-table td) {
  border: none;
  padding: 0;
}

/* 日历单元格样式 */
.calendar-cell {
  position: relative;
  height: 100%;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  min-height: 70px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  background: #ffffff;
  border: 1px solid #f0f2f5;
  margin: 1px;
}

.calendar-cell:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08), rgba(103, 194, 58, 0.08));
  border-color: #409EFF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}



.calendar-cell.has-records {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.03), rgba(103, 194, 58, 0.03));
  border-color: rgba(64, 158, 255, 0.3);
}

.calendar-cell.has-records:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.12), rgba(103, 194, 58, 0.12));
  border-color: #409EFF;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.25);
}

.calendar-cell.selected-date {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2), rgba(103, 194, 58, 0.2));
  border: 2px solid #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 今天日期的特殊样式 - 优化为更自然的效果 */
.calendar-cell.today {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
  border: 2px solid #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  position: relative;
}

.calendar-cell.today .date-number {
  color: #FFFFFF;
  font-weight: 700;
  font-size: 15px;
  background: linear-gradient(135deg, #409EFF, #5daf34);
  box-shadow: 0 1px 4px rgba(64, 158, 255, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

/* 其他月份日期样式 */
.calendar-cell.other-month {
  background: #fafbfc;
  color: #c0c4cc;
  opacity: 0.6;
}

.calendar-cell.other-month:hover {
  background: #f5f7fa;
  opacity: 0.8;
}

.calendar-cell.other-month .date-number {
  color: #c0c4cc;
}

.date-number {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.calendar-cell:hover .date-number {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

/* 今天日期悬浮时保持原有样式 */
.calendar-cell.today:hover .date-number {
  background: linear-gradient(135deg, #409EFF, #5daf34);
  color: #FFFFFF;
}

.record-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
  margin-top: 6px;
  align-items: center;
}

.record-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.8);
  cursor: pointer;
}

.record-dot:hover {
  transform: scale(1.4);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.record-dot.vaccination { background-color: #67C23A; }
.record-dot.deworming { background-color: #E6A23C; }
.record-dot.checkup { background-color: #409EFF; }
.record-dot.illness { background-color: #F56C6C; }
.record-dot.medication { background-color: #909399; }
.record-dot.allergy { background-color: #F56C6C; }
.record-dot.surgery { background-color: #F56C6C; }
.record-dot.other { background-color: #C0C4CC; }

.more-indicator {
  font-size: 9px;
  color: #606266;
  font-weight: 600;
  margin-left: 4px;
  background: rgba(96, 98, 102, 0.1);
  padding: 1px 4px;
  border-radius: 8px;
  border: 1px solid rgba(96, 98, 102, 0.2);
  transition: all 0.3s ease;
}

.more-indicator:hover {
  background: rgba(96, 98, 102, 0.2);
  transform: scale(1.1);
}

/* 月视图样式 */
.month-calendar {
  margin-top: 16px;
}

.month-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 8px;
}

.month-navigation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.month-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  min-width: 120px;
  text-align: center;
}

/* 可点击标题样式 */
.clickable-title {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  padding: 4px 8px;
  user-select: none;
}

.clickable-title:hover {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
  transform: scale(1.02);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.clickable-title:active {
  transform: scale(0.98);
  background: rgba(64, 158, 255, 0.15);
}



/* 隐藏 el-calendar 的默认导航 */
.health-calendar :deep(.el-calendar__header) {
  display: none;
}

/* 周视图样式 */
.week-calendar {
  margin-top: 16px;
}

.week-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 8px;
}

.week-navigation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.week-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.week-range {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.week-number {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

.week-grid {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.week-days-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f5f7fa;
}

.week-day-header {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: #606266;
  border-right: 1px solid #e5e7eb;
}

.week-day-header:last-child {
  border-right: none;
}

.week-dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  min-height: 120px;
}

.week-date-cell {
  position: relative;
  padding: 12px 8px;
  border-right: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.week-date-cell:last-child {
  border-right: none;
}

.week-date-cell:hover {
  background-color: rgba(64, 158, 255, 0.1);
}



.week-date-cell.has-records {
  background-color: rgba(64, 158, 255, 0.05);
}

.week-date-cell.has-records:hover {
  background-color: rgba(64, 158, 255, 0.15);
}

.week-date-cell.selected-date {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.2), rgba(103, 194, 58, 0.2));
  border: 2px solid #409EFF;
}

.week-date-cell.today {
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.1), rgba(64, 158, 255, 0.1));
  font-weight: 600;
}

.week-date-cell.today .week-date-number {
  color: #67C23A;
  font-weight: 700;
}

.week-date-number {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.week-record-indicators {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  justify-content: center;
  max-width: 100%;
}

/* 年视图样式 */
.year-calendar {
  margin-top: 16px;
}

.year-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 8px;
}

.year-navigation {
  display: flex;
  align-items: center;
  gap: 20px;
}

.year-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  min-width: 100px;
  text-align: center;
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.year-month-cell {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.year-month-cell:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}



.year-month-cell.has-records {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(103, 194, 58, 0.05));
}

.year-month-cell.current-month {
  border-color: #67C23A;
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.1), rgba(64, 158, 255, 0.1));
}

.month-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.month-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.records-count {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.records-density {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.density-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

/* 悬浮提示框样式 */
.records-tooltip {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(229, 231, 235, 0.8);
  min-width: 280px;
  max-width: 350px;
  z-index: 9999;
  animation: tooltipFadeIn 0.2s ease-out;
  backdrop-filter: blur(10px);
  position: relative;
  pointer-events: auto;
}

/* 简化的箭头指示器 */
.records-tooltip::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%) scale(1);
  }
}

.tooltip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 18px;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
  border-radius: 12px 12px 0 0;
}

.tooltip-header .el-icon {
  color: #409EFF;
  font-size: 16px;
}

.tooltip-content {
  padding: 14px 18px;
  max-height: 280px;
  overflow-y: auto;
}

.tooltip-record {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
  transition: all 0.2s ease;
  cursor: pointer;
}

.tooltip-record:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.tooltip-record:hover {
  background: rgba(64, 158, 255, 0.05);
  margin: 0 -8px;
  padding-left: 8px;
  padding-right: 8px;
  border-radius: 6px;
}

.tooltip-record:active {
  transform: scale(0.98);
  background: rgba(64, 158, 255, 0.1);
}

.record-type-tag {
  flex-shrink: 0;
  font-weight: 500;
}

.record-desc {
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
  flex: 1;
}

.more-records-hint {
  text-align: center;
  color: #909399;
  font-size: 12px;
  font-style: italic;
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(144, 147, 153, 0.05);
  border-radius: 6px;
  border: 1px dashed rgba(144, 147, 153, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-content {
    padding: 20px;
  }

  .stat-value {
    font-size: 18px;
  }

  .calendar-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .calendar-title-section {
    width: 100%;
  }

  .calendar-controls {
    width: 100%;
    justify-content: space-between;
    flex-direction: column;
    gap: 12px;
  }

  .navigation-buttons {
    order: 2;
    justify-content: center;
  }

  .date-selectors {
    order: 1;
    justify-content: center;
    flex-wrap: wrap;
    gap: 6px;
  }

  .year-selector, .month-selector {
    width: 70px;
  }

  .today-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .today-text {
    display: none;
  }

  .week-calendar {
    font-size: 14px;
  }

  .week-range {
    font-size: 14px;
  }

  .week-number {
    font-size: 12px;
  }

  .week-date-cell {
    padding: 8px 4px;
    min-height: 80px;
  }

  .week-date-number {
    font-size: 16px;
  }

  .year-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .year-month-cell {
    padding: 12px;
    min-height: 100px;
  }

  .month-name {
    font-size: 16px;
  }

  .year-title {
    font-size: 18px;
  }

  .records-tooltip {
    min-width: 250px;
    max-width: 300px;
  }

  .calendar-cell {
    min-height: 50px;
  }
}

@media (max-width: 480px) {
  .calendar-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .calendar-controls {
    flex-direction: column;
    gap: 8px;
  }

  .navigation-buttons {
    justify-content: center;
  }

  .date-selectors {
    justify-content: center;
  }

  .year-selector, .month-selector {
    width: 60px;
  }

  .today-btn {
    padding: 4px 8px;
    font-size: 11px;
  }

  .week-range {
    font-size: 12px;
  }

  .week-number {
    font-size: 10px;
  }

  .week-date-cell {
    padding: 6px 2px;
    min-height: 60px;
  }

  .week-date-number {
    font-size: 14px;
  }

  .week-day-header {
    padding: 8px 4px;
    font-size: 12px;
  }

  .records-tooltip {
    min-width: 200px;
    max-width: 250px;
    font-size: 12px;
  }

  .tooltip-header {
    padding: 8px 12px;
    font-size: 12px;
  }

  .tooltip-content {
    padding: 8px 12px;
  }

  .tooltip-record {
    padding: 4px 0;
  }

  .record-type-tag {
    font-size: 10px;
  }

  .record-desc {
    font-size: 11px;
  }

  .calendar-cell {
    min-height: 40px;
    padding: 2px;
  }

  .date-number {
    font-size: 12px;
  }

  .record-dot {
    width: 4px;
    height: 4px;
  }

  .more-indicator {
    font-size: 8px;
  }

  .year-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .year-month-cell {
    padding: 8px;
    min-height: 80px;
  }

  .month-name {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .records-count {
    font-size: 12px;
  }

  .year-title {
    font-size: 16px;
    min-width: 80px;
  }

  .year-navigation {
    gap: 12px;
  }

  .density-dot {
    width: 6px;
    height: 6px;
  }
}
</style>
