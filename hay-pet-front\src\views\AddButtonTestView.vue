<template>
  <div class="add-button-test">
    <div class="test-container">
      <h1>🎯 添加按钮组件展示</h1>
      <p class="test-description">
        基于Tailwind设计复刻的添加按钮组件，支持多种尺寸、颜色和动画效果
      </p>

      <!-- 当前使用的样式 -->
      <section class="test-section">
        <h2>当前健康记录页面使用的样式</h2>
        <div class="button-group">
          <button
            title="添加新类型"
            class="add-new-button"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40px"
              height="40px"
              viewBox="0 0 24 24"
              class="add-icon-svg"
            >
              <path
                d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                stroke-width="1.5"
              ></path>
              <path d="M8 12H16" stroke-width="1.5"></path>
              <path d="M12 16V8" stroke-width="1.5"></path>
            </svg>
          </button>
          <span class="button-label">当前样式（悬停旋转90度）</span>
        </div>
      </section>

      <!-- 尺寸变体 -->
      <section class="test-section">
        <h2>📏 尺寸变体</h2>
        <div class="button-group">
          <div class="button-item">
            <AddButton size="sm" tooltip="小尺寸" @click="handleClick('小尺寸')" />
            <span class="button-label">小 (32px)</span>
          </div>
          <div class="button-item">
            <AddButton size="md" tooltip="中等尺寸" @click="handleClick('中等尺寸')" />
            <span class="button-label">中 (40px)</span>
          </div>
          <div class="button-item">
            <AddButton size="lg" tooltip="大尺寸" @click="handleClick('大尺寸')" />
            <span class="button-label">大 (48px)</span>
          </div>
          <div class="button-item">
            <AddButton size="xl" tooltip="超大尺寸" @click="handleClick('超大尺寸')" />
            <span class="button-label">超大 (56px)</span>
          </div>
        </div>
      </section>

      <!-- 颜色变体 -->
      <section class="test-section">
        <h2>🎨 颜色变体</h2>
        <div class="button-group">
          <div class="button-item">
            <AddButton variant="primary" tooltip="主色调" @click="handleClick('主色调')" />
            <span class="button-label">主色调</span>
          </div>
          <div class="button-item">
            <AddButton variant="success" tooltip="成功色" @click="handleClick('成功色')" />
            <span class="button-label">成功色</span>
          </div>
          <div class="button-item">
            <AddButton variant="warning" tooltip="警告色" @click="handleClick('警告色')" />
            <span class="button-label">警告色</span>
          </div>
          <div class="button-item">
            <AddButton variant="danger" tooltip="危险色" @click="handleClick('危险色')" />
            <span class="button-label">危险色</span>
          </div>
          <div class="button-item">
            <AddButton variant="info" tooltip="信息色" @click="handleClick('信息色')" />
            <span class="button-label">信息色</span>
          </div>
          <div class="button-item">
            <AddButton variant="neutral" tooltip="中性色" @click="handleClick('中性色')" />
            <span class="button-label">中性色</span>
          </div>
        </div>
      </section>

      <!-- 动画变体 -->
      <section class="test-section">
        <h2>✨ 动画变体</h2>
        <div class="button-group">
          <div class="button-item">
            <AddButton animation="rotate" tooltip="旋转动画" @click="handleClick('旋转动画')" />
            <span class="button-label">旋转</span>
          </div>
          <div class="button-item">
            <AddButton animation="scale" tooltip="缩放动画" @click="handleClick('缩放动画')" />
            <span class="button-label">缩放</span>
          </div>
          <div class="button-item">
            <AddButton animation="pulse" tooltip="脉冲动画" @click="handleClick('脉冲动画')" />
            <span class="button-label">脉冲</span>
          </div>
          <div class="button-item">
            <AddButton animation="none" tooltip="无动画" @click="handleClick('无动画')" />
            <span class="button-label">无动画</span>
          </div>
        </div>
      </section>

      <!-- 状态展示 -->
      <section class="test-section">
        <h2>🔧 状态展示</h2>
        <div class="button-group">
          <div class="button-item">
            <AddButton tooltip="正常状态" @click="handleClick('正常状态')" />
            <span class="button-label">正常状态</span>
          </div>
          <div class="button-item">
            <AddButton :disabled="true" tooltip="禁用状态" />
            <span class="button-label">禁用状态</span>
          </div>
        </div>
      </section>

      <!-- 组合示例 -->
      <section class="test-section">
        <h2>🎭 组合示例</h2>
        <div class="button-group">
          <div class="button-item">
            <AddButton 
              size="lg" 
              variant="success" 
              animation="rotate" 
              tooltip="添加新项目" 
              @click="handleClick('添加新项目')" 
            />
            <span class="button-label">大号成功色旋转</span>
          </div>
          <div class="button-item">
            <AddButton 
              size="md" 
              variant="warning" 
              animation="scale" 
              tooltip="添加警告项" 
              @click="handleClick('添加警告项')" 
            />
            <span class="button-label">中号警告色缩放</span>
          </div>
          <div class="button-item">
            <AddButton 
              size="sm" 
              variant="info" 
              animation="pulse" 
              tooltip="添加信息" 
              @click="handleClick('添加信息')" 
            />
            <span class="button-label">小号信息色脉冲</span>
          </div>
        </div>
      </section>

      <!-- 使用示例代码 -->
      <section class="test-section">
        <h2>📝 使用示例</h2>
        <div class="code-examples">
          <div class="code-block">
            <h3>基础用法</h3>
            <pre><code>&lt;AddButton @click="handleAdd" /&gt;</code></pre>
          </div>
          
          <div class="code-block">
            <h3>自定义配置</h3>
            <pre><code>&lt;AddButton 
  size="lg"
  variant="success"
  animation="rotate"
  tooltip="添加新项目"
  @click="handleAdd"
/&gt;</code></pre>
          </div>
          
          <div class="code-block">
            <h3>禁用状态</h3>
            <pre><code>&lt;AddButton :disabled="true" /&gt;</code></pre>
          </div>
        </div>
      </section>

      <!-- 操作日志 -->
      <section class="test-section" v-if="actionLogs.length > 0">
        <h2>📋 操作日志</h2>
        <div class="action-logs">
          <div 
            v-for="(log, index) in actionLogs"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action">点击了: {{ log.action }}</span>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AddButton from '@/components/common/AddButton.vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const actionLogs = ref([])

// 事件处理
const handleClick = (action) => {
  addLog(action)
  ElMessage.success(`点击了: ${action}`)
}

const addLog = (action) => {
  actionLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    action
  })
  // 限制日志数量
  if (actionLogs.value.length > 10) {
    actionLogs.value = actionLogs.value.slice(0, 10)
  }
}
</script>

<style scoped>
/* 引入设计令牌 */
@import '@/styles/design-tokens.css';

/* 当前使用的样式（复制自HealthRecordsView） */
.add-new-button {
  cursor: pointer;
  outline: none;
  background: transparent;
  border: none;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.add-new-button:hover {
  transform: rotate(90deg);
}

.add-icon-svg {
  stroke: #6c757d;
  fill: none;
  transition: all 0.3s ease;
}

.add-new-button:hover .add-icon-svg {
  stroke: #409EFF;
  fill: rgba(64, 158, 255, 0.1);
}

.add-new-button:active .add-icon-svg {
  stroke: #337ECC;
  fill: rgba(64, 158, 255, 0.2);
}

/* 测试页面样式 */
.add-button-test {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.test-container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-3xl);
}

.test-description {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-8);
}

.test-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
}

h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-6);
  align-items: center;
}

.button-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
}

.button-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  text-align: center;
}

.code-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.code-block {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  border-left: 4px solid var(--color-primary);
}

.code-block h3 {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-md);
  color: var(--color-text-primary);
}

.code-block pre {
  margin: 0;
  background: var(--color-bg-primary);
  padding: var(--spacing-3);
  border-radius: var(--radius-base);
  overflow-x: auto;
}

.code-block code {
  font-family: 'Courier New', monospace;
  font-size: var(--font-size-sm);
  color: var(--color-text-regular);
}

.action-logs {
  max-height: 200px;
  overflow-y: auto;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.log-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-1) 0;
  border-bottom: 1px solid var(--color-border-light);
  font-size: var(--font-size-sm);
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: var(--color-text-secondary);
  font-family: monospace;
}

.log-action {
  color: var(--color-text-regular);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-button-test {
    padding: var(--spacing-4);
  }
  
  .test-section {
    padding: var(--spacing-4);
  }
  
  .button-group {
    gap: var(--spacing-4);
  }
  
  .code-examples {
    grid-template-columns: 1fr;
  }
}
</style>
