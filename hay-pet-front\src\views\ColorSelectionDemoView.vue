<template>
  <div class="color-demo">
    <div class="demo-container">
      <h1>🎨 颜色选择器优化演示</h1>
      <p class="demo-description">
        展示优化后的统一颜色选择布局和动画效果
      </p>

      <!-- 优化对比 -->
      <section class="demo-section">
        <h2>📊 优化对比</h2>
        <div class="comparison-container">
          <div class="comparison-item">
            <h3>优化前</h3>
            <div class="old-layout">
              <div class="old-section">
                <div class="section-title">预设颜色</div>
                <div class="old-grid">
                  <div v-for="i in 6" :key="i" class="old-color-dot"></div>
                </div>
              </div>
              <div class="old-section">
                <div class="section-title">自定义颜色</div>
                <div class="old-custom">
                  <div class="old-custom-button">+</div>
                  <span>点击选择自定义颜色</span>
                </div>
              </div>
            </div>
            <ul class="feature-list">
              <li>❌ 分组标题占用空间</li>
              <li>❌ 分隔线视觉干扰</li>
              <li>❌ 自定义按钮位置分离</li>
              <li>❌ 复杂的颜色选择器交互</li>
              <li>❌ 选中状态不够明显</li>
            </ul>
          </div>

          <div class="comparison-item">
            <h3>优化后</h3>
            <div class="new-layout">
              <div class="section-header-demo">
                <span class="section-title">类型颜色</span>
                <div class="color-picker-demo">
                  <el-color-picker
                    v-model="selectedColor"
                    size="default"
                    :predefine="demoColors"
                    title="Element Plus 颜色选择器 (Alt+点击随机颜色)"
                  />
                </div>
              </div>
              <div class="unified-color-grid">
                <div v-for="color in demoColors" :key="color"
                     class="color-button"
                     :class="{ 'selected': selectedColor === color }"
                     :style="{ backgroundColor: color }"
                     @click="selectedColor = color">
                  <div v-if="selectedColor === color" class="selected-indicator">
                    <span class="check-icon">✓</span>
                  </div>
                </div>
              </div>
            </div>
            <ul class="feature-list">
              <li>✅ Element Plus 原生颜色选择器</li>
              <li>✅ 简洁的右侧布局</li>
              <li>✅ 改进的选中状态指示</li>
              <li>✅ Alt+点击随机颜色功能</li>
              <li>✅ 悬浮提示信息</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 交互效果展示 -->
      <section class="demo-section">
        <h2>✨ 交互效果展示</h2>
        <div class="animation-showcase">
          <div class="showcase-item">
            <h3>Element Plus 颜色选择器</h3>
            <div class="color-picker-showcase">
              <el-color-picker
                v-model="showcaseColor"
                size="large"
                :predefine="demoColors"
                @change="handleShowcaseColorChange"
                title="专业的颜色选择工具"
              />
            </div>
            <p>专业的颜色选择工具，支持预设颜色</p>
          </div>

          <div class="showcase-item">
            <h3>悬停缩放效果</h3>
            <div class="hover-demo">
              <div class="color-button demo-hover" style="background-color: #409EFF;"></div>
              <div class="color-button demo-hover" style="background-color: #67C23A;"></div>
              <div class="color-button demo-hover" style="background-color: #E6A23C;"></div>
            </div>
            <p>悬停时1.1倍缩放 + 阴影增强</p>
          </div>

          <div class="showcase-item">
            <h3>改进的选中状态</h3>
            <div class="selected-demo">
              <div class="color-button selected" style="background-color: #F56C6C;">
                <div class="selected-indicator">
                  <span class="check-icon">✓</span>
                </div>
              </div>
            </div>
            <p>半透明遮罩 + 白色勾选图标 + 蓝色外圈</p>
          </div>

          <div class="showcase-item">
            <h3>随机颜色功能</h3>
            <div class="random-demo">
              <el-button @click="randomDemoColor" type="primary" size="large">
                <el-icon><Refresh /></el-icon>
                随机颜色
              </el-button>
              <div class="random-result" :style="{ backgroundColor: randomResultColor }"></div>
            </div>
            <p>Alt+点击任意颜色按钮或颜色选择器</p>
          </div>
        </div>
      </section>

      <!-- 技术特点 -->
      <section class="demo-section">
        <h2>🔧 技术特点</h2>
        <div class="tech-features">
          <div class="tech-item">
            <div class="tech-icon">🎯</div>
            <h3>Element Plus 集成</h3>
            <p>使用 Element Plus 原生颜色选择器，提供专业的颜色选择体验</p>
          </div>
          <div class="tech-item">
            <div class="tech-icon">🎨</div>
            <h3>改进的选中状态</h3>
            <p>半透明遮罩 + 白色图标 + 蓝色外圈，视觉效果更加清晰</p>
          </div>
          <div class="tech-item">
            <div class="tech-icon">📱</div>
            <h3>响应式设计</h3>
            <p>桌面端6列，移动端4列，颜色选择器位置自适应</p>
          </div>
          <div class="tech-item">
            <div class="tech-icon">⚡</div>
            <h3>交互优化</h3>
            <p>Alt+点击随机颜色，悬浮提示，简化的操作流程</p>
          </div>
          <div class="tech-item">
            <div class="tech-icon">🔧</div>
            <h3>简化架构</h3>
            <p>移除复杂的自定义组件，减少代码复杂度和维护成本</p>
          </div>
          <div class="tech-item">
            <div class="tech-icon">🎪</div>
            <h3>用户体验</h3>
            <p>颜色选择器不会关闭主对话框，支持实时预览效果</p>
          </div>
        </div>
      </section>

      <!-- 选中状态优化展示 -->
      <section class="demo-section">
        <h2>✨ 选中状态优化对比</h2>
        <div class="comparison-container">
          <div class="comparison-item">
            <h3>优化前</h3>
            <div class="old-selected-demo">
              <button
                class="color-button-old selected-old"
                :style="{ backgroundColor: '#409EFF' }"
              >
                <div class="selected-indicator-old">
                  <el-icon class="check-icon-old">
                    <Check />
                  </el-icon>
                </div>
              </button>
            </div>
            <ul class="feature-list">
              <li>❌ 简单的半透明黑色遮罩</li>
              <li>❌ 基础的白色勾选图标</li>
              <li>❌ 无动画效果</li>
              <li>❌ 视觉层次感不足</li>
            </ul>
          </div>

          <div class="comparison-item">
            <h3>优化后</h3>
            <div class="new-selected-demo">
              <button
                class="color-button selected"
                :style="{ backgroundColor: '#409EFF' }"
              >
                <div class="selected-indicator">
                  <el-icon class="check-icon">
                    <Check />
                  </el-icon>
                </div>
              </button>
            </div>
            <ul class="feature-list">
              <li>✅ 蓝绿渐变遮罩背景</li>
              <li>✅ 白色边框设计</li>
              <li>✅ 脉冲 + 弹跳动画</li>
              <li>✅ 增强的阴影和光效</li>
              <li>✅ 毛玻璃背景模糊</li>
              <li>✅ 简洁无外部描边</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 实际效果测试 -->
      <section class="demo-section">
        <h2>🧪 实际效果测试</h2>
        <div class="test-area">
          <p>点击下方按钮体验完整的标签管理对话框：</p>
          <el-button type="primary" size="large" @click="openTestDialog">
            <el-icon><Edit /></el-icon>
            打开标签管理面板
          </el-button>
        </div>
      </section>
    </div>

    <!-- 测试对话框 -->
    <el-dialog
      v-model="showTestDialog"
      title="标签管理面板测试"
      width="480px"
      class="type-management-dialog"
    >
      <div class="type-form-container">
        <el-form label-width="0px">
          <div class="form-section">
            <div class="section-header">
              <span class="section-title">类型名称</span>
              <span class="required-mark">*</span>
            </div>
            <el-input
              v-model="testTagName"
              placeholder="请输入类型名称"
              maxlength="20"
              show-word-limit
              class="type-name-input"
            ></el-input>
          </div>

          <div class="form-section">
            <div class="section-header">
              <span class="section-title">类型颜色</span>
              <span class="required-mark">*</span>
              <div class="color-picker-container">
                <el-color-picker
                  v-model="selectedColor"
                  @change="handleColorPickerChange"
                  :title="'当前颜色: ' + selectedColor"
                  size="default"
                  show-alpha
                  :predefine="demoColors"
                />
              </div>
            </div>

            <div class="color-selection-area">
              <div class="unified-color-grid">
                <button
                  v-for="color in demoColors"
                  :key="color"
                  @click.prevent="selectColor(color)"
                  :class="['color-button', { 'selected': selectedColor === color }]"
                  :style="{ backgroundColor: color }"
                  :title="'选择颜色'"
                  type="button"
                >
                  <div v-if="selectedColor === color" class="selected-indicator">
                    <el-icon class="check-icon">
                      <Check />
                    </el-icon>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer-custom">
          <el-button @click="showTestDialog = false" class="cancel-btn">取消</el-button>
          <el-button type="primary" class="save-btn">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Edit, Plus, Check, Refresh } from '@element-plus/icons-vue'

// 响应式数据
const showTestDialog = ref(false)
const testTagName = ref('测试标签')
const selectedColor = ref('#409EFF')
const showcaseColor = ref('#67C23A')
const randomResultColor = ref('#409EFF')

// 演示颜色
const demoColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
  '#909399', '#9C27B0', '#FF9800', '#4CAF50',
  '#2196F3', '#FF5722', '#795548', '#607D8B'
]

// 方法
const openTestDialog = () => {
  showTestDialog.value = true
}

const selectColor = (color) => {
  selectedColor.value = color
}

const handleColorPickerChange = (color) => {
  if (color) {
    selectedColor.value = color
  }
}



const handleShowcaseColorChange = (color) => {
  if (color) {
    showcaseColor.value = color
  }
}

const randomDemoColor = () => {
  const randomIndex = Math.floor(Math.random() * demoColors.length)
  randomResultColor.value = demoColors[randomIndex]
}
</script>

<style scoped>
/* 引入设计令牌 */
@import '@/styles/design-tokens.css';

.color-demo {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  text-align: center;
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-3xl);
}

.demo-description {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-8);
}

.demo-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
}

h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

/* 对比容器 */
.comparison-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-6);
}

.comparison-item {
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.comparison-item h3 {
  text-align: center;
  margin-bottom: var(--spacing-4);
  color: var(--color-text-primary);
}

/* 旧布局样式 */
.old-layout {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #E4E7ED;
  margin-bottom: var(--spacing-4);
}

.old-section {
  margin-bottom: 20px;
}

.old-section:last-child {
  margin-bottom: 0;
  border-top: 1px solid #E4E7ED;
  padding-top: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 12px;
}

.old-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
}

.old-color-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #ccc;
}

.old-custom {
  display: flex;
  align-items: center;
  gap: 12px;
}

.old-custom-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ff0000, #00ff00, #0000ff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

/* 新布局样式 */
.new-layout {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #E4E7ED;
  margin-bottom: var(--spacing-4);
}

.section-header-demo {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.color-picker-demo {
  margin-left: auto;
}

.unified-color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
}

.color-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.color-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.color-button.selected {
  /* 移除外部蓝色描边，只保留基础样式 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selected-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9) 0%, rgba(103, 194, 58, 0.9) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  animation: selectedPulse 0.3s ease-out;
}

.selected-indicator .check-icon {
  color: white;
  font-size: 22px;
  font-weight: bold;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.6),
    0 0 8px rgba(255, 255, 255, 0.4);
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.5));
  animation: checkIconBounce 0.4s ease-out 0.1s both;
}

@keyframes selectedPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkIconBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  60% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.check-icon {
  color: white;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 功能列表 */
.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  padding: var(--spacing-1) 0;
  font-size: var(--font-size-sm);
}

/* 动画展示 */
.animation-showcase {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.showcase-item {
  text-align: center;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.color-picker-showcase {
  display: flex;
  justify-content: center;
  margin: var(--spacing-2) 0;
}

.hover-demo {
  display: flex;
  gap: var(--spacing-2);
  justify-content: center;
  margin: var(--spacing-2) 0;
}

.demo-hover:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.selected-demo {
  display: flex;
  justify-content: center;
  margin: var(--spacing-2) 0;
}

.random-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
}

.random-result {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid #409EFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 旧版本选中状态样式（用于对比） */
.color-button-old {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.color-button-old.selected-old {
  border-color: #409EFF;
  box-shadow: 0 0 0 3px #409EFF, 0 4px 16px rgba(0, 0, 0, 0.2);
  transform: scale(1.05);
}

.selected-indicator-old {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.selected-indicator-old .check-icon-old {
  color: white;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.3));
}

.old-selected-demo,
.new-selected-demo {
  display: flex;
  justify-content: center;
  margin: var(--spacing-4) 0;
}

/* 技术特点 */
.tech-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-4);
}

.tech-item {
  text-align: center;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
}

.tech-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-2);
}

/* 测试区域 */
.test-area {
  text-align: center;
  padding: var(--spacing-4);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .color-demo {
    padding: var(--spacing-4);
  }
  
  .demo-section {
    padding: var(--spacing-4);
  }
  
  .comparison-container {
    grid-template-columns: 1fr;
  }
  
  .unified-color-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>

<!-- 引入对话框样式 -->
<style>
/* 复用标签管理对话框样式 */
.type-management-dialog {
  --el-dialog-border-radius: 12px;
}

.type-management-dialog .el-dialog__header {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px;
}

.type-management-dialog .el-dialog__title {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.type-form-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.required-mark {
  color: #F56C6C;
  margin-left: 4px;
}

.color-selection-area {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #E4E7ED;
}

.dialog-footer-custom {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 20px 24px;
  background: #fafbfc;
  border-radius: 0 0 12px 12px;
}

.save-btn {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  border: none;
}
</style>
