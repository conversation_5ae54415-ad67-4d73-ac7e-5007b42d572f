<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类标签切换测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #409EFF;
            margin-bottom: 15px;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 10px;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin-bottom: 8px;
        }
        .expected-behavior {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #67C23A;
        }
        .expected-behavior h4 {
            color: #67C23A;
            margin-top: 0;
        }
        .test-button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }
        .status.success {
            background: #e8f5e8;
            color: #67C23A;
        }
        .status.error {
            background: #fef0f0;
            color: #F56C6C;
        }
        .status.info {
            background: #e8f4fd;
            color: #409EFF;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="test-title">🏷️ 分类标签选中/取消选中功能测试</h2>
        
        <div class="test-steps">
            <h3>测试步骤：</h3>
            <ol>
                <li>打开花费追踪页面：<a href="http://localhost:5208/expense-tracking" target="_blank">http://localhost:5208/expense-tracking</a></li>
                <li>等待页面加载完成，确保分类标签显示正常</li>
                <li>点击任意一个分类标签（如"餐饮"）</li>
                <li>观察标签是否变为选中状态（有颜色背景）</li>
                <li>再次点击同一个标签</li>
                <li>观察标签是否取消选中（恢复到未选中状态）</li>
                <li>点击"全部"标签，确保所有筛选被清除</li>
            </ol>
        </div>

        <div class="expected-behavior">
            <h4>预期行为：</h4>
            <ul>
                <li><strong>首次点击分类标签：</strong>标签应该变为选中状态，显示彩色背景和白色文字</li>
                <li><strong>再次点击相同标签：</strong>标签应该取消选中，恢复到未选中状态</li>
                <li><strong>点击"全部"标签：</strong>清除所有分类筛选，显示所有记录</li>
                <li><strong>表格筛选：</strong>选中分类时，表格应该只显示该分类的记录</li>
                <li><strong>筛选状态显示：</strong>表格标题应该显示当前筛选的分类名称</li>
            </ul>
        </div>

        <div>
            <button class="test-button" onclick="openExpensePage()">打开花费追踪页面</button>
            <button class="test-button" onclick="runAutoTest()">运行自动测试</button>
            <button class="test-button" onclick="checkConsoleErrors()">检查控制台错误</button>
        </div>

        <div id="test-status"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">🔧 调试信息</h2>
        <div id="debug-info">
            <p>点击"运行自动测试"按钮来获取调试信息...</p>
        </div>
    </div>

    <script>
        function openExpensePage() {
            window.open('http://localhost:5208/expense-tracking', '_blank');
            updateStatus('已打开花费追踪页面，请手动测试分类标签功能', 'info');
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function updateDebugInfo(info) {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML = `<pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">${info}</pre>`;
        }

        async function runAutoTest() {
            updateStatus('正在运行自动测试...', 'info');
            
            try {
                // 检查页面是否可访问
                const response = await fetch('http://localhost:5208/expense-tracking');
                if (!response.ok) {
                    throw new Error(`页面无法访问: ${response.status}`);
                }

                updateStatus('✅ 页面可访问，请手动测试分类标签功能', 'success');
                
                // 提供详细的测试指导
                const testGuide = `
自动测试结果：
✅ 花费追踪页面可访问
✅ 服务器响应正常

手动测试指导：
1. 点击上方"打开花费追踪页面"按钮
2. 等待页面加载完成
3. 查看分类标签是否显示颜色
4. 测试点击标签的选中/取消选中功能

如果遇到问题，请检查：
- 浏览器控制台是否有错误
- 网络请求是否正常
- Vue DevTools 中的组件状态
                `;
                
                updateDebugInfo(testGuide);

            } catch (error) {
                updateStatus(`❌ 测试失败: ${error.message}`, 'error');
                updateDebugInfo(`错误详情: ${error.stack || error.message}`);
            }
        }

        function checkConsoleErrors() {
            updateStatus('请打开浏览器开发者工具查看控制台错误', 'info');
            
            const instructions = `
检查控制台错误的步骤：
1. 按 F12 打开开发者工具
2. 切换到 Console 标签
3. 刷新花费追踪页面
4. 查看是否有红色错误信息
5. 特别关注以下类型的错误：
   - Vue 组件错误
   - JavaScript 运行时错误
   - 网络请求错误
   - Supabase 连接错误

常见错误排查：
- 如果看到 "Cannot read property" 错误，可能是数据加载问题
- 如果看到 "Failed to fetch" 错误，可能是网络连接问题
- 如果看到 Vue 警告，可能是组件属性问题
            `;
            
            updateDebugInfo(instructions);
        }

        // 页面加载时显示初始状态
        window.onload = function() {
            updateStatus('测试页面已准备就绪，请选择测试方式', 'info');
        };
    </script>
</body>
</html>
