// Dashboard板块配置相关类型定义

export interface DashboardBlock {
  id: string;           // 板块唯一标识
  name: string;         // 板块显示名称
  component: string;    // 对应的Vue组件名
  visible: boolean;     // 是否显示
  order: number;        // 排序序号
  required: boolean;    // 是否为必需板块（不可隐藏）
  icon?: string;        // 板块图标
  description?: string; // 板块描述
}

export interface DashboardConfig {
  blocks: DashboardBlock[];
  lastModified: Date;
}

// 默认板块配置
export const DEFAULT_DASHBOARD_CONFIG: DashboardConfig = {
  blocks: [
    {
      id: 'pet-info',
      name: '宠物信息卡片',
      component: 'PetInfoCard',
      visible: true,
      order: 1,
      required: true,
      icon: 'Avatar',
      description: '显示当前宠物的基本信息'
    },
    {
      id: 'weight-trend',
      name: '体重变化趋势',
      component: 'WeightTrendCard',
      visible: true,
      order: 2,
      required: false,
      icon: 'TrendCharts',
      description: '展示宠物体重变化曲线'
    },
    {
      id: 'expense-overview',
      name: '花费记录概览',
      component: 'ExpenseOverviewCard',
      visible: true,
      order: 3,
      required: false,
      icon: 'Money',
      description: '显示最近的花费统计'
    },
    {
      id: 'reminders',
      name: '提醒事项',
      component: 'RemindersCard',
      visible: true,
      order: 4,
      required: false,
      icon: 'Bell',
      description: '显示待办提醒事项'
    },
    {
      id: 'health-summary',
      name: '健康记录摘要',
      component: 'HealthSummaryCard',
      visible: true,
      order: 5,
      required: false,
      icon: 'FirstAidKit',
      description: '显示最近的健康记录'
    },



    {
      id: 'photo-gallery',
      name: '相册预览',
      component: 'PhotoGalleryCard',
      visible: true,
      order: 9,
      required: false,
      icon: 'Picture',
      description: '显示最新上传的照片'
    }
  ],
  lastModified: new Date()
};