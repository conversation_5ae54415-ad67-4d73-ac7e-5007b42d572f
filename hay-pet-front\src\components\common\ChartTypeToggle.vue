<template>
  <div class="chart-type-toggle">
    <button
      v-for="(option, index) in options"
      :key="option.value"
      @click="handleClick(option.value, index, $event)"
      :class="['chart-type-btn', { active: activeIndex === index }]"
    >
      <el-icon class="chart-icon">
        <component :is="option.icon" />
      </el-icon>
      <span class="btn-label">{{ option.label }}</span>
      <div class="btn-ripple"></div>
    </button>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { gsap } from 'gsap'

// Props
const props = defineProps({
  options: {
    type: Array,
    required: true,
    validator: (options) => {
      return options.every(option => 
        typeof option === 'object' && 
        option.value !== undefined && 
        option.label !== undefined
      )
    }
  },
  modelValue: {
    type: [String, Number],
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// Computed
const activeIndex = computed(() => {
  return props.options.findIndex(option => option.value === props.modelValue)
})

// Methods
const createRippleEffect = (button, event) => {
  const ripple = button.querySelector('.btn-ripple')
  const rect = button.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  const x = event.clientX - rect.left - size / 2
  const y = event.clientY - rect.top - size / 2
  
  gsap.set(ripple, {
    width: size,
    height: size,
    x: x,
    y: y,
    scale: 0,
    opacity: 0.6
  })
  
  gsap.to(ripple, {
    scale: 1,
    opacity: 0,
    duration: 0.6,
    ease: "power2.out"
  })
}

const handleClick = (value, index, event) => {
  if (value === props.modelValue) return
  
  // 创建涟漪效果
  if (event && event.currentTarget) {
    createRippleEffect(event.currentTarget, event)
  }
  
  // 按钮点击动画
  gsap.fromTo(event.currentTarget, 
    { scale: 0.95 },
    { 
      scale: 1,
      duration: 0.2,
      ease: "back.out(1.7)"
    }
  )
  
  // 立即更新值
  emit('update:modelValue', value)
  emit('change', value, index)
}
</script>

<style scoped>
/* 图表类型切换样式 */
.chart-type-toggle {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chart-type-btn {
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  background: transparent;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  white-space: nowrap;
  font-weight: 500;
  color: #606266;
  overflow: hidden;
  transition: all 0.3s ease;
  min-height: 32px;
}

.chart-type-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
  background: rgba(64, 158, 255, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.chart-type-btn.active {
  background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
  border-color: #409EFF;
  color: white;
  box-shadow: 
    0 2px 8px rgba(64, 158, 255, 0.3),
    0 1px 3px rgba(64, 158, 255, 0.4);
}

.chart-type-btn.active:hover {
  background: linear-gradient(135deg, #66b1ff 0%, #409EFF 100%);
  transform: translateY(-1px);
  box-shadow: 
    0 4px 12px rgba(64, 158, 255, 0.4),
    0 2px 6px rgba(64, 158, 255, 0.5);
}

.chart-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.chart-type-btn:hover .chart-icon {
  transform: scale(1.1);
}

.chart-type-btn.active .chart-icon {
  color: white;
}

.btn-label {
  font-size: 12px;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* 涟漪效果 */
.btn-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  pointer-events: none;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-type-toggle {
    gap: 4px;
  }
  
  .chart-type-btn {
    padding: 4px 8px;
    font-size: 11px;
    min-height: 28px;
  }
  
  .chart-icon {
    font-size: 14px;
  }
  
  .btn-label {
    font-size: 11px;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .chart-type-btn {
    border-color: #4c4d4f;
    color: #a8abb2;
    background: rgba(255, 255, 255, 0.02);
  }
  
  .chart-type-btn:hover {
    border-color: #409EFF;
    color: #409EFF;
    background: rgba(64, 158, 255, 0.1);
  }
}
</style>
