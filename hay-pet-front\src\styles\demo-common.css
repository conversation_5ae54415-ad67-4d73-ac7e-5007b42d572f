/* ========================================
   样式演示页面通用样式
   ======================================== */

.page-header-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 演示页面标题 */
.demo-title {
  text-align: center;
  margin-bottom: 32px;
}

.demo-title h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-title p {
  font-size: 18px;
  color: #909399;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 演示区块 */
.demo-section {
  margin-bottom: 48px;
}

.demo-section h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #E4E7ED;
}

/* 演示卡片 */
.demo-card {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #EBEEF5;
}

.demo-card h3 {
  font-size: 18px;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #F5F7FA;
}

/* 演示预览区域 */
.demo-preview {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  border: 1px solid #E4E7ED;
}

/* 代码展示区域 */
.demo-code {
  margin-top: 16px;
}

.demo-code h3 {
  font-size: 16px;
  color: #606266;
  margin-bottom: 12px;
  border: none;
  padding: 0;
}

.demo-code pre {
  background: #2D3748;
  color: #E2E8F0;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.demo-code code {
  background: transparent;
  color: inherit;
  padding: 0;
  font-family: inherit;
}

/* 属性表格样式 */
.props-table,
.events-table,
.slots-table {
  width: 100%;
  margin-top: 16px;
}

.props-table table,
.events-table table,
.slots-table table {
  width: 100%;
  border-collapse: collapse;
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.props-table th,
.props-table td,
.events-table th,
.events-table td,
.slots-table th,
.slots-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #EBEEF5;
}

.props-table th,
.events-table th,
.slots-table th {
  background: #F5F7FA;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.props-table td,
.events-table td,
.slots-table td {
  color: #606266;
  font-size: 14px;
}

.props-table td:first-child,
.events-table td:first-child,
.slots-table td:first-child {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #E6A23C;
  font-weight: 600;
  background: #FDF6EC;
}

.props-table tr:last-child td,
.events-table tr:last-child td,
.slots-table tr:last-child td {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-container {
    padding: 16px;
  }
  
  .demo-title h1 {
    font-size: 24px;
  }
  
  .demo-title p {
    font-size: 16px;
  }
  
  .demo-section h2 {
    font-size: 20px;
  }
  
  .demo-card {
    padding: 16px;
  }
  
  .demo-preview {
    padding: 16px;
  }
  
  .demo-code pre {
    padding: 12px;
    font-size: 12px;
  }
  
  .props-table th,
  .props-table td,
  .events-table th,
  .events-table td,
  .slots-table th,
  .slots-table td {
    padding: 8px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .demo-container {
    padding: 12px;
  }
  
  .demo-title h1 {
    font-size: 20px;
  }
  
  .demo-title p {
    font-size: 14px;
  }
  
  .demo-section {
    margin-bottom: 32px;
  }
  
  .demo-card {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .demo-preview {
    padding: 12px;
  }
  
  /* 表格在小屏幕上的处理 */
  .props-table,
  .events-table,
  .slots-table {
    overflow-x: auto;
  }
  
  .props-table table,
  .events-table table,
  .slots-table table {
    min-width: 500px;
  }
}

/* 特殊样式 */
.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-help-text {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

/* 动画效果 */
.demo-card {
  transition: all 0.3s ease;
}

.demo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.demo-preview {
  transition: all 0.3s ease;
}

.demo-preview:hover {
  border-color: #409EFF;
}

/* 代码高亮 */
.demo-code pre code .keyword {
  color: #FF79C6;
}

.demo-code pre code .string {
  color: #F1FA8C;
}

.demo-code pre code .comment {
  color: #6272A4;
  font-style: italic;
}

.demo-code pre code .tag {
  color: #8BE9FD;
}

.demo-code pre code .attr-name {
  color: #50FA7B;
}

.demo-code pre code .attr-value {
  color: #F1FA8C;
}
