<template>
  <el-card class="dashboard-card photo-gallery-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">最新照片</span>
        <el-button type="primary" link @click="router.push('/photo-album')">
          <el-icon><Picture /></el-icon>
          查看全部
        </el-button>
      </div>
    </template>
    <div class="photo-gallery-content">
      <div v-if="photos.length" class="photos-grid">
        <div 
          v-for="photo in recentPhotos" 
          :key="photo.id" 
          class="photo-item"
          @click="previewPhoto(photo)"
        >
          <el-image
            :src="photo.url"
            :alt="photo.name || '宠物照片'"
            fit="cover"
            class="photo-image"
            :preview-src-list="previewList"
            :initial-index="getPhotoIndex(photo.id)"
            hide-on-click-modal
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>加载失败</span>
              </div>
            </template>
          </el-image>
          <div class="photo-overlay">
            <div class="photo-info">
              <span class="photo-date">{{ formatDate(photo.created_at) }}</span>
              <span class="photo-description" v-if="photo.name">
                {{ photo.name }}
              </span>
            </div>
          </div>
        </div>
        
        <div v-if="photos.length > 6" class="more-photos" @click="router.push('/photo-album')">
          <div class="more-content">
            <el-icon class="more-icon"><MoreFilled /></el-icon>
            <span class="more-text">还有 {{ photos.length - 6 }} 张照片</span>
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无照片">
        <el-button type="primary" @click="router.push('/photo-album')">
          <el-icon><Plus /></el-icon>
          上传照片
        </el-button>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Picture, Plus, MoreFilled } from '@element-plus/icons-vue';
import { usePetStore } from '@/stores/pet';
import { supabase } from '@/utils/supabase';
import type { DashboardBlock } from '@/types/dashboard';

interface Props {
  blockConfig: DashboardBlock;
}

defineProps<Props>();

const router = useRouter();
const petStore = usePetStore();

const photos = ref<any[]>([]);
const loading = ref(false);

const currentPet = computed(() => petStore.currentPet);

const recentPhotos = computed(() => {
  return photos.value.slice(0, 6);
});

const previewList = computed(() => {
  return recentPhotos.value.map(photo => photo.url);
});

async function fetchPhotos() {
  if (!currentPet.value?.id) return;
  
  loading.value = true;
  try {
    const { data, error } = await supabase
      .from('photos')
      .select('*')
      .eq('pet_id', currentPet.value.id)
      .order('created_at', { ascending: false })
      .limit(12);
    
    if (error) throw error;
    photos.value = data || [];
  } catch (error) {
    console.error('获取照片失败:', error);
  } finally {
    loading.value = false;
  }
}

function previewPhoto(photo: any) {
  // 图片预览由 el-image 组件自动处理
}

function getPhotoIndex(photoId: string): number {
  return recentPhotos.value.findIndex(photo => photo.id === photoId);
}

function formatDate(date: string): string {
  const now = new Date();
  const photoDate = new Date(date);
  const diffTime = Math.abs(now.getTime() - photoDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return '今天';
  } else if (diffDays === 2) {
    return '昨天';
  } else if (diffDays <= 7) {
    return `${diffDays - 1}天前`;
  } else {
    return photoDate.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    });
  }
}

onMounted(() => {
  fetchPhotos();
});

// 监听当前宠物变化
watch(currentPet, (newPet) => {
  if (newPet) {
    fetchPhotos();
  } else {
    photos.value = [];
  }
}, { immediate: true });
</script>

<style scoped>
.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.photo-gallery-content {
  padding: 0;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.photo-item:hover {
  transform: scale(1.02);
}

.photo-item:hover .photo-overlay {
  opacity: 1;
}

.photo-image {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 12px 8px 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.photo-date {
  font-size: 12px;
  color: white;
  font-weight: 500;
}

.photo-description {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.more-photos {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  background: var(--el-bg-color-page);
  border: 2px dashed var(--el-border-color);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-photos:hover {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.more-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.more-icon {
  font-size: 24px;
  color: var(--el-text-color-secondary);
}

.more-photos:hover .more-icon {
  color: var(--el-color-primary);
}

.more-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.more-photos:hover .more-text {
  color: var(--el-color-primary);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--el-text-color-secondary);
  background: var(--el-bg-color-page);
}

.image-error .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.image-error span {
  font-size: 12px;
}

@media (max-width: 768px) {
  .photos-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .photo-overlay {
    opacity: 1;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.5));
  }
}

@media (max-width: 480px) {
  .photos-grid {
    gap: 6px;
  }
  
  .photo-overlay {
    padding: 8px 6px 6px;
  }
  
  .photo-date {
    font-size: 11px;
  }
  
  .photo-description {
    font-size: 10px;
  }
}
</style>