-- 花费分类表迁移脚本
-- 请在Supabase SQL编辑器中执行此脚本

-- 创建花费分类表
CREATE TABLE IF NOT EXISTS public.expense_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    color TEXT NOT NULL DEFAULT '#409EFF',
    order_index INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建唯一约束（同一用户下分类名称不能重复）
CREATE UNIQUE INDEX IF NOT EXISTS idx_expense_categories_user_name 
ON public.expense_categories(user_id, name);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_expense_categories_user_id ON public.expense_categories(user_id);
CREATE INDEX IF NOT EXISTS idx_expense_categories_order ON public.expense_categories(user_id, order_index);

-- 启用RLS
ALTER TABLE public.expense_categories ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
-- 用户只能查看自己的分类
DROP POLICY IF EXISTS "Users can view own expense categories" ON public.expense_categories;
CREATE POLICY "Users can view own expense categories" ON public.expense_categories
    FOR SELECT USING (auth.uid() = user_id);

-- 用户只能插入自己的分类
DROP POLICY IF EXISTS "Users can insert own expense categories" ON public.expense_categories;
CREATE POLICY "Users can insert own expense categories" ON public.expense_categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 用户只能更新自己的分类
DROP POLICY IF EXISTS "Users can update own expense categories" ON public.expense_categories;
CREATE POLICY "Users can update own expense categories" ON public.expense_categories
    FOR UPDATE USING (auth.uid() = user_id);

-- 用户只能删除自己的分类
DROP POLICY IF EXISTS "Users can delete own expense categories" ON public.expense_categories;
CREATE POLICY "Users can delete own expense categories" ON public.expense_categories
    FOR DELETE USING (auth.uid() = user_id);

-- 插入默认分类数据（可选）
-- 注意：这里需要替换为实际的用户ID，或者在应用中动态创建
-- INSERT INTO public.expense_categories (user_id, name, color, order_index) VALUES
-- ('your-user-id-here', '食物', '#FF6B6B', 1),
-- ('your-user-id-here', '医疗', '#4ECDC4', 2),
-- ('your-user-id-here', '玩具', '#45B7D1', 3),
-- ('your-user-id-here', '用品', '#96CEB4', 4),
-- ('your-user-id-here', '美容', '#FFEAA7', 5),
-- ('your-user-id-here', '其他', '#F7DC6F', 6);

-- 创建触发器函数来自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为 expense_categories 表创建触发器
DROP TRIGGER IF EXISTS update_expense_categories_updated_at ON public.expense_categories;
CREATE TRIGGER update_expense_categories_updated_at
    BEFORE UPDATE ON public.expense_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 完成提示
SELECT 'expense_categories 表创建完成！' as message;
