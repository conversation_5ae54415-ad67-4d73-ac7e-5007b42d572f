<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花费追踪页面测试 - Hay!Pet</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 700;
        }
        .header p {
            margin: 10px 0 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #303133;
            border-bottom: 3px solid #409EFF;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #409EFF;
        }
        .feature-card h3 {
            color: #409EFF;
            margin-top: 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓";
            color: #67C23A;
            font-weight: bold;
            margin-right: 10px;
        }
        .demo-frame {
            width: 100%;
            height: 800px;
            border: none;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .test-actions {
            text-align: center;
            margin: 30px 0;
        }
        .test-btn {
            background: linear-gradient(135deg, #409EFF, #67C23A);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐾 花费追踪页面重构测试</h1>
            <p>与体重追踪页面保持一致的设计风格和布局结构</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>🎯 重构目标</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>设计风格统一</h3>
                        <ul class="feature-list">
                            <li>骨架屏加载 - 页面初始加载时显示结构化的骨架屏，避免空白页面</li>
                            <li>分层加载体验 - 先显示页面框架，再填充数据内容</li>
                            <li>平滑过渡动画 - 从加载状态到数据显示的无缝切换</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>布局结构对齐</h3>
                        <ul class="feature-list">
                            <li>页面标题区域 - 包含页面标题和主要操作按钮</li>
                            <li>时间控制面板 - 时间维度切换、统计信息展示</li>
                            <li>数据可视化区域 - 图表展示花费趋势</li>
                            <li>统计卡片区域 - 总花费、平均花费、最高花费等关键指标</li>
                            <li>数据表格区域 - 历史花费记录列表</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🚀 核心特性</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>组件复用</h3>
                        <ul class="feature-list">
                            <li><strong>EnhancedViewToggle组件：</strong>时间维度切换（月/季度/年视图）</li>
                            <li><strong>统计卡片设计：</strong>复用体重追踪页面的卡片样式和动画</li>
                            <li><strong>表格功能：</strong>排序、筛选、批量操作功能</li>
                            <li><strong>对话框样式：</strong>统一的添加/编辑操作界面</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>加载体验优化</h3>
                        <ul class="feature-list">
                            <li><strong>骨架屏系统：</strong>图表、统计卡片、数据表格的结构化加载状态</li>
                            <li><strong>分层渲染：</strong>页面框架立即显示，数据内容异步填充</li>
                            <li><strong>平滑过渡：</strong>0.3秒的淡入淡出动画效果</li>
                            <li><strong>一致体验：</strong>所有区域保持统一的最小高度和过渡效果</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📱 响应式适配</h2>
                <ul class="feature-list">
                    <li><strong>桌面端：</strong>完整的多列布局，统计卡片网格显示</li>
                    <li><strong>平板端：</strong>自适应的卡片布局，保持良好的视觉层次</li>
                    <li><strong>移动端：</strong>单列布局，优化的触摸交互体验</li>
                    <li><strong>统一体验：</strong>与体重追踪页面相同的响应式断点和适配策略</li>
                </ul>
            </div>

            <div class="section">
                <h2>🎨 视觉设计</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>颜色方案</h3>
                        <ul class="feature-list">
                            <li>主色调：#409EFF（蓝色）</li>
                            <li>辅助色：#67C23A（绿色）、#E6A23C（橙色）</li>
                            <li>渐变按钮：与体重追踪页面保持一致</li>
                            <li>统计卡片：不同类型使用不同的渐变色彩</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>交互效果</h3>
                        <ul class="feature-list">
                            <li>悬停动画：卡片上浮效果</li>
                            <li>按钮反馈：点击和悬停的视觉反馈</li>
                            <li>表格交互：排序、选择、批量操作</li>
                            <li>加载动画：骨架屏的呼吸效果</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="test-actions">
                <button onclick="location.reload()" class="test-btn">刷新页面 (观察骨架屏)</button>
                <button onclick="window.open('http://localhost:5208/', '_blank')" class="test-btn">打开主应用</button>
            </div>

            <div class="section">
                <h2>🖥️ 花费追踪页面演示</h2>
                <iframe src="http://localhost:5208/#/expense-tracking" class="demo-frame"></iframe>
            </div>
        </div>
    </div>
</body>
</html>
