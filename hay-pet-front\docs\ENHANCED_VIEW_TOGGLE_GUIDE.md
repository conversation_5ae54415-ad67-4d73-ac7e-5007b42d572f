# Enhanced View Toggle 组件使用指南

## 概述

`EnhancedViewToggle` 是一个高度可定制的视图切换组件，提供了丰富的动画效果和预设样式系统。该组件已被集成到预设样式系统中，可以在整个应用中统一使用。

## 基础用法

### 简单示例

```vue
<template>
  <EnhancedViewToggle
    v-model="selectedView"
    :options="viewOptions"
  />
</template>

<script setup>
import { ref } from 'vue'
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue'

const selectedView = ref('cards')
const viewOptions = [
  { value: 'cards', label: '卡片' },
  { value: 'timeline', label: '时间线' },
  { value: 'table', label: '表格' }
]
</script>
```

### 小尺寸版本

```vue
<EnhancedViewToggle
  v-model="selectedView"
  :options="viewOptions"
  size="small"
/>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `String/Number` | - | 当前选中的值（必需） |
| `options` | `Array` | - | 选项数组（必需） |
| `size` | `String` | `'normal'` | 尺寸大小：`'small'` \| `'normal'` |

### options 数组格式

```javascript
const options = [
  { value: 'option1', label: '选项1' },
  { value: 'option2', label: '选项2' },
  { value: 'option3', label: '选项3' }
]
```

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `value` | 选中值变化时触发 |
| `change` | `(value, index)` | 选中值变化时触发，包含值和索引 |

## 特性

### 🎨 丰富的动画效果

- **涟漪点击效果**：点击按钮时的水波纹动画
- **填充背景移动**：选中状态的蓝色背景平滑移动
- **发光脉冲效果**：选中时的发光动画
- **按钮弹跳反馈**：点击时的弹性反馈
- **文字颜色渐变**：选中/未选中状态的颜色过渡

### 📱 响应式设计

- 自动适配移动端屏幕
- 在小屏幕上调整按钮大小和字体
- 保持良好的触摸体验

### ⚡ 高性能

- 使用 GSAP 进行硬件加速动画
- 优化的 DOM 操作
- 流畅的 60fps 动画

## 实际应用示例

### 1. 日历视图切换

```vue
<template>
  <div class="calendar-controls">
    <EnhancedViewToggle
      v-model="calendarView"
      :options="calendarViewOptions"
      @change="handleCalendarViewChange"
    />
  </div>
</template>

<script setup>
const calendarView = ref('month')
const calendarViewOptions = [
  { value: 'month', label: '月视图' },
  { value: 'week', label: '周视图' },
  { value: 'year', label: '年视图' }
]

const handleCalendarViewChange = (newView) => {
  // 处理视图切换逻辑
  console.log('切换到:', newView)
}
</script>
```

### 2. 记录类型切换（小尺寸）

```vue
<template>
  <div class="filter-header">
    <span class="filter-label">记录类型：</span>
    <EnhancedViewToggle
      v-model="viewMode"
      :options="viewModeOptions"
      size="small"
      @change="handleViewModeChange"
    />
  </div>
</template>

<script setup>
const viewMode = ref('cards')
const viewModeOptions = [
  { value: 'cards', label: '卡片' },
  { value: 'timeline', label: '时间线' },
  { value: 'table', label: '表格' }
]
</script>
```

## 预设样式系统

### 可用预设

组件支持以下预设配置：

```javascript
import { getViewTogglePreset } from '@/styles/presets/view-toggle.js'

// 获取预设配置
const preset = getViewTogglePreset('default')
// 或者
const smallPreset = getViewTogglePreset('small')
```

### 预设类型

- `default` - 默认样式
- `small` - 小尺寸样式
- `large` - 大尺寸样式
- `success` - 成功主题
- `warning` - 警告主题
- `danger` - 危险主题

## 样式定制

### CSS 变量

组件支持通过 CSS 变量进行样式定制：

```css
.enhanced-view-toggle {
  --toggle-background: #f5f7fa;
  --toggle-fill-background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
  --toggle-active-color: #ffffff;
  --toggle-inactive-color: #606266;
  --toggle-hover-color: #409EFF;
}
```

### 自定义主题

```vue
<style scoped>
.custom-toggle :deep(.enhanced-view-toggle) {
  --toggle-fill-background: linear-gradient(135deg, #67C23A 0%, #5DAE34 100%);
  --toggle-hover-color: #67C23A;
}
</style>
```

## 最佳实践

### 1. 选项数量建议

- **2-3个选项**：最佳用户体验
- **4-5个选项**：可接受，但需要考虑移动端体验
- **6个以上**：不推荐，考虑使用下拉菜单

### 2. 标签文本

- 保持简洁，建议2-4个字符
- 使用一致的命名规范
- 避免过长的文本导致布局问题

### 3. 响应式考虑

```vue
<template>
  <!-- 桌面端使用普通尺寸 -->
  <EnhancedViewToggle
    v-if="!isMobile"
    v-model="view"
    :options="options"
  />
  
  <!-- 移动端使用小尺寸 -->
  <EnhancedViewToggle
    v-else
    v-model="view"
    :options="options"
    size="small"
  />
</template>
```

## 迁移指南

### 从旧版本迁移

如果你之前使用的是自定义的视图切换组件，可以按以下步骤迁移：

1. **替换组件引用**：
```javascript
// 旧版本
import CustomToggle from './CustomToggle.vue'

// 新版本
import EnhancedViewToggle from '@/components/common/EnhancedViewToggle.vue'
```

2. **更新模板**：
```vue
<!-- 旧版本 -->
<custom-toggle
  :active="activeView"
  @change="handleChange"
>
  <button>选项1</button>
  <button>选项2</button>
</custom-toggle>

<!-- 新版本 -->
<EnhancedViewToggle
  v-model="activeView"
  :options="[
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' }
  ]"
  @change="handleChange"
/>
```

3. **移除旧样式**：删除相关的 CSS 样式文件

## 故障排除

### 常见问题

1. **动画不流畅**
   - 确保已正确安装 GSAP
   - 检查是否有 CSS 冲突

2. **样式不正确**
   - 确保组件样式没有被全局样式覆盖
   - 检查 scoped 样式的使用

3. **响应式问题**
   - 确保容器有足够的宽度
   - 检查移动端的触摸事件

### 调试技巧

```javascript
// 开启调试模式
const debugMode = true

const handleChange = (value, index) => {
  if (debugMode) {
    console.log('View changed:', { value, index })
  }
}
```

## 演示页面

访问 `/style_demo/view-toggle` 查看完整的演示和示例代码。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的视图切换功能
- 集成 GSAP 动画系统
- 响应式设计支持
- 预设样式系统集成
