<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终修复验证</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            margin: 0;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #303133;
            margin-bottom: 40px;
        }

        .test-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }

        .test-section h2 {
            color: #303133;
            margin-bottom: 16px;
            border-bottom: 2px solid #E4E7ED;
            padding-bottom: 8px;
        }

        .demo-area {
            background: #F5F7FA;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            display: flex;
            justify-content: center;
        }

        /* 增强的一体化视图切换样式 */
        .enhanced-view-toggle {
            position: relative;
            display: flex;
            background: #f5f7fa;
            border-radius: 8px;
            padding: 3px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .toggle-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
            border-radius: 8px;
        }

        .toggle-fill {
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
            border-radius: 5px;
            z-index: 1;
            box-shadow: 
                0 2px 8px rgba(64, 158, 255, 0.3),
                0 1px 3px rgba(64, 158, 255, 0.4);
            transform-origin: center;
        }

        .toggle-glow {
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: radial-gradient(ellipse at center, rgba(64, 158, 255, 0.4) 0%, transparent 70%);
            border-radius: 5px;
            z-index: 0;
            opacity: 0;
            filter: blur(4px);
        }

        .enhanced-toggle-btn {
            position: relative;
            z-index: 2;
            background: transparent;
            border: none;
            border-radius: 5px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            white-space: nowrap;
            font-weight: 500;
            flex: 1;
            text-align: center;
            overflow: hidden;
            transition: transform 0.2s ease;
        }

        .enhanced-toggle-btn:hover {
            transform: translateY(-1px);
        }

        .enhanced-toggle-btn:active {
            transform: translateY(0);
        }

        .btn-content {
            position: relative;
            z-index: 3;
            color: #606266;
            transition: color 0.3s ease;
        }

        .enhanced-toggle-btn.active .btn-content {
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .btn-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            pointer-events: none;
            z-index: 1;
        }

        .enhanced-toggle-btn:hover .btn-content {
            color: #409EFF;
        }

        .enhanced-toggle-btn.active:hover .btn-content {
            color: white;
        }

        .result {
            text-align: center;
            color: #606266;
            font-size: 14px;
            margin-top: 12px;
        }

        .fix-summary {
            background: #F0F9FF;
            border: 1px solid #BAE6FD;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .fix-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .fix-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
            font-size: 14px;
        }

        .success {
            color: #67C23A;
        }

        .mock-content {
            background: #FAFAFA;
            border-radius: 8px;
            padding: 20px;
            margin-top: 16px;
            text-align: center;
            color: #909399;
            border: 2px dashed #E4E7ED;
            transition: all 0.3s ease;
        }

        .mock-content.active {
            border-color: #409EFF;
            color: #409EFF;
            background: #F0F9FF;
        }

        .test-log {
            background: #FAFAFA;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            font-family: monospace;
            font-size: 12px;
            color: #606266;
            max-height: 150px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }

        .log-success {
            color: #67C23A;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ 最终修复验证</h1>
        
        <div class="test-section">
            <h2>修复内容总结</h2>
            <div class="fix-summary">
                <div class="fix-item">
                    <span class="fix-icon success">✅</span>
                    <span>修复了填充方块位置错误的问题</span>
                </div>
                <div class="fix-item">
                    <span class="fix-icon success">✅</span>
                    <span>移除了"功能开发中"的提示消息</span>
                </div>
                <div class="fix-item">
                    <span class="fix-icon success">✅</span>
                    <span>统一了所有视图的切换逻辑</span>
                </div>
                <div class="fix-item">
                    <span class="fix-icon success">✅</span>
                    <span>保持了完整的动画效果</span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>日历视图切换测试</h2>
            <div class="demo-area">
                <div class="enhanced-view-toggle" id="calendarToggle" data-active="0">
                    <div class="toggle-background"></div>
                    <div class="toggle-fill" id="calendarFill"></div>
                    <div class="toggle-glow" id="calendarGlow"></div>
                    <button class="enhanced-toggle-btn active" onclick="switchView('month', 0, event)">
                        <span class="btn-content">月视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="switchView('week', 1, event)">
                        <span class="btn-content">周视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                    <button class="enhanced-toggle-btn" onclick="switchView('year', 2, event)">
                        <span class="btn-content">年视图</span>
                        <div class="btn-ripple"></div>
                    </button>
                </div>
            </div>
            <div class="result" id="calendarResult">当前选择：月视图</div>
            
            <!-- 模拟内容区域 -->
            <div class="mock-content active" id="monthContent">
                📅 月视图内容区域
            </div>
            <div class="mock-content" id="weekContent" style="display: none;">
                📊 周视图内容区域
            </div>
            <div class="mock-content" id="yearContent" style="display: none;">
                📈 年视图内容区域
            </div>
            
            <div class="test-log" id="testLog">
                <div class="log-entry log-success">[初始化] 页面加载完成</div>
            </div>
        </div>

        <div class="test-section">
            <h2>测试说明</h2>
            <ul>
                <li><strong>所有视图都可正常切换</strong>：月视图、周视图、年视图</li>
                <li><strong>填充位置正确</strong>：蓝色填充方块会准确移动到对应按钮位置</li>
                <li><strong>无提示消息</strong>：不再显示"功能开发中"的提示</li>
                <li><strong>完整动画效果</strong>：涟漪、填充移动、发光脉冲、按钮弹跳</li>
                <li><strong>即时响应</strong>：点击后立即更新状态和动画</li>
            </ul>
        </div>
    </div>

    <script>
        let currentView = 'month';
        let logEntries = [];

        // 添加日志
        function addLog(message, type = 'success') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logEntries.push({ message: logEntry, type });
            
            const logContainer = document.getElementById('testLog');
            const logElement = document.createElement('div');
            logElement.className = `log-entry log-${type}`;
            logElement.textContent = logEntry;
            logContainer.appendChild(logElement);
            
            // 滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 涟漪效果
        function createRippleEffect(button, event) {
            const ripple = button.querySelector('.btn-ripple');
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;
            
            gsap.set(ripple, {
                width: size,
                height: size,
                x: x,
                y: y,
                scale: 0,
                opacity: 0.6
            });
            
            gsap.to(ripple, {
                scale: 1,
                opacity: 0,
                duration: 0.6,
                ease: "power2.out"
            });
        }

        // 更新填充位置
        function updateFillPosition(activeIndex) {
            const container = document.getElementById('calendarToggle');
            const fill = document.getElementById('calendarFill');
            const glow = document.getElementById('calendarGlow');
            
            if (!container || !fill || activeIndex < 0) return;
            
            const buttons = container.querySelectorAll('.enhanced-toggle-btn');
            if (buttons.length === 0) return;
            
            let offset = 0;
            for (let i = 0; i < activeIndex; i++) {
                offset += buttons[i].offsetWidth;
            }
            
            const activeButton = buttons[activeIndex];
            const width = activeButton.offsetWidth;
            
            addLog(`填充位置更新：offset=${offset}px, width=${width}px, index=${activeIndex}`);
            
            // GSAP 动画：填充背景移动
            gsap.to(fill, {
                x: offset,
                width: width,
                duration: 0.4,
                ease: "power2.out"
            });
            
            // GSAP 动画：发光效果
            if (glow) {
                gsap.to(glow, {
                    x: offset,
                    width: width,
                    duration: 0.4,
                    ease: "power2.out"
                });
                
                // 发光脉冲效果
                gsap.fromTo(glow, 
                    { opacity: 0, scale: 0.8 },
                    { 
                        opacity: 0.3, 
                        scale: 1,
                        duration: 0.3,
                        ease: "power2.out",
                        yoyo: true,
                        repeat: 1
                    }
                );
            }
            
            // 按钮弹跳效果
            gsap.fromTo(activeButton,
                { scale: 0.95 },
                { 
                    scale: 1,
                    duration: 0.3,
                    ease: "back.out(1.7)"
                }
            );
            
            // 文字颜色渐变动画
            buttons.forEach((btn, index) => {
                const content = btn.querySelector('.btn-content');
                btn.classList.toggle('active', index === activeIndex);
                if (index === activeIndex) {
                    gsap.to(content, {
                        color: '#ffffff',
                        duration: 0.3,
                        ease: "power2.out"
                    });
                } else {
                    gsap.to(content, {
                        color: '#606266',
                        duration: 0.3,
                        ease: "power2.out"
                    });
                }
            });
        }

        // 切换内容显示
        function switchContent(view) {
            // 隐藏所有内容
            document.getElementById('monthContent').style.display = 'none';
            document.getElementById('weekContent').style.display = 'none';
            document.getElementById('yearContent').style.display = 'none';
            
            // 移除所有active类
            document.getElementById('monthContent').classList.remove('active');
            document.getElementById('weekContent').classList.remove('active');
            document.getElementById('yearContent').classList.remove('active');
            
            // 显示对应内容
            const contentElement = document.getElementById(view + 'Content');
            if (contentElement) {
                contentElement.style.display = 'block';
                contentElement.classList.add('active');
            }
        }

        // 切换视图（修复后的逻辑）
        function switchView(newView, index, event) {
            if (newView === currentView) {
                addLog('相同视图，跳过切换');
                return;
            }
            
            addLog(`点击切换到：${getViewLabel(newView)} (index: ${index})`);
            
            // 创建涟漪效果
            if (event && event.currentTarget) {
                createRippleEffect(event.currentTarget, event);
                addLog('涟漪效果已触发');
            }
            
            // 立即更新视图状态
            currentView = newView;
            addLog(`视图状态已更新：${newView}`);
            
            // 立即更新填充位置
            updateFillPosition(index);
            
            // 更新显示
            document.getElementById('calendarResult').textContent = `当前选择：${getViewLabel(newView)}`;
            switchContent(newView);
            
            addLog(`切换完成：${getViewLabel(newView)}`, 'success');
        }

        function getViewLabel(view) {
            const labels = {
                'month': '月视图',
                'week': '周视图',
                'year': '年视图'
            };
            return labels[view] || view;
        }

        // 初始化
        window.addEventListener('load', () => {
            updateFillPosition(0);
            switchContent('month');
            addLog('页面初始化完成，所有功能就绪', 'success');
        });
    </script>
</body>
</html>
