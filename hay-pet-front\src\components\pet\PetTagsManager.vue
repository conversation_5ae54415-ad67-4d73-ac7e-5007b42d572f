<template>
  <div class="pet-tags-manager">
    <div class="section-title">🏷️ 宠物标签</div>
    
    <div class="add-tag-section">
      <el-input
        v-model="newTagName"
        placeholder="输入标签，按回车添加"
        clearable
        @keyup.enter="handleAddTag"
        class="new-tag-input"
      />
      <el-button type="primary" @click="handleAddTag" class="add-tag-button">添加标签</el-button>
    </div>

    <!-- 激活的标签显示区域 -->
    <div v-if="activeDisplayTags.length > 0" class="active-tags-display-area">
      <div class="active-tags-list">
        <el-tag
          v-for="tag in activeDisplayTags"
          :key="tag.id"
          :color="tag.color"
          effect="dark"
        >
          {{ tag.name }}
        </el-tag>
      </div>
    </div>
    <div v-else class="active-tags-empty-hint">暂无激活标签</div>

    <el-divider>所有标签管理</el-divider>

    <div v-if="petTags.length === 0" class="empty-hint">
      暂无标签，请在上方输入框添加
    </div>

    <el-table
      v-else
      :data="petTags"
      style="width: 100%"
      row-key="id"
      class="tag-table"
      ref="tagTableRef"
    >
      <el-table-column width="55">
        <template #header>
          <span>启用</span>
        </template>
        <template #default="scope">
          <el-checkbox v-model="scope.row.is_active" @change="handleToggleTagActiveStatus(scope.row)"></el-checkbox>
        </template>
      </el-table-column>
      <el-table-column label="标签名称" prop="name">
        <template #default="scope">
          <template v-if="scope.row.isEditingName">
            <el-input
              v-model="scope.row.name"
              size="small"
              @keyup.enter="handleSaveTagName(scope.row)"
              @blur="handleCancelEditName(scope.row)"
            />
          </template>
          <template v-else>
            <span @dblclick.stop="handleEditTagName(scope.row)">
              {{ scope.row.name }}
            </span>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="标签颜色" prop="color" width="180">
        <template #default="scope">
          <template v-if="scope.row.isEditingColor">
            <el-color-picker
              v-model="scope.row.color"
              size="small"
              @change="handleSaveTagColor(scope.row)"
              @hide="scope.row.isEditingColor = false"
            />
          </template>
          <template v-else>
            <span
              class="tag-color-display"
              :style="{ backgroundColor: scope.row.color }"
              @click="handleCycleTagColor(scope.row)"
              @dblclick.stop="handleEditTagColor(scope.row)"
            ></span>
            <span>{{ scope.row.color }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template #default="scope">
          <el-button link type="danger" size="small" @click="handleDeleteTag(scope.row.id, scope.row.name)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch, computed, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Sortable from 'sortablejs'

const PRESET_COLORS = [
  '#409EFF', // 蓝色
  '#67C23A', // 绿色
  '#E6A23C', // 橙色
  '#F56C6C', // 红色
  '#909399', // 灰色
  '#79bbff', // 浅蓝
  '#85ce61', // 浅绿
  '#e2bb7a', // 浅橙
  '#f09898', // 浅红
  '#b1b3b8'  // 浅灰
];

// Props
const props = defineProps({
  petId: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['tagsUpdated'])

// 依赖注入
const supabase = inject('supabase')

// 响应式数据
const petTags = ref([]) // 存储当前宠物的标签对象 { id, name, color, is_active, isEditingName, isEditingColor, originalName, originalColor, sort_index }
const newTagName = ref('') // 新标签输入框的值

// ElTable 的引用
const tagTableRef = ref(null);

// Sortable 实例
let sortableInstance = null; // 用于存储 Sortable 实例

// 计算属性，用于过滤显示在单独区域的激活标签
const activeDisplayTags = computed(() => {
  const filtered = petTags.value.filter(tag => tag.is_active);
  return filtered;
});

// 处理添加标签
const handleAddTag = async () => {
  const tagName = newTagName.value.trim()
  if (!tagName) {
    ElMessage.warning('标签名称不能为空')
    return
  }

  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')
    
    let tagId;
    let tagColor = PRESET_COLORS[0]; // 默认颜色



    const { data: existingTagArray, error: existingTagError } = await supabase
      .from('pet_tags')
      .select('*') 
      .eq('user_id', user.id)
      .eq('name', tagName); 
    
    if (existingTagError) { 
      console.error('handleAddTag: 检查现有标签错误:', existingTagError); // Keep this error log
      throw existingTagError;
    }
    
    const existingTag = existingTagArray && existingTagArray.length > 0 ? existingTagArray[0] : null; // 检查数组中是否有数据

    if (existingTag) {
      tagId = existingTag.id;
      tagColor = existingTag.color; // 使用现有标签的颜色
      // 检查是否已经关联到当前宠物
      if (petTags.value.some(tag => tag.name === tagName)) {
        ElMessage.warning(`标签 "${tagName}" 已存在于当前宠物`)
        newTagName.value = ''; // 清空输入框
        return;
      }
    } else {
      const tagToInsert = {
        user_id: user.id,
        name: tagName,
        color: tagColor,
        is_active: true, // 新标签默认激活
        sort_index: petTags.value.length // 新标签默认排序索引为当前标签数量
      };

      const { data: newTagResult, error: createError } = await supabase
        .from('pet_tags')
        .insert(tagToInsert)
        .select('id'); 

      if (createError) {
        console.error('handleAddTag: 插入新标签错误:', createError); // Keep this error log
        throw createError;
      }
      tagId = newTagResult[0].id; 
    }

    const { error: addRelationError } = await supabase
      .from('pet_tag_relations')
      .insert({
        pet_id: props.petId,
        tag_id: tagId
      })
    
    if (addRelationError) {
      if (addRelationError.code === '23505') { 
        ElMessage.warning(`标签 "${tagName}" 已存在于当前宠物！`);
      } else {
        console.error('handleAddTag: 添加关系错误:', addRelationError); // Keep this error log
        throw addRelationError;
      }
    }
    
    ElMessage.success(`标签 "${tagName}" 添加成功！`);
    newTagName.value = ''; // 清空输入框
    await fetchPetTags();
    emit('tagsUpdated', petTags.value.map(tag => tag.name));
  } catch (error) {
    console.error('添加标签失败:', error) // Keep this error log
    ElMessage.error('添加标签失败: ' + error.message)
  }
}

// 处理移除标签（从宠物中移除关联）- 这个函数现在可能不再需要直接调用，因为我们将通过 handleDeleteTag 处理
const handleRemoveTag = async (tagName) => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { data: tag, error: tagError } = await supabase
      .from('pet_tags')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', tagName)
      .single()

    if (tagError) throw tagError;
    if (!tag) {
      ElMessage.error('未找到要移除的标签')
      return;
    }

    const { error: removeRelationError } = await supabase
      .from('pet_tag_relations')
      .delete()
      .eq('pet_id', props.petId)
      .eq('tag_id', tag.id)
    
    if (removeRelationError) throw removeRelationError;

    ElMessage.success(`标签 "${tagName}" 已移除！`);
    await fetchPetTags();
    emit('tagsUpdated', petTags.value.map(tag => tag.name));
  } catch (error) {
    console.error('移除标签失败:', error)
    ElMessage.error('移除标签失败: ' + error.message)
  }
}

// 获取宠物标签
const fetchPetTags = async () => {
  if (!props.petId) {
    petTags.value = []
    // 当 petId 为空时，销毁 Sortable 实例以避免问题
    if (sortableInstance) {
        sortableInstance.destroy();
        sortableInstance = null;
    }
    return
  }
  
  try {
    const { data, error } = await supabase
      .from('pet_tag_relations')
      .select(`
        tag_id,
        pet_tags (
          id,
          name,
          color,
          is_active,
          sort_index
        )
      `)
      .eq('pet_id', props.petId)
      .order('sort_index', { foreignTable: 'pet_tags', ascending: true }) // 明确指定通过 pet_tags 表的 sort_index 排序
    
    if (error) throw error
    
    petTags.value = data?.map(relation => ({
      id: relation.pet_tags.id,
      name: relation.pet_tags.name,
      color: relation.pet_tags.color,
      is_active: relation.pet_tags.is_active || false, // 确保有默认值
      sort_index: relation.pet_tags.sort_index, // 获取 sort_index
      isEditingName: false,
      isEditingColor: false,
      originalName: relation.pet_tags.name,
      originalColor: relation.pet_tags.color
    })) || []
    // 数据加载后，确保表格已渲染并初始化 Sortable.js
    initSortableTable();
  } catch (error) {
    console.error('获取宠物标签失败:', error)
    ElMessage.error('获取宠物标签失败: ' + error.message)
  }
}

// 处理标签名称双击编辑
const handleEditTagName = (tag) => {
  tag.isEditingName = true;
  tag.originalName = tag.name; // 保存原始名称以备取消
}

// 处理保存标签名称
const handleSaveTagName = async (tag) => {
  if (!tag.name.trim()) {
    ElMessage.warning('标签名称不能为空');
    tag.name = tag.originalName; // 恢复原始值
    tag.isEditingName = false; // 退出编辑模式
    return;
  }

  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    // 检查新的标签名是否已存在（排除自身）
    const { data: existingTags, error: checkError } = await supabase
      .from('pet_tags')
      .select('id')
      .eq('user_id', user.id)
      .eq('name', tag.name.trim())
      .neq('id', tag.id)

    if (checkError) throw checkError;

    if (existingTags && existingTags.length > 0) {
      ElMessage.warning(`标签 "${tag.name}" 已存在`);
      tag.name = tag.originalName; // 恢复原始值
      tag.isEditingName = false; // 退出编辑模式
      return;
    }

    const { error: updateError } = await supabase
      .from('pet_tags')
      .update({ name: tag.name.trim() })
      .eq('id', tag.id)
      .eq('user_id', user.id);
    
    if (updateError) throw updateError;
    
    ElMessage.success(`标签 "${tag.name}" 修改成功！`);
    tag.isEditingName = false;
    tag.originalName = tag.name; // 更新原始名称
    emit('tagsUpdated', petTags.value.map(t => t.name));
  } catch (error) {
    console.error('修改标签失败:', error);
    ElMessage.error('修改标签失败: ' + error.message);
    tag.name = tag.originalName; // 恢复原始值
    tag.isEditingName = false; // 退出编辑模式
  }
}

// 处理取消标签名称编辑
const handleCancelEditName = (tag) => {
  tag.name = tag.originalName; // 恢复原始值
  tag.isEditingName = false;
}

// 处理点击颜色块，循环预设颜色
const handleCycleTagColor = async (tag) => {
  const currentIndex = PRESET_COLORS.indexOf(tag.color);
  const nextIndex = (currentIndex + 1) % PRESET_COLORS.length;
  tag.color = PRESET_COLORS[nextIndex];
  await handleSaveTagColor(tag);
}

// 处理双击颜色块，打开颜色选择器
const handleEditTagColor = (tag) => {
  tag.isEditingColor = true;
  tag.originalColor = tag.color; // 保存原始颜色
}

// 处理保存标签颜色
const handleSaveTagColor = async (tag) => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { error: updateError } = await supabase
      .from('pet_tags')
      .update({ color: tag.color })
      .eq('id', tag.id)
      .eq('user_id', user.id);
    
    if (updateError) throw updateError;
    
    ElMessage.success(`标签 "${tag.name}" 颜色修改成功！`);
    tag.isEditingColor = false;
    tag.originalColor = tag.color; // 更新原始颜色
  } catch (error) {
    console.error('修改标签颜色失败:', error);
    ElMessage.error('修改标签颜色失败: ' + error.message);
    tag.color = tag.originalColor; // 恢复原始颜色
    tag.isEditingColor = false;
  }
}

// 处理标签激活状态切换
const handleToggleTagActiveStatus = async (tag) => {
  try {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    const { error: updateError } = await supabase
      .from('pet_tags')
      .update({ is_active: tag.is_active })
      .eq('id', tag.id)
      .eq('user_id', user.id);
    
    if (updateError) throw updateError;
    
    ElMessage.success(`标签 "${tag.name}" 已${tag.is_active ? '激活' : '停用'}！`);
    await fetchPetTags(); // 重新获取数据以更新表格显示和激活标签显示区域
  } catch (error) {
    console.error('修改标签激活状态失败:', error);
    ElMessage.error('修改标签激活状态失败: ' + error.message);
    tag.is_active = !tag.is_active; // 恢复原始状态
  }
}

// 处理删除标签（从表格中删除，并可能从 pet_tags 表中删除）
const handleDeleteTag = async (tagId, tagName) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标签 "${tagName}" 吗？此操作将同时解除与所有宠物的关联。如果该标签不再被任何宠物使用，它将被永久删除。`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('用户未登录')

    // 1. 删除与该标签相关的所有 pet_tag_relations 记录
    const { error: deleteRelationsError } = await supabase
      .from('pet_tag_relations')
      .delete()
      .eq('tag_id', tagId)
    
    if (deleteRelationsError) throw deleteRelationsError;

    // 2. 检查该标签是否还在 pet_tags 中被其他宠物使用
    const { data: remainingRelations, error: checkRelationsError } = await supabase
      .from('pet_tag_relations')
      .select('id')
      .eq('tag_id', tagId)

    if (checkRelationsError) throw checkRelationsError;

    if (!remainingRelations || remainingRelations.length === 0) {
      // 如果没有其他宠物使用该标签，则从 pet_tags 表中删除该标签
      const { error: deleteTagError } = await supabase
        .from('pet_tags')
        .delete()
        .eq('id', tagId)
        .eq('user_id', user.id);
      
      if (deleteTagError) throw deleteTagError;
    }

    ElMessage.success(`标签 "${tagName}" 已删除！`);
    await fetchPetTags();
    emit('tagsUpdated', petTags.value.map(tag => tag.name));
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除标签失败:', error);
      ElMessage.error('删除标签失败: ' + error.message);
    }
  }
}

// 处理拖拽结束
const handleDragEnd = async () => {
  try {
    const updates = petTags.value.map((tag, index) => ({
      id: tag.id,
      name: tag.name, // 包含 name
      color: tag.color, // 包含 color
      is_active: tag.is_active, // 包含 is_active
      sort_index: index // 使用当前在数组中的索引作为新的 sort_index
    }));



    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('用户未登录');

    const { error: updateError } = await supabase
      .from('pet_tags')
      .upsert(updates, { onConflict: 'id' });
    
    if (updateError) throw updateError;

    ElMessage.success('标签排序更新成功！');
  } catch (error) {
    console.error('更新标签排序失败:', error);
    ElMessage.error('更新标签排序失败: ' + error.message);
  }
}

// 初始化 Sortable.js
const initSortableTable = () => {
  if (tagTableRef.value) {
    nextTick(() => {
      const el = tagTableRef.value.$el.querySelector('.el-table__body-wrapper tbody');
      if (el) {
        // 如果 Sortable 实例已存在，先销毁
        if (sortableInstance) {
          sortableInstance.destroy();
          sortableInstance = null;
        }

        sortableInstance = new Sortable(el, {
          animation: 150, // 拖拽动画时间
          handle: '.el-table__row', // 整个行作为拖拽句柄
          onEnd: (evt) => {
            const { oldIndex, newIndex } = evt;
            // 确保拖拽发生在有效索引范围内
            if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== newIndex) {
              const [movedItem] = petTags.value.splice(oldIndex, 1);
              petTags.value.splice(newIndex, 0, movedItem);
              handleDragEnd(); // 保存新的排序到数据库
            }
          },
        });
      }
    });
  }
};

// 监听宠物ID变化
watch(() => props.petId, (newPetId) => {
  if (newPetId) {
    fetchPetTags()
  } else {
    petTags.value = []
  }
}, { immediate: true })

// 组件挂载时获取数据
onMounted(() => {
    // fetchPetTags 会在数据加载后自动调用 initSortableTable
    fetchPetTags()
})

// 组件卸载时销毁 Sortable 实例
onUnmounted(() => {
  if (sortableInstance) {
    sortableInstance.destroy();
    sortableInstance = null;
  }
})
</script>

<style scoped lang="scss">
.pet-tags-manager {
  padding: 0;
  background: transparent;
  border-radius: 0;
  margin-bottom: 0;
  max-height: 500px; /* 设置最大高度 */
  overflow-y: auto; /* 超出部分显示垂直滚动条 */
  box-sizing: border-box; /* 确保 padding 包含在 max-height 内 */
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
  }

  .add-tag-section {
    display: flex;
    gap: 10px;
    margin-bottom: 16px;

    .new-tag-input {
      flex-grow: 1;
    }
  }
  
  .empty-hint {
    color: #909399;
    font-size: 14px;
    font-style: italic;
    padding: 16px;
    text-align: center;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 4px;
    border: 2px dashed #e4e7ed;
    width: 100%;
    margin-bottom: 16px;
  }

  .active-tags-display-area {
    margin-top: 20px; /* 调整间距 */
    margin-bottom: 20px;
    padding: 0; /* 移除内边距 */
    background: none; /* 移除背景 */
    border: none; /* 移除边框 */

    .display-area-title { /* 这个类现在已经不存在于模板中，可以移除 */
      display: none;
    }

    .active-tags-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .el-tag {
        font-weight: 600;
        cursor: default;
        border-radius: 12px;
        border: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        /* color 属性会直接设置背景色 */
      }
    }
  }

  .active-tags-empty-hint {
    color: #909399;
    font-size: 14px;
    font-style: italic;
    padding: 5px 0; /* 简化内边距 */
    text-align: left; /* 左对齐 */
    margin-top: 20px;
    margin-bottom: 20px;
    /* 移除背景、边框 */
    background: none;
    border: none;
  }

  .add-tag-section {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    align-items: flex-end;

    .new-tag-input {
      flex: 1;

      :deep(.el-input__wrapper) {
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409EFF;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }

        &.is-focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
        }
      }
    }

    .add-tag-button {
      border-radius: 12px;
      font-weight: 600;
      background: linear-gradient(135deg, #409EFF 0%, #36D1DC 50%, #5B86E5 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      }
    }
  }

  .el-divider {
    margin: 25px 0;
    .el-divider__text {
      background-color: transparent;
      color: #606266;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .tag-table {
    .tag-color-display {
      display: inline-block;
      width: 24px; /* 调整大小 */
      height: 24px; /* 调整大小 */
      border-radius: 8px;
      margin-right: 8px;
      vertical-align: middle;
      border: 2px solid rgba(255, 255, 255, 0.8);
      cursor: pointer; /* 添加指针样式 */
      transition: all 0.3s ease; /* 添加过渡效果 */
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: scale(1.15); /* 悬停放大 */
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }
    }

    /* 为编辑状态下的输入框添加一些样式，使其更好看 */
    .el-input.el-input--small {
      width: 100%;
    }

    /* 删除按钮样式 */
    .el-button--danger.is-link {
      font-weight: 600;
      border-radius: 8px;
      padding: 4px 8px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #F56C6C 0%, #FF6B9D 50%, #C44569 100%);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
      }
    }
  }
}
</style>