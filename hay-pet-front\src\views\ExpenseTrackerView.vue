<template>
  <el-container class="expense-tracker-view min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <el-header class="bg-white border-b border-gray-200 sticky top-0 z-50 transition-all duration-300 shadow-md">
      <div class="container mx-auto px-4 py-3 flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <div class="text-pet text-2xl">
            <el-icon><Paw /></el-icon>
          </div>
          <h1 class="text-xl font-bold text-pet">宠物开销记录</h1>
        </div>
        
        <div class="flex items-center space-x-4">
          <el-button circle class="p-2" id="add-btn" @click="openModal(false)">
            <el-icon><Plus /></el-icon>
          </el-button>
          
          <el-dropdown id="user-menu">
            <span class="el-dropdown-link flex items-center space-x-2 focus:outline-none">
              <el-avatar src="https://picsum.photos/id/1025/200/200" alt="用户头像" class="w-8 h-8 border-2 border-pet" />
              <span class="hidden md:inline font-medium">铲屎官</span>
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人中心</el-dropdown-item>
                <el-dropdown-item>设置</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主要内容 -->
    <el-main class="flex-grow container mx-auto px-4 py-6">
      <!-- 统计概览 -->
      <el-row :gutter="24" class="mb-8">
        <el-col :xs="24" :sm="12" :md="8" class="mb-6 md:mb-0">
          <el-card shadow="hover" class="card-shadow card-hover animate-slide-up">
            <div class="flex items-start justify-between">
              <div>
                <p class="text-gray-500 font-medium mb-1">总支出</p>
                <h3 class="text-3xl font-bold text-gray-800">¥{{ totalExpense.toFixed(2) }}</h3>
                <p :class="['text-sm mt-2 flex items-center', expenseChange >= 0 ? 'text-danger' : 'text-success']">
                  <el-icon><component :is="expenseChange >= 0 ? 'ArrowUpBold' : 'ArrowDownBold'" /></el-icon> 
                  {{ Math.abs(expenseChange) }}% <span class="text-gray-500 ml-1">vs 上月</span>
                </p>
              </div>
              <div class="bg-danger/10 p-3 rounded-lg">
                <el-icon class="text-danger text-xl"><Money /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8" class="mb-6 md:mb-0">
          <el-card shadow="hover" class="card-shadow card-hover animate-slide-up" style="animation-delay: 0.2s">
            <div class="flex items-start justify-between">
              <div>
                <p class="text-gray-500 font-medium mb-1">本月记录</p>
                <h3 class="text-3xl font-bold text-gray-800">{{ thisMonthCount }}</h3>
                <p :class="['text-sm mt-2 flex items-center', recordChange >= 0 ? 'text-success' : 'text-danger']">
                  <el-icon><component :is="recordChange >= 0 ? 'ArrowUpBold' : 'ArrowDownBold'" /></el-icon> 
                  {{ Math.abs(recordChange) }}% <span class="text-gray-500 ml-1">vs 上月</span>
                </p>
              </div>
              <div class="bg-success/10 p-3 rounded-lg">
                <el-icon class="text-success text-xl"><List /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="24" :md="8">
          <el-card shadow="hover" class="card-shadow card-hover animate-slide-up" style="animation-delay: 0.3s">
            <div class="flex items-start justify-between">
              <div>
                <p class="text-gray-500 font-medium mb-1">平均支出</p>
                <h3 class="text-3xl font-bold text-gray-800">¥{{ averageExpense.toFixed(2) }}</h3>
                <p class="text-warning text-sm mt-2 flex items-center">
                  <el-icon><InfoFilled /></el-icon> 每笔支出
                </p>
              </div>
              <div class="bg-warning/10 p-3 rounded-lg">
                <el-icon class="text-warning text-xl"><PieChart /></el-icon>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 图表区域 -->
      <el-row :gutter="24" class="mb-8">
        <el-col :xs="24" :lg="12" class="mb-6 lg:mb-0">
          <el-card shadow="never" class="card-shadow animate-slide-up" style="animation-delay: 0.4s">
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800">支出分类</h3>
                <el-radio-group v-model="chartTimeRange" size="small">
                  <el-radio-button label="month">本月</el-radio-button>
                  <el-radio-button label="all">全部</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="h-64">
              <canvas id="expense-chart-canvas"></canvas>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :lg="12">
          <el-card shadow="never" class="card-shadow animate-slide-up" style="animation-delay: 0.5s">
            <template #header>
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800">月度趋势</h3>
                <el-button text circle>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
              </div>
            </template>
            <div class="h-64">
              <canvas id="trend-chart-canvas"></canvas>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 最近记录 -->
      <section class="mb-12">
        <div class="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <h2 class="text-[clamp(1.5rem,3vw,2rem)] font-bold text-gray-800">最近记录</h2>
          
          <div class="flex space-x-3 mt-4 md:mt-0">
            <el-select v-model="selectedCategory" placeholder="所有类型" id="category-filter" class="w-full md:w-auto">
              <el-option label="所有类型" value="all" />
              <el-option label="宠物食品" value="food" />
              <el-option label="医疗保健" value="medical" />
              <el-option label="宠物用品" value="accessories" />
              <el-option label="美容洗澡" value="grooming" />
              <el-option label="训练服务" value="training" />
              <el-option label="其他支出" value="other" />
            </el-select>
            
            <el-input v-model="searchTerm" placeholder="搜索记录..." id="search-input" :prefix-icon="Search" class="w-full md:w-auto" />
          </div>
        </div>
        
        <el-card shadow="never" class="card-shadow animate-slide-up" style="animation-delay: 0.6s">
          <el-table :data="filteredRecords" style="width: 100%" empty-text="暂无支出记录">
            <template #empty>
              <div class="px-6 py-12 text-center text-gray-500">
                <div class="mb-4 text-4xl">
                  <el-icon><FolderOpened /></el-icon>
                </div>
                <p class="text-lg">暂无支出记录</p>
                <p class="text-sm mt-2">点击右上角 "+" 添加第一条记录</p>
              </div>
            </template>
            <el-table-column prop="date" label="日期" width="120" :formatter="formatTableDate" />
            <el-table-column prop="category" label="分类" width="120">
              <template #default="{ row }">
                <el-tag :type="categoryTagType(row.category)" disable-transitions>
                  {{ categoryNames[row.category] }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="amount" label="金额" width="100" align="right">
              <template #default="{ row }">
                <span class="font-medium text-danger">-¥{{ row.amount.toFixed(2) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template #default="{ row }">
                <el-button text circle @click="editRecord(row.id)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button text circle type="danger" @click="deleteRecord(row.id)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </section>
    </el-main>

    <!-- 添加/编辑记录模态框 -->
    <el-dialog v-model="modalVisible" :title="modalTitle" width="90%" max-width="500px" custom-class="animate-slide-up">
      <el-form :model="recordForm" ref="recordFormRef" label-position="top">
        <el-form-item label="金额" prop="amount" :rules="[{ required: true, message: '请输入金额' }]" >
          <el-input-number v-model="recordForm.amount" :precision="2" :step="0.01" :min="0" controls-position="right" class="w-full" placeholder="0.00">
            <template #prefix>¥</template>
          </el-input-number>
        </el-form-item>
        
        <el-form-item label="分类" prop="category" :rules="[{ required: true, message: '请选择分类' }]" >
          <el-select v-model="recordForm.category" placeholder="选择分类" class="w-full">
            <el-option label="宠物食品" value="food" />
            <el-option label="医疗保健" value="medical" />
            <el-option label="宠物用品" value="accessories" />
            <el-option label="美容洗澡" value="grooming" />
            <el-option label="训练服务" value="training" />
            <el-option label="其他支出" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="日期" prop="date" :rules="[{ required: true, message: '请选择日期' }]" >
          <el-date-picker v-model="recordForm.date" type="date" placeholder="选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="w-full" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input v-model="recordForm.description" placeholder="添加描述..." />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="modalVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRecord">{{ recordForm.id ? '更新记录' : '保存记录' }}</el-button>
      </template>
    </el-dialog>

    <!-- 页脚 -->
    <el-footer class="bg-white border-t border-gray-200 py-6">
      <div class="container mx-auto px-4 text-center text-sm text-gray-500">
        © {{ new Date().getFullYear() }} 宠物开销记录. 为你的毛孩子做好财务规划.
      </div>
    </el-footer>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Paw, Plus, ArrowDown, Money, List, InfoFilled, PieChart, MoreFilled, Search, FolderOpened, Edit, Delete, ArrowUpBold, ArrowDownBold
} from '@element-plus/icons-vue';
import Chart from 'chart.js/auto';
import 'chartjs-adapter-date-fns'; // 如果使用时间轴

// 响应式数据
const records = ref([
  { id: 1, date: '2025-06-19', category: 'food', description: '皇家猫粮 2kg', amount: 189.00 },
  { id: 2, date: '2025-06-15', category: 'medical', description: '宠物医院体检', amount: 350.00 },
  { id: 3, date: '2025-06-10', category: 'accessories', description: '猫抓板和猫窝', amount: 120.00 },
  { id: 4, date: '2025-05-28', category: 'grooming', description: '猫咪洗澡美容', amount: 150.00 },
  { id: 5, date: '2025-05-20', category: 'food', description: '猫罐头 x12', amount: 240.00 },
  { id: 6, date: '2025-05-10', category: 'medical', description: '驱虫药', amount: 85.00 },
  { id: 7, date: '2025-04-25', category: 'accessories', description: '自动喂食器', amount: 320.00 },
  { id: 8, date: '2025-04-15', category: 'grooming', description: '猫咪剪指甲', amount: 60.00 },
  { id: 9, date: '2025-04-05', category: 'food', description: '进口猫粮 1kg', amount: 120.00 },
  { id: 10, date: '2025-03-20', category: 'medical', description: '疫苗接种', amount: 200.00 },
]);

const selectedCategory = ref('all');
const searchTerm = ref('');
const chartTimeRange = ref('month'); // 'month' or 'all'

// 模态框相关
const modalVisible = ref(false);
const modalTitle = ref('添加记录');
const recordFormRef = ref(null);
const recordForm = ref({
  id: null,
  amount: null,
  category: '',
  date: new Date().toISOString().split('T')[0],
  description: ''
});

// 图表实例
let expenseChartInstance = null;
let trendChartInstance = null;

// 分类信息
const categoryNames = {
  food: '宠物食品',
  medical: '医疗保健',
  accessories: '宠物用品',
  grooming: '美容洗澡',
  training: '训练服务',
  other: '其他支出'
};

const categoryTagType = (category) => {
  const types = {
    food: 'primary',
    medical: 'danger',
    accessories: 'success',
    grooming: 'warning',
    training: 'info',
    other: ''
  };
  return types[category] || '';
};

// 计算属性
const filteredRecords = computed(() => {
  return records.value
    .filter(record => {
      if (selectedCategory.value === 'all') return true;
      return record.category === selectedCategory.value;
    })
    .filter(record => {
      if (!searchTerm.value) return true;
      return record.description.toLowerCase().includes(searchTerm.value.toLowerCase());
    })
    .sort((a, b) => new Date(b.date) - new Date(a.date));
});

const getRecordsByMonth = (year, month) => {
  return records.value.filter(record => {
    const recordDate = new Date(record.date);
    return recordDate.getFullYear() === year && recordDate.getMonth() === month;
  });
};

const currentMonthRecords = computed(() => {
  const today = new Date();
  return getRecordsByMonth(today.getFullYear(), today.getMonth());
});

const lastMonthRecords = computed(() => {
  const today = new Date();
  const lastMonthDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  return getRecordsByMonth(lastMonthDate.getFullYear(), lastMonthDate.getMonth());
});

const totalExpense = computed(() => {
  return currentMonthRecords.value.reduce((sum, record) => sum + record.amount, 0);
});

const thisMonthCount = computed(() => currentMonthRecords.value.length);

const averageExpense = computed(() => {
  return thisMonthCount.value > 0 ? totalExpense.value / thisMonthCount.value : 0;
});

const expenseChange = computed(() => {
  const lastMonthTotal = lastMonthRecords.value.reduce((sum, record) => sum + record.amount, 0);
  if (lastMonthTotal === 0) return thisMonthCount.value > 0 ? 100 : 0; // 如果上月无支出，本月有则视为100%增长
  return ((totalExpense.value - lastMonthTotal) / lastMonthTotal * 100);
});

const recordChange = computed(() => {
  const lastMonthCount = lastMonthRecords.value.length;
  if (lastMonthCount === 0) return thisMonthCount.value > 0 ? 100 : 0;
  return ((thisMonthCount.value - lastMonthCount) / lastMonthCount * 100);
});

// 方法
const openModal = (isEditing, recordId = null) => {
  modalTitle.value = isEditing ? '编辑记录' : '添加记录';
  if (isEditing && recordId) {
    const record = records.value.find(r => r.id === recordId);
    if (record) {
      recordForm.value = { ...record };
    }
  } else {
    recordForm.value = {
      id: null,
      amount: null,
      category: '',
      date: new Date().toISOString().split('T')[0],
      description: ''
    };
  }
  modalVisible.value = true;
  nextTick(() => {
    recordFormRef.value?.clearValidate();
  });
};

const saveRecord = async () => {
  if (!recordFormRef.value) return;
  try {
    await recordFormRef.value.validate();
    const newRecordData = { ...recordForm.value };
    if (newRecordData.id) {
      // 更新记录
      const index = records.value.findIndex(r => r.id === newRecordData.id);
      if (index !== -1) {
        records.value[index] = newRecordData;
      }
    } else {
      // 添加记录
      newRecordData.id = Date.now(); // 简单唯一ID
      records.value.push(newRecordData);
    }
    modalVisible.value = false;
    ElMessage.success(newRecordData.id && !isEditing ? '记录添加成功' : '记录更新成功');
    updateCharts(); // 更新图表
  } catch (error) {
    // ElMessage.error('请检查表单信息');
    console.log('表单验证失败', error);
  }
};

const deleteRecord = (id) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    records.value = records.value.filter(record => record.id !== id);
    ElMessage.success('记录删除成功');
    updateCharts(); // 更新图表
  }).catch(() => {
    // 用户取消删除
  });
};

const editRecord = (id) => {
  openModal(true, id);
};

const formatTableDate = (row, column, cellValue) => {
  if (!cellValue) return '';
  const date = new Date(cellValue);
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
};

// 图表相关方法
const getCategoryChartData = () => {
  const categoryMap = {};
  const recordsToChart = chartTimeRange.value === 'month' ? currentMonthRecords.value : records.value;
  
  recordsToChart.forEach(record => {
    if (!categoryMap[record.category]) {
      categoryMap[record.category] = 0;
    }
    categoryMap[record.category] += record.amount;
  });
  
  const labels = Object.keys(categoryMap).map(category => categoryNames[category] || '未知分类');
  const data = Object.values(categoryMap);
  
  return {
    labels,
    datasets: [{
      data,
      backgroundColor: [
        '#3B82F6', // blue
        '#EF4444', // red
        '#10B981', // green
        '#8B5CF6', // purple
        '#F59E0B', // yellow
        '#6B7280'  // gray
      ],
      borderWidth: 0
    }]
  };
};

const getTrendChartData = () => {
  const labels = [];
  const data = [];
  const numMonths = 6; // 显示最近6个月
  const today = new Date();

  for (let i = numMonths - 1; i >= 0; i--) {
    const date = new Date(today.getFullYear(), today.getMonth() - i, 1);
    const year = date.getFullYear();
    const month = date.getMonth();
    labels.push(`${year}年${month + 1}月`);
    
    const monthRecords = getRecordsByMonth(year, month);
    data.push(monthRecords.reduce((sum, record) => sum + record.amount, 0));
  }
  
  return {
    labels,
    datasets: [{
      label: '月度支出',
      data,
      backgroundColor: '#FF6B8B',
      borderRadius: 4
    }]
  };
};

const initCharts = () => {
  nextTick(() => {
    const expenseCtx = document.getElementById('expense-chart-canvas')?.getContext('2d');
    if (expenseCtx) {
      if (expenseChartInstance) expenseChartInstance.destroy();
      expenseChartInstance = new Chart(expenseCtx, {
        type: 'doughnut',
        data: getCategoryChartData(),
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right',
            }
          },
          cutout: '70%'
        }
      });
    }

    const trendCtx = document.getElementById('trend-chart-canvas')?.getContext('2d');
    if (trendCtx) {
      if (trendChartInstance) trendChartInstance.destroy();
      trendChartInstance = new Chart(trendCtx, {
        type: 'bar',
        data: getTrendChartData(),
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false, // 通常条形图的图例不是很必要，如果只有一个数据集
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                drawBorder: false
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          }
        }
      });
    }
  });
};

const updateCharts = () => {
  if (expenseChartInstance) {
    expenseChartInstance.data = getCategoryChartData();
    expenseChartInstance.update();
  }
  if (trendChartInstance) {
    trendChartInstance.data = getTrendChartData();
    trendChartInstance.update();
  }
};

// 生命周期钩子
onMounted(() => {
  initCharts();
});

// 监听图表时间范围变化
watch(chartTimeRange, () => {
  updateCharts();
});

</script>

<style scoped lang="scss">
// 沿用 Tailwind CSS 类名，或根据需要转换为 SCSS
// 例如：
.text-pet {
  color: #FF6B8B; // 主色调
}

.card-shadow {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.03);
}

.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

.animate-slide-up {
  animation: slideUp 0.5s ease forwards;
  opacity: 0;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 确保 Element Plus 图标正确显示
.el-icon {
  vertical-align: middle;
}

// 响应式调整
@media (max-width: 768px) {
  .el-dialog {
    --el-dialog-width: 90% !important;
  }
  .el-table {
    font-size: 12px; // 移动端表格字体可以小一些
    .el-button--text {
      padding: 4px; // 调整按钮padding
    }
  }
  .el-select, .el-input {
    width: 100%;
  }
}

</style>