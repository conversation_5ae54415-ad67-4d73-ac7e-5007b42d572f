-- 创建预算管理表
CREATE TABLE IF NOT EXISTS public.budgets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    pet_id UUID REFERENCES public.pets(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL,
    monthly_limit DECIMAL(10,2) NOT NULL,
    current_spent DECIMAL(10,2) DEFAULT 0,
    alert_threshold DECIMAL(3,2) DEFAULT 0.8, -- 80%预警阈值
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(pet_id, category)
);

-- 创建预算历史记录表
CREATE TABLE IF NOT EXISTS public.budget_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    budget_id UUID REFERENCES public.budgets(id) ON DELETE CASCADE,
    month DATE NOT NULL, -- 月份（YYYY-MM-01格式）
    planned_amount DECIMAL(10,2) NOT NULL,
    actual_amount DECIMAL(10,2) DEFAULT 0,
    variance_percentage DECIMAL(5,2), -- 差异百分比
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 启用行级安全
ALTER TABLE public.budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_history ENABLE ROW LEVEL SECURITY;

-- 创建预算表的RLS策略
-- 用户只能查看自己宠物的预算
DROP POLICY IF EXISTS "Users can view own pet budgets" ON public.budgets;
CREATE POLICY "Users can view own pet budgets" ON public.budgets
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = budgets.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能为自己的宠物创建预算
DROP POLICY IF EXISTS "Users can insert budgets for own pets" ON public.budgets;
CREATE POLICY "Users can insert budgets for own pets" ON public.budgets
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = budgets.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能更新自己宠物的预算
DROP POLICY IF EXISTS "Users can update own pet budgets" ON public.budgets;
CREATE POLICY "Users can update own pet budgets" ON public.budgets
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = budgets.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能删除自己宠物的预算
DROP POLICY IF EXISTS "Users can delete own pet budgets" ON public.budgets;
CREATE POLICY "Users can delete own pet budgets" ON public.budgets
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.pets
            WHERE pets.id = budgets.pet_id
            AND pets.user_id = auth.uid()
        )
    );

-- 创建预算历史表的RLS策略
-- 用户只能查看自己宠物的预算历史
DROP POLICY IF EXISTS "Users can view own pet budget history" ON public.budget_history;
CREATE POLICY "Users can view own pet budget history" ON public.budget_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.budgets
            JOIN public.pets ON pets.id = budgets.pet_id
            WHERE budgets.id = budget_history.budget_id
            AND pets.user_id = auth.uid()
        )
    );

-- 用户只能为自己的宠物创建预算历史
DROP POLICY IF EXISTS "Users can insert budget history for own pets" ON public.budget_history;
CREATE POLICY "Users can insert budget history for own pets" ON public.budget_history
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.budgets
            JOIN public.pets ON pets.id = budgets.pet_id
            WHERE budgets.id = budget_history.budget_id
            AND pets.user_id = auth.uid()
        )
    );

-- 创建触发器函数：自动更新预算的当前支出
CREATE OR REPLACE FUNCTION update_budget_spent()
RETURNS TRIGGER AS $$
BEGIN
    -- 当插入或更新花费记录时，更新对应的预算支出
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE public.budgets 
        SET 
            current_spent = (
                SELECT COALESCE(SUM(amount), 0)
                FROM public.expense_records 
                WHERE pet_id = NEW.pet_id 
                AND category = NEW.category
                AND DATE_TRUNC('month', date::date) = DATE_TRUNC('month', CURRENT_DATE)
            ),
            updated_at = NOW()
        WHERE pet_id = NEW.pet_id 
        AND category = NEW.category;
    END IF;
    
    -- 当删除花费记录时，更新对应的预算支出
    IF TG_OP = 'DELETE' THEN
        UPDATE public.budgets 
        SET 
            current_spent = (
                SELECT COALESCE(SUM(amount), 0)
                FROM public.expense_records 
                WHERE pet_id = OLD.pet_id 
                AND category = OLD.category
                AND DATE_TRUNC('month', date::date) = DATE_TRUNC('month', CURRENT_DATE)
            ),
            updated_at = NOW()
        WHERE pet_id = OLD.pet_id 
        AND category = OLD.category;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_update_budget_spent ON public.expense_records;
CREATE TRIGGER trigger_update_budget_spent
    AFTER INSERT OR UPDATE OR DELETE ON public.expense_records
    FOR EACH ROW
    EXECUTE FUNCTION update_budget_spent();

-- 插入默认预算类别（可选）
INSERT INTO public.budgets (pet_id, category, monthly_limit, alert_threshold)
SELECT 
    p.id,
    category,
    500.00, -- 默认月预算500元
    0.8     -- 80%预警
FROM public.pets p
CROSS JOIN (
    VALUES 
        ('食物'),
        ('医疗'),
        ('玩具'),
        ('用品'),
        ('美容'),
        ('其他')
) AS categories(category)
WHERE NOT EXISTS (
    SELECT 1 FROM public.budgets b 
    WHERE b.pet_id = p.id AND b.category = categories.category
);
