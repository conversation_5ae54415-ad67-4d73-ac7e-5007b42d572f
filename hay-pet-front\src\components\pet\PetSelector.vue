<template>
  <div class="pet-selector-wizard">

    <!-- 主要内容区域 -->
    <div class="wizard-content">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="state-container loading-state">
        <div class="state-content">
          <div class="loading-spinner">
            <el-icon class="is-loading"><Loading /></el-icon>
          </div>
          <h3>加载中...</h3>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="state-container error-state">
        <div class="state-content">
          <el-icon class="error-icon"><Warning /></el-icon>
          <h3>加载失败</h3>
          <el-button type="primary" @click="retryLoad">重试</el-button>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="petTypes.length === 0" class="state-container empty-state">
        <div class="state-content">
          <el-icon class="empty-icon"><Box /></el-icon>
          <h3>暂无数据</h3>
        </div>
      </div>
      
      <!-- 步骤1: 宠物类型选择 -->
      <div v-else-if="currentStep === 1" class="step-content type-selection">
        <div class="step-header">
          <h2 class="step-title">选择宠物类型</h2>
        </div>
        
        <div class="selection-grid type-grid">
          <div
            v-for="type in petTypes"
            :key="type.id"
            class="avatar-option"
            :class="{ selected: internalSelectedType === type.id }"
            @click="handleTypeSelect(type.id)"
          >
            <img 
              :src="type.image" 
              :alt="type.name" 
              class="avatar-image" 
              @error="handleImageError"
            />
            <div v-if="internalSelectedType === type.id" class="selected-overlay">
              <el-icon class="check-icon"><Check /></el-icon>
            </div>
          </div>
        </div>
        
        <div class="step-actions">
          <el-button 
            type="primary" 
            size="large"
            :disabled="!internalSelectedType"
            @click="nextStep"
          >
            下一步：选择品种
            <el-icon class="ml-2"><ArrowRight /></el-icon>
          </el-button>
        </div>
      </div>
      
      <!-- 步骤2: 宠物品种选择 -->
      <div v-else-if="currentStep === 2" class="step-content breed-selection">
        <div class="step-header">
          <h2 class="step-title">选择{{ selectedTypeInfo?.name }}品种</h2>
        </div>
        
        <div v-if="currentBreeds.length === 0" class="no-breeds-message">
          <el-button type="primary" @click="completeSelection">完成选择</el-button>
        </div>
        
        <div v-else class="selection-grid breed-grid">
          <div
            v-for="breed in currentBreeds"
            :key="breed.id"
            class="avatar-option"
            :class="{ selected: internalSelectedBreed === breed.id }"
            @click="handleBreedSelect(breed.id)"
          >
            <img 
              :src="breed.image" 
              :alt="breed.name" 
              class="avatar-image" 
              @error="handleImageError"
            />
            <div v-if="internalSelectedBreed === breed.id" class="selected-overlay">
              <el-icon class="check-icon"><Check /></el-icon>
            </div>
          </div>
        </div>
        
        <div class="step-actions">
          <el-button size="large" @click="previousStep">
            <el-icon class="mr-2"><ArrowLeft /></el-icon>
            上一步
          </el-button>
          <el-button 
            type="primary" 
            size="large"
            :disabled="currentBreeds.length > 0 && !internalSelectedBreed"
            @click="completeSelection"
          >
            完成选择
            <el-icon class="ml-2"><Check /></el-icon>
          </el-button>
        </div>
      </div>
      
      <!-- 步骤3: 完成确认 -->
      <div v-else-if="currentStep === 3" class="step-content completion">
        <div class="completion-content">
          <div class="success-icon">
            <el-icon><SuccessFilled /></el-icon>
          </div>
          
          <h2 class="completion-title">选择完成！</h2>
          <p class="completion-description">您已成功选择了宠物类型和品种</p>
          
          <div class="selection-summary">
            <div class="summary-item">
              <label>宠物类型：</label>
              <div class="summary-value">
                <img :src="selectedTypeInfo?.image" :alt="selectedTypeInfo?.name" />
                <span>{{ selectedTypeInfo?.name }}</span>
              </div>
            </div>
            
            <div v-if="selectedBreedInfo" class="summary-item">
              <label>宠物品种：</label>
              <div class="summary-value">
                <img :src="selectedBreedInfo?.image" :alt="selectedBreedInfo?.name" />
                <span>{{ selectedBreedInfo?.name }}</span>
              </div>
            </div>
          </div>
          
          <div class="completion-actions">
            <el-button @click="resetSelection">重新选择</el-button>
            <el-button type="primary" @click="confirmSelection">确认选择</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { 
  Loading, 
  Warning, 
  Box, 
  Check, 
  ArrowRight, 
  ArrowLeft, 
  Collection,
  InfoFilled,
  SuccessFilled
} from '@element-plus/icons-vue'
import { useGlobalPetConfig } from '@/composables/usePetConfig'
import { ElMessage } from 'element-plus'

// Props 定义
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ typeId: null, breedId: null })
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits([
  'update:modelValue',
  'type-change',
  'selection-complete'
])

// 使用全局宠物配置
const {
  isLoading,
  error,
  petTypes,
  loadConfig
} = useGlobalPetConfig()

// 向导步骤状态
const currentStep = ref(1)
const internalSelectedType = ref(props.modelValue.typeId)
const internalSelectedBreed = ref(props.modelValue.breedId)

// 计算属性
const currentBreeds = computed(() => {
  if (!internalSelectedType.value) return []
  const selectedType = petTypes.value.find(type => type.id === internalSelectedType.value)
  return selectedType?.breeds || []
})

const selectedTypeInfo = computed(() => {
  return petTypes.value.find(type => type.id === internalSelectedType.value)
})

const selectedBreedInfo = computed(() => {
  return currentBreeds.value.find(breed => breed.id === internalSelectedBreed.value)
})

const isSelectionComplete = computed(() => {
  return internalSelectedType.value && 
    (currentBreeds.value.length === 0 || internalSelectedBreed.value)
})

// 步骤控制方法
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const resetSelection = () => {
  currentStep.value = 1
  internalSelectedType.value = null
  internalSelectedBreed.value = null
  updateModelValue()
}

// 选择处理方法
const handleTypeSelect = (typeId) => {
  if (props.disabled) return
  
  internalSelectedType.value = typeId
  internalSelectedBreed.value = null // 重置品种选择
  
  updateModelValue()
  emit('type-change', typeId)
  
  // 自动进入下一步
  setTimeout(() => {
    nextStep()
  }, 300)
}

const handleBreedSelect = (breedId) => {
  if (props.disabled) return
  
  internalSelectedBreed.value = breedId
  updateModelValue()
}

const completeSelection = () => {
  if (isSelectionComplete.value) {
    currentStep.value = 3
    ElMessage.success('宠物类型选择完成！')
  }
}

const confirmSelection = () => {
  emit('selection-complete', {
    typeId: internalSelectedType.value,
    breedId: internalSelectedBreed.value,
    typeInfo: selectedTypeInfo.value,
    breedInfo: selectedBreedInfo.value
  })
  ElMessage.success('选择已确认！')
}

// 工具方法
const updateModelValue = () => {
  const newValue = { 
    typeId: internalSelectedType.value, 
    breedId: internalSelectedBreed.value 
  }
  emit('update:modelValue', newValue)
}

const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
  event.target.src = '/default-pet-image.png'
}

const retryLoad = () => {
  loadConfig()
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  internalSelectedType.value = newValue.typeId
  internalSelectedBreed.value = newValue.breedId
  
  // 根据选择状态调整步骤
  if (newValue.typeId && newValue.breedId) {
    currentStep.value = 3
  } else if (newValue.typeId) {
    currentStep.value = 2
  } else {
    currentStep.value = 1
  }
}, { deep: true, immediate: true })

// 生命周期
onMounted(() => {
  // 初始化步骤状态
  if (props.modelValue.typeId && props.modelValue.breedId) {
    currentStep.value = 3
  } else if (props.modelValue.typeId) {
    currentStep.value = 2
  }
})
</script>

<style scoped lang="scss">
.pet-selector-wizard {
  max-width: 380px;
  width: 100%;
  margin: 0 auto;
  padding: 12px;
  position: relative;
  
  // 响应式设计
  @media (max-width: 768px) {
    max-width: 340px;
    padding: 10px;
  }
  
  @media (max-width: 480px) {
    max-width: 300px;
    padding: 8px;
  }
}

// 主要内容区域
.wizard-content {
  padding: 12px;
  position: relative;
  z-index: 1;
  min-height: 220px;
  
  @media (max-width: 768px) {
    padding: 10px;
    min-height: 200px;
  }
  
  @media (max-width: 480px) {
    padding: 8px;
    min-height: 180px;
  }
}

// 状态容器
.state-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  
  .state-content {
    text-align: center;
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 10px 0;
    }
  }
}

.loading-state {
  .loading-spinner {
    font-size: 40px;
    color: #667eea;
    margin-bottom: 14px;
  }
}

.error-state {
  .error-icon {
    font-size: 40px;
    color: #ef4444;
    margin-bottom: 14px;
  }
}

.empty-state {
  .empty-icon {
    font-size: 40px;
    color: #9ca3af;
    margin-bottom: 14px;
  }
}

// 步骤内容
.step-content {
  .step-header {
    text-align: center;
    margin-bottom: 16px;
    
    .step-title {
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 0;
      
      @media (max-width: 480px) {
        font-size: 16px;
      }
    }
    
    .step-description {
      font-size: 16px;
      color: #6b7280;
      line-height: 1.6;
    }
    
    .selected-type-info {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      
      .selected-type-avatar {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid #e5e7eb;
      }
    }
  }
}

// 选择网格
.selection-grid {
  display: grid;
  gap: 12px;
  margin-bottom: 24px;
  justify-items: center;
  padding: 0 8px;
  
  &.type-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 280px;
    margin: 0 auto 24px;
    
    @media (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;
      max-width: 240px;
    }
    
    @media (max-width: 480px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      max-width: 200px;
      padding: 0 4px;
    }
  }
  
  &.breed-grid {
    grid-template-columns: repeat(4, 1fr);
    max-width: 320px;
    margin: 0 auto 24px;
    
    @media (max-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      max-width: 280px;
    }
    
    @media (max-width: 480px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      max-width: 200px;
      padding: 0 4px;
    }
  }
}

// 头像选择
.avatar-option {
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .avatar-image {
    width: 56px;
    height: 56px;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.2s ease;
    border: 2px solid #e5e7eb;
    
    @media (max-width: 768px) {
      width: 50px;
      height: 50px;
    }
    
    @media (max-width: 480px) {
      width: 44px;
      height: 44px;
    }
  }
  
  .selected-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(34, 197, 94, 0.85);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .check-icon {
      font-size: 18px;
      color: #ffffff;
      
      @media (max-width: 480px) {
        font-size: 16px;
      }
    }
  }
  
  &:hover {
    .avatar-image {
      transform: scale(1.05);
      border-color: #667eea;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }
  }
  
  &.selected {
    .avatar-image {
      border-color: #22c55e;
      box-shadow: 0 2px 12px rgba(34, 197, 94, 0.4);
    }
  }
}

// 无品种消息
.no-breeds-message {
  text-align: center;
  padding: 20px;
  margin-bottom: 20px;
}

// 步骤操作按钮
.step-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  
  .el-button {
    padding: 8px 24px;
    font-weight: 500;
    border-radius: 8px;
  }
}

// 完成页面
.completion {
  .completion-content {
    text-align: center;
    
    .success-icon {
      font-size: 64px;
      color: #22c55e;
      margin-bottom: 24px;
    }
    
    .completion-title {
      font-size: 32px;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 12px;
    }
    
    .completion-description {
      font-size: 16px;
      color: #6b7280;
      margin-bottom: 32px;
    }
  }
}

.selection-summary {
  background: #f9fafb;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  
  .summary-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    
    &:not(:last-child) {
      border-bottom: 1px solid #e5e7eb;
    }
    
    label {
      font-weight: 600;
      color: #374151;
    }
    
    .summary-value {
      display: flex;
      align-items: center;
      gap: 12px;
      
      img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
      }
      
      span {
        font-weight: 500;
        color: #1f2937;
      }
    }
  }
}

.completion-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

// 响应式设计
@media (max-width: 1400px) {
  .pet-selector-wizard {
    max-width: 460px;
    padding: 22px;
    margin: 12px;
  }
  
  .wizard-content {
    padding: 26px;
    min-height: 400px;
  }
  
  .selection-grid {
    gap: 18px;
  }
}

@media (max-width: 1200px) {
  .pet-selector-wizard {
    max-width: 440px;
    padding: 20px;
    margin: 10px;
  }
  
  .wizard-content {
    padding: 24px;
    min-height: 380px;
  }
  
  .selection-grid {
    gap: 16px;
  }
}

@media (max-width: 900px) {
  .pet-selector-wizard {
    max-width: 420px;
    padding: 18px;
    border-radius: 18px;
    margin: 8px;
  }
  
  .wizard-content {
    padding: 22px;
    min-height: 360px;
  }
  
  .step-header .step-title {
    font-size: 24px;
  }
  
  .selection-grid {
    gap: 14px;
  }
  
  .selection-card {
    padding: 18px;
  }
}

@media (max-width: 768px) {
  .pet-selector-wizard {
    max-width: 380px;
    padding: 16px;
    border-radius: 16px;
    margin: 6px;
  }
  
  .wizard-content {
    padding: 18px;
    min-height: 340px;
  }
  
  .step-header {
    margin-bottom: 24px;
    
    .step-title {
      font-size: 20px;
    }
    
    .step-description {
      font-size: 13px;
    }
    
    .selected-type-info {
      flex-direction: column;
      gap: 10px;
      
      .selected-type-avatar {
        width: 56px;
        height: 56px;
      }
    }
  }
  
  .selection-grid {
    gap: 14px;
    margin-bottom: 32px;
  }
  
  .selection-card {
    padding: 18px;
    
    .card-image-container .card-image {
      height: 100px;
    }
    
    .card-content {
      .card-title {
        font-size: 16px;
      }
      
      .card-description {
        font-size: 13px;
      }
    }
  }
  
  .breed-card {
    .card-image-container .card-image {
      height: 80px;
    }
  }
  
  .step-actions {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    
    .el-button {
      width: 100%;
      max-width: 280px;
      padding: 14px 24px;
    }
  }
  
  .progress-steps {
    .step {
      .step-circle {
        width: 44px;
        height: 44px;
        font-size: 15px;
      }
      
      .step-label {
        font-size: 13px;
      }
    }
    
    .step-connector {
      width: 40px;
      margin: 0 8px;
    }
  }
  
  .completion {
    .completion-content {
      .success-icon {
        font-size: 56px;
      }
      
      .completion-title {
        font-size: 28px;
      }
    }
  }
  
  .selection-summary {
    padding: 20px;
    
    .summary-item {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      padding: 16px 0;
      
      .summary-value {
        align-self: flex-end;
      }
    }
  }
}

@media (max-width: 600px) {
  .pet-selector-wizard {
    max-width: 340px;
    padding: 14px;
    border-radius: 14px;
    margin: 4px;
  }
  
  .wizard-content {
    padding: 16px;
    min-height: 300px;
  }
  
  .wizard-progress {
    margin-bottom: 20px;
  }
  
  .step-header {
    margin-bottom: 20px;
    
    .step-title {
      font-size: 18px;
    }
    
    .step-description {
      font-size: 12px;
    }
  }
  
  .selection-grid {
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .selection-card {
    padding: 12px;
    
    .card-image-container .card-image {
      height: 75px;
    }
    
    .card-content {
      .card-title {
        font-size: 13px;
      }
      
      .card-description {
        font-size: 11px;
      }
    }
  }
  
  .breed-card {
    .card-image-container .card-image {
      height: 60px;
    }
  }
}

@media (max-width: 480px) {
  .pet-selector-wizard {
    max-width: 320px;
    padding: 12px;
    margin: 2px;
  }
  
  .wizard-content {
    padding: 14px;
    min-height: 280px;
  }
  
  .wizard-progress {
    margin-bottom: 18px;
    
    .step {
      .step-circle {
        width: 34px;
        height: 34px;
        font-size: 12px;
        margin-bottom: 6px;
      }
      
      .step-label {
        font-size: 10px;
      }
    }
    
    .step-connector {
      width: 20px;
      margin: 0 3px;
    }
  }
  
  .step-header {
    margin-bottom: 16px;
    
    .step-title {
      font-size: 16px;
    }
    
    .step-description {
      font-size: 11px;
    }
  }
  
  .selection-grid {
    gap: 8px;
    margin-bottom: 16px;
  }
  
  .selection-card {
    padding: 14px;
    
    .card-image-container {
      margin-bottom: 12px;
      
      .card-image {
        height: 80px;
      }
    }
    
    .card-content {
      .card-title {
        font-size: 14px;
        margin-bottom: 6px;
      }
      
      .card-description {
        font-size: 11px;
        margin-bottom: 8px;
      }
      
      .card-stats .stat-item {
        font-size: 11px;
      }
    }
    
    .selection-indicator {
      width: 28px;
      height: 28px;
      top: 12px;
      right: 12px;
    }
  }
  
  .breed-card {
    .card-image-container .card-image {
      height: 60px;
    }
  }
  
  .step-actions {
    gap: 10px;
    
    .el-button {
      max-width: 100%;
      padding: 12px 20px;
      font-size: 14px;
    }
  }
  
  .completion {
    .completion-content {
      .success-icon {
        font-size: 48px;
        margin-bottom: 20px;
      }
      
      .completion-title {
        font-size: 24px;
        margin-bottom: 10px;
      }
      
      .completion-description {
        font-size: 14px;
        margin-bottom: 24px;
      }
    }
  }
  
  .selection-summary {
    padding: 16px;
    margin-bottom: 24px;
    
    .summary-item {
      padding: 12px 0;
      
      label {
        font-size: 14px;
      }
      
      .summary-value {
        img {
          width: 28px;
          height: 28px;
        }
        
        span {
          font-size: 14px;
        }
      }
    }
  }
  
  .no-breeds-message {
    padding: 24px;
    
    .info-icon {
      font-size: 28px;
      margin-bottom: 12px;
    }
    
    p {
      font-size: 14px;
      margin-bottom: 16px;
    }
  }
}

@media (max-width: 360px) {
  .pet-selector-wizard {
    max-width: 300px;
    padding: 10px;
    margin: 1px;
  }
  
  .wizard-content {
    padding: 12px;
    min-height: 260px;
  }
  
  .wizard-progress {
    margin-bottom: 14px;
    
    .step {
      .step-circle {
        width: 30px;
        height: 30px;
        font-size: 11px;
        margin-bottom: 4px;
      }
      
      .step-label {
        font-size: 9px;
      }
    }
    
    .step-connector {
      width: 15px;
      margin: 0 2px;
    }
  }
  
  .step-header {
    margin-bottom: 14px;
    
    .step-title {
      font-size: 14px;
    }
    
    .step-description {
      font-size: 10px;
    }
  }
  
  .selection-grid {
    gap: 6px;
    margin-bottom: 14px;
  }
  
  .selection-card {
    padding: 8px;
    
    .card-image-container {
      margin-bottom: 6px;
      
      .card-image {
        height: 50px;
      }
    }
    
    .card-content {
      .card-title {
        font-size: 11px;
        margin-bottom: 3px;
      }
      
      .card-description {
        font-size: 9px;
        margin-bottom: 4px;
      }
      
      .card-stats .stat-item {
        font-size: 9px;
      }
    }
    
    .selection-indicator {
      width: 20px;
      height: 20px;
      top: 6px;
      right: 6px;
    }
  }
  
  .breed-card {
    .card-image-container .card-image {
      height: 40px;
    }
  }
  
  .step-actions {
    .el-button {
      padding: 8px 14px;
      font-size: 12px;
    }
  }
}

// 工具类
.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}
</style>