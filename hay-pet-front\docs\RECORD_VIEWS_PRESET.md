# RecordViewsPreset 组件文档

## 概述

`RecordViewsPreset` 是一个高度可复用的记录视图预设组件，提供了统一的三种视图模式：卡片视图、时间线视图和表格视图。该组件封装了完整的交互逻辑和统一的设计语言，可以轻松适配不同的业务场景。

## 特性

- ✅ **三种视图模式**：卡片视图、时间线视图、表格视图
- ✅ **统一设计语言**：24x24px圆形按钮、类别颜色边框、完成状态样式
- ✅ **丰富的交互功能**：双击编辑、批量操作、完成状态切换
- ✅ **高度可配置**：支持自定义数据字段映射
- ✅ **响应式设计**：适配不同屏幕尺寸
- ✅ **完整的事件系统**：支持所有必要的业务操作

## 安装和使用

### 基本使用

```vue
<template>
  <RecordViewsPreset
    :records="records"
    :view-mode="viewMode"
    :selected-records="selectedRecords"
    :color-mapping="colorMapping"
    :label-mapping="labelMapping"
    @edit="handleEdit"
    @delete="handleDelete"
    @toggle-completion="handleToggleCompletion"
    @batch-complete="handleBatchComplete"
    @batch-delete="handleBatchDelete"
    @selection-change="handleSelectionChange"
    @view-mode-change="handleViewModeChange"
  >
    <template #empty-action>
      <el-button type="primary" @click="addRecord">
        添加记录
      </el-button>
    </template>
  </RecordViewsPreset>
</template>

<script setup>
import RecordViewsPreset from '@/components/presets/RecordViewsPreset.vue';

const records = ref([
  {
    id: 1,
    date: '2024-01-15',
    category: 'health',
    description: '定期体检',
    is_completed: false
  }
]);

const viewMode = ref('cards');
const selectedRecords = ref([]);

const colorMapping = {
  health: '#67C23A',
  exercise: '#409EFF',
  diet: '#E6A23C'
};

const labelMapping = {
  health: '健康',
  exercise: '运动',
  diet: '饮食'
};
</script>
```

## API 参考

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `records` | Array | `[]` | 记录数据数组 |
| `viewMode` | String | `'cards'` | 当前视图模式：'cards' \| 'timeline' \| 'table' |
| `selectedRecords` | Array | `[]` | 选中的记录数组 |
| `colorMapping` | Object | `{}` | 类别颜色映射 |
| `labelMapping` | Object | `{}` | 类别标签映射 |
| `dateField` | String | `'date'` | 日期字段名 |
| `categoryField` | String | `'category'` | 类别字段名 |
| `descriptionField` | String | `'description'` | 描述字段名 |
| `completedField` | String | `'is_completed'` | 完成状态字段名 |
| `multiSelect` | Boolean | `true` | 是否支持多选 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `edit` | `record` | 编辑记录事件 |
| `delete` | `record` | 删除记录事件 |
| `toggle-completion` | `record` | 切换完成状态事件 |
| `batch-complete` | `records` | 批量完成事件 |
| `batch-delete` | `records` | 批量删除事件 |
| `selection-change` | `selection` | 选择变化事件 |
| `view-mode-change` | `mode` | 视图模式变化事件 |

### Slots

| 插槽名 | 说明 |
|--------|------|
| `empty-action` | 空状态时的操作按钮 |

## 数据格式

### 记录数据结构

```javascript
{
  id: Number|String,           // 唯一标识符
  date: String,               // 日期 (YYYY-MM-DD 格式)
  category: String,           // 类别标识
  description: String,        // 描述内容
  is_completed: Boolean       // 完成状态
}
```

### 颜色映射

```javascript
{
  'category1': '#67C23A',     // 类别1对应的颜色
  'category2': '#409EFF',     // 类别2对应的颜色
  // ...
}
```

### 标签映射

```javascript
{
  'category1': '健康',        // 类别1对应的显示标签
  'category2': '运动',        // 类别2对应的显示标签
  // ...
}
```

## 视图模式

### 卡片视图 (cards)
- 网格布局，每个记录显示为一张卡片
- 支持点击选择、双击编辑
- 显示类别颜色边框和完成状态

### 时间线视图 (timeline)
- 按日期分组显示
- 左侧完成按钮，右侧删除按钮
- 支持双击编辑

### 表格视图 (table)
- 表格形式展示所有记录
- 支持批量选择和操作
- 表格头部显示批量操作按钮
- 支持双击行编辑

## 样式定制

组件使用 scoped 样式，如需定制可以通过以下方式：

1. **CSS 变量覆盖**（推荐）
2. **深度选择器** `:deep()`
3. **全局样式类**

## 最佳实践

1. **数据管理**：确保记录数据包含必要的字段
2. **事件处理**：实现所有必要的事件处理函数
3. **状态同步**：及时更新 selectedRecords 和 viewMode
4. **错误处理**：在事件处理中添加适当的错误处理
5. **性能优化**：对于大量数据，考虑分页或虚拟滚动

## 示例场景

- 健康记录管理
- 任务列表
- 事件日志
- 学习记录
- 工作日志
- 项目里程碑

## 浏览器兼容性

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 更新日志

### v1.0.0
- 初始版本发布
- 支持三种视图模式
- 完整的交互功能
- 统一的设计语言
