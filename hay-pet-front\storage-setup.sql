-- Supabase 存储桶设置脚本
-- 请在 Supabase SQL 编辑器中执行此脚本

-- 创建存储桶（如果不存在）
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'pet-media', 
  'pet-media', 
  true, 
  52428800, -- 50MB 限制
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/tiff']
)
ON CONFLICT (id) DO UPDATE SET
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/tiff'],
  public = true;

-- 删除现有的存储策略（如果存在）
DROP POLICY IF EXISTS "Public Access" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload" ON storage.objects;
DROP POLICY IF EXISTS "Users can update own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete own files" ON storage.objects;

-- 创建存储策略
-- 1. 公开访问策略（所有人都可以查看图片）
CREATE POLICY "Public Access" ON storage.objects
  FOR SELECT USING (bucket_id = 'pet-media');

-- 2. 认证用户可以上传文件
CREATE POLICY "Authenticated users can upload" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'pet-media' 
    AND auth.role() = 'authenticated'
    AND (storage.foldername(name))[1] = auth.uid()::text
  );

-- 3. 用户只能更新自己的文件
CREATE POLICY "Users can update own files" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'pet-media' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- 4. 用户只能删除自己的文件
CREATE POLICY "Users can delete own files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'pet-media' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- 验证存储桶是否创建成功
SELECT * FROM storage.buckets WHERE id = 'pet-media';

-- 完成提示
-- 执行完成后，您的存储桶将配置为：
-- 1. 允许上传图片文件（JPEG, PNG, GIF, WebP）
-- 2. 文件大小限制为 50MB
-- 3. 公开访问图片
-- 4. 用户只能管理自己上传的文件