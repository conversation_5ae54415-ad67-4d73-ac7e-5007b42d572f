<template>
  <el-card class="dashboard-card health-summary-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <span class="card-title">健康记录摘要</span>
        <el-button type="primary" link @click="router.push('/records')">
          <el-icon><Document /></el-icon>
          查看详细
        </el-button>
      </div>
    </template>
    <div class="health-summary-content">
      <div v-if="healthRecords.length" class="health-content">
        <!-- 健康统计 - 仅在卡片模式下显示 -->
        <div v-if="props.viewMode !== 'compact'" class="health-stats">
          <div class="stat-category">
            <div class="category-header">
              <el-icon class="category-icon vaccine"><Aim /></el-icon>
              <span class="category-title">疫苗接种</span>
            </div>
            <div class="category-content">
              <span class="category-count">{{ vaccineCount }} 次</span>
              <span class="category-latest" v-if="latestVaccine">
                最近: {{ formatDate(latestVaccine.date) }}
              </span>
            </div>
          </div>
          
          <div class="stat-category">
            <div class="category-header">
              <el-icon class="category-icon deworming"><MagicStick /></el-icon>
              <span class="category-title">驱虫记录</span>
            </div>
            <div class="category-content">
              <span class="category-count">{{ dewormingCount }} 次</span>
              <span class="category-latest" v-if="latestDeworming">
                最近: {{ formatDate(latestDeworming.date) }}
              </span>
            </div>
          </div>
          
          <div class="stat-category">
            <div class="category-header">
              <el-icon class="category-icon medical"><FirstAidKit /></el-icon>
              <span class="category-title">医疗记录</span>
            </div>
            <div class="category-content">
              <span class="category-count">{{ medicalCount }} 次</span>
              <span class="category-latest" v-if="latestMedical">
                最近: {{ formatDate(latestMedical.date) }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="recent-records">
          <h4 class="section-title">最近记录</h4>
          <div class="records-list">
            <div v-for="record in recentRecords" :key="record.id" class="record-item">
              <div class="record-icon">
                <el-icon v-if="record.record_type === 'vaccination'" class="vaccine"><Aim /></el-icon>
                <el-icon v-else-if="record.record_type === 'deworming'" class="deworming"><MagicStick /></el-icon>
                <el-icon v-else class="medical"><FirstAidKit /></el-icon>
              </div>
              <div class="record-info">
                <span class="record-title">{{ record.title }}</span>
                <span class="record-description" v-if="record.description">{{ record.description }}</span>
                <span class="record-date">{{ formatDate(record.date) }}</span>
              </div>
              <div class="record-type">
                <div
                  class="health-record-tag"
                  :class="`tag-${record.record_type}`"
                >
                  {{ getRecordTypeLabel(record.record_type) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无健康记录">
        <el-button type="primary" @click="router.push('/records')">
          <el-icon><Plus /></el-icon>
          添加健康记录
        </el-button>
      </el-empty>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Document, Aim, MagicStick, FirstAidKit, Plus } from '@element-plus/icons-vue';
import { usePetStore } from '@/stores/pet';
import { supabase } from '@/utils/supabase';
import type { DashboardBlock } from '@/types/dashboard';

interface Props {
  blockConfig: DashboardBlock;
  viewMode?: 'card' | 'compact';
}

const props = defineProps<Props>();

const router = useRouter();
const petStore = usePetStore();

const healthRecords = ref<any[]>([]);
const loading = ref(false);

const currentPet = computed(() => petStore.currentPet);

const vaccineRecords = computed(() => 
  healthRecords.value.filter(record => record.record_type === 'vaccination')
);

const dewormingRecords = computed(() => 
  healthRecords.value.filter(record => record.record_type === 'deworming')
);

const medicalRecords = computed(() => 
  healthRecords.value.filter(record => record.record_type === 'medical')
);

const vaccineCount = computed(() => vaccineRecords.value.length);
const dewormingCount = computed(() => dewormingRecords.value.length);
const medicalCount = computed(() => medicalRecords.value.length);

const latestVaccine = computed(() => 
  vaccineRecords.value.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0]
);

const latestDeworming = computed(() => 
  dewormingRecords.value.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0]
);

const latestMedical = computed(() => 
  medicalRecords.value.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0]
);

const recentRecords = computed(() => {
  return [...healthRecords.value]
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);
});

async function fetchHealthRecords() {
  if (!currentPet.value?.id) return;
  
  loading.value = true;
  try {
    const { data, error } = await supabase
      .from('health_records')
      .select('*')
      .eq('pet_id', currentPet.value.id)
      .order('date', { ascending: false })
      .limit(20);
    
    if (error) throw error;
    healthRecords.value = data || [];
  } catch (error) {
    console.error('获取健康记录失败:', error);
  } finally {
    loading.value = false;
  }
}

function getRecordTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    vaccination: '疫苗',
    deworming: '驱虫',
    medical: '医疗'
  };
  return labels[type] || type;
}

function getRecordTagType(type: string): string {
  const types: Record<string, string> = {
    vaccination: 'success',
    deworming: 'warning',
    medical: 'danger'
  };
  return types[type] || 'info';
}

function formatDate(date: string): string {
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

onMounted(() => {
  fetchHealthRecords();
});

// 监听当前宠物变化
watch(currentPet, (newPet) => {
  if (newPet) {
    fetchHealthRecords();
  } else {
    healthRecords.value = [];
  }
}, { immediate: true });
</script>

<style scoped>
.dashboard-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.health-summary-content {
  padding: 0;
}

.health-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.health-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-category {
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.category-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.category-icon.vaccine {
  background: var(--el-color-success);
}

.category-icon.deworming {
  background: var(--el-color-warning);
}

.category-icon.medical {
  background: var(--el-color-danger);
}

.category-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.category-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.category-count {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.category-latest {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.recent-records {
  margin-top: 8px;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.record-icon {
  flex-shrink: 0;
}

.record-icon .vaccine {
  color: var(--el-color-success);
}

.record-icon .deworming {
  color: var(--el-color-warning);
}

.record-icon .medical {
  color: var(--el-color-danger);
}

.record-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.record-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.record-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.record-date {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.record-type {
  flex-shrink: 0;
}

.health-record-tag {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.health-record-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tag-vaccination {
  background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
}

.tag-deworming {
  background: linear-gradient(135deg, #E6A23C 0%, #F7BA2A 100%);
}

.tag-medical,
.tag-checkup {
  background: linear-gradient(135deg, #409EFF 0%, #66B1FF 100%);
}

.tag-illness {
  background: linear-gradient(135deg, #F56C6C 0%, #F78989 100%);
}

.tag-medication {
  background: linear-gradient(135deg, #909399 0%, #A6A9AD 100%);
}

.tag-allergy {
  background: linear-gradient(135deg, #FF5722 0%, #FF7043 100%);
}

.tag-surgery {
  background: linear-gradient(135deg, #9C27B0 0%, #BA68C8 100%);
}

.tag-other {
  background: linear-gradient(135deg, #607D8B 0%, #78909C 100%);
}

@media (max-width: 768px) {
  .health-stats {
    grid-template-columns: 1fr;
  }
  
  .record-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .record-type {
    align-self: flex-end;
  }
}
</style>