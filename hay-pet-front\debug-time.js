import { chromium } from 'playwright';

(async () => {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  // 监听控制台输出
  page.on('console', msg => {
    console.log('浏览器控制台:', msg.text());
  });

  // 监听网络请求
  page.on('request', request => {
    if (request.url().includes('expense_records')) {
      console.log('请求URL:', request.url());
      console.log('请求方法:', request.method());
      if (request.postData()) {
        console.log('请求数据:', request.postData());
      }
    }
  });

  // 监听网络响应
  page.on('response', response => {
    if (response.url().includes('expense_records')) {
      console.log('响应状态:', response.status());
    }
  });

  try {
    // 导航到页面
    await page.goto('http://localhost:5214/expense-tracking');
    await page.waitForLoadState('networkidle');

    console.log('页面加载完成');

    // 点击添加花费记录按钮
    await page.click('button:has-text("添加花费记录")');
    await page.waitForSelector('.el-dialog', { state: 'visible' });

    console.log('对话框已打开');

    // 检查日期字段的值
    const dateInput = await page.locator('.el-date-editor input').first();
    const dateValue = await dateInput.inputValue();
    console.log('日期输入框的值:', dateValue);

    // 填写表单
    await page.fill('input[placeholder="请输入金额"]', '35.00');
    await page.click('.el-select');
    await page.click('.el-option:has-text("美容")');
    await page.fill('textarea[placeholder="输入备注信息（可选）"]', '测试时间记录');

    console.log('表单填写完成');

    // 提交表单
    await page.click('button:has-text("确定")');

    // 等待提交完成
    await page.waitForTimeout(2000);

    console.log('表单已提交');

    // 检查最新记录的时间
    const firstRow = await page.locator('table tbody tr').first();
    if (await firstRow.count() > 0) {
      const dateCell = await firstRow.locator('td').nth(1).textContent();
      console.log('最新记录的时间:', dateCell);
    }

  } catch (error) {
    console.error('测试过程中出错:', error);
  }

  // 保持浏览器打开以便观察
  await page.waitForTimeout(10000);
  await browser.close();
})();
