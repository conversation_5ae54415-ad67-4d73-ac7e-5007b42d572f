<template>
  <div class="photo-album-view">
    <div v-if="!currentPetId" class="no-pet-selected">
      <el-empty description="请先在左侧选择一个宠物以查看或上传照片"></el-empty>
    </div>
    <div v-else class="album-container">
      <!-- 页面标题和操作按钮 -->
      <div class="page-header">
        <div class="header-content">
          <h2 class="page-title">相册管理</h2>
          <div class="header-actions">
            <el-button
              class="add-photo-btn"
              type="primary"
              size="large"
              @click="openUploadDialog"
            >
              <el-icon class="add-icon"><Plus /></el-icon>
              <span class="btn-text">上传照片</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 统计卡片面板 -->
      <div v-if="photos.length > 0" class="stats-section">
        <div class="stats-grid">
          <el-card class="stat-card total-photos" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">总照片数</div>
                <div class="stat-value">{{ photos.length }}</div>
                <div class="stat-date">全部照片</div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card month-photos" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">本月新增</div>
                <div class="stat-value">{{ monthlyPhotos }}</div>
                <div class="stat-date">{{ currentMonthDisplay }}</div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card favorite-photos" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">收藏照片</div>
                <div class="stat-value">{{ favoritePhotos }}</div>
                <div class="stat-date">精选收藏</div>
              </div>
            </div>
          </el-card>

          <el-card class="stat-card recent-activity" shadow="hover">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">最新上传</div>
                <div class="stat-value">{{ recentActivity }}</div>
                <div class="stat-date">{{ latestPhotoDate }}</div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 分类筛选和视图切换 -->
      <div class="filter-section">
        <el-card shadow="hover">
          <div class="filter-content">
            <div class="filter-left">
              <div class="filter-group">
                <div class="filter-header">
                  <div class="filter-header-left">
                    <span class="filter-label">照片分类：</span>
                  </div>

                  <!-- 视图切换和批量操作按钮 -->
                  <div class="filter-header-right">
                    <!-- 批量操作按钮 -->
                    <div v-if="selectedPhotos.length > 0" class="batch-actions-header">
                      <span class="selected-info">已选中 {{ selectedPhotos.length }} 张</span>
                      <div class="batch-actions-buttons">
                        <el-button
                          class="batch-btn favorite-batch-btn"
                          type="warning"
                          size="small"
                          @click="batchToggleFavorite"
                        >
                          <el-icon><Star /></el-icon>
                          {{ isBatchFavorited ? '取消收藏' : '收藏' }}
                        </el-button>
                        <el-button
                          class="batch-btn delete-batch-btn"
                          type="danger"
                          size="small"
                          @click="batchDeletePhotos"
                        >
                          <el-icon><Delete /></el-icon>
                          删除
                        </el-button>
                        <el-button
                          class="batch-btn clear-selection-btn"
                          type="info"
                          size="small"
                          @click="clearSelection"
                        >
                          <el-icon><Close /></el-icon>
                          取消选择
                        </el-button>
                      </div>
                    </div>

                    <!-- 视图切换按钮 -->
                    <el-button-group class="view-toggle">
                      <el-button
                        :type="viewMode === 'grid' ? 'primary' : 'default'"
                        @click="handleViewModeChange('grid')"
                        size="small"
                        class="view-btn"
                      >网格</el-button>
                      <el-button
                        :type="viewMode === 'timeline' ? 'primary' : 'default'"
                        @click="handleViewModeChange('timeline')"
                        size="small"
                        class="view-btn"
                      >时间线</el-button>
                      <el-button
                        :type="viewMode === 'waterfall' ? 'primary' : 'default'"
                        @click="handleViewModeChange('waterfall')"
                        size="small"
                        class="view-btn"
                      >瀑布流</el-button>
                    </el-button-group>

                    <!-- 排序切换按钮 -->
                    <el-button-group class="sort-toggle">
                      <el-tooltip
                        :content="getUploadTimeSortTooltip()"
                        placement="top"
                        effect="light"
                        :show-arrow="true"
                        popper-class="custom-tooltip"
                      >
                        <el-button
                          :type="sortMode === 'upload_time' ? 'primary' : 'default'"
                          @click="sortMode = 'upload_time'"
                          size="small"
                          class="sort-btn"
                        >
                          <el-icon><Clock /></el-icon>
                          上传时间
                        </el-button>
                      </el-tooltip>
                      <el-tooltip
                        :content="getFileTimeSortTooltip()"
                        placement="top"
                        effect="light"
                        :show-arrow="true"
                        popper-class="custom-tooltip"
                      >
                        <el-button
                          :type="sortMode === 'file_time' ? 'primary' : 'default'"
                          @click="sortMode = 'file_time'"
                          size="small"
                          class="sort-btn"
                        >
                          <el-icon><Camera /></el-icon>
                          文件时间
                        </el-button>
                      </el-tooltip>
                    </el-button-group>
                  </div>
                </div>
                <div class="categories-container">
                  <!-- 全部标签（不可拖拽） -->
                  <div
                    @click="handleCategoryFilter('')"
                    class="category-tag clickable-tag all-tag"
                    :class="{ 'is-active': selectedCategories.length === 0 }"
                  >
                    <div class="tag-content">
                      <span class="tag-text">全部</span>
                    </div>
                  </div>

                  <!-- 可拖拽的分类标签 -->
                  <draggable
                    v-model="photoCategories"
                    v-bind="dragOptions"
                    @start="onDragStart"
                    @end="onDragEnd"
                    item-key="id"
                    :key="photoCategories.length"
                    class="draggable-container"
                  >
                    <template #item="{ element: category }">
                      <div
                        @click="handleCategoryFilter(category.id)"
                        @contextmenu.prevent="showCategoryContextMenu($event, category)"
                        class="category-tag clickable-tag draggable-tag"
                        :class="{
                          'is-active': selectedCategories.includes(category.id),
                          'is-dragging': isDragging
                        }"
                        :style="selectedCategories.includes(category.id) ? {
                          background: `linear-gradient(135deg, ${category.color} 0%, ${adjustBrightness(category.color, 20)} 100%)`,
                          borderColor: 'rgba(255, 255, 255, 0.3)'
                        } : {
                          background: `linear-gradient(135deg, ${adjustBrightness(category.color, 80)} 0%, ${adjustBrightness(category.color, 90)} 100%)`,
                          color: category.color,
                          borderColor: adjustBrightness(category.color, 60)
                        }"
                      >
                        <div class="tag-content">
                          <span class="tag-text">{{ category.name }}</span>
                          <div class="tag-actions" v-show="!isDragging">
                            <el-icon
                              @click.stop="editCategory(category)"
                              class="edit-category-icon"
                            >
                              <Edit />
                            </el-icon>
                            <el-icon
                              @click.stop="deleteCategory(category.id)"
                              class="delete-category-icon"
                            >
                              <Close />
                            </el-icon>
                          </div>
                        </div>
                      </div>
                    </template>
                  </draggable>

                  <!-- 收藏筛选标签 -->
                  <div
                    @click="toggleFavoriteFilter"
                    class="category-tag clickable-tag favorite-category-tag"
                    :class="{ 'is-active': selectedTags.includes('favorite') }"
                  >
                    <div class="tag-content">
                      <el-icon class="tag-icon"><Star /></el-icon>
                      <span class="tag-text">收藏</span>
                    </div>
                  </div>

                  <!-- 无标签筛选标签 -->
                  <div
                    @click="toggleUntaggedFilter"
                    class="category-tag clickable-tag untagged-category-tag"
                    :class="{ 'is-active': selectedTags.includes('untagged') }"
                  >
                    <div class="tag-content">
                      <el-icon class="tag-icon"><Document /></el-icon>
                      <span class="tag-text">无标签</span>
                    </div>
                  </div>

                  <!-- 添加按钮（不可拖拽） -->
                  <div
                    @click="showAddCategoryDialog = true"
                    class="add-category-tag"
                    title="添加新分类"
                  >
                    <el-icon class="add-icon">
                      <Plus />
                    </el-icon>
                  </div>
                </div>
              </div>


            </div>
          </div>
        </el-card>
      </div>

      <!-- 照片展示区域 -->
      <div class="photos-section">

        <!-- 视图切换动画容器 -->
        <transition name="view-fade" mode="out-in">
          <!-- 瀑布流视图 -->
          <div v-if="viewMode === 'waterfall'" key="waterfall" class="waterfall-view">
            <PhotoProvider
              v-if="filteredPhotos.length > 0"
            >
              <div
                class="photos-masonry photos-container"
                @mousedown="startSelection"
                @mousemove="updateSelection"
                @mouseup="endSelection"
                @mouseleave="endSelection"
              >
                <!-- 框选选择框 -->
                <div class="selection-box" :style="getSelectionBoxStyle('waterfall')"></div>

                <div
                  v-for="photo in filteredPhotos"
                  :key="photo.id"
                  class="unified-photo-card waterfall-variant"
                  :class="{ 'selected': selectedPhotos.some(p => p.id === photo.id) }"
                  :data-photo-id="photo.id"
                  @touchstart.passive="handleTouchStart($event, photo)"
                >
                  <!-- 图片容器 -->
                  <div class="photo-container">
                    <PhotoConsumer
                      :intro="getPhotoIntro(photo)"
                      :src="photo.url"
                    >
                      <img
                        :src="photo.url"
                        :alt="photo.name || '无标题'"
                        class="photo-image"
                        loading="lazy"
                        @error="handleImageError"
                      />
                    </PhotoConsumer>

                    <!-- 悬浮遮罩层 -->
                    <div class="photo-overlay">
                      <!-- 顶部操作区 -->
                      <div class="overlay-top">
                        <div class="photo-checkbox" @click.stop="togglePhotoSelection(photo)">
                          <el-checkbox
                            :model-value="selectedPhotos.some(p => p.id === photo.id)"
                          />
                        </div>

                        <div class="photo-favorite" @click.stop="toggleFavorite(photo)">
                          <el-icon :class="{ 'is-favorite': photo.is_favorite }">
                            <StarFilled v-if="photo.is_favorite" />
                            <Star v-else />
                          </el-icon>
                        </div>
                      </div>

                      <!-- 底部信息区 -->
                      <div class="overlay-bottom">
                        <div class="photo-info">
                          <div class="photo-title">{{ photo.name || '无标题' }}</div>
                          <div v-if="photo.caption" class="photo-caption">{{ photo.caption }}</div>

                          <!-- 分类标签 -->
                          <div v-if="photo.tags && photo.tags.length > 0" class="photo-tags">
                            <el-tag
                              v-for="tag in photo.tags.slice(0, 2)"
                              :key="tag.id"
                              size="small"
                              :color="tag.color"
                              effect="light"
                              class="photo-tag"
                            >
                              {{ tag.name }}
                            </el-tag>
                            <el-tag
                              v-if="photo.tags.length > 2"
                              size="small"
                              type="info"
                              effect="plain"
                              class="photo-tag-more"
                              :title="photo.tags.slice(2).map(t => t.name).join(', ')"
                            >
                              +{{ photo.tags.length - 2 }}
                            </el-tag>
                          </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="photo-actions">
                          <el-button size="small" type="primary" @click.stop="editPhoto(photo)">
                            <el-icon><Edit /></el-icon>
                          </el-button>
                          <el-button size="small" type="danger" @click.stop="deletePhoto(photo)">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </PhotoProvider>
            <div v-else class="empty-state">
              <div class="empty-content">
                <div class="empty-icon">📸</div>
                <h3 class="empty-title">暂无照片</h3>
                <p class="empty-description">
                  {{ selectedCategories.length > 0 || selectedTags.length > 0 ? '当前筛选条件下暂无照片' : '还没有上传任何照片，快来记录美好时光吧！' }}
                </p>
                <el-button
                  type="primary"
                  @click="openUploadDialog"
                  class="empty-action-btn"
                >
                  <el-icon><Plus /></el-icon>
                  上传第一张照片
                </el-button>
              </div>
            </div>
          </div>

          <!-- 传统网格视图 -->
          <div v-else-if="viewMode === 'grid'" key="grid" class="grid-view">
            <PhotoProvider
              v-if="filteredPhotos.length > 0"
            >
              <div
                class="photos-grid photos-container"
                @mousedown="startSelection"
                @mousemove="updateSelection"
                @mouseup="endSelection"
                @mouseleave="endSelection"
              >
                <!-- 框选选择框 -->
                <div class="selection-box" :style="getSelectionBoxStyle('grid')"></div>

                <div
                  v-for="photo in filteredPhotos"
                  :key="photo.id"
                  class="unified-photo-card"
                  :class="{ 'selected': selectedPhotos.some(p => p.id === photo.id) }"
                  :data-photo-id="photo.id"
                  @touchstart.passive="handleTouchStart($event, photo)"
                >
                  <!-- 图片容器 -->
                  <div class="photo-container">
                    <PhotoConsumer
                      :src="photo.url"
                      :intro="getPhotoIntro(photo)"
                    >
                      <el-image
                        :src="photo.url"
                        class="photo-image"
                        fit="cover"
                      >
                        <template #error>
                          <div class="image-error">
                            <el-icon><Picture /></el-icon>
                            <span>加载失败</span>
                          </div>
                        </template>
                      </el-image>
                    </PhotoConsumer>

                    <!-- 悬浮遮罩层 -->
                    <div class="photo-overlay">
                      <!-- 顶部操作区 -->
                      <div class="overlay-top">
                        <div class="photo-checkbox" @click.stop="togglePhotoSelection(photo)">
                          <el-checkbox
                            :model-value="selectedPhotos.some(p => p.id === photo.id)"
                          />
                        </div>
                        <div class="photo-favorite" @click.stop="toggleFavorite(photo)">
                          <el-icon :class="{ 'is-favorite': photo.is_favorite }">
                            <StarFilled v-if="photo.is_favorite" />
                            <Star v-else />
                          </el-icon>
                        </div>
                      </div>

                      <!-- 底部信息区 -->
                      <div class="overlay-bottom">
                        <div class="photo-info">
                          <div class="photo-title">{{ photo.name || '无标题' }}</div>
                          <div v-if="photo.caption" class="photo-caption">{{ photo.caption }}</div>

                          <!-- 分类标签 -->
                          <div v-if="photo.tags && photo.tags.length > 0" class="photo-tags">
                            <el-tag
                              v-for="tag in photo.tags.slice(0, 2)"
                              :key="tag.id"
                              size="small"
                              :color="tag.color"
                              effect="light"
                              class="photo-tag"
                            >
                              {{ tag.name }}
                            </el-tag>
                            <el-tag
                              v-if="photo.tags.length > 2"
                              size="small"
                              type="info"
                              effect="plain"
                              class="photo-tag-more"
                              :title="photo.tags.slice(2).map(t => t.name).join(', ')"
                            >
                              +{{ photo.tags.length - 2 }}
                            </el-tag>
                          </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="photo-actions">
                          <el-button size="small" type="primary" @click.stop="editPhoto(photo)">
                            <el-icon><Edit /></el-icon>
                          </el-button>
                          <el-button size="small" type="danger" @click.stop="deletePhoto(photo)">
                            <el-icon><Delete /></el-icon>
                          </el-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </PhotoProvider>
            <div v-else class="empty-state">
              <div class="empty-content">
                <div class="empty-icon">📸</div>
                <h3 class="empty-title">暂无照片</h3>
                <p class="empty-description">
                  {{ selectedCategories.length > 0 || selectedTags.length > 0 ? '当前筛选条件下暂无照片' : '还没有上传任何照片，快来记录美好时光吧！' }}
                </p>
                <el-button
                  type="primary"
                  @click="openUploadDialog"
                  class="empty-action-btn"
                >
                  <el-icon><Plus /></el-icon>
                  上传第一张照片
                </el-button>
              </div>
            </div>
          </div>

          <!-- 时间线视图 -->
          <div v-else-if="viewMode === 'timeline'" key="timeline" class="timeline-view">
            <PhotoProvider
              v-if="filteredPhotos.length > 0"
            >
              <el-timeline class="photo-timeline">
                <el-timeline-item
                  v-for="(group, date, groupIndex) in timelineGroups"
                  :key="date"
                  :timestamp="formatFriendlyDate(date)"
                  placement="top"
                  size="large"
                  type="primary"
                  :icon="Camera"
                  class="timeline-item"
                >
                  <!-- 时间节点标题 -->
                  <template #dot>
                    <div class="timeline-dot">
                      <el-icon class="timeline-icon">
                        <Camera />
                      </el-icon>
                    </div>
                  </template>

                  <!-- 日期统计信息 -->
                  <div class="timeline-header">
                    <h3 class="timeline-date-title">{{ formatFriendlyDate(date) }}</h3>
                    <div class="timeline-stats">
                      <el-tag type="info" size="small">
                        {{ group.length }} 张照片
                      </el-tag>
                      <el-tag v-if="getDayFavoriteCount(group) > 0" type="warning" size="small">
                        {{ getDayFavoriteCount(group) }} 张收藏
                      </el-tag>
                    </div>
                  </div>

                  <!-- 照片网格 -->
                  <div
                    class="timeline-photos-grid photos-container"
                    :data-timeline-group="groupIndex"
                    @mousedown="startSelection"
                    @mousemove="updateSelection"
                    @mouseup="endSelection"
                    @mouseleave="endSelection"
                  >
                    <!-- 框选选择框 -->
                    <div class="selection-box" :style="getSelectionBoxStyle(`timeline-${groupIndex}`)"></div>

                    <div
                      v-for="photo in group"
                      :key="photo.id"
                      class="unified-photo-card timeline-variant"
                      :class="{ 'selected': selectedPhotos.some(p => p.id === photo.id) }"
                      :data-photo-id="photo.id"
                      @touchstart.passive="handleTouchStart($event, photo)"
                    >
                      <!-- 图片容器 -->
                      <div class="photo-container">
                        <PhotoConsumer
                          :intro="getPhotoIntro(photo)"
                          :src="photo.url"
                        >
                          <el-image
                            :src="photo.url"
                            :alt="photo.name || '无标题'"
                            class="photo-image"
                            fit="cover"
                          >
                            <template #error>
                              <div class="image-error">
                                <el-icon><Picture /></el-icon>
                                <span>加载失败</span>
                              </div>
                            </template>
                          </el-image>
                        </PhotoConsumer>

                        <!-- 悬浮遮罩层 -->
                        <div class="photo-overlay">
                          <!-- 顶部操作区 -->
                          <div class="overlay-top">
                            <div class="photo-checkbox" @click.stop="togglePhotoSelection(photo)">
                              <el-checkbox
                                :model-value="selectedPhotos.some(p => p.id === photo.id)"
                              />
                            </div>
                            <div class="photo-favorite" @click.stop="toggleFavorite(photo)">
                              <el-icon :class="{ 'is-favorite': photo.is_favorite }">
                                <StarFilled v-if="photo.is_favorite" />
                                <Star v-else />
                              </el-icon>
                            </div>
                          </div>

                          <!-- 底部信息区 -->
                          <div class="overlay-bottom">
                            <div class="photo-info">
                              <div class="photo-title">{{ photo.name || '无标题' }}</div>
                              <div class="photo-time">{{ formatTime(photo.created_at) }}</div>
                              <div v-if="photo.caption" class="photo-caption">{{ photo.caption }}</div>

                              <!-- 分类标签 -->
                              <div v-if="photo.tags && photo.tags.length > 0" class="photo-tags">
                                <el-tag
                                  v-for="tag in photo.tags.slice(0, 2)"
                                  :key="tag.id"
                                  size="small"
                                  :color="tag.color"
                                  effect="light"
                                  class="photo-tag"
                                >
                                  {{ tag.name }}
                                </el-tag>
                                <el-tag
                                  v-if="photo.tags.length > 2"
                                  size="small"
                                  type="info"
                                  effect="plain"
                                  class="photo-tag-more"
                                  :title="photo.tags.slice(2).map(t => t.name).join(', ')"
                                >
                                  +{{ photo.tags.length - 2 }}
                                </el-tag>
                              </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="photo-actions">
                              <el-button size="small" type="primary" @click.stop="editPhoto(photo)">
                                <el-icon><Edit /></el-icon>
                              </el-button>
                              <el-button size="small" type="danger" @click.stop="deletePhoto(photo)">
                                <el-icon><Delete /></el-icon>
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </PhotoProvider>
            <div v-else class="empty-state">
              <div class="empty-content">
                <div class="empty-icon">📸</div>
                <h3 class="empty-title">暂无照片</h3>
                <p class="empty-description">
                  {{ selectedCategories.length > 0 || selectedTags.length > 0 ? '当前筛选条件下暂无照片' : '还没有上传任何照片，快来记录美好时光吧！' }}
                </p>
                <el-button
                  type="primary"
                  @click="openUploadDialog"
                  class="empty-action-btn"
                >
                  <el-icon><Plus /></el-icon>
                  上传第一张照片
                </el-button>
              </div>
            </div>
          </div>
        </transition>
      </div>

      <!-- 上传照片对话框 - 优化版本 -->
      <el-dialog
        v-model="showUploadDialog"
        title="批量上传照片"
        width="65%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="upload-dialog-enhanced"
        top="5vh"
        :append-to-body="true"
        :lock-scroll="true"
      >
        <!-- 隐藏的文件上传组件 -->
        <el-upload
          ref="uploadRef"
          v-model:file-list="newPhoto.fileList"
          :auto-upload="false"
          :multiple="true"
          :accept="'image/*'"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :show-file-list="false"
          style="display: none;"
        />

        <div class="upload-container-enhanced">
          <!-- 文件选择和操作区域 -->
          <div class="upload-header-section">
            <div class="header-content">
              <div class="section-info">
                <h3 class="section-title">
                  <el-icon class="title-icon"><Document /></el-icon>
                  文件列表
                  <span v-if="newPhoto.exifInfo.length > 0" class="file-count">
                    ({{ newPhoto.exifInfo.length }} 个文件)
                  </span>
                </h3>
                <p class="section-subtitle" v-if="newPhoto.exifInfo.length === 0">
                  选择要上传的照片文件，支持批量选择
                </p>
              </div>
              <div class="header-actions">
                <el-button
                  size="default"
                  type="primary"
                  @click="$refs.uploadRef.$el.querySelector('input').click()"
                  :icon="Plus"
                  class="action-btn primary-btn"
                  :disabled="uploadState.isUploading"
                >
                  选择照片
                </el-button>
                <el-button
                  v-if="newPhoto.exifInfo.length > 0"
                  size="default"
                  type="danger"
                  @click="clearAllFiles"
                  :disabled="uploadState.isUploading"
                  :icon="Delete"
                  class="action-btn danger-btn"
                >
                  清空列表
                </el-button>
              </div>
            </div>
          </div>

          <!-- 上传进度总览 - 增强版 -->
          <div v-if="uploadState.isUploading" class="upload-progress-section-enhanced">
            <div class="progress-header">
              <h4 class="progress-title">
                <el-icon class="progress-icon"><Clock /></el-icon>
                上传进度
              </h4>
              <div class="progress-percentage">
                {{ Math.round(uploadState.overallProgress) }}%
              </div>
            </div>
            <el-progress
              :percentage="uploadState.overallProgress"
              :status="uploadState.overallProgress === 100 ? 'success' : ''"
              :stroke-width="10"
              class="main-progress-enhanced"
              :show-text="false"
            />
            <div class="progress-stats-enhanced">
              <div class="stat-item success">
                <el-icon><CircleCheck /></el-icon>
                <span class="stat-label">成功</span>
                <span class="stat-value">{{ uploadState.successCount || 0 }}</span>
              </div>
              <div class="stat-item error">
                <el-icon><CircleClose /></el-icon>
                <span class="stat-label">失败</span>
                <span class="stat-value">{{ uploadState.errorCount || 0 }}</span>
              </div>
              <div class="stat-item pending">
                <el-icon><Clock /></el-icon>
                <span class="stat-label">剩余</span>
                <span class="stat-value">{{ uploadState.pendingCount || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 文件列表表格 - 增强版 -->
          <div v-if="newPhoto.exifInfo.length > 0" class="file-table-container-enhanced">
            <el-table
              :data="newPhoto.exifInfo"
              style="width: 100%"
              size="default"
              class="file-table-enhanced"
              :show-header="true"
              max-height="450"
              stripe
              :header-cell-style="{ backgroundColor: '#f8f9fa', fontWeight: 'bold' }"
              :row-style="{ transition: 'all 0.3s ease' }"
            >
              <el-table-column label="预览" width="70" align="center" fixed="left">
                <template #default="scope">
                  <div class="file-preview-enhanced">
                    <el-image
                      :src="getFilePreviewUrl(scope.row.uid)"
                      class="preview-image-enhanced"
                      fit="cover"
                      :preview-disabled="true"
                    >
                      <template #error>
                        <div class="preview-error-enhanced">
                          <el-icon><Picture /></el-icon>
                        </div>
                      </template>
                    </el-image>
                    <div class="preview-overlay-simple">
                      <div class="preview-icon-simple">📷</div>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="文件信息" prop="name" min-width="120" show-overflow-tooltip>
                <template #default="scope">
                  <div class="file-info-enhanced">
                    <div class="file-name-enhanced">
                      <span class="name-text">{{ scope.row.name }}</span>
                      <el-tag v-if="scope.row.exifData?.dateTimeOriginal" size="small" type="success" class="exif-tag">
                        EXIF
                      </el-tag>
                    </div>
                    <div class="file-meta">
                      <span class="size-text">{{ formatFileSize(scope.row.size) }}</span>
                      <span class="type-text">{{ scope.row.name.split('.').pop().toUpperCase() }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="描述" min-width="140">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.caption"
                    placeholder="输入照片描述..."
                    type="textarea"
                    :rows="2"
                    maxlength="200"
                    resize="none"
                    size="default"
                    class="description-input-enhanced"
                    :show-word-limit="true"
                    clearable
                  />
                </template>
              </el-table-column>

              <el-table-column label="标签分类" min-width="140">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.tags"
                    placeholder="选择分类..."
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    clearable
                    size="default"
                    class="tags-select-enhanced"
                    :max-collapse-tags="2"
                    filterable
                    :reserve-keyword="false"
                  >
                    <el-option
                      v-for="category in photoCategories"
                      :key="category.id"
                      :label="category.name"
                      :value="category.id"
                    >
                      <div class="category-option-enhanced">
                        <span
                          class="category-color-dot"
                          :style="{ backgroundColor: category.color }"
                        ></span>
                        <span class="category-name">{{ category.name }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="时间信息" width="120" align="center">
                <template #default="scope">
                  <div class="time-info-compact">
                    <div class="time-item">
                      <el-icon class="time-icon"><Camera /></el-icon>
                      <span v-if="scope.row.exifData?.dateTimeOriginal" class="time-text">
                        {{ formatPhotoTime(scope.row.realPhotoTime) }}
                      </span>
                      <span v-else class="time-placeholder">
                        文件时间
                      </span>
                    </div>
                    <div class="time-item">
                      <el-icon class="time-icon"><Document /></el-icon>
                      <span class="time-text">{{ formatFileTime(scope.row.lastModified) }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="状态" width="100" align="center">
                <template #default="scope">
                  <div class="status-enhanced">
                    <el-tag
                      v-if="getFileUploadState(scope.row.uid).status === 'success'"
                      type="success"
                      size="default"
                      :icon="CircleCheck"
                      effect="light"
                      class="status-tag success"
                    >
                      完成
                    </el-tag>
                    <el-tag
                      v-else-if="getFileUploadState(scope.row.uid).status === 'error'"
                      type="danger"
                      size="default"
                      :icon="CircleClose"
                      effect="light"
                      class="status-tag error"
                    >
                      失败
                    </el-tag>
                    <el-tag
                      v-else-if="getFileUploadState(scope.row.uid).status === 'uploading'"
                      type="warning"
                      size="default"
                      :icon="Refresh"
                      effect="light"
                      class="status-tag uploading"
                    >
                      上传中
                    </el-tag>
                    <el-tag
                      v-else
                      type="info"
                      size="default"
                      effect="light"
                      class="status-tag waiting"
                    >
                      等待
                    </el-tag>
                    <!-- 上传进度条 -->
                    <el-progress
                      v-if="getFileUploadState(scope.row.uid).status === 'uploading'"
                      :percentage="getFileUploadState(scope.row.uid).progress || 0"
                      :stroke-width="6"
                      :show-text="false"
                      class="file-progress-enhanced"
                    />
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="90" align="center" fixed="right">
                <template #default="scope">
                  <div class="file-actions-enhanced">
                    <el-tooltip content="重试上传" placement="top" v-if="getFileUploadState(scope.row.uid).status === 'error'">
                      <el-button
                        size="default"
                        type="warning"
                        :icon="Refresh"
                        circle
                        @click="retryFileUpload(scope.row.uid)"
                        class="action-btn retry-btn"
                      />
                    </el-tooltip>
                    <el-tooltip content="删除文件" placement="top">
                      <el-button
                        size="default"
                        type="danger"
                        :icon="Delete"
                        circle
                        :disabled="uploadState.isUploading && getFileUploadState(scope.row.uid).status === 'uploading'"
                        @click="removeFileFromList(scope.row.uid)"
                        class="action-btn delete-btn"
                      />
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 空状态 - 增强版 -->
          <div v-else class="empty-file-list-enhanced">
            <el-empty description="还没有选择任何照片" :image-size="100">
              <template #image>
                <div class="empty-image">
                  <el-icon class="empty-icon"><Picture /></el-icon>
                </div>
              </template>
              <template #description>
                <div class="empty-description">
                  <h4>还没有选择任何照片</h4>
                  <p>点击上方"选择照片"按钮选择要上传的照片文件</p>
                </div>
              </template>
            </el-empty>
          </div>
        </div>
        <!-- 对话框底部操作栏 - 增强版 -->
        <template #footer>
          <div class="dialog-footer-enhanced">
            <div class="footer-left">
              <el-button
                v-if="uploadState.isUploading && uploadState.canCancel"
                type="warning"
                @click="cancelUpload"
                :icon="Close"
                size="large"
                class="cancel-btn"
              >
                取消上传
              </el-button>
              <div v-if="uploadState.isUploading" class="upload-status-info">
                <span class="status-text">
                  正在上传第 {{ uploadState.currentFileIndex + 1 }} / {{ uploadState.totalFiles }} 个文件
                </span>
                <div class="status-details">
                  <span class="detail-item">
                    <el-icon><CircleCheck /></el-icon>
                    {{ uploadState.successCount || 0 }} 成功
                  </span>
                  <span class="detail-item">
                    <el-icon><CircleClose /></el-icon>
                    {{ uploadState.errorCount || 0 }} 失败
                  </span>
                </div>
              </div>
            </div>
            <div class="footer-center">
              <el-button
                @click="closeUploadDialog"
                :disabled="uploadState.isUploading"
                size="large"
                class="close-btn"
              >
                {{ uploadState.isUploading ? '上传中...' : '关闭' }}
              </el-button>
              <el-button
                type="primary"
                @click="uploadPhotos"
                :disabled="newPhoto.fileList.length === 0 || uploadState.isUploading || uploadState.hasUploaded"
                :loading="uploadState.isUploading"
                size="large"
                class="upload-btn"
              >
                <template v-if="uploadState.hasUploaded">
                  <el-icon><CircleCheck /></el-icon>
                  已上传完成
                </template>
                <template v-else-if="uploadState.isUploading">
                  <el-icon class="rotating"><Refresh /></el-icon>
                  上传中 ({{ Math.round(uploadState.overallProgress) }}%)
                </template>
                <template v-else>
                  <el-icon><Plus /></el-icon>
                  开始上传 ({{ newPhoto.fileList.length }} 个文件)
                </template>
              </el-button>
            </div>
          </div>
        </template>
      </el-dialog>

      <!-- 添加/编辑分类对话框 -->
      <el-dialog
        v-model="showAddCategoryDialog"
        :title="currentCategory.id ? '编辑分类' : '添加分类'"
        width="400px"
      >
        <el-form :model="currentCategory" label-width="80px">
          <el-form-item label="分类名称" required>
            <el-input
              v-model="currentCategory.name"
              placeholder="请输入分类名称"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="分类颜色" required>
            <el-color-picker
              v-model="currentCategory.color"
              :predefine="predefinedColors"
              show-alpha
            />
          </el-form-item>
          <el-form-item label="分类描述">
            <el-input
              v-model="currentCategory.description"
              placeholder="请输入分类描述（可选）"
              type="textarea"
              :rows="2"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showAddCategoryDialog = false">取消</el-button>
            <el-button type="primary" @click="saveCategory">保存</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 编辑照片对话框 - 增强版本 -->
      <el-dialog
        v-model="showEditPhotoDialog"
        width="600px"
        class="edit-photo-dialog-enhanced"
        :append-to-body="true"
        :lock-scroll="true"
        top="5vh"
        :z-index="2500"
      >
        <template #header>
          <div class="edit-dialog-header">
            <h3 class="edit-dialog-title">编辑照片信息</h3>
          </div>
        </template>
        <PhotoProvider>
          <div class="edit-photo-container-enhanced">
            <!-- 照片预览和基本信息 - 参考相册预览卡片样式 -->
            <div class="photo-header-enhanced">
              <div class="photo-container-edit">
                <PhotoConsumer
                  v-if="editingPhoto.url"
                  :src="editingPhoto.url"
                  :intro="editingPhoto.name || '编辑中的照片'"
                >
                  <el-image
                    :src="editingPhoto.url"
                    class="photo-image-edit"
                    fit="cover"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <span>加载失败</span>
                      </div>
                    </template>
                  </el-image>
                </PhotoConsumer>

                <!-- 悬浮遮罩层 - 参考相册预览卡片 -->
                <div class="photo-overlay-edit">
                  <!-- 底部横排信息 -->
                  <div class="photo-details-edit">
                    <div class="detail-item-edit">
                      <el-icon><Document /></el-icon>
                      {{ formatFileSize(editingPhoto.size || editingPhoto.file_size) }}
                    </div>
                    <div class="detail-item-edit">
                      <el-icon><Picture /></el-icon>
                      {{ editingPhoto.type || editingPhoto.file_type || 'image/jpeg' }}
                    </div>
                    <div class="detail-item-edit" v-if="editingPhoto.taken_at || editingPhoto.real_taken_at">
                      <el-icon><Clock /></el-icon>
                      {{ formatDateTime(editingPhoto.taken_at || editingPhoto.real_taken_at) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

          <!-- 编辑表单 - 增强版 -->
          <el-form :model="editingPhoto" label-width="100px" class="edit-form-enhanced" :label-position="'left'">
            <el-form-item label="照片名称" required class="form-item-enhanced">
              <el-input
                v-model="editingPhoto.name"
                placeholder="请输入照片名称..."
                maxlength="50"
                show-word-limit
                clearable
                size="large"
                class="input-enhanced"
              >
                <template #prefix>
                  <el-icon><Edit /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="照片描述" class="form-item-enhanced">
              <el-input
                v-model="editingPhoto.caption"
                placeholder="请输入照片描述..."
                type="textarea"
                :rows="4"
                maxlength="200"
                show-word-limit
                resize="none"
                class="textarea-enhanced"
              />
            </el-form-item>

            <el-form-item class="form-item-enhanced time-form-item">
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>拍摄时间</span>
                  <el-tooltip
                    content="可以修改照片的拍摄时间，这将影响照片的排序。留空则使用原始拍摄时间。"
                    placement="top"
                    effect="dark"
                  >
                    <el-icon class="help-icon"><Warning /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="time-input-wrapper-enhanced">
                <el-date-picker
                  v-model="editingPhoto.taken_at"
                  type="datetime"
                  :placeholder="editingPhoto.taken_at ? formatDateTime(editingPhoto.taken_at) : formatDateTime(editingPhoto.real_taken_at)"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="flex: 1"
                  clearable
                  :default-value="editingPhoto.real_taken_at ? new Date(editingPhoto.real_taken_at) : null"
                  class="time-picker-enhanced"
                  size="large"
                >
                  <template #prefix>
                    <el-icon><Clock /></el-icon>
                  </template>
                </el-date-picker>
                <el-button
                  v-if="editingPhoto.taken_at && editingPhoto.taken_at !== editingPhoto.real_taken_at"
                  type="warning"
                  size="large"
                  @click="resetToRealTime"
                  :icon="Refresh"
                  title="重置为真实时间"
                  class="reset-time-btn-enhanced"
                >
                  重置
                </el-button>
              </div>
            </el-form-item>

            <el-form-item class="form-item-enhanced category-form-item">
              <template #label>
                <div class="form-label-with-tooltip">
                  <span>照片分类</span>
                  <el-tooltip
                    content="可以选择多个分类来更好地组织您的照片，便于后续查找和管理。"
                    placement="top"
                    effect="dark"
                  >
                    <el-icon class="help-icon"><Warning /></el-icon>
                  </el-tooltip>
                </div>
              </template>
              <el-select
                v-model="editingPhoto.tags"
                placeholder="选择分类（可多选）..."
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                :max-collapse-tags="3"
                style="width: 100%"
                size="large"
                class="category-select-enhanced"
                filterable
                :reserve-keyword="false"
              >
                <template #prefix>
                  <el-icon><Star /></el-icon>
                </template>
                <el-option
                  v-for="category in photoCategories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                >
                  <div class="category-option-enhanced">
                    <span
                      class="category-color-dot-enhanced"
                      :style="{ backgroundColor: category.color }"
                    ></span>
                    <span class="category-name-enhanced">{{ category.name }}</span>
                    <span v-if="category.description" class="category-desc">{{ category.description }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          </div>
        </PhotoProvider>

        <template #footer>
          <div class="dialog-footer-enhanced edit-footer-centered">
            <el-button
              type="danger"
              :icon="Delete"
              size="large"
              @click="deletePhoto"
              class="delete-photo-btn"
            >
              删除照片
            </el-button>
            <el-button
              type="primary"
              @click="savePhotoEdit"
              size="large"
              class="save-btn-enhanced"
            >
              <el-icon><CircleCheck /></el-icon>
              保存修改
            </el-button>
          </div>
        </template>
      </el-dialog>





      <!-- 右键菜单 -->
      <div
        v-if="showContextMenu"
        class="context-menu"
        :style="contextMenuStyle"
        @click.stop
      >
        <div class="context-menu-item" @click="editCategory(contextMenuCategory)">
          <el-icon><Edit /></el-icon>
          编辑
        </div>
        <div class="context-menu-item danger" @click="deleteCategory(contextMenuCategory.id)">
          <el-icon><Delete /></el-icon>
          删除
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, inject, watch, computed, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Edit, Picture, Calendar, Star, StarFilled, Clock, Close, Document, Camera, CircleCheck, CircleClose, Refresh, Warning } from '@element-plus/icons-vue';
import draggable from 'vuedraggable';
import { LazyImg, Waterfall } from 'vue-waterfall-plugin-next';
import 'vue-waterfall-plugin-next/dist/style.css';
import { PhotoProvider, PhotoConsumer } from 'vue3-photo-preview';
import 'vue3-photo-preview/dist/index.css';
import EXIF from 'exif-js';

// 错误处理
const handlePhotoError = (error) => {
  console.warn('PhotoConsumer error:', error);
};

// 处理组件错误
const handleComponentError = (error, instance, info) => {
  console.warn('Component error:', error, info);
  return false; // 阻止错误传播
};

// 处理图片加载错误
const handleImageError = (event) => {
  console.warn('Image load error:', event.target.src);
  event.target.style.display = 'none';
};

const supabase = inject('supabase');

// 响应式数据
const photos = ref([]);
const photoCategories = ref([]);
const selectedPhotos = ref([]);
const selectedCategories = ref([]);
const viewMode = ref('grid'); // waterfall, grid, timeline
const showUploadDialog = ref(false);
const showAddCategoryDialog = ref(false);
const showEditPhotoDialog = ref(false);

const showContextMenu = ref(false);
const contextMenuStyle = ref({});
const contextMenuCategory = ref(null);
const isDragging = ref(false);

// 框选功能相关数据
const isSelecting = ref(false);
const selectionBox = ref({
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
  visible: false
});
const photoRefs = ref(new Map());

// 表单数据
const newPhoto = ref({
  fileList: [],
  caption: '',
  tags: [],
  exifInfo: [] // 存储每个文件的EXIF信息
});

// 上传状态管理
const uploadState = ref({
  isUploading: false,
  hasUploaded: false, // 防止重复上传
  overallProgress: 0,
  currentFileIndex: 0,
  totalFiles: 0,
  uploadSpeed: 0, // KB/s
  estimatedTimeRemaining: 0, // 秒
  startTime: null,
  uploadedBytes: 0,
  totalBytes: 0,
  canCancel: true
});

// 单个文件的上传状态
const fileUploadStates = ref(new Map()); // uid -> { status, progress, error, retryCount }

const currentCategory = ref({
  id: null,
  name: '',
  color: '#409EFF',
  description: ''
});

const editingPhoto = ref({
  id: null,
  name: '',
  caption: '',
  taken_at: '',
  real_taken_at: '',
  tags: [],
  url: '',
  storage_path: ''
});

// 拖拽配置
const dragOptions = {
  animation: 300,
  group: 'photo-categories',
  disabled: false,
  ghostClass: 'ghost'
};

// 预定义颜色
const predefinedColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C',
  '#909399', '#9C27B0', '#FF9800', '#4CAF50',
  '#2196F3', '#FF5722', '#795548', '#607D8B'
];

const currentPetId = ref(localStorage.getItem('currentPetId'));
const BUCKET_NAME = 'pet-media';

// 特殊标签相关数据（收藏、无标签等）
const selectedTags = ref([]);

// 排序模式相关数据
const sortMode = ref('upload_time'); // 'upload_time' | 'file_time'



// 计算属性
const filteredPhotos = computed(() => {
  let filtered = photos.value;

  // 按分类筛选 (OR逻辑 - 照片只要包含任一选中的分类即可显示)
  if (selectedCategories.value.length > 0) {
    filtered = filtered.filter(photo => {
      if (!photo.tags || photo.tags.length === 0) return false;
      const photoTagIds = photo.tags.map(tag => tag.id);
      return selectedCategories.value.some(categoryId => photoTagIds.includes(categoryId));
    });
  }

  // 特殊标签筛选 (AND逻辑)
  if (selectedTags.value.length > 0) {
    filtered = filtered.filter(photo => {
      return selectedTags.value.every(tagId => {
        if (tagId === 'favorite') {
          return photo.is_favorite;
        }
        if (tagId === 'untagged') {
          // 无标签：既没有标签也没有收藏
          return (!photo.tags || photo.tags.length === 0) && !photo.is_favorite;
        }
        // 其他特殊标签的处理逻辑可以在这里添加
        return false;
      });
    });
  }

  // 根据排序模式进行排序
  const sorted = [...filtered].map(addFileTimeToPhoto).sort((a, b) => {
    let dateA, dateB;

    if (sortMode.value === 'file_time') {
      // 按文件时间排序：优先使用提取的文件时间，回退到created_at
      // 优先使用临时编辑时间，然后是数据库中的taken_at，最后是文件时间
      if (a._tempTakenAt) {
        dateA = new Date(a._tempTakenAt);
      } else if (a.taken_at) {
        dateA = new Date(a.taken_at);
      } else {
        dateA = new Date(a._fileTime || a.created_at);
      }

      if (b._tempTakenAt) {
        dateB = new Date(b._tempTakenAt);
      } else if (b.taken_at) {
        dateB = new Date(b.taken_at);
      } else {
        dateB = new Date(b._fileTime || b.created_at);
      }
    } else {
      // 按上传时间排序：使用created_at
      // 优先使用临时编辑时间，然后是数据库中的taken_at，最后是created_at
      if (a._tempTakenAt) {
        dateA = new Date(a._tempTakenAt);
      } else if (a.taken_at) {
        dateA = new Date(a.taken_at);
      } else {
        dateA = new Date(a.created_at);
      }

      if (b._tempTakenAt) {
        dateB = new Date(b._tempTakenAt);
      } else if (b.taken_at) {
        dateB = new Date(b.taken_at);
      } else {
        dateB = new Date(b.created_at);
      }
    }

    // 降序排列（最新的在前）
    return dateB - dateA;
  });

  return sorted;
});

const monthlyPhotos = computed(() => {
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  return photos.value.filter(photo => {
    const photoDate = new Date(photo.created_at);
    return photoDate.getMonth() === currentMonth && photoDate.getFullYear() === currentYear;
  }).length;
});

const favoritePhotos = computed(() => {
  return photos.value.filter(photo => photo.is_favorite).length;
});

const currentMonthDisplay = computed(() => {
  return new Date().toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' });
});

const recentActivity = computed(() => {
  if (photos.value.length === 0) return '无照片';
  const latestPhoto = photos.value.reduce((latest, current) => {
    const latestDate = new Date(latest.created_at);
    const currentDate = new Date(current.created_at);
    return currentDate > latestDate ? current : latest;
  });
  return latestPhoto.name || '最新照片';
});



const latestPhotoDate = computed(() => {
  if (photos.value.length === 0) return '无记录';
  const latestPhoto = photos.value.reduce((latest, current) => {
    const latestDate = new Date(latest.created_at);
    const currentDate = new Date(current.created_at);
    return currentDate > latestDate ? current : latest;
  });
  return formatDate(latestPhoto.created_at);
});

const previewList = computed(() => {
  return filteredPhotos.value.map(photo => photo.url);
});

const isBatchFavorited = computed(() => {
  return selectedPhotos.value.length > 0 && selectedPhotos.value.every(photo => photo.is_favorite);
});

const timelineGroups = computed(() => {
  const groups = {};
  filteredPhotos.value.forEach(photo => {
    // 根据排序模式选择分组依据的时间
    let dateToUse;
    if (sortMode.value === 'file_time') {
      // 优先使用临时编辑时间，然后是数据库中的taken_at，最后是文件时间
      if (photo._tempTakenAt) {
        dateToUse = photo._tempTakenAt;
      } else if (photo.taken_at) {
        dateToUse = photo.taken_at;
      } else {
        dateToUse = photo._fileTime || photo.created_at;
      }
    } else {
      // 优先使用临时编辑时间，然后是数据库中的taken_at，最后是created_at
      if (photo._tempTakenAt) {
        dateToUse = photo._tempTakenAt;
      } else if (photo.taken_at) {
        dateToUse = photo.taken_at;
      } else {
        dateToUse = photo.created_at;
      }
    }

    const date = formatDateForGrouping(dateToUse);
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(photo);
  });

  // 按日期排序（最新的在前）
  const sortedGroups = {};
  Object.keys(groups)
    .sort((a, b) => new Date(b) - new Date(a))
    .forEach(date => {
      // 每个日期内的照片按时间排序（最新的在前）
      sortedGroups[date] = groups[date].sort((a, b) => {
        let dateA, dateB;
        if (sortMode.value === 'file_time') {
          // 优先使用临时编辑时间，然后是数据库中的taken_at，最后是文件时间
          if (a._tempTakenAt) {
            dateA = new Date(a._tempTakenAt);
          } else if (a.taken_at) {
            dateA = new Date(a.taken_at);
          } else {
            dateA = new Date(a._fileTime || a.created_at);
          }

          if (b._tempTakenAt) {
            dateB = new Date(b._tempTakenAt);
          } else if (b.taken_at) {
            dateB = new Date(b.taken_at);
          } else {
            dateB = new Date(b._fileTime || b.created_at);
          }
        } else {
          // 优先使用临时编辑时间，然后是数据库中的taken_at，最后是created_at
          if (a._tempTakenAt) {
            dateA = new Date(a._tempTakenAt);
          } else if (a.taken_at) {
            dateA = new Date(a.taken_at);
          } else {
            dateA = new Date(a.created_at);
          }

          if (b._tempTakenAt) {
            dateB = new Date(b._tempTakenAt);
          } else if (b.taken_at) {
            dateB = new Date(b.taken_at);
          } else {
            dateB = new Date(b.created_at);
          }
        }
        return dateB - dateA;
      });
    });

  return sortedGroups;
});

// 瀑布流照片数据
const waterfallPhotos = computed(() => {
  return filteredPhotos.value.map(photo => ({
    id: photo.id,
    url: photo.url, // 这是 img-selector 指定的字段
    name: photo.name || '无标题',
    caption: photo.caption || '',
    created_at: photo.created_at,
    is_favorite: photo.is_favorite || false,
    category_id: photo.category_id,
    tags: photo.tags || [],
    // 保留原始数据
    ...photo
  }));
});

// 显示的拍摄时间（优先显示真实时间，如果用户修改了则显示修改后的时间）
const displayTakenTime = computed(() => {
  if (editingPhoto.value.taken_at) {
    return formatDateTime(editingPhoto.value.taken_at);
  }
  if (editingPhoto.value.real_taken_at) {
    return formatDateTime(editingPhoto.value.real_taken_at);
  }
  return '无法获取拍摄时间';
});

// 工具函数
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleDateString();
};

// 清理文件名，移除特殊字符和中文字符
const sanitizeFileName = (fileName) => {
  if (!fileName) return 'untitled.jpg';

  // 获取文件扩展名
  const lastDotIndex = fileName.lastIndexOf('.');
  const name = lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName;
  const extension = lastDotIndex > 0 ? fileName.substring(lastDotIndex).toLowerCase() : '.jpg';

  // 清理文件名：只保留ASCII字母、数字、连字符和下划线
  let cleanName = name
    .replace(/[^a-zA-Z0-9\-_]/g, '_') // 只保留ASCII字母数字和安全字符
    .replace(/_{2,}/g, '_') // 多个连续下划线替换为单个
    .replace(/^_+|_+$/g, ''); // 移除开头和结尾的下划线

  // 限制长度并确保不为空
  cleanName = cleanName.substring(0, 30) || 'photo';

  // 确保扩展名是安全的，只保留常见的图片扩展名
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff'];
  const safeExtension = allowedExtensions.includes(extension) ? extension : '.jpg';

  return cleanName + safeExtension;
};

const formatFullDateTime = (dateStr) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleString();
};

const formatDateTime = (dateStr) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const formatTime = (dateStr) => {
  if (!dateStr) return '';
  return new Date(dateStr).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// 用于分组的日期格式化（YYYY-MM-DD）
const formatDateForGrouping = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toISOString().split('T')[0];
};

// 友好的日期显示
const formatFriendlyDate = (dateStr) => {
  if (!dateStr) return '';

  const date = new Date(dateStr);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  // 重置时间部分以便比较日期
  const dateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const yesterdayOnly = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());

  if (dateOnly.getTime() === todayOnly.getTime()) {
    return '今天';
  } else if (dateOnly.getTime() === yesterdayOnly.getTime()) {
    return '昨天';
  } else {
    // 显示为 "2025年6月19日"
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
};

// 获取某天的收藏照片数量
const getDayFavoriteCount = (photos) => {
  return photos.filter(photo => photo.is_favorite).length;
};

const adjustBrightness = (color, percent) => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  const factor = percent / 100;
  const newR = Math.round(r + (255 - r) * factor);
  const newG = Math.round(g + (255 - g) * factor);
  const newB = Math.round(b + (255 - b) * factor);

  const toHex = (n) => {
    const hex = n.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
};

const getPhotoIndex = (photoId) => {
  return filteredPhotos.value.findIndex(photo => photo.id === photoId);
};

const getPhotoIntro = (photo) => {
  let intro = photo.name || '无标题';
  const description = photo.caption || photo.description;
  if (description) {
    intro += `\n\n${description}`;
  }

  // 显示拍摄时间信息
  let timeInfo = '';

  // 优先显示taken_at（用户设置的拍摄时间）
  if (photo.taken_at) {
    const takenDate = new Date(photo.taken_at).toLocaleString('zh-CN');
    timeInfo = `拍摄时间: ${takenDate}`;
  } else {
    // 如果没有taken_at，尝试显示从文件名提取的时间
    const fileTime = extractFileTimeFromPath(photo.storage_path);
    if (fileTime) {
      const fileDate = fileTime.toLocaleString('zh-CN');
      timeInfo = `拍摄时间: ${fileDate} (从文件提取)`;
    } else {
      // 最后回退到上传时间
      const uploadDate = new Date(photo.created_at).toLocaleString('zh-CN');
      timeInfo = `上传时间: ${uploadDate}`;
    }
  }

  if (timeInfo) {
    intro += `\n\n${timeInfo}`;
  }

  // 添加分类信息
  if (photo.tags && photo.tags.length > 0) {
    const tagNames = photo.tags.map(tag => tag.name).join(', ');
    intro += `\n\n分类: ${tagNames}`;
  }

  return intro;
};

// 图片预览功能
const previewPhoto = (photo) => {
  // 简单的图片预览，可以后续集成更复杂的预览组件
  window.open(photo.url, '_blank');
};

// 事件处理函数
const handleViewModeChange = (mode) => {
  viewMode.value = mode;
};

const handleCategoryFilter = (categoryId) => {
  if (categoryId === '') {
    // 点击"全部"标签，清除所有分类选择
    selectedCategories.value = [];
  } else {
    // 切换分类选择状态
    const index = selectedCategories.value.indexOf(categoryId);
    if (index > -1) {
      // 如果已选中，则取消选择
      selectedCategories.value.splice(index, 1);
    } else {
      // 如果未选中，则添加到选择列表
      selectedCategories.value.push(categoryId);
    }
  }

  // 清除特殊标签筛选，确保分类筛选和特殊标签筛选不会冲突
  selectedTags.value = [];
};

const selectPhoto = (photo) => {
  const index = selectedPhotos.value.findIndex(p => p.id === photo.id);
  if (index > -1) {
    selectedPhotos.value.splice(index, 1);
  } else {
    selectedPhotos.value.push(photo);
  }
};

const togglePhotoSelection = (photo) => {
  console.log('togglePhotoSelection called with photo:', photo.name || photo.id);
  selectPhoto(photo);
  console.log('Selected photos count:', selectedPhotos.value.length);
};



// 清空选择
const clearSelection = () => {
  console.log('Clearing selection');
  selectedPhotos.value = [];
};



// 键盘快捷键处理
const handleKeydown = (event) => {
  // Ctrl+A 全选
  if (event.ctrlKey && event.key === 'a') {
    event.preventDefault();
    selectedPhotos.value = [...filteredPhotos.value];
  }
  // Escape 处理：优先关闭预览，再关闭对话框
  else if (event.key === 'Escape') {
    // 检查是否有vue3-photo-preview预览窗口打开
    const photoViewPortal = document.querySelector('.PhotoView-Portal');
    if (photoViewPortal && photoViewPortal.style.display !== 'none') {
      // 如果预览窗口打开，不处理ESC，让vue3-photo-preview自己处理
      return;
    }

    // 如果编辑对话框打开，关闭它
    if (showEditPhotoDialog.value) {
      showEditPhotoDialog.value = false;
      event.preventDefault();
      return;
    }

    // 否则处理选择相关的ESC
    clearSelection();
    endSelection();
  }
  // Delete 删除选中的照片
  else if (event.key === 'Delete' && selectedPhotos.value.length > 0) {
    batchDeletePhotos();
  }
};

// 框选功能
const currentContainer = ref(null);
const activeContainerId = ref(null); // 当前活动的容器ID
const selectionModifiers = ref({
  shift: false,
  ctrl: false
});

const startSelection = (event) => {
  // 只在容器的空白区域开始框选
  // 检查点击的是否是容器本身或者空白区域
  const isContainer = event.target.classList.contains('photos-container') ||
                     event.target.classList.contains('photos-masonry') ||
                     event.target.classList.contains('photos-grid') ||
                     event.target.classList.contains('timeline-photos-grid');

  if (!isContainer) {
    return;
  }

  // 记录键盘修饰键状态
  selectionModifiers.value = {
    shift: event.shiftKey,
    ctrl: event.ctrlKey || event.metaKey // Mac 上的 Cmd 键
  };

  isSelecting.value = true;
  currentContainer.value = event.currentTarget;

  // 为容器分配唯一ID并记录当前活动容器
  if (!event.currentTarget.dataset.containerId) {
    // 为时间线容器使用特殊的ID格式
    if (event.currentTarget.hasAttribute('data-timeline-group')) {
      const groupIndex = event.currentTarget.getAttribute('data-timeline-group');
      event.currentTarget.dataset.containerId = `timeline-${groupIndex}`;
    } else if (event.currentTarget.classList.contains('photos-masonry')) {
      event.currentTarget.dataset.containerId = 'waterfall';
    } else if (event.currentTarget.classList.contains('photos-grid')) {
      event.currentTarget.dataset.containerId = 'grid';
    } else {
      event.currentTarget.dataset.containerId = `container-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
  }
  activeContainerId.value = event.currentTarget.dataset.containerId;

  const rect = event.currentTarget.getBoundingClientRect();
  selectionBox.value = {
    startX: event.clientX - rect.left + event.currentTarget.scrollLeft,
    startY: event.clientY - rect.top + event.currentTarget.scrollTop,
    endX: event.clientX - rect.left + event.currentTarget.scrollLeft,
    endY: event.clientY - rect.top + event.currentTarget.scrollTop,
    visible: true
  };

  // 添加选择状态类
  event.currentTarget.classList.add('selecting');

  // 添加全局事件监听器
  document.addEventListener('mousemove', handleGlobalMouseMove);
  document.addEventListener('mouseup', handleGlobalMouseUp);

  // 阻止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();
};

const updateSelection = (event) => {
  if (!isSelecting.value) return;

  const rect = event.currentTarget.getBoundingClientRect();
  selectionBox.value.endX = event.clientX - rect.left + event.currentTarget.scrollLeft;
  selectionBox.value.endY = event.clientY - rect.top + event.currentTarget.scrollTop;

  // 实时更新选中的照片
  updateSelectedPhotosInBox();
};

// 全局鼠标移动处理
const handleGlobalMouseMove = (event) => {
  if (!isSelecting.value || !currentContainer.value) return;

  // 更新修饰键状态（用户可能在拖拽过程中按下或释放修饰键）
  selectionModifiers.value = {
    shift: event.shiftKey,
    ctrl: event.ctrlKey || event.metaKey
  };

  const rect = currentContainer.value.getBoundingClientRect();
  selectionBox.value.endX = event.clientX - rect.left + currentContainer.value.scrollLeft;
  selectionBox.value.endY = event.clientY - rect.top + currentContainer.value.scrollTop;

  // 实时更新选中的照片
  updateSelectedPhotosInBox();
};

// 全局鼠标释放处理
const handleGlobalMouseUp = (event) => {
  if (!isSelecting.value) return;

  endSelection();
};

const endSelection = (event) => {
  if (!isSelecting.value) return;

  isSelecting.value = false;
  selectionBox.value.visible = false;

  // 移除全局事件监听器
  document.removeEventListener('mousemove', handleGlobalMouseMove);
  document.removeEventListener('mouseup', handleGlobalMouseUp);

  // 移除选择状态类
  if (currentContainer.value) {
    currentContainer.value.classList.remove('selecting');
  } else {
    // 如果没有容器引用，查找并移除所有选择状态类
    document.querySelectorAll('.photos-container.selecting').forEach(container => {
      container.classList.remove('selecting');
    });
  }

  currentContainer.value = null;
  activeContainerId.value = null; // 清除活动容器ID
};

const updateSelectedPhotosInBox = () => {
  if (!selectionBox.value.visible || !currentContainer.value) return;

  const box = {
    left: Math.min(selectionBox.value.startX, selectionBox.value.endX),
    top: Math.min(selectionBox.value.startY, selectionBox.value.endY),
    right: Math.max(selectionBox.value.startX, selectionBox.value.endX),
    bottom: Math.max(selectionBox.value.startY, selectionBox.value.endY)
  };

  // 使用当前活动的容器
  const activeContainer = currentContainer.value;
  const photoElements = activeContainer.querySelectorAll('.unified-photo-card');
  const containerRect = activeContainer.getBoundingClientRect();

  // 找出框选区域内的照片
  const photosInBox = [];
  photoElements.forEach((element, index) => {
    const rect = element.getBoundingClientRect();
    const relativeRect = {
      left: rect.left - containerRect.left + activeContainer.scrollLeft,
      top: rect.top - containerRect.top + activeContainer.scrollTop,
      right: rect.right - containerRect.left + activeContainer.scrollLeft,
      bottom: rect.bottom - containerRect.top + activeContainer.scrollTop
    };

    // 检查照片是否与选择框相交
    const isIntersecting = !(
      relativeRect.right < box.left ||
      relativeRect.left > box.right ||
      relativeRect.bottom < box.top ||
      relativeRect.top > box.bottom
    );

    if (isIntersecting) {
      const photoId = element.getAttribute('data-photo-id');
      const photo = filteredPhotos.value.find(p => p.id.toString() === photoId);
      if (photo) {
        photosInBox.push(photo);
      }
    }
  });

  // 根据修饰键状态决定选择行为
  if (selectionModifiers.value.ctrl) {
    // Ctrl + 框选：减选模式 - 从当前选择中移除框选区域内的照片
    selectedPhotos.value = selectedPhotos.value.filter(photo =>
      !photosInBox.some(boxPhoto => boxPhoto.id === photo.id)
    );
  } else if (selectionModifiers.value.shift) {
    // Shift + 框选：加选模式 - 将框选区域内的照片添加到当前选择
    const newPhotos = photosInBox.filter(photo =>
      !selectedPhotos.value.some(selected => selected.id === photo.id)
    );
    selectedPhotos.value = [...selectedPhotos.value, ...newPhotos];
  } else {
    // 默认框选：替换模式 - 清空当前选择，仅选中框选区域内的照片
    selectedPhotos.value = [...photosInBox];
  }
};

// 计算选择框样式 - 支持多容器
const getSelectionBoxStyle = (containerId) => {
  // 只有当前活动容器才显示选择框
  if (!selectionBox.value.visible || !activeContainerId.value) {
    return { display: 'none' };
  }

  // 简化匹配逻辑：直接比较容器ID
  if (activeContainerId.value !== containerId) {
    return { display: 'none' };
  }

  const left = Math.min(selectionBox.value.startX, selectionBox.value.endX);
  const top = Math.min(selectionBox.value.startY, selectionBox.value.endY);
  const width = Math.abs(selectionBox.value.endX - selectionBox.value.startX);
  const height = Math.abs(selectionBox.value.endY - selectionBox.value.startY);

  return {
    position: 'absolute',
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
    border: '2px dashed #409EFF',
    backgroundColor: 'rgba(64, 158, 255, 0.1)',
    pointerEvents: 'none',
    zIndex: 1000
  };
};

// 触摸设备交互处理
const handleTouchStart = (event, photo) => {
  // 检测是否为触摸设备
  if ('ontouchstart' in window) {
    const card = event.currentTarget;
    card.classList.add('touch-active');

    // 设置定时器，3秒后自动隐藏遮罩
    setTimeout(() => {
      card.classList.remove('touch-active');
    }, 3000);
  }
};

const toggleFavorite = async (photo) => {
  try {
    const { error } = await supabase
      .from('photos')
      .update({ is_favorite: !photo.is_favorite })
      .eq('id', photo.id);

    if (error) throw error;

    photo.is_favorite = !photo.is_favorite;
    ElMessage.success(photo.is_favorite ? '已添加到收藏' : '已取消收藏');
  } catch (error) {
    console.error('更新收藏状态失败:', error);
    ElMessage.error('操作失败: ' + error.message);
  }
};

const batchToggleFavorite = async () => {
  if (selectedPhotos.value.length === 0) return;

  try {
    const newFavoriteState = !isBatchFavorited.value;
    const photoIds = selectedPhotos.value.map(photo => photo.id);

    const { error } = await supabase
      .from('photos')
      .update({ is_favorite: newFavoriteState })
      .in('id', photoIds);

    if (error) throw error;

    selectedPhotos.value.forEach(photo => {
      photo.is_favorite = newFavoriteState;
    });

    ElMessage.success(`已${newFavoriteState ? '添加到' : '取消'}收藏 ${selectedPhotos.value.length} 张照片`);
    selectedPhotos.value = [];
  } catch (error) {
    console.error('批量更新收藏状态失败:', error);
    ElMessage.error('操作失败: ' + error.message);
  }
};

const batchDeletePhotos = async () => {
  if (selectedPhotos.value.length === 0) {
    ElMessage.warning('请先选择要删除的照片');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPhotos.value.length} 张照片吗？此操作不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const photoIds = selectedPhotos.value.map(photo => photo.id);

    const { error } = await supabase
      .from('photos')
      .delete()
      .in('id', photoIds);

    if (error) throw error;

    ElMessage.success(`成功删除 ${selectedPhotos.value.length} 张照片`);
    selectedPhotos.value = [];
    fetchPhotos();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除照片失败:', error);
      ElMessage.error('批量删除照片失败: ' + error.message);
    }
  }
};

const deletePhoto = async (photo) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张照片吗？此操作不可恢复。',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const { error } = await supabase
      .from('photos')
      .delete()
      .eq('id', photo.id);

    if (error) throw error;

    ElMessage.success('照片删除成功');
    fetchPhotos();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除照片失败:', error);
      ElMessage.error('删除照片失败: ' + error.message);
    }
  }
};

const editPhoto = (photo) => {
  // 获取真实的拍摄时间（优先级：taken_at > 文件时间 > created_at）
  let realTakenTime = null;
  if (photo.taken_at) {
    realTakenTime = photo.taken_at;
  } else {
    // 尝试从文件路径提取时间
    const fileTime = extractFileTimeFromPath(photo.storage_path);
    if (fileTime) {
      realTakenTime = fileTime.toISOString();
    } else {
      realTakenTime = photo.created_at;
    }
  }

  editingPhoto.value = {
    id: photo.id,
    name: photo.name || '',
    caption: photo.caption || photo.description || '',
    taken_at: photo.taken_at || '', // 用户设置的拍摄时间
    real_taken_at: realTakenTime, // 真实的拍摄时间（用于重置）
    category_id: photo.category_id || '',
    tags: photo.tags ? photo.tags.map(tag => tag.id) : [],
    url: photo.url, // 添加照片URL用于预览
    storage_path: photo.storage_path, // 添加存储路径用于时间提取
    size: photo.size || 0, // 文件大小
    type: photo.type || 'image/jpeg' // 文件类型
  };
  showEditPhotoDialog.value = true;
};

// 重置为真实拍摄时间
const resetToRealTime = () => {
  editingPhoto.value.taken_at = '';
  ElMessage.success('已重置为真实拍摄时间');
};

// 监听编辑照片的拍摄时间变化，实时更新照片列表显示
watch(
  () => editingPhoto.value.taken_at,
  (newTakenAt) => {
    if (editingPhoto.value.id && showEditPhotoDialog.value) {
      // 找到对应的照片并更新临时显示时间
      const photoIndex = photos.value.findIndex(photo => photo.id === editingPhoto.value.id);
      if (photoIndex !== -1) {
        // 创建一个新的照片对象，添加临时时间字段
        const updatedPhoto = { ...photos.value[photoIndex] };
        updatedPhoto._tempTakenAt = newTakenAt; // 使用临时字段存储编辑中的时间

        // 更新照片数组
        photos.value[photoIndex] = updatedPhoto;
      }
    }
  },
  { immediate: false }
);

// 监听编辑对话框关闭，清理临时时间字段
watch(
  () => showEditPhotoDialog.value,
  (isOpen) => {
    if (!isOpen && editingPhoto.value.id) {
      // 对话框关闭时，清理临时时间字段
      const photoIndex = photos.value.findIndex(photo => photo.id === editingPhoto.value.id);
      if (photoIndex !== -1) {
        const updatedPhoto = { ...photos.value[photoIndex] };
        delete updatedPhoto._tempTakenAt; // 清理临时字段
        photos.value[photoIndex] = updatedPhoto;
      }
    }
  }
);





// 格式化真实拍摄时间显示
const formatRealTakenTime = () => {
  if (!editingPhoto.value.real_taken_at) return '无法获取';

  const date = new Date(editingPhoto.value.real_taken_at);
  const now = new Date();
  const isFileTime = !editingPhoto.value.storage_path ||
    extractFileTimeFromPath(editingPhoto.value.storage_path);

  const timeStr = date.toLocaleString('zh-CN');
  const source = isFileTime ? '(从文件提取)' : '(文件创建时间)';

  return `${timeStr} ${source}`;
};

const savePhotoEdit = async () => {
  try {
    if (!editingPhoto.value.name.trim()) {
      ElMessage.warning('请输入照片名称');
      return;
    }

    const { error } = await supabase
      .from('photos')
      .update({
        name: editingPhoto.value.name.trim(),
        caption: editingPhoto.value.caption?.trim() || null,
        taken_at: editingPhoto.value.taken_at || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', editingPhoto.value.id);

    if (error) throw error;

    // 更新分类关联
    const currentPhoto = photos.value.find(p => p.id === editingPhoto.value.id);
    const currentCategoryIds = currentPhoto?.tags?.map(tag => tag.id) || [];
    const newCategoryIds = editingPhoto.value.tags;

    // 找出需要添加和删除的分类
    const categoriesToAdd = newCategoryIds.filter(categoryId => !currentCategoryIds.includes(categoryId));
    const categoriesToRemove = currentCategoryIds.filter(categoryId => !newCategoryIds.includes(categoryId));

    // 删除不需要的分类关联
    if (categoriesToRemove.length > 0) {
      const { error: removeError } = await supabase
        .from('photo_category_relations')
        .delete()
        .eq('photo_id', editingPhoto.value.id)
        .in('category_id', categoriesToRemove);

      if (removeError) {
        console.error('删除分类关联失败:', removeError);
      }
    }

    // 添加新的分类关联
    if (categoriesToAdd.length > 0) {
      const categoryRelations = categoriesToAdd.map(categoryId => ({
        photo_id: editingPhoto.value.id,
        category_id: categoryId
      }));

      const { error: addError } = await supabase
        .from('photo_category_relations')
        .insert(categoryRelations);

      if (addError) {
        console.error('添加分类关联失败:', addError);
      }
    }

    // 重新获取照片数据以更新标签显示
    await fetchPhotos();

    // 清理临时字段（如果照片仍在列表中）
    const photoIndex = photos.value.findIndex(photo => photo.id === editingPhoto.value.id);
    if (photoIndex !== -1) {
      const updatedPhoto = { ...photos.value[photoIndex] };
      delete updatedPhoto._tempTakenAt;
      photos.value[photoIndex] = updatedPhoto;
    }

    showEditPhotoDialog.value = false;
    ElMessage.success('照片信息更新成功');
  } catch (error) {
    console.error('更新照片信息失败:', error);
    ElMessage.error('更新失败: ' + error.message);
  }
};

// 分类管理函数
const onDragStart = () => {
  isDragging.value = true;
};

const onDragEnd = async () => {
  isDragging.value = false;

  // 更新排序
  try {
    const updates = photoCategories.value.map((category, index) => ({
      id: category.id,
      sort_order: index
    }));

    for (const update of updates) {
      const { error } = await supabase
        .from('photo_categories')
        .update({ sort_order: update.sort_order })
        .eq('id', update.id);

      if (error) throw error;
    }

    ElMessage.success('分类排序已更新');
  } catch (error) {
    console.error('更新分类排序失败:', error);
    ElMessage.error('更新排序失败: ' + error.message);
    fetchCategories(); // 重新获取数据
  }
};

const showCategoryContextMenu = (event, category) => {
  contextMenuCategory.value = category;
  contextMenuStyle.value = {
    position: 'fixed',
    left: `${event.clientX}px`,
    top: `${event.clientY}px`,
    zIndex: 9999
  };
  showContextMenu.value = true;

  // 点击其他地方关闭菜单
  const closeMenu = () => {
    showContextMenu.value = false;
    document.removeEventListener('click', closeMenu);
  };

  setTimeout(() => {
    document.addEventListener('click', closeMenu);
  }, 100);
};

const editCategory = (category) => {
  currentCategory.value = { ...category };
  showAddCategoryDialog.value = true;
};

const deleteCategory = async (categoryId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个分类吗？分类下的照片将变为未分类。',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    const { error } = await supabase
      .from('photo_categories')
      .delete()
      .eq('id', categoryId);

    if (error) throw error;

    ElMessage.success('分类删除成功');
    fetchCategories();

    // 如果当前选中的分类被删除，从选择列表中移除
    const index = selectedCategories.value.indexOf(categoryId);
    if (index > -1) {
      selectedCategories.value.splice(index, 1);
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error);
      ElMessage.error('删除分类失败: ' + error.message);
    }
  }
};









const toggleFavoriteFilter = () => {
  const favoriteIndex = selectedTags.value.indexOf('favorite');
  if (favoriteIndex > -1) {
    selectedTags.value.splice(favoriteIndex, 1);
  } else {
    selectedTags.value.push('favorite');
  }

  // 清除分类筛选，确保分类筛选和特殊标签筛选不会冲突
  selectedCategories.value = [];
};

const toggleUntaggedFilter = () => {
  const untaggedIndex = selectedTags.value.indexOf('untagged');
  if (untaggedIndex > -1) {
    selectedTags.value.splice(untaggedIndex, 1);
  } else {
    selectedTags.value.push('untagged');
  }

  // 清除分类筛选，确保分类筛选和特殊标签筛选不会冲突
  selectedCategories.value = [];
};



const getTagById = (tagId) => {
  // 现在统一使用分类数据
  return photoCategories.value.find(category => category.id === tagId);
};

// 纯前端文件时间提取函数
const extractFileTimeFromPath = (storagePath) => {
  try {
    if (!storagePath) return null;

    // 从storage_path中提取时间戳
    // 格式: "petId/timestamp_filename.ext"
    const fileName = storagePath.split('/').pop(); // 获取文件名部分
    const timestampMatch = fileName.match(/^(\d{13})_/); // 匹配13位时间戳

    if (timestampMatch) {
      const timestamp = parseInt(timestampMatch[1]);
      // 验证时间戳是否合理（2020年之后）
      if (timestamp > 1577836800000) { // 2020-01-01的时间戳
        return new Date(timestamp);
      }
    }

    return null;
  } catch (error) {
    console.error('解析文件时间戳失败:', error);
    return null;
  }
};

// 为照片对象添加文件时间（纯前端处理）
const addFileTimeToPhoto = (photo) => {
  if (photo._fileTime !== undefined) {
    return photo; // 已经处理过
  }

  // 尝试从文件路径提取时间
  const fileTime = extractFileTimeFromPath(photo.storage_path);

  // 添加文件时间属性（使用下划线前缀表示这是前端添加的属性）
  photo._fileTime = fileTime;

  return photo;
};

// 排序模式切换函数
const toggleSortMode = () => {
  sortMode.value = sortMode.value === 'upload_time' ? 'file_time' : 'upload_time';
  console.log('切换排序模式:', sortMode.value);
};

// EXIF信息提取函数
const extractEXIFInfo = (file) => {
  return new Promise((resolve) => {
    EXIF.getData(file, function() {
      const exifData = {
        // 拍摄时间
        dateTime: EXIF.getTag(this, 'DateTime'),
        dateTimeOriginal: EXIF.getTag(this, 'DateTimeOriginal'),
        dateTimeDigitized: EXIF.getTag(this, 'DateTimeDigitized'),

        // 相机信息
        make: EXIF.getTag(this, 'Make'),
        model: EXIF.getTag(this, 'Model'),

        // 拍摄参数
        fNumber: EXIF.getTag(this, 'FNumber'),
        exposureTime: EXIF.getTag(this, 'ExposureTime'),
        iso: EXIF.getTag(this, 'ISOSpeedRatings'),
        focalLength: EXIF.getTag(this, 'FocalLength'),

        // GPS信息
        gpsLatitude: EXIF.getTag(this, 'GPSLatitude'),
        gpsLongitude: EXIF.getTag(this, 'GPSLongitude'),
        gpsLatitudeRef: EXIF.getTag(this, 'GPSLatitudeRef'),
        gpsLongitudeRef: EXIF.getTag(this, 'GPSLongitudeRef'),

        // 图像信息
        imageWidth: EXIF.getTag(this, 'PixelXDimension'),
        imageHeight: EXIF.getTag(this, 'PixelYDimension'),
        orientation: EXIF.getTag(this, 'Orientation')
      };

      resolve(exifData);
    });
  });
};

// 从EXIF获取真实拍摄时间
const getRealPhotoTime = (exifData, file) => {
  // 优先级：DateTimeOriginal > DateTime > DateTimeDigitized > file.lastModified
  const timeFields = [
    exifData.dateTimeOriginal,
    exifData.dateTime,
    exifData.dateTimeDigitized
  ];

  for (const timeStr of timeFields) {
    if (timeStr) {
      try {
        // EXIF时间格式: "YYYY:MM:DD HH:MM:SS"
        const formattedTime = timeStr.replace(/:/g, '-').replace(/-(\d{2}:\d{2}:\d{2})/, ' $1');
        const date = new Date(formattedTime);
        if (!isNaN(date.getTime())) {
          return date;
        }
      } catch (error) {
        console.warn('解析EXIF时间失败:', timeStr, error);
      }
    }
  }

  // 回退到文件修改时间
  return new Date(file.lastModified || Date.now());
};

// 格式化EXIF信息用于显示
const formatEXIFForDisplay = (exifData) => {
  const info = [];

  // 拍摄时间
  if (exifData.dateTimeOriginal) {
    info.push(`拍摄时间: ${exifData.dateTimeOriginal}`);
  }

  // 相机信息
  if (exifData.make && exifData.model) {
    info.push(`相机: ${exifData.make} ${exifData.model}`);
  }

  // 拍摄参数
  const params = [];
  if (exifData.fNumber) params.push(`f/${exifData.fNumber}`);
  if (exifData.exposureTime) params.push(`${exifData.exposureTime}s`);
  if (exifData.iso) params.push(`ISO${exifData.iso}`);
  if (exifData.focalLength) params.push(`${exifData.focalLength}mm`);

  if (params.length > 0) {
    info.push(`参数: ${params.join(' ')}`);
  }

  // 图像尺寸
  if (exifData.imageWidth && exifData.imageHeight) {
    info.push(`尺寸: ${exifData.imageWidth}×${exifData.imageHeight}`);
  }

  return info.join('\n');
};

// 显示排序模式信息
const getSortModeInfo = () => {
  if (sortMode.value === 'file_time') {
    const photosWithFileTime = filteredPhotos.value.filter(photo => photo._fileTime).length;
    const totalPhotos = filteredPhotos.value.length;
    return `文件时间排序 (${photosWithFileTime}/${totalPhotos} 张照片有文件时间)`;
  } else {
    return '上传时间排序';
  }
};

// 获取文件时间排序的详细tooltip信息
const getFileTimeSortTooltip = () => {
  const totalPhotos = filteredPhotos.value.length;
  if (totalPhotos === 0) {
    return '切换控制照片的时间模式：优先使用照片的真实拍摄时间';
  }

  const photosWithTakenAt = filteredPhotos.value.filter(photo => photo.taken_at).length;
  const photosWithFileTime = filteredPhotos.value.filter(photo => photo._fileTime && !photo.taken_at).length;
  const photosWithUploadTime = totalPhotos - photosWithTakenAt - photosWithFileTime;

  let tooltip = '切换控制照片的时间模式\n';
  tooltip += '优先级：设置时间 > 文件时间 > 上传时间\n\n';
  tooltip += `统计：设置${photosWithTakenAt} | 文件${photosWithFileTime} | 上传${photosWithUploadTime}`;

  return tooltip;
};

// 获取上传时间排序的详细tooltip信息
const getUploadTimeSortTooltip = () => {
  const totalPhotos = filteredPhotos.value.length;
  if (totalPhotos === 0) {
    return '切换控制照片的时间模式：使用照片上传到系统的时间';
  }

  let tooltip = '切换控制照片的时间模式\n';
  tooltip += '按照添加到相册的顺序排序\n';
  tooltip += `共 ${totalPhotos} 张照片`;

  return tooltip;
};







// 数据获取函数
const fetchPhotos = async () => {
  if (!currentPetId.value) {
    photos.value = [];
    return;
  }

  try {
    // 获取照片及其分类信息
    const { data, error } = await supabase
      .from('photos')
      .select(`
        *,
        photo_category_relations (
          photo_categories (
            id,
            name,
            color
          )
        )
      `)
      .eq('pet_id', currentPetId.value)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // 处理分类数据结构
    photos.value = (data || []).map(photo => ({
      ...photo,
      tags: photo.photo_category_relations?.map(relation => relation.photo_categories) || []
    }));
  } catch (error) {
    console.error('获取照片列表失败:', error);
    ElMessage.error('获取照片列表失败: ' + error.message);
    photos.value = [];
  }
};

const fetchCategories = async () => {
  try {
    const { data, error } = await supabase
      .from('photo_categories')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) throw error;
    photoCategories.value = data || [];
  } catch (error) {
    console.error('获取分类列表失败:', error);
    ElMessage.error('获取分类列表失败: ' + error.message);
    photoCategories.value = [];
  }
};

const openUploadDialog = () => {
  newPhoto.value = {
    fileList: [],
    caption: '',
    tags: [],
    exifInfo: []
  };
  resetUploadState();
  showUploadDialog.value = true;
};

// 重置上传状态
const resetUploadState = () => {
  uploadState.value = {
    isUploading: false,
    hasUploaded: false,
    overallProgress: 0,
    currentFileIndex: 0,
    totalFiles: 0,
    uploadSpeed: 0,
    estimatedTimeRemaining: 0,
    startTime: null,
    uploadedBytes: 0,
    totalBytes: 0,
    canCancel: true
  };
  fileUploadStates.value.clear();
};

// 获取文件上传状态
const getFileUploadState = (uid) => {
  return fileUploadStates.value.get(uid) || {
    status: 'ready',
    progress: 0,
    error: null,
    retryCount: 0
  };
};

// 设置文件上传状态
const setFileUploadState = (uid, state) => {
  const currentState = getFileUploadState(uid);
  fileUploadStates.value.set(uid, { ...currentState, ...state });
};

// 格式化上传速度
const formatSpeed = (bytesPerSecond) => {
  if (bytesPerSecond < 1024) return `${bytesPerSecond.toFixed(0)} B/s`;
  if (bytesPerSecond < 1024 * 1024) return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
  return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
};

// 格式化上传剩余时间
const formatUploadTime = (seconds) => {
  if (seconds < 60) return `${Math.round(seconds)}秒`;
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${Math.round(seconds % 60)}秒`;
  return `${Math.floor(seconds / 3600)}小时${Math.floor((seconds % 3600) / 60)}分`;
};

// 关闭上传对话框
const closeUploadDialog = () => {
  if (uploadState.value.isUploading) {
    ElMessageBox.confirm('上传正在进行中，确定要关闭吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      cancelUpload();
      showUploadDialog.value = false;
    });
  } else {
    showUploadDialog.value = false;
  }
};

// 取消上传
const cancelUpload = () => {
  ElMessageBox.confirm(
    '确定要取消当前上传吗？已上传的文件将保留。',
    '取消上传',
    {
      confirmButtonText: '确定取消',
      cancelButtonText: '继续上传',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    uploadState.value.canCancel = false;
    uploadState.value.isUploading = false;

    // 标记所有等待中和上传中的文件为已取消
    newPhoto.value.fileList.forEach(file => {
      const state = getFileUploadState(file.uid);
      if (state.status === 'waiting' || state.status === 'uploading') {
        setFileUploadState(file.uid, { status: 'cancelled', error: '用户取消上传' });
      }
    });

    ElMessage.info('上传已取消');
    resetUploadState();
  }).catch(() => {
    // 用户选择继续上传，不做任何操作
  });
};

// 重试文件上传
const retryFileUpload = async (uid) => {
  const currentState = getFileUploadState(uid);
  const maxRetries = 3;

  if (currentState.retryCount >= maxRetries) {
    ElMessage.error(`文件重试次数已达上限 (${maxRetries}次)`);
    return;
  }

  const fileInfo = newPhoto.value.exifInfo.find(info => info.uid === uid);
  if (!fileInfo) {
    ElMessage.error('文件信息不存在');
    return;
  }

  const file = newPhoto.value.fileList.find(f => f.uid === uid);
  if (!file) {
    ElMessage.error('文件不存在');
    return;
  }

  const retryCount = currentState.retryCount + 1;
  setFileUploadState(uid, {
    status: 'uploading',
    progress: 0,
    error: null,
    retryCount: retryCount
  });

  ElMessage.info(`正在重试上传 (第${retryCount}次)...`);

  try {
    await uploadSingleFile(file, fileInfo, newPhoto.value.exifInfo.indexOf(fileInfo));
    setFileUploadState(uid, { status: 'success', progress: 100 });
    ElMessage.success('重试上传成功！');
  } catch (error) {
    setFileUploadState(uid, {
      status: 'error',
      error: error.message,
      retryCount: retryCount
    });
    ElMessage.error(`重试失败: ${error.message}`);
  }
};

const handleFileChange = async (file, fileList) => {
  newPhoto.value.fileList = fileList;

  // 为新添加的文件提取EXIF信息
  if (file.raw && !newPhoto.value.exifInfo.find(info => info.uid === file.uid)) {
    // 初始化文件上传状态
    setFileUploadState(file.uid, { status: 'ready', progress: 0 });

    try {
      const exifData = await extractEXIFInfo(file.raw);
      const realPhotoTime = getRealPhotoTime(exifData, file.raw);

      // 创建文件信息对象
      const fileInfo = {
        uid: file.uid,
        name: file.name,
        size: file.size,
        lastModified: new Date(file.raw.lastModified),
        realPhotoTime: realPhotoTime,
        exifData: exifData,
        exifDisplay: formatEXIFForDisplay(exifData),
        caption: '', // 单独的描述
        tags: [] // 单独的标签
      };

      // 添加到exifInfo数组
      newPhoto.value.exifInfo.push(fileInfo);

      console.log('文件EXIF信息提取完成:', fileInfo);
    } catch (error) {
      console.error('提取EXIF信息失败:', error);
      // 即使EXIF提取失败，也要创建基本的文件信息
      const fileInfo = {
        uid: file.uid,
        name: file.name,
        size: file.size,
        lastModified: new Date(file.raw.lastModified),
        realPhotoTime: new Date(file.raw.lastModified),
        exifData: null,
        exifDisplay: {},
        caption: '',
        tags: []
      };
      newPhoto.value.exifInfo.push(fileInfo);
    }
  }

  // 清理已删除文件的EXIF信息和上传状态
  const currentUids = fileList.map(f => f.uid);
  newPhoto.value.exifInfo = newPhoto.value.exifInfo.filter(info => currentUids.includes(info.uid));

  // 清理不存在文件的上传状态
  for (const [uid] of fileUploadStates.value) {
    if (!currentUids.includes(uid)) {
      fileUploadStates.value.delete(uid);
    }
  }
};

// 获取文件预览URL（用于表格缩略图）
const getFilePreviewUrl = (uid) => {
  const fileItem = newPhoto.value.fileList.find(f => f.uid === uid);
  if (fileItem && fileItem.url) {
    return fileItem.url;
  }
  // 如果没有URL，尝试创建blob URL
  if (fileItem && fileItem.raw) {
    return URL.createObjectURL(fileItem.raw);
  }
  return '';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 从文件列表中移除文件
const removeFileFromList = (uid) => {
  // 移除文件
  newPhoto.value.fileList = newPhoto.value.fileList.filter(f => f.uid !== uid);
  // 移除对应的EXIF信息
  newPhoto.value.exifInfo = newPhoto.value.exifInfo.filter(info => info.uid !== uid);
  // 移除上传状态
  fileUploadStates.value.delete(uid);
};

// 清空所有文件
const clearAllFiles = () => {
  newPhoto.value.fileList = [];
  newPhoto.value.exifInfo = [];
  fileUploadStates.value.clear();
  uploadState.value.hasUploaded = false;
};

// 处理文件移除
const handleFileRemove = (file, fileList) => {
  newPhoto.value.fileList = fileList;
  // 移除对应的EXIF信息
  newPhoto.value.exifInfo = newPhoto.value.exifInfo.filter(info => info.uid !== file.uid);
  // 移除上传状态
  fileUploadStates.value.delete(file.uid);
};

// 格式化文件时间
const formatFileTime = (timestamp) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 格式化照片时间
const formatPhotoTime = (date) => {
  if (!date) return '';
  if (typeof date === 'string') {
    date = new Date(date);
  }
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const uploadPhotos = async () => {
  if (newPhoto.value.fileList.length === 0) {
    ElMessage.warning('请先选择要上传的照片');
    return;
  }

  if (!currentPetId.value) {
    ElMessage.error('请先选择一个宠物');
    return;
  }

  if (uploadState.value.isUploading || uploadState.value.hasUploaded) {
    return;
  }

  // 初始化上传状态
  uploadState.value.isUploading = true;
  uploadState.value.hasUploaded = false;
  uploadState.value.totalFiles = newPhoto.value.fileList.length;
  uploadState.value.currentFileIndex = 0;
  uploadState.value.overallProgress = 0;
  uploadState.value.startTime = Date.now();
  uploadState.value.uploadedBytes = 0;
  uploadState.value.totalBytes = newPhoto.value.fileList.reduce((total, file) => total + file.size, 0);
  uploadState.value.canCancel = true;

  // 初始化所有文件状态为等待中
  newPhoto.value.fileList.forEach(file => {
    setFileUploadState(file.uid, { status: 'waiting', progress: 0 });
  });

  try {
    // 逐个上传文件以便跟踪进度
    for (let index = 0; index < newPhoto.value.fileList.length; index++) {
      if (!uploadState.value.canCancel) {
        throw new Error('上传已取消');
      }

      const fileItem = newPhoto.value.fileList[index];
      const fileInfo = newPhoto.value.exifInfo.find(info => info.uid === fileItem.uid);

      uploadState.value.currentFileIndex = index + 1;
      setFileUploadState(fileItem.uid, { status: 'uploading', progress: 0 });

      try {
        await uploadSingleFile(fileItem, fileInfo, index);
        setFileUploadState(fileItem.uid, { status: 'success', progress: 100 });

        // 更新已上传字节数
        uploadState.value.uploadedBytes += fileItem.size;

        // 计算整体进度
        uploadState.value.overallProgress = (uploadState.value.uploadedBytes / uploadState.value.totalBytes) * 100;

        // 计算上传速度和剩余时间
        const elapsedTime = (Date.now() - uploadState.value.startTime) / 1000;
        uploadState.value.uploadSpeed = uploadState.value.uploadedBytes / elapsedTime;

        const remainingBytes = uploadState.value.totalBytes - uploadState.value.uploadedBytes;
        uploadState.value.estimatedTimeRemaining = remainingBytes / uploadState.value.uploadSpeed;

      } catch (error) {
        setFileUploadState(fileItem.uid, {
          status: 'error',
          error: error.message,
          retryCount: 0
        });
        throw error;
      }
    }

    uploadState.value.hasUploaded = true;
    uploadState.value.overallProgress = 100;
    uploadState.value.estimatedTimeRemaining = 0;

    ElMessage.success(`成功上传 ${newPhoto.value.fileList.length} 张照片！`);

    // 延迟一秒后自动关闭对话框
    setTimeout(() => {
      if (uploadState.value.hasUploaded) {
        showUploadDialog.value = false;
        resetUploadState();
        newPhoto.value.fileList = [];
        newPhoto.value.exifInfo = [];
        fetchPhotos();
      }
    }, 1000);

  } catch (error) {
    console.error('上传照片失败:', error);
    ElMessage.error('上传失败: ' + error.message);
    uploadState.value.canCancel = false;
  } finally {
    uploadState.value.isUploading = false;
  }
};

// 上传单个文件
const uploadSingleFile = async (fileItem, fileInfo, index) => {
  const file = fileItem.raw;

  if (!fileInfo) {
    throw new Error(`文件 ${file.name} 的信息不完整`);
  }

  // 使用真实拍摄时间
  const realPhotoTime = fileInfo.realPhotoTime;

  // 清理文件名
  const cleanFileName = sanitizeFileName(file.name);
  // 使用真实拍摄时间作为文件名时间戳，添加随机数和索引避免重复
  const photoTimestamp = realPhotoTime.getTime();
  const randomSuffix = Math.random().toString(36).substr(2, 6);
  const fileName = `${currentPetId.value}/${photoTimestamp}_${index}_${randomSuffix}_${cleanFileName}`;

  console.log('原始文件名:', file.name);
  console.log('清理后文件名:', cleanFileName);
  console.log('最终存储路径:', fileName);

  // 验证文件路径长度和字符
  if (fileName.length > 200) {
    throw new Error(`文件路径过长 (${fileName.length}/200 字符): ${file.name}`);
  }

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    throw new Error(`不支持的文件类型: ${file.type}。仅支持图片文件。`);
  }

  // 验证文件大小 (限制为 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    throw new Error(`文件过大: ${(file.size / 1024 / 1024).toFixed(1)}MB。最大支持 10MB。`);
  }

  // 上传到 Supabase Storage
  const { data: uploadData, error: uploadError } = await supabase.storage
    .from(BUCKET_NAME)
    .upload(fileName, file, {
      contentType: file.type,
      cacheControl: '3600',
      upsert: false
    });

  if (uploadError) {
    console.error('上传错误详情:', uploadError);
    let errorMessage = '文件上传失败';

    if (uploadError.message) {
      if (uploadError.message.includes('duplicate')) {
        errorMessage = '文件名重复，请重命名后重试';
      } else if (uploadError.message.includes('size')) {
        errorMessage = '文件大小超出限制';
      } else if (uploadError.message.includes('type')) {
        errorMessage = '文件类型不支持';
      } else if (uploadError.message.includes('network')) {
        errorMessage = '网络连接失败，请检查网络后重试';
      } else {
        errorMessage = `上传失败: ${uploadError.message}`;
      }
    }

    throw new Error(errorMessage);
  }

  // 获取公开URL
  const { data: { publicUrl } } = supabase.storage
    .from(BUCKET_NAME)
    .getPublicUrl(uploadData.path);

  // 保存照片记录到数据库
  const photoData = {
    pet_id: currentPetId.value,
    url: publicUrl,
    name: fileInfo.caption || file.name, // 使用单独的描述或原始文件名
    caption: fileInfo.caption,
    size: file.size,
    type: file.type,
    storage_path: uploadData.path,
    is_favorite: false
  };

  // 如果有真实拍摄时间，添加到数据中
  if (realPhotoTime && realPhotoTime.getTime() !== fileInfo.lastModified.getTime()) {
    photoData.taken_at = realPhotoTime.toISOString();
  }

  const { data, error } = await supabase
    .from('photos')
    .insert([photoData])
    .select();

  if (error) {
    console.error('数据库保存错误:', error);
    let errorMessage = '保存照片记录失败';

    if (error.message) {
      if (error.message.includes('duplicate')) {
        errorMessage = '照片记录重复';
      } else if (error.message.includes('foreign key')) {
        errorMessage = '宠物信息不存在，请刷新页面后重试';
      } else if (error.message.includes('permission')) {
        errorMessage = '没有权限保存照片记录';
      } else {
        errorMessage = `数据库错误: ${error.message}`;
      }
    }

    throw new Error(errorMessage);
  }

  // 如果有标签，保存照片标签关联
  if (fileInfo.tags && fileInfo.tags.length > 0) {
    const photoTagData = fileInfo.tags.map(tagId => ({
      photo_id: data[0].id,
      category_id: tagId
    }));

    const { error: tagError } = await supabase
      .from('photo_category_relations')
      .insert(photoTagData);

    if (tagError) {
      console.error('保存标签关联失败:', tagError);
      // 标签保存失败不影响照片上传
    }
  }

  return data[0];
};

const saveCategory = async () => {
  if (!currentCategory.value.name.trim()) {
    ElMessage.error('分类名称不能为空');
    return;
  }

  try {
    const categoryData = {
      name: currentCategory.value.name.trim(),
      color: currentCategory.value.color,
      description: currentCategory.value.description || null,
      sort_order: currentCategory.value.id ? currentCategory.value.sort_order : photoCategories.value.length
    };

    if (currentCategory.value.id) {
      // 编辑分类
      const { error } = await supabase
        .from('photo_categories')
        .update(categoryData)
        .eq('id', currentCategory.value.id);

      if (error) throw error;
      ElMessage.success('分类更新成功');
    } else {
      // 新增分类
      const { error } = await supabase
        .from('photo_categories')
        .insert([categoryData]);

      if (error) throw error;
      ElMessage.success('分类添加成功');
    }

    showAddCategoryDialog.value = false;
    currentCategory.value = {
      id: null,
      name: '',
      color: '#409EFF',
      description: ''
    };
    fetchCategories();
  } catch (error) {
    console.error('保存分类失败:', error);
    ElMessage.error('保存分类失败: ' + error.message);
  }
};

// 监听当前宠物变化
watch(() => currentPetId.value, (newPetId) => {
  if (newPetId) {
    fetchPhotos();
  } else {
    photos.value = [];
  }
});

// 生命周期
onMounted(() => {
  console.log('PhotoAlbumView mounted');

  // 如果没有宠物ID，尝试从localStorage重新获取
  if (!currentPetId.value) {
    const storedPetId = localStorage.getItem('currentPetId');
    if (storedPetId) {
      currentPetId.value = storedPetId;
    }
  }

  fetchPhotos();
  fetchCategories();

  // 添加键盘事件监听器
  document.addEventListener('keydown', handleKeydown);

  // 验证组件设置
  nextTick(() => {
    console.log('PhotoAlbumView: Components loaded successfully');
  });

  // 监听宠物切换事件
  window.addEventListener('currentPetChanged', (event) => {
    currentPetId.value = event.detail.petId;
  });
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('mousemove', handleGlobalMouseMove);
  document.removeEventListener('mouseup', handleGlobalMouseUp);
});
</script>

<style scoped>
.photo-album-view {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.no-pet-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

/* 页面头部 */
.page-header {
  margin-bottom: 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

/* 自定义按钮样式 */
.add-photo-btn {
  position: relative;
  background: linear-gradient(135deg, #409EFF 0%, #36D1DC 50%, #5B86E5 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.add-photo-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.add-photo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.4);
}

.add-photo-btn:hover::before {
  left: 100%;
}

.add-photo-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.delete-batch-btn, .favorite-batch-btn {
  background: linear-gradient(135deg, #F56C6C 0%, #FF6B9D 50%, #C44569 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.favorite-batch-btn {
  background: linear-gradient(135deg, #E6A23C 0%, #F7BA2A 50%, #EA8010 100%);
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.delete-batch-btn:hover, .favorite-batch-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(245, 108, 108, 0.4);
}

.favorite-batch-btn:hover {
  box-shadow: 0 8px 20px rgba(230, 162, 60, 0.4);
}

.delete-batch-btn:active, .favorite-batch-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.favorite-batch-btn:active {
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

.add-icon, .delete-icon, .star-icon {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.add-photo-btn:hover .add-icon {
  transform: rotate(90deg) scale(1.1);
}

.delete-batch-btn:hover .delete-icon {
  transform: scale(1.1);
}

.favorite-batch-btn:hover .star-icon {
  transform: scale(1.1);
}

.btn-text {
  position: relative;
  z-index: 1;
}

/* 统计卡片网格 */
.stats-section {
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409EFF, #67C23A, #E6A23C);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 24px;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.total-photos .stat-icon {
  background: linear-gradient(135deg, #409EFF, #67C23A);
}

.month-photos .stat-icon {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.favorite-photos .stat-icon {
  background: linear-gradient(135deg, #E6A23C, #F7BA2A);
}

.recent-activity .stat-icon {
  background: linear-gradient(135deg, #909399, #606266);
}

.stat-info {
  flex: 1;
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 2px;
}

.stat-date {
  font-size: 12px;
  color: #C0C4CC;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-section .el-card {
  border-radius: 12px;
  overflow: hidden;
}

.filter-content {
  padding: 0;
}

.filter-left {
  width: 100%;
}

.filter-group {
  padding: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 12px;
}

.filter-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-header-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
  align-self: flex-start;
}

/* 排序和处理按钮样式 */
.sort-toggle {
  /* 移除左边距，现在垂直排列 */
}

.sort-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.sort-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

/* 自定义tooltip样式 */
:deep(.custom-tooltip) {
  background: white !important;
  border: 1px solid rgba(64, 158, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  padding: 12px 16px !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  max-width: 300px !important;
  white-space: pre-line !important;
}

:deep(.custom-tooltip .el-popper__arrow::before) {
  background: white !important;
  border: 1px solid rgba(64, 158, 255, 0.2) !important;
}

/* 上传表格样式 */
.upload-table-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 上传进度样式 */
.overall-progress-container {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-title {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.progress-stats {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.overall-progress {
  margin-bottom: 8px;
}

.upload-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
}

.upload-speed, .time-remaining {
  font-weight: 500;
}

/* 文件状态样式 */
.file-status-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.status-waiting, .status-uploading, .status-success, .status-error, .status-ready, .status-cancelled {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-waiting {
  color: #909399;
}

.status-uploading {
  color: #409EFF;
  flex-direction: column;
  gap: 6px;
}

.status-success {
  color: #67C23A;
}

.status-error {
  color: #F56C6C;
}

.status-ready {
  color: #606266;
}

.status-cancelled {
  color: #909399;
}

.status-icon {
  font-size: 16px;
}

.status-icon.success {
  color: #67C23A;
}

.status-icon.error {
  color: #F56C6C;
}

.status-icon.cancelled {
  color: #909399;
}

.status-text {
  font-size: 10px;
  margin-top: 2px;
}

.error-info-icon {
  font-size: 14px;
  color: #F56C6C;
  margin-left: 4px;
  cursor: help;
  transition: all 0.3s ease;
}

.error-info-icon:hover {
  color: #F78989;
  transform: scale(1.2);
}

/* 文件操作按钮 */
.file-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

/* 对话框底部样式 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.footer-left, .footer-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.upload-info-table {
  border-radius: 12px;
  overflow: hidden;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.upload-info-table .el-table__header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
}

.upload-info-table .el-table__header th {
  background: transparent;
  color: white;
  font-weight: 700;
  border-bottom: none;
  padding: 18px 12px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.upload-info-table .el-table__body tr {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-info-table .el-table__body tr:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.06), rgba(103, 194, 58, 0.06));
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.upload-info-table .el-table__body td {
  padding: 16px 12px;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1);
  vertical-align: middle;
}

.table-thumbnail {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 3px solid transparent;
  background-clip: padding-box;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.table-thumbnail::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border-radius: 12px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.table-thumbnail:hover::before {
  opacity: 1;
}

.table-thumbnail:hover {
  transform: scale(1.08) rotate(2deg);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
}

.table-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
}

.table-thumbnail:hover img {
  transform: scale(1.1);
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  color: #409EFF;
  font-size: 20px;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 8px;
  background: rgba(64, 158, 255, 0.1);
}

.file-name-cell:hover .file-icon {
  color: white;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  transform: rotate(10deg) scale(1.1);
}

.file-size {
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.3);
}

.time-text {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
  font-weight: 600;
  background: rgba(64, 158, 255, 0.05);
  padding: 4px 8px;
  border-radius: 6px;
}

.photo-time-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.time-source-tag {
  font-size: 10px;
  padding: 4px 10px;
  border-radius: 8px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* EXIF信息显示样式（保留用于其他地方） */
.exif-info-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #DCDFE6;
  border-radius: 8px;
  background: #FAFAFA;
}

.exif-info-item {
  padding: 12px;
  border-bottom: 1px solid #EBEEF5;
}

.exif-info-item:last-child {
  border-bottom: none;
}

.file-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
}

.file-name {
  font-size: 14px;
}

.time-info {
  margin-bottom: 8px;
}

.time-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 13px;
}

.time-row.highlight {
  background: rgba(64, 158, 255, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
}

.time-label {
  color: #606266;
  min-width: 100px;
}

.time-value {
  color: #303133;
  font-weight: 500;
}

.exif-details {
  font-size: 12px;
  color: #909399;
  background: white;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-line;
}

/* 批量操作按钮区域 */
.batch-actions-header {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.selected-info {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
}

.batch-actions-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.batch-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  padding: 6px 12px;
  font-size: 13px;
}

.batch-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.batch-btn:active {
  transform: translateY(0);
}

.batch-btn.favorite-batch-btn {
  background: linear-gradient(135deg, #E6A23C 0%, #F7BA2A 100%);
  color: white;
}

.batch-btn.favorite-batch-btn:hover {
  background: linear-gradient(135deg, #D19E11 0%, #E6A23C 100%);
}

.batch-btn.delete-batch-btn {
  background: linear-gradient(135deg, #F56C6C 0%, #FF6B9D 100%);
  color: white;
}

.batch-btn.delete-batch-btn:hover {
  background: linear-gradient(135deg, #E85A5A 0%, #F56C6C 100%);
}

.batch-btn.clear-selection-btn {
  background: linear-gradient(135deg, #909399 0%, #B3B6BC 100%);
  color: white;
}

.batch-btn.clear-selection-btn:hover {
  background: linear-gradient(135deg, #7D8088 0%, #909399 100%);
}

.filter-label {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.view-toggle {
  border-radius: 8px;
  overflow: hidden;
}

.view-btn {
  border-radius: 0;
  border: 1px solid #DCDFE6;
  background: white;
  color: #606266;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
  color: #409EFF;
  border-color: #409EFF;
}

.view-btn.is-type-primary {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  border-color: #409EFF;
}

/* 分类标签容器 */
.categories-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.draggable-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.category-tag, .add-category-tag {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.category-tag {
  min-height: 36px;
}

.clickable-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.all-tag {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  border-color: #dee2e6;
}

.all-tag.is-active {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.draggable-tag {
  position: relative;
}

.draggable-tag.is-dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.tag-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
}

.tag-text {
  transition: all 0.3s ease;
  text-align: center;
  flex: 1;
}

.tag-actions {
  display: flex;
  gap: 4px;
  margin-left: 8px;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
}

.draggable-tag:hover .tag-text {
  transform: translateX(-8px);
}

.draggable-tag:hover .tag-actions {
  opacity: 1;
  transform: translateX(0);
}

.edit-category-icon, .delete-category-icon {
  font-size: 14px;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.edit-category-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.delete-category-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ff4757;
  transform: scale(1.1);
}

.add-category-tag {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  border: 2px dashed #dee2e6;
  min-width: 40px;
  min-height: 36px;
  justify-content: center;
}

.add-category-tag:hover {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-color: transparent;
}

.add-category-tag .add-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.add-category-tag:hover .add-icon {
  transform: rotate(90deg) scale(1.1);
}

/* 收藏筛选标签样式 */
.favorite-category-tag {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.1) 0%, rgba(245, 108, 108, 0.2) 100%);
  border-color: #F56C6C;
  color: #F56C6C;
}

.favorite-category-tag:hover {
  background: linear-gradient(135deg, rgba(245, 108, 108, 0.2) 0%, rgba(245, 108, 108, 0.3) 100%);
  border-color: #F56C6C;
  color: #F56C6C;
  transform: translateY(-1px);
}

.favorite-category-tag.is-active {
  background: linear-gradient(135deg, #F56C6C 0%, #FF7F7F 100%);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
}

.favorite-category-tag .tag-icon {
  font-size: 14px;
  margin-right: 4px;
}

/* 无标签筛选标签样式 */
.untagged-category-tag {
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.1) 0%, rgba(144, 147, 153, 0.2) 100%);
  border-color: #909399;
  color: #909399;
}

.untagged-category-tag:hover {
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.2) 0%, rgba(144, 147, 153, 0.3) 100%);
  border-color: #909399;
  color: #909399;
  transform: translateY(-1px);
}

.untagged-category-tag.is-active {
  background: linear-gradient(135deg, #909399 0%, #A6A9AD 100%);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.4);
}

.untagged-category-tag .tag-icon {
  font-size: 14px;
  margin-right: 4px;
}

/* 照片展示区域 */
.photos-section {
  margin-bottom: 24px;
}



.batch-actions {
  display: flex;
  gap: 12px;
}

/* 瀑布流视图 */
.waterfall-view {
  width: 100%;
}

.photos-waterfall {
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 400px;
  width: 100%;
  overflow: visible; /* 改为visible确保悬浮元素不被裁切 */
  position: relative;
  z-index: 1;
}

/* 传统网格视图 */
.grid-view {
  width: 100%;
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 400px;
  overflow: visible; /* 确保悬浮元素不被裁切 */
  position: relative;
  z-index: 1;
}

/* 统一照片卡片样式 */
.unified-photo-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  border: 1px solid transparent;
  margin-bottom: 16px;
  break-inside: avoid;
}

.unified-photo-card:hover {
  transform: scale(1.02);
}

.unified-photo-card.selected {
  border: 2px solid #409EFF;
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  transform: translateY(-6px);
}

/* 瀑布流容器 */
.waterfall-view {
  padding: 20px;
  background: #f8f9fa;
}

/* 自定义瀑布流布局 */
.photos-masonry {
  columns: 5;
  column-gap: 16px;
  max-width: 1400px;
  margin: 0 auto;
}

@media (max-width: 1200px) {
  .photos-masonry {
    columns: 4;
  }
}

@media (max-width: 900px) {
  .photos-masonry {
    columns: 3;
  }
}

@media (max-width: 600px) {
  .photos-masonry {
    columns: 2;
  }
}

@media (max-width: 400px) {
  .photos-masonry {
    columns: 1;
  }
}

/* 瀑布流变体 */
.unified-photo-card.waterfall-variant {
  margin-bottom: 16px !important;
}

/* 时间线变体 */
.unified-photo-card.timeline-variant {
  margin-bottom: 0 !important;
}

/* 强制移除任何可能的子元素样式冲突 */
.unified-photo-card > *:not(.photo-container) {
  display: none !important;
}

/* 照片容器 */
.photo-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  /* 确保容器只包含图片和遮罩层 */
  display: flex;
  flex-direction: column;
}

/* 强制移除任何可能的额外内容 */
.photo-container > *:not(.photo-image):not(.photo-overlay):not(.PhotoConsumer) {
  display: none !important;
}

/* 照片图片 */
.photo-image {
  width: 100%;
  height: 240px; /* 网格视图固定高度 */
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: block;
  border: none !important;
  background: transparent !important;
  border-radius: 12px;
}

/* 确保网格视图的卡片高度与图片一致 */
.grid-view .unified-photo-card {
  height: 240px;
}

.grid-view .photo-container {
  height: 100%;
}

.unified-photo-card.waterfall-variant .photo-image {
  height: auto; /* 瀑布流自适应高度 */
}

.unified-photo-card.timeline-variant .photo-image {
  height: 160px; /* 时间线视图高度 */
}

.unified-photo-card:hover .photo-image {
  transform: scale(1.05);
}

/* 卡片整体悬浮效果 */
.unified-photo-card {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

/* 已在上方定义，移除重复 */

/* 移除复杂的高光层效果 */

/* 悬浮遮罩层 */
.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.6) 100%
  );
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 16px;
  border-radius: 12px;
  z-index: 5;
  overflow: visible;
  pointer-events: none; /* 让遮罩层不阻挡点击事件 */
}

.unified-photo-card:hover .photo-overlay {
  opacity: 1;
  z-index: 20; /* 悬浮时提升层级 */
}

/* 让遮罩层内的控件能够接收点击事件 */
.photo-overlay > * {
  pointer-events: auto;
}

/* 遮罩层顶部区域 */
.overlay-top {
  display: flex;
  justify-content: space-between;
  align-items: center; /* 改为center确保垂直居中对齐 */
  gap: 12px;
  min-height: 28px; /* 确保有足够的高度 */
}

/* 选择框 - 简化为纯图标，与收藏按钮尺寸匹配 */
.photo-checkbox {
  background: none;
  border: none;
  padding: 0;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
  width: 28px;
  height: 28px;
  flex-shrink: 0;
}

.photo-checkbox:hover {
  transform: scale(1.1);
}

/* Element Plus 复选框样式覆盖 - 填充式设计，与收藏按钮匹配 */
.photo-checkbox :deep(.el-checkbox) {
  margin: 0 !important;
  padding: 0 !important;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.photo-checkbox :deep(.el-checkbox__input) {
  margin: 0 !important;
  padding: 0 !important;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

.photo-checkbox :deep(.el-checkbox__label) {
  display: none !important;
}

/* 未选中状态：透明背景 + 白色边框 */
.photo-checkbox :deep(.el-checkbox__inner) {
  width: 28px !important;
  height: 28px !important;
  border: 2px solid rgba(255, 255, 255, 0.9) !important;
  border-radius: 6px !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  position: relative !important;
  margin: 0 !important;
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
}

/* 选中状态：纯色填充，无对号 */
.photo-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: #409EFF;
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

/* 隐藏对号图标 */
.photo-checkbox :deep(.el-checkbox__inner::after) {
  display: none;
}

/* 悬停效果 - 简化 */
.photo-checkbox:hover :deep(.el-checkbox__inner) {
  border-color: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
}

.photo-checkbox:hover :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: #66b1ff;
  border-color: #66b1ff;
}

/* 收藏按钮 - 简化为纯图标 */
.photo-favorite {
  background: none;
  border: none;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
}

.photo-favorite:hover {
  transform: scale(1.2);
}

.photo-favorite .el-icon {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 已收藏状态 - 简约风格 */
.photo-favorite .el-icon.is-favorite {
  color: #F7BA2A;
}

.photo-favorite:hover .el-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.6));
}

.photo-favorite:hover .el-icon.is-favorite {
  color: #FFD700;
}

/* 点击反馈效果 */
.photo-favorite:active {
  transform: scale(1.05);
}

.photo-favorite:hover .el-icon.is-favorite {
  color: #FFD700;
  filter: drop-shadow(0 3px 8px rgba(247, 186, 42, 0.8));
}

/* 遮罩层底部区域 */
.overlay-bottom {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 16px;
}

/* 照片信息 */
.photo-info {
  flex: 1;
  color: white;
  min-width: 0; /* 确保文本截断正常工作 */
}

.photo-title {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 6px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.7);
  background: linear-gradient(135deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5));
}

.photo-time {
  font-size: 12px;
  opacity: 0.95;
  margin-bottom: 8px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
  color: #E8E8E8 !important;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.photo-caption {
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 10px;
  opacity: 0.92;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
  color: #F5F5F5;
  font-weight: 400;
}

/* 分类标签 */
.photo-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
  z-index: 5;
  position: relative;
}

.photo-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  color: #333 !important;
  border: 1px solid rgba(255, 255, 255, 0.8) !important;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
  max-width: 80px;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: default;
}

/* 渐变色彩效果 */
.photo-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 50%, #E6A23C 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.photo-tag:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  color: white !important;
}

.photo-tag:hover::before {
  opacity: 1;
}

.photo-tag-more {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  color: #666 !important;
  border: 1px solid rgba(255, 255, 255, 0.6) !important;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: help;
  position: relative;
}

.photo-tag-more:hover {
  background: rgba(255, 255, 255, 1) !important;
  color: #333 !important;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 操作按钮 */
.photo-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
  align-items: flex-end;
  z-index: 10;
  position: relative;
}

.photo-actions .el-button {
  width: 32px;
  height: 32px;
  padding: 0 !important;
  border-radius: 6px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.6) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s ease;
  position: relative;
  display: flex !important;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.photo-actions .el-button:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 1) !important;
  border-color: rgba(255, 255, 255, 0.8) !important;
}

.photo-actions .el-button:active {
  transform: scale(0.95);
}

.photo-actions .el-button .el-icon {
  font-size: 14px;
  color: #666 !important;
  transition: all 0.3s ease;
}

/* 编辑按钮特殊样式 */
.photo-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(64, 158, 255, 0.6) !important;
}

.photo-actions .el-button--primary .el-icon {
  color: #409EFF !important;
}

.photo-actions .el-button--primary:hover {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #409EFF !important;
}

.photo-actions .el-button--primary:hover .el-icon {
  color: #409EFF !important;
}

/* 删除按钮特殊样式 */
.photo-actions .el-button--danger {
  background: rgba(255, 255, 255, 0.9) !important;
  border-color: rgba(245, 108, 108, 0.6) !important;
}

.photo-actions .el-button--danger .el-icon {
  color: #F56C6C !important;
}

.photo-actions .el-button--danger:hover {
  background: rgba(255, 255, 255, 1) !important;
  border-color: #F56C6C !important;
}

.photo-actions .el-button--danger:hover .el-icon {
  color: #F56C6C !important;
}

/* Element Plus 按钮样式强制覆盖 */
.photo-actions .el-button:focus,
.photo-actions .el-button:focus-visible {
  outline: none !important;
}

.photo-actions .el-button.is-disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
}

/* 确保图标居中 */
.photo-actions .el-button .el-icon {
  margin: 0 !important;
  vertical-align: middle;
}



/* 移除任何可能的额外文本或信息显示 */
:deep(.waterfall-item) > *:not(.unified-photo-card) {
  display: none !important;
}



/* 图片容器样式 */
.photo-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 确保网格视图也没有额外信息 */
.photos-grid .unified-photo-card > *:not(.photo-container) {
  display: none !important;
}

/* 确保照片容器占满整个卡片 */
.photo-container {
  width: 100%;
  height: 100%;
  position: relative;
}

/* PhotoConsumer 样式优化 */
:deep(.PhotoConsumer) {
  display: block;
  width: 100%;
  cursor: pointer;
}

:deep(.PhotoConsumer img) {
  transition: transform 0.3s ease;
}

.unified-photo-card:hover :deep(.PhotoConsumer img) {
  transform: scale(1.05);
}

/* 框选功能样式 */
.photos-container {
  position: relative;
  user-select: none;
}

.selection-box {
  position: absolute;
  border: 1px solid #409EFF;
  background-color: rgba(64, 158, 255, 0.08);
  pointer-events: none;
  z-index: 1000;
  border-radius: 2px;
  transition: none;
}

/* 防止在框选时触发其他交互 */
.photos-container.selecting .unified-photo-card {
  pointer-events: none;
}

.photos-container.selecting .unified-photo-card .photo-overlay {
  pointer-events: none;
}

/* 图片错误状态 */
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #f5f7fa, #e8eaed);
  color: #909399;
  gap: 8px;
  border-radius: 8px;
}

.image-error .el-icon {
  font-size: 32px;
  opacity: 0.6;
}

.image-error span {
  font-size: 12px;
  font-weight: 500;
}



/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.empty-content {
  text-align: center;
  max-width: 400px;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-description {
  font-size: 14px;
  color: #909399;
  line-height: 1.6;
  margin-bottom: 24px;
}

.empty-action-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: none;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.empty-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
}

/* 视图切换动画 */
.view-fade-enter-active, .view-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.view-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.view-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .photo-album-view {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .add-photo-btn, .delete-batch-btn, .favorite-batch-btn {
    padding: 10px 20px;
    font-size: 13px;
  }

  .btn-text {
    display: none;
  }

  .add-icon, .delete-icon, .star-icon {
    margin-right: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-content {
    padding: 20px;
  }

  .stat-value {
    font-size: 18px;
  }

  .filter-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .filter-header-left {
    width: 100%;
  }

  .filter-header-right {
    width: 100%;
    justify-content: space-between;
    margin-top: 8px;
  }

  .batch-actions-header {
    order: -1;
    margin-bottom: 8px;
  }

  .batch-actions-buttons {
    gap: 6px;
  }

  .batch-btn {
    padding: 5px 10px;
    font-size: 12px;
  }

  .view-toggle .view-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .categories-container {
    gap: 8px;
  }

  .category-tag, .add-category-tag {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
  }

  .photos-waterfall {
    padding: 16px;
  }

  .photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
    padding: 16px;
  }

  .grid-view .photo-image {
    height: 180px;
  }

  .grid-view .unified-photo-card {
    height: 180px;
  }

  .grid-view .image-error {
    height: 180px;
  }

  .image-error {
    height: 160px;
  }


}

@media (max-width: 480px) {
  .photo-album-view {
    padding: 12px;
  }

  .page-title {
    font-size: 20px;
  }

  .stat-content {
    padding: 16px;
    gap: 12px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .stat-value {
    font-size: 16px;
  }

  .add-photo-btn, .delete-batch-btn, .favorite-batch-btn {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 10px;
  }

  .header-actions {
    gap: 12px;
  }

  .photos-waterfall {
    padding: 12px;
  }

  .photos-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
  }

  .grid-view .photo-image {
    height: 160px;
  }

  .grid-view .unified-photo-card {
    height: 160px;
  }

  .grid-view .image-error {
    height: 160px;
  }

  .image-error {
    height: 140px;
  }



  .category-tag, .add-category-tag {
    padding: 4px 8px;
    font-size: 11px;
    min-height: 28px;
  }

  .filter-group {
    padding: 16px;
  }
}

/* 拖拽样式 */
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
  transform: rotate(5deg);
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 120px;
  z-index: 9999;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 14px;
  color: #606266;
}

.context-menu-item:hover {
  background-color: #F5F7FA;
}

.context-menu-item.danger {
  color: #F56C6C;
}

.context-menu-item.danger:hover {
  background-color: #FEF0F0;
}

/* vue3-photo-preview 样式自定义 */
:deep(.PhotoView-Portal) {
  z-index: 9999 !important;
}

:deep(.PhotoView-Backdrop) {
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(10px);
}

:deep(.PhotoView-PhotoWrap) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.PhotoView-Toolbar) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9), rgba(103, 194, 58, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin: 20px;
  padding: 12px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

:deep(.PhotoView-Toolbar .PhotoView-ToolbarIcon) {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.PhotoView-Toolbar .PhotoView-ToolbarIcon:hover) {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

:deep(.PhotoView-Slider) {
  border-radius: 12px;
}

:deep(.PhotoView-SliderArrow) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9), rgba(103, 194, 58, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

:deep(.PhotoView-SliderArrow:hover) {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

:deep(.PhotoView-SliderArrow svg) {
  color: white;
  width: 24px;
  height: 24px;
}

/* 加载动画 */
:deep(.PhotoView-Loading) {
  color: #409EFF;
}

/* 图片信息显示 */
:deep(.PhotoView-PhotoIntro) {
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.9), rgba(103, 194, 58, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin: 20px;
  padding: 16px 20px;
  color: white;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  white-space: pre-line;
}

/* Element Plus 时间线视图样式 */
.timeline-view {
  padding: 20px;
  background: #f8f9fa;
}

.photo-timeline {
  max-width: 1200px;
  margin: 0 auto;
}

/* Element Plus Timeline 组件样式覆盖 */
:deep(.el-timeline-item__timestamp) {
  display: none; /* 隐藏默认时间戳，使用自定义头部 */
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 60px; /* 增加左边距为自定义节点留出空间 */
  position: relative;
}

:deep(.el-timeline-item__tail) {
  border-left: 3px solid #E4E7ED;
  left: 20px; /* 调整连接线位置 */
}

:deep(.el-timeline-item__node) {
  background-color: transparent;
  border: none;
  left: 0; /* 重置节点位置 */
  transform: none;
}

:deep(.el-timeline-item__content) {
  margin: 0; /* 移除默认边距 */
}

/* 时间线项目样式 */
.timeline-item {
  margin-bottom: 60px; /* 增加项目间距 */
}

:deep(.el-timeline-item:last-child .el-timeline-item__tail) {
  display: none; /* 隐藏最后一项的连接线 */
}

/* 自定义时间线节点 */
.timeline-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  border: 3px solid white;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
}

.timeline-icon {
  color: white;
  font-size: 18px;
}

/* 时间线头部 */
.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  margin-left: -60px; /* 向左偏移以对齐时间线节点 */
  padding: 16px 20px 16px 80px; /* 左边距为节点留出空间 */
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
}

.timeline-date-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.timeline-stats {
  display: flex;
  gap: 8px;
}

/* 照片网格 */
.timeline-photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  padding: 0 20px;
  margin-left: -60px; /* 向左偏移以对齐时间线节点 */
  padding-left: 80px; /* 为节点留出空间 */
  overflow: visible; /* 确保悬浮元素不被裁切 */
  position: relative;
  z-index: 1;
}

/* 时间线模式下的照片卡片特殊处理 */
.timeline-photos-grid .unified-photo-card {
  position: relative;
  z-index: 2;
}

.timeline-photos-grid .unified-photo-card .photo-overlay {
  z-index: 15; /* 确保在时间线元素之上 */
}

/* 重构后的上传对话框样式 */
.upload-dialog-redesigned {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.upload-container {
  display: flex;
  gap: 24px;
  min-height: 500px;
}

.upload-left-panel {
  flex: 1;
  min-width: 400px;
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
}

.upload-right-panel {
  flex: 1.5;
  min-width: 500px;
  padding: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.upload-section {
  margin-bottom: 24px;
}

.upload-dragger-redesigned {
  :deep(.el-upload-dragger) {
    width: 100%;
    height: 200px;
    border: 2px dashed #409EFF;
    border-radius: 12px;
    background: white;
    transition: all 0.3s ease;
  }

  :deep(.el-upload-dragger:hover) {
    border-color: #67C23A;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.05), rgba(103, 194, 58, 0.05));
  }
}

.upload-content-redesigned {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.upload-icon-large {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 16px;
}

.upload-text-redesigned {
  text-align: center;
}

.upload-title-large {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.upload-hint-detailed {
  font-size: 14px;
  color: #909399;
  margin: 4px 0;
}

.batch-settings-section {
  margin-bottom: 24px;
}

.settings-form {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-row {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
}

.upload-progress-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-overview {
  margin-top: 16px;
}

.main-progress {
  margin-bottom: 12px;
}

.progress-stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #606266;
}

.file-list-section {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 0;
}

.section-header .section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header .header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.file-table-container {
  flex: 1;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.file-table-redesigned {
  :deep(.el-table__header) {
    background: linear-gradient(135deg, #409EFF, #67C23A);
  }

  :deep(.el-table__header th) {
    background: transparent;
    color: white;
    font-weight: 600;
  }
}

.file-preview-redesigned {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image-redesigned {
  width: 100%;
  height: 100%;
}

.preview-error-redesigned {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.file-name-redesigned {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name-text {
  font-weight: 500;
  color: #303133;
}

.size-text {
  font-size: 12px;
  color: #909399;
}

.time-info-redesigned {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
}

.time-row {
  display: flex;
  gap: 4px;
}

.time-label {
  color: #909399;
  min-width: 30px;
}

.time-value {
  color: #303133;
}

.time-placeholder {
  color: #C0C4CC;
  font-style: italic;
}

.status-redesigned {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.file-progress {
  width: 100%;
  margin-top: 4px;
}

.file-actions-redesigned {
  display: flex;
  gap: 4px;
}

.empty-file-list {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dialog-footer-redesigned {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.footer-right {
  display: flex;
  gap: 12px;
}

.upload-status-text {
  font-size: 14px;
  color: #606266;
}

/* 编辑照片对话框样式 */
.edit-photo-dialog-redesigned {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.edit-photo-container {
  display: flex;
  gap: 24px;
  min-height: 400px;
}

.photo-preview-section {
  flex: 1;
  min-width: 250px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-wrapper {
  width: 100%;
  max-width: 300px;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 100%;
  height: 100%;
}

.preview-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  color: #909399;
  gap: 8px;
}

.edit-form-section {
  flex: 1.5;
  min-width: 350px;
  padding: 24px;
}

.edit-form {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-hint {
  margin-top: 8px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .upload-container {
    flex-direction: column;
    gap: 16px;
  }

  .upload-left-panel,
  .upload-right-panel {
    min-width: auto;
    padding: 16px;
  }

  .edit-photo-container {
    flex-direction: column;
    gap: 16px;
  }

  .photo-preview-section,
  .edit-form-section {
    min-width: auto;
    padding: 16px;
  }

  .preview-wrapper {
    max-width: 200px;
  }

  .unified-photo-card {
    margin-bottom: 12px;
  }

  .photo-image {
    height: 180px;
  }

  .unified-photo-card.timeline-variant .photo-image {
    height: 140px;
  }

  .photo-overlay {
    padding: 14px;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.75) 0%,
      rgba(0, 0, 0, 0.35) 30%,
      rgba(0, 0, 0, 0.15) 50%,
      rgba(0, 0, 0, 0.35) 70%,
      rgba(0, 0, 0, 0.85) 100%
    );
  }

  .photo-favorite {
    width: 38px;
    height: 38px;
  }

  .photo-checkbox {
    width: 26px;
    height: 26px;
  }

  .photo-checkbox :deep(.el-checkbox) {
    width: 26px;
    height: 26px;
  }

  .photo-checkbox :deep(.el-checkbox__input) {
    width: 26px;
    height: 26px;
  }

  .photo-checkbox :deep(.el-checkbox__inner) {
    width: 26px;
    height: 26px;
  }

  .photo-favorite {
    width: 26px;
    height: 26px;
  }

  .photo-favorite .el-icon {
    font-size: 18px;
  }

  .photo-actions {
    gap: 6px;
  }

  .photo-actions .el-button {
    width: 34px;
    height: 34px;
    border-radius: 8px;
  }

  .photo-title {
    font-size: 15px;
    margin-bottom: 5px;
  }

  .photo-caption {
    font-size: 12px;
    line-height: 1.4;
  }

  .photo-time {
    font-size: 11px;
    margin-bottom: 6px;
  }

  .photo-tags {
    gap: 4px;
    margin-top: 6px;
  }

  .photo-tag {
    font-size: 10px;
    padding: 3px 8px !important;
    border-radius: 10px;
    max-width: 70px;
  }

  .photo-tag-more {
    font-size: 10px;
    padding: 3px 8px !important;
    border-radius: 10px;
  }
}

/* 小屏幕适配 */
@media (max-width: 480px) {
  .photos-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
  }

  .photo-image {
    height: 160px;
  }

  .grid-view .unified-photo-card {
    height: 160px;
  }

  .unified-photo-card.timeline-variant .photo-image {
    height: 120px;
  }

  .photo-overlay {
    padding: 12px;
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0.4) 30%,
      rgba(0, 0, 0, 0.2) 50%,
      rgba(0, 0, 0, 0.4) 70%,
      rgba(0, 0, 0, 0.9) 100%
    );
  }

  .overlay-bottom {
    gap: 10px;
  }

  .photo-favorite {
    width: 34px;
    height: 34px;
  }

  .photo-checkbox {
    width: 24px;
    height: 24px;
  }

  .photo-checkbox :deep(.el-checkbox) {
    width: 24px;
    height: 24px;
  }

  .photo-checkbox :deep(.el-checkbox__input) {
    width: 24px;
    height: 24px;
  }

  .photo-checkbox :deep(.el-checkbox__inner) {
    width: 24px;
    height: 24px;
  }

  .photo-favorite {
    width: 24px;
    height: 24px;
  }

  .photo-favorite .el-icon {
    font-size: 16px;
  }

  .photo-actions {
    gap: 4px;
  }

  .photo-actions .el-button {
    width: 30px;
    height: 30px;
    border-radius: 8px;
  }

  .photo-title {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .photo-caption {
    font-size: 11px;
    line-height: 1.3;
    margin-bottom: 6px;
  }

  .photo-time {
    font-size: 10px;
    margin-bottom: 5px;
  }

  .photo-tags {
    gap: 3px;
    margin-top: 5px;
  }

  .photo-tag {
    font-size: 9px;
    padding: 2px 6px !important;
    border-radius: 8px;
    max-width: 60px;
  }

  .photo-tag-more {
    font-size: 9px;
    padding: 2px 6px !important;
    border-radius: 8px;
  }

  .timeline-photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
    padding-left: 60px;
  }
}

/* 响应式设计 - 时间线视图 */
@media (max-width: 768px) {
  .timeline-view {
    padding: 15px;
  }

  :deep(.el-timeline-item__wrapper) {
    padding-left: 50px; /* 移动端减少左边距 */
  }

  :deep(.el-timeline-item__tail) {
    left: 15px; /* 调整连接线位置 */
  }

  .timeline-dot {
    width: 30px;
    height: 30px;
  }

  .timeline-icon {
    font-size: 14px;
  }

  .timeline-photos-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 15px;
    margin-left: -50px; /* 移动端调整偏移 */
    padding-left: 65px; /* 为较小的节点留出空间 */
    padding-right: 10px;
  }

  .photo-image {
    height: 140px;
  }

  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-left: -50px; /* 移动端调整偏移 */
    padding: 12px 16px 12px 65px; /* 为较小的节点留出空间 */
  }

  .timeline-date-title {
    font-size: 18px;
  }

  .timeline-stats {
    align-self: stretch;
    justify-content: flex-end;
  }

  .timeline-item {
    margin-bottom: 30px;
  }

  .photo-info {
    padding: 12px;
  }

  .photo-favorite {
    width: 32px;
    height: 32px;
  }

  .photo-actions .el-button {
    width: 28px;
    height: 28px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .unified-photo-card .photo-overlay {
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.85) 0%,
      rgba(0, 0, 0, 0.4) 30%,
      rgba(0, 0, 0, 0.2) 50%,
      rgba(0, 0, 0, 0.4) 70%,
      rgba(0, 0, 0, 0.9) 100%
    );
  }

  .unified-photo-card.touch-active .photo-overlay {
    opacity: 1;
    backdrop-filter: blur(6px);
  }

  .photo-checkbox,
  .photo-favorite,
  .photo-actions .el-button {
    min-height: 44px;
    min-width: 44px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .photo-checkbox {
    width: 36px;
    height: 36px;
  }

  .photo-checkbox :deep(.el-checkbox) {
    width: 36px;
    height: 36px;
  }

  .photo-checkbox :deep(.el-checkbox__input) {
    width: 36px;
    height: 36px;
  }

  .photo-checkbox :deep(.el-checkbox__inner) {
    width: 36px;
    height: 36px;
  }

  .photo-favorite {
    width: 36px;
    height: 36px;
  }

  .photo-favorite .el-icon {
    font-size: 24px;
  }

  .photo-favorite {
    width: 44px;
    height: 44px;
  }

  .photo-actions .el-button {
    width: 44px;
    height: 44px;
    border-radius: 12px;
  }

  /* 触摸反馈 */
  .photo-checkbox:active,
  .photo-favorite:active,
  .photo-actions .el-button:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* 增强文字可读性 */
  .photo-title {
    font-size: 16px;
    font-weight: 700;
  }

  .photo-caption {
    font-size: 13px;
    line-height: 1.5;
  }

  .photo-time {
    font-size: 12px;
  }
}

/* 分类选择器样式 */
.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

/* 多选下拉框标签样式 */
:deep(.el-select__tags) {
  max-width: calc(100% - 30px);
}

:deep(.el-tag) {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.el-tag.el-tag--info) {
  background-color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-8);
  color: var(--el-color-info);
}



/* 标签相关样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
  margin-top: 8px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 16px;
  border: 2px solid;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;
  gap: 4px;
  min-height: 28px;
}

.tag-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.tag-item.is-active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  font-weight: 600;
  border-width: 3px;
}

.favorite-tag {
  background: linear-gradient(135deg, #E6A23C 0%, #F7BA2A 100%);
  border-color: #E6A23C;
  color: white;
}

.favorite-tag:not(.is-active) {
  background: transparent;
  color: #E6A23C;
}

.tag-icon {
  font-size: 14px;
}

.tag-text {
  font-weight: 500;
}

.tag-count {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 600;
}

.add-tag-item {
  background: transparent;
  border-color: #DCDFE6;
  color: #909399;
  border-style: dashed;
}

.add-tag-item:hover {
  border-color: #409EFF;
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}





/* 标签选择器 */
.tag-selector {
  width: 100%;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
  min-height: 24px;
}

.selected-tags .el-tag {
  border-radius: 12px;
  font-weight: 500;
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  /* 上传对话框移动端适配 */
  .upload-table-container {
    overflow-x: auto;
  }

  .upload-info-table {
    min-width: 600px;
  }

  .upload-info-table .el-table__header th,
  .upload-info-table .el-table__body td {
    padding: 8px 4px;
    font-size: 12px;
  }

  .table-thumbnail {
    width: 40px;
    height: 40px;
  }

  .file-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .file-name-cell span {
    font-size: 11px;
    line-height: 1.2;
  }

  .time-text {
    font-size: 10px;
  }

  .time-source-tag {
    font-size: 8px;
    padding: 1px 4px;
  }

  .photo-time-cell {
    gap: 2px;
  }

  /* 对话框宽度调整 */
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto;
  }

  .el-dialog__body {
    padding: 15px;
  }

  /* 表格列宽调整 */
  .upload-info-table .el-table__cell:nth-child(1) { /* 预览列 */
    width: 60px;
  }

  .upload-info-table .el-table__cell:nth-child(2) { /* 文件名列 */
    min-width: 120px;
  }

  .upload-info-table .el-table__cell:nth-child(3) { /* 大小列 */
    width: 60px;
  }

  .upload-info-table .el-table__cell:nth-child(4) { /* 文件修改时间列 */
    width: 100px;
  }

  .upload-info-table .el-table__cell:nth-child(5) { /* 识别拍摄时间列 */
    width: 100px;
  }

  .upload-info-table .el-table__cell:nth-child(6) { /* 操作列 */
    width: 60px;
  }
}

/* 简化上传对话框样式 */
.upload-dialog-simplified {
  border-radius: 16px;
  overflow: hidden;

  /* 响应式调整 */
  @media (max-width: 768px) {
    width: 95% !important;
    margin: 0 auto;

    .el-dialog__body {
      padding: 15px;
    }
  }
}

.upload-container-simplified {
  padding: 0;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
}

.file-list-header .section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.file-list-header .header-actions {
  display: flex;
  gap: 12px;
}

.upload-progress-section {
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.main-progress {
  margin-bottom: 12px;
}

.progress-stats {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.file-table-simplified {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  /* 响应式调整 */
  @media (max-width: 768px) {
    .el-table__body td {
      padding: 8px 4px;
      font-size: 12px;
    }

    .description-input {
      .el-textarea__inner {
        font-size: 12px;
      }
    }

    .tags-select {
      .el-input__inner {
        font-size: 12px;
      }
    }
  }
}

.file-table-simplified .file-preview {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
}

.file-table-simplified .preview-image {
  width: 100%;
  height: 100%;
}

.file-table-simplified .preview-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.file-table-simplified .file-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-table-simplified .name-text {
  font-weight: 500;
  color: #303133;
}

.file-table-simplified .size-text {
  font-size: 12px;
  color: #909399;
}

.file-table-simplified .description-input {
  width: 100%;
}

.file-table-simplified .tags-select {
  width: 100%;
}

.file-table-simplified .time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-table-simplified .time-row {
  display: flex;
  gap: 4px;
  font-size: 12px;
}

.file-table-simplified .time-label {
  color: #909399;
  font-weight: 500;
}

.file-table-simplified .time-value {
  color: #303133;
}

.file-table-simplified .time-placeholder {
  color: #C0C4CC;
  font-style: italic;
}

.file-table-simplified .status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.file-table-simplified .file-progress {
  width: 100%;
}

.file-table-simplified .file-actions {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.empty-file-list {
  padding: 40px;
  text-align: center;
}

/* 编辑照片对话框样式 */
.edit-photo-dialog-simplified {
  border-radius: 16px;
  overflow: hidden;
}

.edit-photo-container-simplified {
  padding: 0;
}

.photo-header {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin-bottom: 20px;
}

.photo-thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}

.thumbnail-error {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
}

.photo-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.photo-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.photo-meta {
  margin: 0;
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.edit-form-simplified {
  padding: 0 20px 20px 20px;
}

.time-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;

  .time-picker-input {
    flex: 1;

    .el-input__inner {
      font-weight: 500;
      color: #303133;
    }
  }

  .reset-time-btn {
    flex-shrink: 0;
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
    background: linear-gradient(135deg, #409EFF, #67C23A);
    color: white;
    border: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }
}

.time-display-input {
  cursor: pointer;
}

.edit-time-btn,
.reset-time-btn {
  padding: 0;
  margin: 0 4px;
  border: none;
  background: none;
  color: #409EFF;
}

.edit-time-btn:hover,
.reset-time-btn:hover {
  color: #66b1ff;
}

.time-hint {
  margin-top: 8px;
  line-height: 1.4;
}

.time-hint .el-text {
  display: block;
  margin-bottom: 4px;
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .upload-info-table {
    min-width: 500px;
  }

  .upload-info-table .el-table__header th,
  .upload-info-table .el-table__body td {
    padding: 6px 2px;
    font-size: 11px;
  }

  .table-thumbnail {
    width: 35px;
    height: 35px;
  }

  .file-name-cell span {
    font-size: 10px;
  }

  .time-text {
    font-size: 9px;
  }

  .file-size {
    font-size: 10px;
  }

  .el-button.is-circle {
    width: 28px;
    height: 28px;
  }

  .el-button.is-circle .el-icon {
    font-size: 12px;
  }

  .time-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }
}

/* ===== 增强版上传对话框样式 ===== */
.upload-dialog-enhanced {
  .el-dialog__header {
    background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 20px 24px;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
  }
}

.upload-container-enhanced {
  padding: 0;
}

.upload-header-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 24px;
  border-bottom: 1px solid #e4e7ed;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.section-info {
  flex: 1;
}

.section-title {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;

  .title-icon {
    color: #409EFF;
    font-size: 22px;
  }

  .file-count {
    font-size: 14px;
    color: #67C23A;
    font-weight: 500;
  }
}

.section-subtitle {
  margin: 0;
  font-size: 14px;
  color: #909399;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.action-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &.primary-btn {
    background: linear-gradient(135deg, #409EFF, #67C23A);
    border: none;
  }

  &.danger-btn {
    background: linear-gradient(135deg, #F56C6C, #E6A23C);
    border: none;
  }
}

/* 上传进度区域增强 */
.upload-progress-section-enhanced {
  padding: 20px 24px;
  background: linear-gradient(135deg, #f0f9ff 0%, #f0fff4 100%);
  border-bottom: 1px solid #e4e7ed;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;

  .progress-icon {
    color: #409EFF;
  }
}

.progress-percentage {
  font-size: 24px;
  font-weight: 700;
  color: #67C23A;
}

.main-progress-enhanced {
  margin-bottom: 16px;

  .el-progress-bar__outer {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
  }

  .el-progress-bar__inner {
    background: linear-gradient(90deg, #409EFF 0%, #67C23A 100%);
    border-radius: 10px;
  }
}

.progress-stats-enhanced {
  display: flex;
  justify-content: space-around;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  flex: 1;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.success {
    border-left: 4px solid #67C23A;
  }

  &.error {
    border-left: 4px solid #F56C6C;
  }

  &.pending {
    border-left: 4px solid #E6A23C;
  }

  .el-icon {
    font-size: 18px;
  }

  .stat-label {
    font-size: 12px;
    color: #909399;
    font-weight: 500;
  }

  .stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

/* 文件表格增强 */
.file-table-container-enhanced {
  padding: 0 24px 24px 24px;
}

.file-table-enhanced {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .el-table__header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .el-table__row {
    &:hover {
      background: linear-gradient(135deg, #f0f9ff 0%, #f0fff4 100%);
    }
  }
}

.file-preview-enhanced {
  position: relative;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

    .preview-overlay-simple {
      opacity: 1;
    }
  }
}

.preview-image-enhanced {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.preview-error-enhanced {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  color: #909399;
  font-size: 12px;
  gap: 4px;
}

/* 简化的预览覆盖层样式 */
.preview-overlay-simple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 8px;
}

.preview-icon-simple {
  font-size: 18px;
  color: white;
  text-align: center;
  line-height: 1;
}

.file-info-enhanced {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-name-enhanced {
  display: flex;
  align-items: center;
  gap: 8px;

  .name-text {
    font-weight: 500;
    color: #303133;
    flex: 1;
  }

  .exif-tag {
    font-size: 10px;
    padding: 2px 6px;
  }
}

.file-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;

  .size-text {
    color: #67C23A;
    font-weight: 500;
  }

  .type-text {
    color: #409EFF;
    font-weight: 500;
  }
}

.description-input-enhanced {
  .el-textarea__inner {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:focus {
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

.tags-select-enhanced {
  .el-input__wrapper {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

.category-option-enhanced {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;

  .category-color-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .category-name {
    flex: 1;
    font-weight: 500;
  }
}

.time-info-enhanced {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-row {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;

  &.primary {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  }

  &.secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }

  .time-icon {
    color: #409EFF;
    font-size: 14px;
  }

  .time-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .time-label {
    font-size: 10px;
    color: #909399;
    font-weight: 500;
  }

  .time-value {
    font-size: 12px;
    color: #303133;
    font-weight: 500;
  }

  .time-placeholder {
    font-size: 12px;
    color: #C0C4CC;
    font-style: italic;
  }
}

.status-enhanced {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.status-tag {
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;

  &.uploading {
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.file-progress-enhanced {
  width: 100%;
  margin-top: 4px;

  .el-progress-bar__outer {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
  }

  .el-progress-bar__inner {
    background: linear-gradient(90deg, #E6A23C 0%, #67C23A 100%);
    border-radius: 6px;
  }
}

.file-actions-enhanced {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &.retry-btn:hover {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
  }

  &.delete-btn:hover {
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
  }
}

/* 空状态增强 */
.empty-file-list-enhanced {
  padding: 60px 24px;
  text-align: center;
}

.empty-image {
  width: 100px;
  height: 100px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .empty-icon {
    font-size: 40px;
    color: #409EFF;
  }
}

.empty-description {
  margin-bottom: 24px;

  h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #303133;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: #909399;
  }
}

.empty-action-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
  }
}

/* 对话框底部增强 */
.dialog-footer-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  padding: 0;
}

/* 底部按钮居中样式 */
.footer-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  width: 100%;
}

.footer-left {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.upload-status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .status-text {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
  }

  .status-details {
    display: flex;
    gap: 16px;

    .detail-item {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #909399;

      .el-icon {
        font-size: 14px;
      }
    }
  }
}

.footer-right {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.cancel-btn,
.close-btn,
.upload-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 120px;

  &:hover {
    transform: translateY(-2px);
  }
}

.upload-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: none;

  &:hover {
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
  }

  .rotating {
    animation: rotate 1s linear infinite;
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== 增强版编辑对话框样式 ===== */
.edit-photo-dialog-enhanced {
  .el-dialog__header {
    background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 12px 12px 0 0;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 20px 24px;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
  }
}

.edit-photo-container-enhanced {
  padding: 0;
}

/* 照片预览区域增强 - 参考相册预览卡片样式 */
.photo-header-enhanced {
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 0;
  border-bottom: 1px solid #e4e7ed;
  min-height: 200px;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

.photo-container-edit {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  cursor: pointer;
}

.photo-image-edit {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.photo-container-edit:hover .photo-image-edit {
  transform: scale(1.05);
}

.photo-overlay-edit {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.6) 100%
  );
  opacity: 0;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 16px;
  border-radius: 12px 12px 0 0;
  pointer-events: none;
}

.photo-container-edit:hover .photo-overlay-edit {
  opacity: 1;
}

.photo-details-edit {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  justify-content: center;
}

.detail-item-edit {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #E8E8E8;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
  letter-spacing: 0.3px;

  .el-icon {
    font-size: 14px;
    opacity: 0.9;
  }
}

.photo-header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 24px;
}

.photo-preview-click-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    .preview-overlay-simple {
      opacity: 1;
    }
  }
}

/* 这些样式已经不再需要，因为我们现在使用背景图片方式 */

/* 编辑对话框中的简化预览覆盖层样式 */
.preview-overlay-simple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none; /* 让点击事件穿透到PhotoConsumer */
  border-radius: 12px;
}

.preview-content-simple {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.preview-icon-simple {
  font-size: 24px;
  color: white;
  text-align: center;
  line-height: 1;
}

.preview-text-simple {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 1;
  white-space: nowrap;
}

.photo-info-section {
  width: 320px;
  max-width: 40%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 12px;
  position: relative;
  z-index: 2;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.photo-title-enhanced {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(64, 158, 255, 0.2);

  .title-icon {
    color: #409EFF;
    font-size: 20px;
  }
}

.photo-meta-enhanced {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 0;
  border-bottom: 1px solid rgba(64, 158, 255, 0.1);
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: rgba(64, 158, 255, 0.05);
    border-radius: 6px;
    padding: 10px 8px;
    margin: 0 -8px;
  }

  .meta-label {
    font-size: 12px;
    color: #909399;
    font-weight: 500;
    min-width: 70px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .meta-value {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
    flex: 1;
  }
}

/* 表单增强 */
.edit-form-enhanced {
  padding: 24px;
}

.form-item-enhanced {
  margin-bottom: 24px;

  .el-form-item__label {
    font-weight: 600;
    color: #303133;
    font-size: 14px;
  }

  .el-form-item__content {
    margin-top: 8px;
  }
}

.input-enhanced {
  .el-input__wrapper {
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }

  .el-input__inner {
    font-size: 16px;
    font-weight: 500;
  }
}

.textarea-enhanced {
  .el-textarea__inner {
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    font-size: 14px;
    line-height: 1.6;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

/* 时间选择器增强 */
.time-form-item {
  .el-form-item__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

.time-input-wrapper-enhanced {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-picker-enhanced {
  .el-input__wrapper {
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

.reset-time-btn-enhanced {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #E6A23C, #F56C6C);
  border: none;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
  }
}

.time-hint-enhanced {
  .el-alert {
    border-radius: 8px;
  }
}

.time-info-enhanced {
  padding: 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 8px;
  border-left: 4px solid #409EFF;

  .el-text {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
  }
}

/* 分类选择器增强 */
.category-form-item {
  .el-form-item__content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.category-select-enhanced {
  .el-input__wrapper {
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}

.category-option-enhanced {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;

  .category-color-dot-enhanced {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .category-name-enhanced {
    flex: 1;
    font-weight: 500;
    color: #303133;
  }

  .category-desc {
    font-size: 12px;
    color: #909399;
    font-style: italic;
  }
}

.category-hint {
  padding: 8px 12px;
  background: linear-gradient(135deg, #f0fff4 0%, #f0f9ff 100%);
  border-radius: 6px;
  border-left: 3px solid #67C23A;

  .el-text {
    font-size: 12px;
  }
}

/* 编辑对话框底部增强 */
.edit-footer {
  .footer-left {
    flex: 0;
  }

  .footer-right {
    flex: 1;
    justify-content: flex-end;
  }
}

/* 编辑对话框底部居中样式 */
.edit-footer-centered {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 0;
}

.delete-photo-btn {
  background: linear-gradient(135deg, #F56C6C, #E6A23C);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(245, 108, 108, 0.3);
  }
}

.cancel-btn-enhanced,
.save-btn-enhanced {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 120px;

  &:hover {
    transform: translateY(-2px);
  }
}

.save-btn-enhanced {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  border: none;

  &:hover {
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-dialog-enhanced,
  .edit-photo-dialog-enhanced {
    width: 95% !important;
    margin: 0 auto;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .photo-header-enhanced {
    min-height: 150px;
  }

  .photo-header-overlay {
    justify-content: center;
    align-items: center;
    padding: 16px;
  }

  .photo-info-section {
    width: 100%;
    max-width: 100%;
    align-items: center;
  }

  .dialog-footer-enhanced {
    flex-direction: column;
    gap: 16px;
  }

  .edit-footer-centered {
    flex-direction: column;
    gap: 16px;
  }

  .footer-right {
    width: 100%;
    justify-content: center;
  }

  .time-input-wrapper-enhanced {
    flex-direction: column;
    align-items: stretch;
  }

  .file-table-enhanced {
    font-size: 12px;
  }

  .action-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .upload-header-section,
  .edit-form-enhanced {
    padding: 16px;
  }

  .section-title {
    font-size: 18px;
  }

  .photo-title-enhanced {
    font-size: 18px;
  }

  .file-table-container-enhanced {
    padding: 0 16px 16px 16px;
  }

  .upload-progress-section-enhanced {
    padding: 16px;
  }

  .progress-stats-enhanced {
    flex-direction: column;
    gap: 8px;
  }

  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    padding: 8px 12px;
  }
}

/* 编辑对话框标题样式 */
.edit-dialog-header {
  text-align: center;
  padding: 8px 0;
}

.edit-dialog-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin: 0;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 时间信息紧凑样式 */
.time-info-compact {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px;
  font-size: 11px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.time-item .time-icon {
  font-size: 12px;
  flex-shrink: 0;
  color: #909399;
}

.time-item .time-text {
  font-size: 11px;
  color: #409EFF;
  font-weight: 500;
  line-height: 1.2;
}

.time-item .time-placeholder {
  font-size: 11px;
  font-style: italic;
  color: #909399;
  line-height: 1.2;
}

/* 表单标签提示样式 */
.form-label-with-tooltip {
  display: flex;
  align-items: center;
  gap: 6px;
}

.help-icon {
  font-size: 14px;
  color: #909399;
  cursor: help;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.help-icon:hover {
  color: #409EFF;
  opacity: 1;
  transform: scale(1.1);
}

/* 确保vue3-photo-preview在对话框之上 */
:deep(.PhotoSlider) {
  z-index: 3000 !important; /* 高于Element Plus对话框的z-index */
}

:deep(.photo-slider) {
  z-index: 3000 !important;
}

/* 确保所有可能的vue3-photo-preview相关类都有足够高的z-index */
:deep(.PhotoView) {
  z-index: 3000 !important;
}

:deep(.PhotoView-Slider) {
  z-index: 3000 !important;
}

:deep(.PhotoView-PhotoWrap) {
  z-index: 3000 !important;
}

/* 全局确保vue3-photo-preview组件在最顶层 */
:global(.PhotoView-Portal) {
  z-index: 3000 !important;
}

:global(.PhotoView) {
  z-index: 3000 !important;
}

:global(.PhotoView-Slider) {
  z-index: 3000 !important;
}


</style>
