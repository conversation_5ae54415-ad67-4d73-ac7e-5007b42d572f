<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一体化视图切换测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 40px;
            margin: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #303133;
            margin-bottom: 40px;
        }

        .test-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }

        .test-section h2 {
            color: #303133;
            margin-bottom: 16px;
            border-bottom: 2px solid #E4E7ED;
            padding-bottom: 8px;
        }

        .demo-area {
            background: #F5F7FA;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            display: flex;
            justify-content: center;
        }

        /* 一体化视图切换样式 */
        .simple-view-toggle {
            position: relative;
            display: flex;
            background: #f5f7fa;
            border-radius: 8px;
            padding: 3px;
            overflow: hidden;
        }

        .simple-view-toggle.small {
            padding: 2px;
        }

        /* 动态填充背景 */
        .simple-view-toggle::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            height: calc(100% - 6px);
            background: #409EFF;
            border-radius: 5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1;
            box-shadow: 0 1px 3px rgba(64, 158, 255, 0.3);
        }

        .simple-view-toggle.small::before {
            top: 2px;
            left: 2px;
            height: calc(100% - 4px);
        }

        .simple-toggle-btn {
            position: relative;
            z-index: 2;
            background: transparent;
            border: none;
            border-radius: 5px;
            padding: 8px 16px;
            font-size: 14px;
            color: #606266;
            cursor: pointer;
            transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            font-weight: 500;
            flex: 1;
            text-align: center;
        }

        .simple-view-toggle.small .simple-toggle-btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .simple-toggle-btn:hover {
            color: #409EFF;
        }

        .simple-toggle-btn.active {
            color: white;
        }

        .simple-toggle-btn.active:hover {
            color: white;
        }

        /* 动态计算填充位置 */
        .simple-view-toggle[data-active="0"]::before {
            width: 33.33%;
            transform: translateX(0);
        }

        .simple-view-toggle[data-active="1"]::before {
            width: 33.33%;
            transform: translateX(100%);
        }

        .simple-view-toggle[data-active="2"]::before {
            width: 33.33%;
            transform: translateX(200%);
        }

        .result {
            text-align: center;
            color: #606266;
            font-size: 14px;
            margin-top: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 一体化视图切换测试</h1>
        
        <div class="test-section">
            <h2>标准尺寸测试</h2>
            <div class="demo-area">
                <div class="simple-view-toggle" id="toggle1" data-active="0">
                    <button class="simple-toggle-btn active" onclick="setActive('toggle1', 0)">月视图</button>
                    <button class="simple-toggle-btn" onclick="setActive('toggle1', 1)">周视图</button>
                    <button class="simple-toggle-btn" onclick="setActive('toggle1', 2)">年视图</button>
                </div>
            </div>
            <div class="result" id="result1">当前选择：月视图</div>
        </div>

        <div class="test-section">
            <h2>小号尺寸测试</h2>
            <div class="demo-area">
                <div class="simple-view-toggle small" id="toggle2" data-active="0">
                    <button class="simple-toggle-btn active" onclick="setActive('toggle2', 0)">卡片</button>
                    <button class="simple-toggle-btn" onclick="setActive('toggle2', 1)">时间线</button>
                    <button class="simple-toggle-btn" onclick="setActive('toggle2', 2)">表格</button>
                </div>
            </div>
            <div class="result" id="result2">当前选择：卡片</div>
        </div>

        <div class="test-section">
            <h2>动画效果说明</h2>
            <ul>
                <li><strong>一体化设计</strong>：三个按钮融为一体，无分隔间距</li>
                <li><strong>填充动画</strong>：蓝色填充背景会平滑移动到选中按钮位置</li>
                <li><strong>变形效果</strong>：使用 transform 实现硬件加速的流畅动画</li>
                <li><strong>缓动函数</strong>：cubic-bezier(0.4, 0, 0.2, 1) 提供自然的动画曲线</li>
                <li><strong>颜色过渡</strong>：文字颜色同步变化，提供清晰的视觉反馈</li>
            </ul>
        </div>
    </div>

    <script>
        const options = [
            ['月视图', '周视图', '年视图'],
            ['卡片', '时间线', '表格']
        ];

        function setActive(toggleId, index) {
            const toggle = document.getElementById(toggleId);
            const buttons = toggle.querySelectorAll('.simple-toggle-btn');
            const resultId = toggleId === 'toggle1' ? 'result1' : 'result2';
            const result = document.getElementById(resultId);
            const optionSet = toggleId === 'toggle1' ? options[0] : options[1];
            
            // 更新按钮状态
            buttons.forEach((btn, i) => {
                btn.classList.toggle('active', i === index);
            });
            
            // 更新data-active属性来触发CSS动画
            toggle.setAttribute('data-active', index);
            
            // 更新结果显示
            result.textContent = `当前选择：${optionSet[index]}`;
        }
    </script>
</body>
</html>
