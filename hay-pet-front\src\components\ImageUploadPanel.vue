<template>
  <div class="image-upload-panel">
    <!-- 上传区域 -->
    <div class="upload-area">
      <el-upload
        ref="uploadRef"
        :show-file-list="false"
        :before-upload="handleBeforeUpload"
        :on-change="handleFileChange"
        :auto-upload="false"
        accept="image/*"
        drag
        class="upload-dragger"
      >
        <div class="upload-content">
          <el-icon class="upload-icon"><UploadFilled /></el-icon>
          <div class="upload-text">
            <p class="upload-title">点击或拖拽图片到此处上传</p>
            <p class="upload-hint">支持 JPG、PNG、WebP 格式，文件大小不超过 {{ maxSizeMB }}MB</p>
          </div>
        </div>
      </el-upload>
    </div>
    
    <!-- 已选择的图片预览 -->
    <div v-if="selectedFile" class="selected-image-preview">
      <div class="preview-header">
        <span class="preview-title">已选择图片</span>
        <el-button link @click="clearSelection">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="preview-content">
        <div class="image-info">
          <img :src="previewUrl" alt="预览图" class="preview-image" />
          <div class="file-details">
            <p><strong>文件名:</strong> {{ selectedFile.name }}</p>
            <p><strong>大小:</strong> {{ formatFileSize(selectedFile.size) }}</p>
            <p><strong>类型:</strong> {{ selectedFile.type }}</p>
            <p><strong>尺寸:</strong> {{ imageWidth }} × {{ imageHeight }}px</p>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button type="primary" @click="openCropper">
            <el-icon><Crop /></el-icon>
            裁切图片
          </el-button>
          <el-button @click="directUpload" :loading="uploading">
            <el-icon><Upload /></el-icon>
            直接上传
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 上传历史记录 -->
    <div v-if="uploadHistory.length > 0" class="upload-history">
      <div class="history-header">
        <span class="history-title">最近上传</span>
        <el-button link size="small" @click="clearHistory">
          清空记录
        </el-button>
      </div>
      
      <div class="history-list">
        <div 
          v-for="(item, index) in uploadHistory" 
          :key="index"
          class="history-item"
          @click="selectFromHistory(item)"
        >
          <img :src="item.url" alt="历史图片" class="history-image" />
          <div class="history-info">
            <p class="history-name">{{ item.name }}</p>
            <p class="history-time">{{ formatTime(item.uploadTime) }}</p>
          </div>
          <el-button 
            link 
            size="small" 
            @click.stop="removeFromHistory(index)"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 图片裁切对话框 -->
    <NewImageCropperDialog
      v-model="showCropper"
      :image-src="previewUrl"
      :aspect-ratio="cropAspectRatio"
      @confirm="handleCropConfirm"
      @cancel="handleCropCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Close, Crop, Upload, Delete } from '@element-plus/icons-vue'
import NewImageCropperDialog from './NewImageCropperDialog.vue'
import dayjs from 'dayjs'

// 组件属性
const props = defineProps({
  // 最大文件大小(MB)
  maxSize: {
    type: Number,
    default: 5
  },
  // 允许的文件类型
  acceptTypes: {
    type: Array,
    default: () => ['image/jpeg', 'image/png', 'image/webp']
  },
  // 裁切比例 (0表示自由裁切)
  aspectRatio: {
    type: Number,
    default: 1
  },
  // 是否显示上传历史
  showHistory: {
    type: Boolean,
    default: true
  },
  // 历史记录最大数量
  maxHistoryCount: {
    type: Number,
    default: 10
  }
})

// 组件事件
const emit = defineEmits(['upload-success', 'upload-error', 'file-selected'])

// 响应式数据
const uploadRef = ref()
const selectedFile = ref(null)
const previewUrl = ref('')
const imageWidth = ref(0)
const imageHeight = ref(0)
const uploading = ref(false)
const showCropper = ref(false)
const uploadHistory = ref([])

// 计算属性
const maxSizeMB = computed(() => props.maxSize)
const cropAspectRatio = computed(() => props.aspectRatio)

// 文件选择前验证
const handleBeforeUpload = (file) => {
  // 检查文件类型
  if (!props.acceptTypes.includes(file.type)) {
    ElMessage.error(`不支持的文件类型: ${file.type}`)
    return false
  }
  
  // 检查文件大小
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return false
  }
  
  return true
}

// 文件选择处理
const handleFileChange = (file) => {
  if (!file.raw) return
  
  selectedFile.value = file.raw
  
  // 创建预览URL
  previewUrl.value = URL.createObjectURL(file.raw)
  
  // 获取图片尺寸
  const img = new Image()
  img.onload = () => {
    imageWidth.value = img.naturalWidth
    imageHeight.value = img.naturalHeight
  }
  img.src = previewUrl.value
  
  emit('file-selected', file.raw)
}

// 清除选择
const clearSelection = () => {
  selectedFile.value = null
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
  imageWidth.value = 0
  imageHeight.value = 0
  
  // 清空上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 打开裁切器
const openCropper = () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择图片')
    return
  }
  showCropper.value = true
}

// 裁切确认
const handleCropConfirm = async (croppedFile) => {
  try {
    // 替换当前选择的文件
    selectedFile.value = croppedFile
    
    // 更新预览URL
    if (previewUrl.value) {
      URL.revokeObjectURL(previewUrl.value)
    }
    previewUrl.value = URL.createObjectURL(croppedFile)
    
    // 更新图片尺寸
    const img = new Image()
    img.onload = () => {
      imageWidth.value = img.naturalWidth
      imageHeight.value = img.naturalHeight
    }
    img.src = previewUrl.value
    
    ElMessage.success('图片裁切完成，正在上传...')
    
    // 自动上传裁切后的图片
    uploading.value = true
    
    // 触发上传成功事件，让父组件处理实际上传逻辑
    emit('upload-success', croppedFile)
    
    // 添加到历史记录
    addToHistory({
      name: croppedFile.name,
      url: previewUrl.value,
      uploadTime: new Date(),
      file: croppedFile
    })
    
    // 清除当前选择
    clearSelection()
    
  } catch (error) {
    console.error('裁切后上传失败:', error)
    ElMessage.error('裁切后上传失败')
    emit('upload-error', error)
  } finally {
    uploading.value = false
  }
}

// 裁切取消
const handleCropCancel = () => {
  showCropper.value = false
}

// 直接上传
const directUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择图片')
    return
  }
  
  uploading.value = true
  
  try {
    // 触发上传成功事件，让父组件处理实际上传逻辑
    emit('upload-success', selectedFile.value)
    
    // 添加到历史记录
    addToHistory({
      name: selectedFile.value.name,
      url: previewUrl.value,
      uploadTime: new Date(),
      file: selectedFile.value
    })
    
    // 清除当前选择
    clearSelection()
    
  } catch (error) {
    console.error('上传失败:', error)
    emit('upload-error', error)
  } finally {
    uploading.value = false
  }
}

// 添加到历史记录
const addToHistory = (item) => {
  if (!props.showHistory) return
  
  // 避免重复添加
  const exists = uploadHistory.value.some(h => h.name === item.name)
  if (exists) return
  
  uploadHistory.value.unshift(item)
  
  // 限制历史记录数量
  if (uploadHistory.value.length > props.maxHistoryCount) {
    const removed = uploadHistory.value.splice(props.maxHistoryCount)
    // 清理被移除项的URL
    removed.forEach(item => {
      if (item.url && item.url.startsWith('blob:')) {
        URL.revokeObjectURL(item.url)
      }
    })
  }
  
  // 保存到本地存储
  saveHistoryToStorage()
}

// 从历史记录选择
const selectFromHistory = (item) => {
  selectedFile.value = item.file
  previewUrl.value = item.url
  
  // 重新获取图片尺寸
  const img = new Image()
  img.onload = () => {
    imageWidth.value = img.naturalWidth
    imageHeight.value = img.naturalHeight
  }
  img.src = item.url
  
  emit('file-selected', item.file)
}

// 从历史记录移除
const removeFromHistory = (index) => {
  const item = uploadHistory.value[index]
  if (item.url && item.url.startsWith('blob:')) {
    URL.revokeObjectURL(item.url)
  }
  uploadHistory.value.splice(index, 1)
  saveHistoryToStorage()
}

// 清空历史记录
const clearHistory = () => {
  uploadHistory.value.forEach(item => {
    if (item.url && item.url.startsWith('blob:')) {
      URL.revokeObjectURL(item.url)
    }
  })
  uploadHistory.value = []
  saveHistoryToStorage()
}

// 保存历史记录到本地存储
const saveHistoryToStorage = () => {
  try {
    const historyData = uploadHistory.value.map(item => ({
      name: item.name,
      uploadTime: item.uploadTime
      // 注意：不保存file和url，因为它们在页面刷新后会失效
    }))
    localStorage.setItem('imageUploadHistory', JSON.stringify(historyData))
  } catch (error) {
    console.warn('保存上传历史失败:', error)
  }
}

// 从本地存储加载历史记录
const loadHistoryFromStorage = () => {
  try {
    const stored = localStorage.getItem('imageUploadHistory')
    if (stored) {
      const historyData = JSON.parse(stored)
      // 只加载基本信息，不包含文件和URL
      // 实际使用中可能需要根据具体需求调整
    }
  } catch (error) {
    console.warn('加载上传历史失败:', error)
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (date) => {
  return dayjs(date).format('MM-DD HH:mm')
}

// 组件挂载时加载历史记录
loadHistoryFromStorage()

// 组件卸载时清理 blob URL
onUnmounted(() => {
  // 清理当前预览的 blob URL
  if (previewUrl.value && previewUrl.value.startsWith('blob:')) {
    URL.revokeObjectURL(previewUrl.value)
  }
  
  // 清理历史记录中的 blob URL
  uploadHistory.value.forEach(item => {
    if (item.url && item.url.startsWith('blob:')) {
      URL.revokeObjectURL(item.url)
    }
  })
})
</script>

<style scoped lang="scss">
.image-upload-panel {
  .upload-area {
    margin-bottom: 24px;

    .upload-dragger {
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 200px;
        border: 2px dashed #e9ecef;
        border-radius: 12px;
        background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          border-color: #667eea;
          background: #fafbfc;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(102, 126, 234, 0.15);

          &::before {
            opacity: 1;
          }
        }
      }
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      position: relative;
      z-index: 1;

      .upload-icon {
        font-size: 56px;
        color: #d1d5db;
        margin-bottom: 20px;
        transition: all 0.3s ease;
      }

      .upload-text {
        text-align: center;

        .upload-title {
          font-size: 18px;
          font-weight: 600;
          color: #495057;
          margin: 0 0 8px 0;
        }

        .upload-hint {
          font-size: 14px;
          color: #6c757d;
          margin: 0;
          line-height: 1.5;
        }
      }
    }
  }
  
  .selected-image-preview {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .preview-title {
        font-size: 18px;
        font-weight: 600;
        color: #495057;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .el-button {
        border-radius: 50%;
        width: 32px;
        height: 32px;
        padding: 0;
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #6c757d;
        transition: all 0.3s ease;

        &:hover {
          background: #e9ecef;
          color: #495057;
          transform: scale(1.1);
        }
      }
    }

    .preview-content {
      .image-info {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .preview-image {
          width: 140px;
          height: 140px;
          object-fit: cover;
          border-radius: 12px;
          border: 2px solid #e9ecef;
          transition: all 0.3s ease;

          &:hover {
            border-color: #667eea;
            transform: scale(1.02);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.2);
          }
        }

        .file-details {
          flex: 1;

          p {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #6c757d;
            line-height: 1.5;

            strong {
              color: #495057;
              margin-right: 8px;
              font-weight: 600;
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: 12px;

        .el-button {
          border-radius: 8px;
          padding: 12px 20px;
          font-weight: 500;
          transition: all 0.3s ease;

          &.el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;

            &:hover {
              background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
              transform: translateY(-1px);
              box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
            }
          }

          &:not(.el-button--primary) {
            background: #f8f9fa;
            border-color: #e9ecef;
            color: #495057;

            &:hover {
              background: #e9ecef;
              border-color: #dee2e6;
              transform: translateY(-1px);
            }
          }
        }
      }
    }
  }
  
  .upload-history {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

    .history-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .history-title {
        font-size: 18px;
        font-weight: 600;
        color: #495057;
      }

      .el-button {
        border-radius: 8px;
        font-size: 12px;
        padding: 8px 16px;
        background: #f8f9fa;
        border-color: #e9ecef;
        color: #6c757d;

        &:hover {
          background: #e9ecef;
          color: #495057;
        }
      }
    }

    .history-list {
      .history-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid transparent;

        &:hover {
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
          border-color: #e9ecef;
          transform: translateX(4px);
        }

        .history-image {
          width: 48px;
          height: 48px;
          object-fit: cover;
          border-radius: 8px;
          border: 2px solid #e9ecef;
          transition: all 0.3s ease;
        }

        .history-info {
          flex: 1;

          .history-name {
            font-size: 14px;
            font-weight: 500;
            color: #495057;
            margin: 0 0 4px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .history-time {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
          }
        }

        &:hover .history-image {
          border-color: #667eea;
          transform: scale(1.05);
        }
      }
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .selected-image-preview {
      .preview-content {
        .image-info {
          flex-direction: column;
          text-align: center;

          .preview-image {
            width: 120px;
            height: 120px;
            margin: 0 auto 16px;
          }
        }

        .action-buttons {
          justify-content: center;
          flex-wrap: wrap;
        }
      }
    }

    .upload-history {
      .history-list {
        .history-item {
          .history-image {
            width: 40px;
            height: 40px;
          }
        }
      }
    }
  }
}
</style>