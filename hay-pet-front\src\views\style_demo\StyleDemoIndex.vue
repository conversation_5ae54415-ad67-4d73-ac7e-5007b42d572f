<template>
  <div class="style-demo-index">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🎨 Hay!Pet 预设样式系统</h1>
        <p class="page-description">
          统一的组件样式预设系统，确保整个项目的视觉一致性和可维护性
        </p>
      </div>

      <!-- 快速导航 -->
      <div class="quick-nav">
        <h2>📋 组件导航</h2>
        <div class="nav-grid">
          <router-link
            v-for="component in components"
            :key="component.path"
            :to="component.path"
            class="nav-card"
          >
            <div class="nav-icon">{{ component.icon }}</div>
            <h3>{{ component.name }}</h3>
            <p>{{ component.description }}</p>
            <div class="nav-meta">
              <span class="version">{{ component.version }}</span>
              <span class="status" :class="component.status">{{ getStatusText(component.status) }}</span>
            </div>
          </router-link>
        </div>
      </div>

      <!-- 简单的预设样式展示 -->
      <div class="simple-demo">
        <h2>🎨 预设样式快速预览</h2>

        <!-- 颜色展示 -->
        <div class="color-demo">
          <h3>颜色系统</h3>
          <div class="color-swatches">
            <div class="color-swatch" style="background: #409EFF;">主色调</div>
            <div class="color-swatch" style="background: #67C23A;">成功色</div>
            <div class="color-swatch" style="background: #E6A23C;">警告色</div>
            <div class="color-swatch" style="background: #F56C6C;">危险色</div>
          </div>
        </div>

        <!-- 按钮展示 -->
        <div class="button-demo">
          <h3>按钮样式</h3>
          <div class="button-group">
            <button class="demo-btn primary">主要按钮</button>
            <button class="demo-btn success">成功按钮</button>
            <button class="demo-btn warning">警告按钮</button>
            <button class="demo-btn danger">危险按钮</button>
          </div>
        </div>

        <!-- 标签展示 -->
        <div class="tag-demo">
          <h3>标签样式</h3>
          <div class="tag-group">
            <span class="demo-tag primary">主要标签</span>
            <span class="demo-tag success">成功标签</span>
            <span class="demo-tag warning">警告标签</span>
            <span class="demo-tag danger">危险标签</span>
          </div>
        </div>
      </div>

      <!-- 设计原则 -->
      <div class="design-principles">
        <h2>🎯 设计原则</h2>
        <div class="principles-grid">
          <div class="principle-card">
            <div class="principle-icon">🎨</div>
            <h3>统一性</h3>
            <p>确保所有组件使用相同的设计语言和视觉风格</p>
          </div>
          <div class="principle-card">
            <div class="principle-icon">🔄</div>
            <h3>可复用性</h3>
            <p>避免跨文件复制粘贴样式代码，提高开发效率</p>
          </div>
          <div class="principle-card">
            <div class="principle-icon">🔧</div>
            <h3>可维护性</h3>
            <p>集中管理样式配置，便于后续调整和升级</p>
          </div>
          <div class="principle-card">
            <div class="principle-icon">📈</div>
            <h3>扩展性</h3>
            <p>为其他组件类型的标准化奠定基础</p>
          </div>
        </div>
      </div>

      <!-- 技术特性 -->
      <div class="tech-features">
        <h2>⚡ 技术特性</h2>
        <div class="features-grid">
          <div class="feature-item">
            <h3>🎨 设计令牌系统</h3>
            <p>统一的颜色、间距、字体等设计变量</p>
            <code>@import '@/styles/design-tokens.css'</code>
          </div>
          <div class="feature-item">
            <h3>📱 响应式设计</h3>
            <p>自动适配桌面、平板、手机等不同设备</p>
            <code>@media (max-width: 768px)</code>
          </div>
          <div class="feature-item">
            <h3>🎭 主题支持</h3>
            <p>支持多种视觉主题和样式变体</p>
            <code>variant="primary|success|warning"</code>
          </div>
          <div class="feature-item">
            <h3>⚡ 性能优化</h3>
            <p>CSS动画硬件加速，流畅的交互体验</p>
            <code>transform: translateZ(0)</code>
          </div>
        </div>
      </div>

      <!-- 快速开始 -->
      <div class="quick-start">
        <h2>🚀 快速开始</h2>
        <div class="start-steps">
          <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3>引入样式文件</h3>
              <pre><code>@import '@/styles/design-tokens.css';
@import '@/styles/tag-presets.css';
@import '@/styles/color-picker-presets.css';
@import '@/styles/stats-card-presets.css';
@import '@/styles/drag-sort-presets.css';
@import '@/styles/page-header-presets.css';</code></pre>
            </div>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3>使用预设组件</h3>
              <pre><code>&lt;StandardTag text="示例标签" variant="primary" /&gt;
&lt;StandardColorPicker v-model="color" /&gt;
&lt;StandardStatsGrid :stats-items="statsData" /&gt;
&lt;PageHeaderBar title="页面标题" @add-click="handleAdd" /&gt;
&lt;CalendarViewPreset title="日历" :records="records" /&gt;</code></pre>
            </div>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <div class="step-content">
              <h3>查看完整示例</h3>
              <p>点击上方导航卡片查看各组件的详细使用方法</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 版本信息 -->
      <div class="version-info">
        <h2>📈 版本历史</h2>
        <div class="version-timeline">
          <div class="version-item">
            <div class="version-badge">v1.9.0</div>
            <div class="version-content">
              <h3>记录视图预设组件</h3>
              <p>新增RecordViewsPreset组件，统一的三视图模式组件，支持卡片、时间线、表格视图切换，包含完整的交互功能、统一设计语言和高度可配置的字段映射</p>
              <span class="version-date">2024-06-23</span>
            </div>
          </div>
          <div class="version-item">
            <div class="version-badge">v1.6.0</div>
            <div class="version-content">
              <h3>日历视图预设组件</h3>
              <p>新增CalendarViewPreset组件，从事件记录页面提取的完整日历功能，支持月视图、周视图、年视图，包含记录指示器、悬浮提示和响应式设计</p>
              <span class="version-date">2024-06-20</span>
            </div>
          </div>
          <div class="version-item">
            <div class="version-badge">v1.5.0</div>
            <div class="version-content">
              <h3>页面标题栏组件</h3>
              <p>新增PageHeaderBar组件，基于事件记录页面设计，支持多种样式变体、主题色和自定义插槽</p>
              <span class="version-date">2024-06-20</span>
            </div>
          </div>
          <div class="version-item">
            <div class="version-badge">v1.4.0</div>
            <div class="version-content">
              <h3>拖拽排序组件</h3>
              <p>新增完整的拖拽排序解决方案，包括视觉反馈、数据库更新、错误处理和移动端优化</p>
              <span class="version-date">2024-06-20</span>
            </div>
          </div>
          <div class="version-item">
            <div class="version-badge">v1.3.0</div>
            <div class="version-content">
              <h3>统计卡片组件</h3>
              <p>新增StandardStatsCard和StandardStatsGrid组件，支持自适应布局和多选标签功能</p>
              <span class="version-date">2024-06-20</span>
            </div>
          </div>
          <div class="version-item">
            <div class="version-badge">v1.2.0</div>
            <div class="version-content">
              <h3>颜色选择器组件</h3>
              <p>新增StandardColorPicker组件，支持多种尺寸和布局</p>
              <span class="version-date">2024-06-20</span>
            </div>
          </div>
          <div class="version-item">
            <div class="version-badge">v1.1.0</div>
            <div class="version-content">
              <h3>添加按钮预设</h3>
              <p>扩展标签组件，新增添加按钮样式预设</p>
              <span class="version-date">2024-06-15</span>
            </div>
          </div>
          <div class="version-item">
            <div class="version-badge">v1.0.0</div>
            <div class="version-content">
              <h3>初始版本</h3>
              <p>基础标签组件预设系统，包含设计令牌和响应式设计</p>
              <span class="version-date">2024-06-10</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 组件列表
const components = ref([
  {
    name: 'StandardTag 标签组件',
    path: '/style_demo/standard-tag',
    icon: '🏷️',
    description: '统一的标签组件，支持多种尺寸、颜色和交互状态',
    version: 'v1.1.0',
    status: 'stable'
  },
  {
    name: 'StandardColorPicker 颜色选择器',
    path: '/style_demo/color-picker',
    icon: '🎨',
    description: '优化的颜色选择器组件，包含选中动画和渐变效果',
    version: 'v1.2.0',
    status: 'stable'
  },
  {
    name: 'Simple ViewToggle 一体化视图切换',
    path: '/style_demo/simple-view-toggle',
    icon: '🔄',
    description: '一体化设计的视图切换组件，带有流畅的填充变形动画，简洁而优雅',
    version: 'v1.4.0',
    status: 'stable'
  },
  {
    name: 'Enhanced ViewToggle 增强动画视图切换',
    path: '/style_demo/enhanced-view-toggle',
    icon: '🚀',
    description: '使用 GSAP 和 VueUse Motion 打造的高级动画效果，包含涟漪、发光、弹跳等丰富交互',
    version: 'v2.0.0',
    status: 'experimental'
  },
  {
    name: 'Design Tokens 设计令牌',
    path: '/style_demo/design-tokens',
    icon: '🎯',
    description: '统一的设计变量系统，包含颜色、间距、字体等',
    version: 'v1.0.0',
    status: 'stable'
  },
  {
    name: 'Button Presets 按钮预设',
    path: '/style_demo/button-presets',
    icon: '🔘',
    description: '标准化的按钮样式，包含添加按钮和交互效果',
    version: 'v1.1.0',
    status: 'stable'
  },
  {
    name: 'StandardStatsCard 统计卡片',
    path: '/style_demo/stats-card',
    icon: '📊',
    description: '自适应统计卡片组件，支持多种主题、尺寸和状态管理',
    version: 'v1.3.0',
    status: 'stable'
  },
  {
    name: 'DragSort 拖拽排序',
    path: '/style_demo/drag-sort',
    icon: '🔄',
    description: '完整的拖拽排序解决方案，包括视觉反馈、数据库更新和错误处理',
    version: 'v1.4.0',
    status: 'stable'
  },
  {
    name: 'PageHeaderBar 页面标题栏',
    path: '/style_demo/page-header',
    icon: '📋',
    description: '统一的页面标题栏组件，基于事件记录页面设计，支持多种样式变体和自定义配置',
    version: 'v1.5.0',
    status: 'stable'
  },
  {
    name: 'CalendarViewPreset 日历视图预设',
    path: '/style_demo/calendar-view',
    icon: '📅',
    description: '完整的日历视图组件，支持月视图、周视图、年视图，包含记录指示器、悬浮提示和响应式设计',
    version: 'v1.6.0',
    status: 'stable'
  },
  {
    name: 'EventRecordsPreset 事件记录预设',
    path: '/style_demo/event-records',
    icon: '📋',
    description: '完整的事件记录页面预设，包含日历视图、统计卡片、筛选功能、记录展示等完整功能',
    version: 'v1.7.0',
    status: 'stable'
  },
  {
    name: 'CalendarOptimized 优化日历视图',
    path: '/style_demo/calendar-optimized',
    icon: '✨',
    description: '优化后的日历视图演示，包含今天日期高亮、增强的记录指示器、流畅动画等视觉优化',
    version: 'v1.8.0',
    status: 'stable'
  },
  {
    name: 'RecordViewsPreset 记录视图预设',
    path: '/style_demo/record-views-preset',
    icon: '📋',
    description: '统一的三视图模式组件，支持卡片、时间线、表格视图切换，包含完整的交互功能和统一设计语言',
    version: 'v1.9.0',
    status: 'stable'
  }
])

// 状态文本映射
const getStatusText = (status) => {
  const statusMap = {
    stable: '稳定',
    beta: '测试',
    alpha: '开发中',
    deprecated: '已弃用'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.style-demo-index {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-header h1 {
  font-size: 32px;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: 18px;
  color: #909399;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* 快速导航 */
.quick-nav {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.quick-nav h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 20px;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.nav-card {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 20px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  border-color: #409EFF;
}

.nav-card:hover::before {
  transform: scaleX(1);
}

.nav-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.nav-card h3 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 18px;
}

.nav-card p {
  color: #909399;
  margin-bottom: 12px;
  line-height: 1.5;
}

.nav-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.version {
  font-size: 12px;
  color: #C0C4CC;
  background: #FAFAFA;
  padding: 4px 8px;
  border-radius: 4px;
}

.status {
  font-size: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
}

.status.stable {
  background: #B3E19D;
  color: #529B2E;
}

/* 简单演示样式 */
.simple-demo {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.simple-demo h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 20px;
}

.simple-demo h3 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 16px;
}

.color-demo,
.button-demo,
.tag-demo {
  margin-bottom: 32px;
}

.color-swatches {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.color-swatch {
  padding: 16px 24px;
  color: white;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
  min-width: 120px;
}

.button-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-btn.primary {
  background: #409EFF;
  color: white;
}

.demo-btn.success {
  background: #67C23A;
  color: white;
}

.demo-btn.warning {
  background: #E6A23C;
  color: white;
}

.demo-btn.danger {
  background: #F56C6C;
  color: white;
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tag-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.demo-tag {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.demo-tag.primary {
  background: #409EFF;
}

.demo-tag.success {
  background: #67C23A;
}

.demo-tag.warning {
  background: #E6A23C;
}

.demo-tag.danger {
  background: #F56C6C;
}

/* 其他区块样式 */
.design-principles,
.tech-features,
.quick-start,
.version-info {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.design-principles h2,
.tech-features h2,
.quick-start h2,
.version-info h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 20px;
}

.principles-grid,
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.principle-card,
.feature-item {
  background: #F5F7FA;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
}

.principle-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.principle-card h3,
.feature-item h3 {
  color: #303133;
  margin-bottom: 8px;
}

.principle-card p,
.feature-item p {
  color: #909399;
  line-height: 1.5;
}

.feature-item code {
  display: block;
  background: #FAFAFA;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 8px;
  color: #409EFF;
}

/* 快速开始 */
.start-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content h3 {
  color: #303133;
  margin-bottom: 8px;
}

.step-content pre {
  background: #FAFAFA;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.step-content code {
  color: #409EFF;
}

/* 版本信息 */
.version-timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.version-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background: #F5F7FA;
  border-radius: 12px;
}

.version-badge {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 50px;
  font-weight: bold;
  font-size: 12px;
  flex-shrink: 0;
}

.version-content h3 {
  color: #303133;
  margin-bottom: 4px;
}

.version-content p {
  color: #909399;
  margin-bottom: 8px;
}

.version-date {
  font-size: 12px;
  color: #C0C4CC;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .style-demo-index {
    padding: 16px;
  }

  .nav-grid {
    grid-template-columns: 1fr;
  }

  .principles-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }

  .step {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .version-item {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
}
</style>
