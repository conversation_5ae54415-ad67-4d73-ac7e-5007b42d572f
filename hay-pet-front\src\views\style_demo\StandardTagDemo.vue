<template>
  <div class="tag-demo">
    <div class="demo-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>🏷️ StandardTag 标签组件</h1>
        <p class="page-description">
          统一的标签组件，支持多种尺寸、颜色和交互状态，基于 health-records 面板标签组件的设计模式
        </p>
        <router-link to="/style_demo" class="back-link">
          ← 返回样式系统首页
        </router-link>
      </div>

      <!-- 基础用法 -->
      <div class="demo-section">
        <h2>📋 基础用法</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>默认标签</h3>
            <div class="demo-area">
              <StandardTag text="默认标签" />
              <StandardTag text="主色调" variant="primary" />
              <StandardTag text="成功" variant="success" />
              <StandardTag text="警告" variant="warning" />
              <StandardTag text="危险" variant="danger" />
            </div>
            <div class="code-example">
              <pre><code>&lt;StandardTag text="默认标签" /&gt;
&lt;StandardTag text="主色调" variant="primary" /&gt;
&lt;StandardTag text="成功" variant="success" /&gt;</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 尺寸变体 -->
      <div class="demo-section">
        <h2>📏 尺寸变体</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>不同尺寸</h3>
            <div class="demo-area">
              <StandardTag text="超小" size="xs" variant="primary" />
              <StandardTag text="小号" size="sm" variant="primary" />
              <StandardTag text="中号" size="md" variant="primary" />
              <StandardTag text="大号" size="lg" variant="primary" />
              <StandardTag text="超大" size="xl" variant="primary" />
            </div>
            <div class="code-example">
              <pre><code>&lt;StandardTag text="超小" size="xs" /&gt;
&lt;StandardTag text="小号" size="sm" /&gt;
&lt;StandardTag text="中号" size="md" /&gt;</code></pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互状态 -->
      <div class="demo-section">
        <h2>🎯 交互状态</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>激活状态</h3>
            <div class="demo-area">
              <StandardTag text="普通状态" variant="primary" />
              <StandardTag text="激活状态" variant="primary" :active="true" />
              <StandardTag text="禁用状态" variant="primary" :disabled="true" />
              <StandardTag text="加载状态" variant="primary" :loading="true" />
            </div>
          </div>
          
          <div class="showcase-item">
            <h3>交互功能</h3>
            <div class="demo-area">
              <StandardTag 
                text="可编辑" 
                variant="success" 
                :editable="true"
                @edit="handleEdit"
              />
              <StandardTag 
                text="可删除" 
                variant="warning" 
                :deletable="true"
                @delete="handleDelete"
              />
              <StandardTag 
                text="可拖拽" 
                variant="info" 
                :draggable="true"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 自定义样式 -->
      <div class="demo-section">
        <h2>🎨 自定义样式</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>自定义颜色</h3>
            <div class="demo-area">
              <StandardTag 
                text="自定义背景" 
                :background-color="customColor"
              />
              <StandardTag 
                text="自定义文字" 
                :color="customTextColor"
                variant="primary"
              />
              <StandardTag 
                text="自定义边框" 
                :border-color="customBorderColor"
                variant="info"
              />
            </div>
            <div class="color-controls">
              <div class="control-group">
                <label>背景色：</label>
                <el-color-picker v-model="customColor" />
              </div>
              <div class="control-group">
                <label>文字色：</label>
                <el-color-picker v-model="customTextColor" />
              </div>
              <div class="control-group">
                <label>边框色：</label>
                <el-color-picker v-model="customBorderColor" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实际应用示例 -->
      <div class="demo-section">
        <h2>💼 实际应用示例</h2>
        <div class="demo-showcase">
          <div class="showcase-item">
            <h3>健康记录类型标签</h3>
            <div class="demo-area health-records-demo">
              <StandardTag 
                v-for="type in healthRecordTypes"
                :key="type.value"
                :text="type.label"
                :background-color="type.color"
                :active="selectedType === type.value"
                @click="selectedType = type.value"
              />
            </div>
          </div>
          
          <div class="showcase-item">
            <h3>标签管理界面</h3>
            <div class="demo-area tag-management-demo">
              <StandardTag 
                v-for="tag in managedTags"
                :key="tag.id"
                :text="tag.name"
                :variant="tag.variant"
                :editable="true"
                :deletable="true"
                @edit="editTag(tag)"
                @delete="deleteTag(tag.id)"
              />
              <button class="add-tag-btn" @click="addTag">
                <el-icon><Plus /></el-icon>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- API 文档 -->
      <div class="demo-section">
        <h2>📖 API 文档</h2>
        <div class="api-tables">
          <div class="api-table">
            <h3>Props</h3>
            <table>
              <thead>
                <tr>
                  <th>属性名</th>
                  <th>类型</th>
                  <th>默认值</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>text</td>
                  <td>String</td>
                  <td>''</td>
                  <td>标签文本内容</td>
                </tr>
                <tr>
                  <td>variant</td>
                  <td>String</td>
                  <td>'primary'</td>
                  <td>标签变体：primary/success/warning/danger/info</td>
                </tr>
                <tr>
                  <td>size</td>
                  <td>String</td>
                  <td>'md'</td>
                  <td>标签尺寸：xs/sm/md/lg/xl</td>
                </tr>
                <tr>
                  <td>active</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>是否为激活状态</td>
                </tr>
                <tr>
                  <td>clickable</td>
                  <td>Boolean</td>
                  <td>true</td>
                  <td>是否可点击</td>
                </tr>
                <tr>
                  <td>disabled</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>是否禁用</td>
                </tr>
                <tr>
                  <td>editable</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>是否显示编辑按钮</td>
                </tr>
                <tr>
                  <td>deletable</td>
                  <td>Boolean</td>
                  <td>false</td>
                  <td>是否显示删除按钮</td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="api-table">
            <h3>Events</h3>
            <table>
              <thead>
                <tr>
                  <th>事件名</th>
                  <th>参数</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>click</td>
                  <td>event</td>
                  <td>点击标签时触发</td>
                </tr>
                <tr>
                  <td>edit</td>
                  <td>event</td>
                  <td>点击编辑按钮时触发</td>
                </tr>
                <tr>
                  <td>delete</td>
                  <td>event</td>
                  <td>点击删除按钮时触发</td>
                </tr>
                <tr>
                  <td>mouseenter</td>
                  <td>event</td>
                  <td>鼠标进入时触发</td>
                </tr>
                <tr>
                  <td>mouseleave</td>
                  <td>event</td>
                  <td>鼠标离开时触发</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import StandardTag from '@/components/common/StandardTag.vue'

// 响应式数据
const customColor = ref('#FF6B6B')
const customTextColor = ref('#FFFFFF')
const customBorderColor = ref('#4ECDC4')
const selectedType = ref('vaccination')

// 健康记录类型
const healthRecordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病就诊', color: '#F56C6C' },
  { value: 'medication', label: '用药记录', color: '#909399' }
])

// 管理的标签
const managedTags = ref([
  { id: 1, name: '重要', variant: 'danger' },
  { id: 2, name: '紧急', variant: 'warning' },
  { id: 3, name: '完成', variant: 'success' },
  { id: 4, name: '待办', variant: 'info' }
])

// 方法
const handleEdit = () => {
  ElMessage.success('编辑标签')
}

const handleDelete = () => {
  ElMessage.warning('删除标签')
}

const editTag = (tag) => {
  ElMessage.info(`编辑标签: ${tag.name}`)
}

const deleteTag = (id) => {
  managedTags.value = managedTags.value.filter(tag => tag.id !== id)
  ElMessage.success('标签已删除')
}

const addTag = () => {
  const newTag = {
    id: Date.now(),
    name: `新标签${managedTags.value.length + 1}`,
    variant: 'primary'
  }
  managedTags.value.push(newTag)
  ElMessage.success('标签已添加')
}
</script>

<style scoped>
/* 引入设计令牌 */
@import '@/styles/design-tokens.css';

.tag-demo {
  min-height: 100vh;
  background: var(--gradient-neutral);
  padding: var(--spacing-6);
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.page-header h1 {
  font-size: var(--font-size-3xl);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  max-width: 800px;
  margin: 0 auto var(--spacing-4);
  line-height: 1.6;
}

.back-link {
  display: inline-flex;
  align-items: center;
  color: var(--color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--duration-base) ease;
}

.back-link:hover {
  color: var(--color-primary-dark);
}

/* 演示区块 */
.demo-section {
  background: var(--color-bg-primary);
  border-radius: var(--radius-card);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-base);
}

.demo-section h2 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-xl);
  border-bottom: 2px solid var(--color-border-light);
  padding-bottom: var(--spacing-2);
}

.demo-showcase {
  display: grid;
  gap: var(--spacing-6);
}

.showcase-item h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
}

.demo-area {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-3);
}

.code-example {
  background: var(--color-bg-tertiary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-3);
}

.code-example pre {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-primary);
  overflow-x: auto;
}

/* 颜色控制器 */
.color-controls {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.control-group label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  min-width: 60px;
}

/* 实际应用示例 */
.health-records-demo .standard-tag {
  cursor: pointer;
}

.tag-management-demo {
  align-items: center;
}

.add-tag-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px dashed var(--color-border-base);
  background: transparent;
  color: var(--color-text-placeholder);
  cursor: pointer;
  transition: all var(--duration-base) ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-tag-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: scale(1.05);
}

/* API 表格 */
.api-tables {
  display: grid;
  gap: var(--spacing-6);
}

.api-table h3 {
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-3);
}

.api-table table {
  width: 100%;
  border-collapse: collapse;
  background: var(--color-bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.api-table th,
.api-table td {
  padding: var(--spacing-3);
  text-align: left;
  border-bottom: 1px solid var(--color-border-light);
}

.api-table th {
  background: var(--color-bg-tertiary);
  font-weight: 600;
  color: var(--color-text-primary);
}

.api-table td {
  color: var(--color-text-secondary);
}

.api-table td:first-child {
  font-family: monospace;
  color: var(--color-primary);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tag-demo {
    padding: var(--spacing-4);
  }
  
  .demo-area {
    justify-content: center;
  }
  
  .color-controls {
    justify-content: center;
  }
  
  .api-table {
    overflow-x: auto;
  }
}
</style>
