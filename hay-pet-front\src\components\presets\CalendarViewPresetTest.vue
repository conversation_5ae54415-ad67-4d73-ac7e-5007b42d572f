<template>
  <div class="calendar-test">
    <h2>CalendarViewPreset 功能测试</h2>
    
    <!-- 测试控制面板 -->
    <div class="test-controls">
      <h3>测试控制</h3>
      <div class="control-group">
        <el-button @click="runAllTests" type="primary">运行所有测试</el-button>
        <el-button @click="clearResults" type="default">清除结果</el-button>
        <el-button @click="addTestRecord" type="success">添加测试记录</el-button>
        <el-button @click="removeTestRecord" type="danger">移除测试记录</el-button>
      </div>
      
      <div class="control-group">
        <label>测试视图：</label>
        <el-radio-group v-model="testView">
          <el-radio-button label="month">月视图</el-radio-button>
          <el-radio-button label="week">周视图</el-radio-button>
          <el-radio-button label="year">年视图</el-radio-button>
        </el-radio-group>
      </div>
      
      <div class="control-group">
        <label>测试日期：</label>
        <el-date-picker
          v-model="testDate"
          type="date"
          placeholder="选择测试日期"
          @change="handleTestDateChange"
        />
      </div>
    </div>

    <!-- 测试结果显示 -->
    <div class="test-results">
      <h3>测试结果</h3>
      <div class="results-summary">
        <div class="summary-item">
          <span class="label">总测试数：</span>
          <span class="value">{{ testResults.length }}</span>
        </div>
        <div class="summary-item">
          <span class="label">通过：</span>
          <span class="value success">{{ passedTests }}</span>
        </div>
        <div class="summary-item">
          <span class="label">失败：</span>
          <span class="value error">{{ failedTests }}</span>
        </div>
        <div class="summary-item">
          <span class="label">成功率：</span>
          <span class="value">{{ successRate }}%</span>
        </div>
      </div>
      
      <div class="results-list">
        <div
          v-for="(result, index) in testResults"
          :key="index"
          class="result-item"
          :class="{ passed: result.passed, failed: !result.passed }"
        >
          <div class="result-header">
            <span class="result-name">{{ result.name }}</span>
            <span class="result-status">{{ result.passed ? '✅ 通过' : '❌ 失败' }}</span>
          </div>
          <div class="result-description">{{ result.description }}</div>
          <div v-if="result.error" class="result-error">错误：{{ result.error }}</div>
          <div class="result-time">执行时间：{{ result.executionTime }}ms</div>
        </div>
      </div>
    </div>

    <!-- 日历组件测试实例 -->
    <div class="calendar-instance">
      <h3>日历组件实例</h3>
      <CalendarViewPreset
        ref="calendarRef"
        title="测试日历"
        :records="testRecords"
        :record-types="recordTypes"
        :color-mapping="colorMapping"
        :label-mapping="labelMapping"
        :initial-view="testView"
        :initial-date="testDate"
        @date-click="handleDateClick"
        @view-change="handleViewChange"
        @date-change="handleDateChange"
        @month-change="handleMonthChange"
        @year-change="handleYearChange"
      />
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <h3>事件日志</h3>
      <div class="log-controls">
        <el-button @click="clearEventLog" size="small">清除日志</el-button>
      </div>
      <div class="log-content">
        <div
          v-for="(log, index) in eventLog"
          :key="index"
          class="log-item"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import CalendarViewPreset from './CalendarViewPreset.vue'

// 测试数据
const testRecords = ref([
  {
    id: 1,
    record_type: 'vaccination',
    date: '2024-06-15',
    description: '狂犬病疫苗接种',
    created_at: '2024-06-15T10:00:00Z'
  },
  {
    id: 2,
    record_type: 'checkup',
    date: '2024-06-15',
    description: '常规体检',
    created_at: '2024-06-15T14:30:00Z'
  },
  {
    id: 3,
    record_type: 'deworming',
    date: '2024-06-20',
    description: '体内驱虫',
    created_at: '2024-06-20T09:15:00Z'
  },
  {
    id: 4,
    record_type: 'illness',
    date: '2024-06-25',
    description: '轻微感冒',
    created_at: '2024-06-25T16:45:00Z'
  }
])

const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病', color: '#F56C6C' },
  { value: 'medication', label: '用药', color: '#909399' },
  { value: 'surgery', label: '手术', color: '#F56C6C' },
  { value: 'other', label: '其他', color: '#C0C4CC' }
])

const colorMapping = {
  vaccination: 'vaccination',
  deworming: 'deworming',
  checkup: 'checkup',
  illness: 'illness',
  medication: 'medication',
  surgery: 'surgery',
  other: 'other'
}

const labelMapping = {
  vaccination: '疫苗接种',
  deworming: '驱虫',
  checkup: '体检',
  illness: '疾病',
  medication: '用药',
  surgery: '手术',
  other: '其他'
}

// 测试控制
const testView = ref('month')
const testDate = ref(new Date())
const calendarRef = ref(null)

// 测试结果
const testResults = ref([])
const eventLog = ref([])

// 计算属性
const passedTests = computed(() => testResults.value.filter(r => r.passed).length)
const failedTests = computed(() => testResults.value.filter(r => !r.passed).length)
const successRate = computed(() => {
  if (testResults.value.length === 0) return 0
  return Math.round((passedTests.value / testResults.value.length) * 100)
})

// 测试函数
const runTest = async (name, description, testFn) => {
  const startTime = Date.now()
  try {
    await testFn()
    const executionTime = Date.now() - startTime
    testResults.value.push({
      name,
      description,
      passed: true,
      executionTime
    })
  } catch (error) {
    const executionTime = Date.now() - startTime
    testResults.value.push({
      name,
      description,
      passed: false,
      error: error.message,
      executionTime
    })
  }
}

// 具体测试用例
const testComponentMount = async () => {
  if (!calendarRef.value) {
    throw new Error('日历组件未正确挂载')
  }
}

const testRecordDisplay = async () => {
  const recordsForDate = testRecords.value.filter(record => {
    const recordDate = new Date(record.date)
    return recordDate.toDateString() === new Date('2024-06-15').toDateString()
  })
  
  if (recordsForDate.length !== 2) {
    throw new Error(`期望找到2条记录，实际找到${recordsForDate.length}条`)
  }
}

const testViewSwitch = async () => {
  const originalView = testView.value
  testView.value = 'week'
  await nextTick()
  
  if (testView.value !== 'week') {
    throw new Error('视图切换失败')
  }
  
  testView.value = originalView
}

const testDateNavigation = async () => {
  const originalDate = new Date(testDate.value)
  const newDate = new Date('2024-07-01')
  testDate.value = newDate
  await nextTick()
  
  if (testDate.value.getTime() !== newDate.getTime()) {
    throw new Error('日期导航失败')
  }
  
  testDate.value = originalDate
}

const testColorMapping = async () => {
  const vaccinationColor = colorMapping.vaccination
  if (vaccinationColor !== 'vaccination') {
    throw new Error('颜色映射配置错误')
  }
}

const testLabelMapping = async () => {
  const vaccinationLabel = labelMapping.vaccination
  if (vaccinationLabel !== '疫苗接种') {
    throw new Error('标签映射配置错误')
  }
}

// 运行所有测试
const runAllTests = async () => {
  testResults.value = []
  
  await runTest('组件挂载测试', '验证日历组件是否正确挂载', testComponentMount)
  await runTest('记录显示测试', '验证记录是否正确显示在日历中', testRecordDisplay)
  await runTest('视图切换测试', '验证月/周/年视图切换功能', testViewSwitch)
  await runTest('日期导航测试', '验证日期导航功能', testDateNavigation)
  await runTest('颜色映射测试', '验证记录类型颜色映射', testColorMapping)
  await runTest('标签映射测试', '验证记录类型标签映射', testLabelMapping)
}

// 清除结果
const clearResults = () => {
  testResults.value = []
}

// 添加测试记录
const addTestRecord = () => {
  const newRecord = {
    id: Date.now(),
    record_type: 'medication',
    date: new Date().toISOString().split('T')[0],
    description: '测试用药记录',
    created_at: new Date().toISOString()
  }
  testRecords.value.push(newRecord)
  addEventLog('添加测试记录', newRecord)
}

// 移除测试记录
const removeTestRecord = () => {
  if (testRecords.value.length > 0) {
    const removed = testRecords.value.pop()
    addEventLog('移除测试记录', removed)
  }
}

// 事件处理
const handleDateClick = (dateStr, records) => {
  addEventLog('日期点击', { date: dateStr, recordCount: records.length })
}

const handleViewChange = (newView) => {
  addEventLog('视图切换', { view: newView })
}

const handleDateChange = (newDate) => {
  addEventLog('日期变化', { date: newDate.toISOString().split('T')[0] })
}

const handleMonthChange = (month) => {
  addEventLog('月份变化', { month })
}

const handleYearChange = (year) => {
  addEventLog('年份变化', { year })
}

const handleTestDateChange = (date) => {
  addEventLog('测试日期变化', { date: date?.toISOString().split('T')[0] })
}

// 事件日志管理
const addEventLog = (event, data) => {
  eventLog.value.unshift({
    time: new Date().toLocaleTimeString(),
    event,
    data: JSON.stringify(data)
  })
  
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

const clearEventLog = () => {
  eventLog.value = []
}
</script>

<style scoped>
.calendar-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.calendar-test h2 {
  color: #303133;
  margin-bottom: 32px;
  text-align: center;
}

.test-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.test-controls h3 {
  margin-bottom: 16px;
  color: #409EFF;
}

.control-group {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-group label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
}

.test-results {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.test-results h3 {
  margin-bottom: 16px;
  color: #409EFF;
}

.results-summary {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.summary-item .label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.summary-item .value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.summary-item .value.success {
  color: #67C23A;
}

.summary-item .value.error {
  color: #F56C6C;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
}

.result-item.passed {
  border-color: #67C23A;
  background: rgba(103, 194, 58, 0.05);
}

.result-item.failed {
  border-color: #F56C6C;
  background: rgba(245, 108, 108, 0.05);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-name {
  font-weight: 600;
  color: #303133;
}

.result-status {
  font-size: 14px;
}

.result-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 4px;
}

.result-error {
  color: #F56C6C;
  font-size: 12px;
  margin-bottom: 4px;
}

.result-time {
  color: #909399;
  font-size: 12px;
}

.calendar-instance {
  margin-bottom: 24px;
}

.calendar-instance h3 {
  margin-bottom: 16px;
  color: #409EFF;
}

.event-log {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.event-log h3 {
  margin-bottom: 16px;
  color: #409EFF;
}

.log-controls {
  margin-bottom: 16px;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f5f7fa;
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #909399;
  font-weight: 500;
  min-width: 80px;
}

.log-event {
  color: #409EFF;
  font-weight: 600;
  min-width: 120px;
}

.log-data {
  color: #606266;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  flex: 1;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calendar-test {
    padding: 16px;
  }

  .control-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .results-summary {
    flex-direction: column;
    gap: 12px;
  }

  .log-item {
    flex-direction: column;
    gap: 4px;
  }

  .log-time,
  .log-event {
    min-width: auto;
  }
}
</style>
