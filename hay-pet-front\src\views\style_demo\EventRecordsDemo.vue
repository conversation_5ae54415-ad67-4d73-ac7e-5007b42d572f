<template>
  <div class="event-records-demo">
    <div class="demo-header">
      <h1>📅 事件记录预设组件演示</h1>
      <p>展示完整的事件记录页面预设，包含日历视图、统计卡片、筛选功能等</p>
    </div>

    <!-- 基础演示 -->
    <div class="demo-section">
      <h2>🎨 完整事件记录页面</h2>
      <div class="demo-content">
        <EventRecordsPreset
          title="宠物健康记录"
          calendar-title="健康记录日历"
          add-button-text="添加健康记录"
          :records="eventRecords"
          :record-types="recordTypes"
          :color-mapping="colorMapping"
          :label-mapping="labelMapping"
          :show-stats="true"
          @add-click="handleAddClick"
          @date-click="handleDateClick"
          @view-change="handleViewChange"
          @date-change="handleDateChange"
          @type-filter="handleTypeFilter"
          @view-mode-change="handleViewModeChange"
        />
      </div>
    </div>

    <!-- 自定义统计项演示 -->
    <div class="demo-section">
      <h2>📊 自定义统计项</h2>
      <div class="demo-content">
        <EventRecordsPreset
          title="自定义统计演示"
          calendar-title="自定义统计日历"
          :records="eventRecords"
          :record-types="recordTypes"
          :color-mapping="colorMapping"
          :label-mapping="labelMapping"
          :show-stats="true"
          :custom-stats-items="customStatsItems"
          @add-click="handleAddClick"
        />
      </div>
    </div>

    <!-- 无统计卡片演示 -->
    <div class="demo-section">
      <h2>📋 纯日历模式</h2>
      <div class="demo-content">
        <EventRecordsPreset
          title="纯日历模式"
          calendar-title="简洁日历视图"
          :records="eventRecords"
          :record-types="recordTypes"
          :color-mapping="colorMapping"
          :label-mapping="labelMapping"
          :show-stats="false"
          @add-click="handleAddClick"
        />
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="event-log-section">
      <h2>📋 事件日志</h2>
      <div class="event-log">
        <div v-if="eventLogs.length === 0" class="no-events">
          暂无事件记录，请与组件交互
        </div>
        <div
          v-for="(log, index) in eventLogs"
          :key="index"
          class="log-item"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Document, Calendar, Bell, Clock, TrendCharts, Star } from '@element-plus/icons-vue'
import EventRecordsPreset from '@/components/presets/EventRecordsPreset.vue'

// 事件日志
const eventLogs = ref([])

// 模拟事件记录数据
const eventRecords = ref([
  {
    id: 1,
    record_type: 'vaccination',
    date: '2024-06-20',
    description: '狂犬病疫苗第一针',
    notes: '接种正常，无不良反应',
    next_due_date: '2024-07-20'
  },
  {
    id: 2,
    record_type: 'checkup',
    date: '2024-06-22',
    description: '常规体检',
    notes: '身体状况良好'
  },
  {
    id: 3,
    record_type: 'medication',
    date: '2024-06-25',
    description: '驱虫药',
    notes: '口服驱虫药一粒'
  },
  {
    id: 4,
    record_type: 'illness',
    date: '2024-06-18',
    description: '轻微感冒',
    notes: '已康复'
  },
  {
    id: 5,
    record_type: 'vaccination',
    date: '2024-06-15',
    description: '疫苗接种',
    notes: '第二针疫苗'
  },
  {
    id: 6,
    record_type: 'deworming',
    date: '2024-06-10',
    description: '体内驱虫',
    notes: '定期驱虫'
  }
])

// 记录类型配置
const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病就诊', color: '#F56C6C' },
  { value: 'medication', label: '用药记录', color: '#909399' },
  { value: 'allergy', label: '过敏记录', color: '#FF5722' },
  { value: 'surgery', label: '手术记录', color: '#9C27B0' },
  { value: 'other', label: '其他', color: '#607D8B' }
])

// 颜色映射
const colorMapping = ref({
  vaccination: 'vaccination',
  deworming: 'deworming',
  checkup: 'checkup',
  illness: 'illness',
  medication: 'medication',
  allergy: 'allergy',
  surgery: 'surgery',
  other: 'other'
})

// 标签映射
const labelMapping = ref({
  vaccination: '疫苗接种',
  deworming: '驱虫',
  checkup: '体检',
  illness: '疾病就诊',
  medication: '用药记录',
  allergy: '过敏记录',
  surgery: '手术记录',
  other: '其他'
})

// 自定义统计项
const customStatsItems = ref([
  {
    key: 'custom-total',
    type: 'total-records',
    icon: Star,
    label: '自定义总数',
    value: 42,
    date: '自定义数据'
  },
  {
    key: 'custom-health',
    type: 'month-records',
    icon: Calendar,
    label: '健康指数',
    value: '优秀',
    date: '本月评估'
  },
  {
    key: 'custom-trend',
    type: 'activity-frequency',
    icon: TrendCharts,
    label: '趋势分析',
    value: '上升',
    date: '数据趋势'
  }
])

// 事件处理方法
const addLog = (event, data = '') => {
  const log = {
    time: new Date().toLocaleTimeString(),
    event,
    data: typeof data === 'object' ? JSON.stringify(data) : data
  }
  eventLogs.value.unshift(log)
  
  // 限制日志数量
  if (eventLogs.value.length > 20) {
    eventLogs.value = eventLogs.value.slice(0, 20)
  }
}

const handleAddClick = () => {
  addLog('点击添加按钮')
}

const handleDateClick = (dateStr, records) => {
  addLog('点击日期', `日期: ${dateStr}, 记录数: ${records.length}`)
}

const handleViewChange = (newView) => {
  addLog('切换日历视图', `新视图: ${newView}`)
}

const handleDateChange = (date) => {
  addLog('日期变化', `新日期: ${date.toLocaleDateString()}`)
}

const handleTypeFilter = (selectedTypes) => {
  addLog('筛选记录类型', `选中类型: ${selectedTypes.join(', ') || '全部'}`)
}

const handleViewModeChange = (newMode) => {
  addLog('切换视图模式', `新模式: ${newMode}`)
}
</script>

<style scoped>
.event-records-demo {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 48px;
}

.demo-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-header p {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
}

.demo-section {
  margin-bottom: 48px;
}

.demo-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f2f5;
}

.demo-content {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

/* 事件日志样式 */
.event-log-section {
  margin-top: 48px;
  padding-top: 24px;
  border-top: 2px solid #f0f2f5;
}

.event-log-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.event-log {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  max-height: 400px;
  overflow-y: auto;
}

.no-events {
  padding: 24px;
  text-align: center;
  color: #909399;
  font-style: italic;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #909399;
  font-family: monospace;
  min-width: 80px;
  flex-shrink: 0;
}

.log-event {
  color: #409EFF;
  font-weight: 500;
  min-width: 120px;
  flex-shrink: 0;
}

.log-data {
  color: #606266;
  flex: 1;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .event-records-demo {
    padding: 16px;
  }

  .demo-header h1 {
    font-size: 24px;
  }

  .demo-section h2 {
    font-size: 20px;
  }

  .demo-content {
    padding: 16px;
  }

  .log-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .log-time,
  .log-event {
    min-width: auto;
  }
}
</style>
