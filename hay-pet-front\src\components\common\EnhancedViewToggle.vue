<template>
  <div 
    class="enhanced-view-toggle" 
    :class="{ small: size === 'small' }" 
    :data-active="activeIndex" 
    ref="toggleRef"
  >
    <div class="toggle-background"></div>
    <div class="toggle-fill" ref="fillRef"></div>
    <div class="toggle-glow" ref="glowRef"></div>
    <button
      v-for="(option, index) in options"
      :key="option.value"
      @click="handleClick(option.value, index, $event)"
      :class="['enhanced-toggle-btn', { active: activeIndex === index }]"
    >
      <span class="btn-content">{{ option.label }}</span>
      <div class="btn-ripple"></div>
    </button>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue'
import { gsap } from 'gsap'

// Props
const props = defineProps({
  options: {
    type: Array,
    required: true,
    validator: (options) => {
      return options.every(option => 
        typeof option === 'object' && 
        option.value !== undefined && 
        option.label !== undefined
      )
    }
  },
  modelValue: {
    type: [String, Number],
    required: true
  },
  size: {
    type: String,
    default: 'normal',
    validator: (value) => ['normal', 'small'].includes(value)
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// Refs
const toggleRef = ref(null)
const fillRef = ref(null)
const glowRef = ref(null)

// Computed
const activeIndex = computed(() => {
  return props.options.findIndex(option => option.value === props.modelValue)
})

// Methods
const createRippleEffect = (button, event) => {
  const ripple = button.querySelector('.btn-ripple')
  const rect = button.getBoundingClientRect()
  const size = Math.max(rect.width, rect.height)
  const x = event.clientX - rect.left - size / 2
  const y = event.clientY - rect.top - size / 2
  
  gsap.set(ripple, {
    width: size,
    height: size,
    x: x,
    y: y,
    scale: 0,
    opacity: 0.6
  })
  
  gsap.to(ripple, {
    scale: 1,
    opacity: 0,
    duration: 0.6,
    ease: "power2.out"
  })
}

const updateFillPosition = (targetIndex) => {
  if (!toggleRef.value || !fillRef.value || targetIndex < 0) return
  
  const buttons = toggleRef.value.querySelectorAll('.enhanced-toggle-btn')
  if (buttons.length === 0) return
  
  let offset = 0
  for (let i = 0; i < targetIndex; i++) {
    offset += buttons[i].offsetWidth
  }
  
  const activeButton = buttons[targetIndex]
  const width = activeButton.offsetWidth
  
  // GSAP 动画：填充背景移动
  gsap.to(fillRef.value, {
    x: offset,
    width: width,
    duration: 0.4,
    ease: "power2.out"
  })
  
  // GSAP 动画：发光效果
  if (glowRef.value) {
    gsap.to(glowRef.value, {
      x: offset,
      width: width,
      duration: 0.4,
      ease: "power2.out"
    })
    
    // 发光脉冲效果
    gsap.fromTo(glowRef.value, 
      { opacity: 0, scale: 0.8 },
      { 
        opacity: 0.3, 
        scale: 1,
        duration: 0.3,
        ease: "power2.out",
        yoyo: true,
        repeat: 1
      }
    )
  }
  
  // 按钮弹跳效果
  gsap.fromTo(activeButton,
    { scale: 0.95 },
    { 
      scale: 1,
      duration: 0.3,
      ease: "back.out(1.7)"
    }
  )
  
  // 文字颜色渐变动画
  buttons.forEach((btn, index) => {
    const content = btn.querySelector('.btn-content')
    if (index === targetIndex) {
      gsap.to(content, {
        color: '#ffffff',
        duration: 0.3,
        ease: "power2.out"
      })
    } else {
      gsap.to(content, {
        color: '#606266',
        duration: 0.3,
        ease: "power2.out"
      })
    }
  })
}

const handleClick = (value, index, event) => {
  if (value === props.modelValue) return
  
  // 创建涟漪效果
  if (event && event.currentTarget) {
    createRippleEffect(event.currentTarget, event)
  }
  
  // 立即更新值
  emit('update:modelValue', value)
  emit('change', value, index)
  
  // 立即更新填充位置
  nextTick(() => {
    updateFillPosition(index)
  })
}

// Lifecycle
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      updateFillPosition(activeIndex.value)
    }, 100)
  })
})

// Watch for external changes
import { watch } from 'vue'
watch(() => props.modelValue, () => {
  nextTick(() => {
    updateFillPosition(activeIndex.value)
  })
})
</script>

<style scoped>
/* 增强的一体化视图切换样式 */
.enhanced-view-toggle {
  position: relative;
  display: flex;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 3px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.enhanced-view-toggle.small {
  padding: 2px;
}

/* 背景层 */
.toggle-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #e8ecf0 100%);
  border-radius: 8px;
}

/* 动态填充背景 */
.toggle-fill {
  position: absolute;
  top: 3px;
  left: 3px;
  height: calc(100% - 6px);
  background: linear-gradient(135deg, #409EFF 0%, #337ECC 100%);
  border-radius: 5px;
  z-index: 1;
  box-shadow: 
    0 2px 8px rgba(64, 158, 255, 0.3),
    0 1px 3px rgba(64, 158, 255, 0.4);
  transform-origin: center;
}

.enhanced-view-toggle.small .toggle-fill {
  top: 2px;
  left: 2px;
  height: calc(100% - 4px);
}

/* 发光效果 */
.toggle-glow {
  position: absolute;
  top: 3px;
  left: 3px;
  height: calc(100% - 6px);
  background: radial-gradient(ellipse at center, rgba(64, 158, 255, 0.4) 0%, transparent 70%);
  border-radius: 5px;
  z-index: 0;
  opacity: 0;
  filter: blur(4px);
}

.enhanced-view-toggle.small .toggle-glow {
  top: 2px;
  left: 2px;
  height: calc(100% - 4px);
}

/* 按钮样式 */
.enhanced-toggle-btn {
  position: relative;
  z-index: 2;
  background: transparent;
  border: none;
  border-radius: 5px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  font-weight: 500;
  flex: 1;
  text-align: center;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.enhanced-view-toggle.small .enhanced-toggle-btn {
  padding: 6px 12px;
  font-size: 12px;
}

.enhanced-toggle-btn:hover {
  transform: translateY(-1px);
}

.enhanced-toggle-btn:active {
  transform: translateY(0);
}

/* 按钮内容 */
.btn-content {
  position: relative;
  z-index: 3;
  color: #606266;
  transition: color 0.3s ease;
}

.enhanced-toggle-btn.active .btn-content {
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 涟漪效果 */
.btn-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  pointer-events: none;
  z-index: 1;
}

/* 悬停效果增强 */
.enhanced-toggle-btn:hover .btn-content {
  color: #409EFF;
}

.enhanced-toggle-btn.active:hover .btn-content {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-toggle-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .enhanced-view-toggle.small .enhanced-toggle-btn {
    padding: 4px 8px;
    font-size: 11px;
  }
}
</style>
