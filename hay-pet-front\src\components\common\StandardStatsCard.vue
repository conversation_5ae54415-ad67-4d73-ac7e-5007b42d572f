<template>
  <el-card 
    :class="[
      'stat-card',
      variant,
      size,
      {
        'loading': loading,
        'error': error,
        'compact': size === 'compact',
        'large': size === 'large'
      }
    ]"
    shadow="hover"
    @click="handleClick"
  >
    <div class="stat-content">
      <div class="stat-icon" :style="iconStyle">
        <el-icon v-if="!loading">
          <component :is="icon" />
        </el-icon>
        <el-icon v-else class="is-loading">
          <Loading />
        </el-icon>
      </div>
      <div class="stat-info">
        <div class="stat-label">{{ label }}</div>
        <div 
          :class="[
            'stat-value',
            { 'updating': isUpdating }
          ]"
        >
          {{ displayValue }}
        </div>
        <div class="stat-date">{{ date }}</div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { Loading } from '@element-plus/icons-vue';

const props = defineProps({
  // 基础属性
  label: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  date: {
    type: String,
    default: ''
  },
  icon: {
    type: [String, Object],
    required: true
  },
  
  // 样式属性
  variant: {
    type: String,
    default: 'default',
    validator: (value) => [
      'default', 'total-records', 'month-records', 
      'pending-reminders', 'recent-activity', 'activity-frequency',
      'primary', 'success', 'warning', 'danger', 'custom'
    ].includes(value)
  },
  size: {
    type: String,
    default: 'normal',
    validator: (value) => ['compact', 'normal', 'large'].includes(value)
  },
  
  // 状态属性
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  },
  
  // 自定义样式
  customGradient: {
    type: String,
    default: ''
  },
  iconColor: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['click', 'update']);

// 内部状态
const isUpdating = ref(false);
const previousValue = ref(props.value);

// 计算属性
const displayValue = computed(() => {
  if (props.loading) return '---';
  if (props.error) return 'Error';
  return props.value;
});

const iconStyle = computed(() => {
  const style = {};
  
  if (props.customGradient) {
    style.background = props.customGradient;
  }
  
  if (props.iconColor) {
    style.color = props.iconColor;
  }
  
  return style;
});

// 监听数值变化，添加更新动画
watch(() => props.value, (newValue, oldValue) => {
  if (newValue !== oldValue && !props.loading) {
    previousValue.value = oldValue;
    isUpdating.value = true;
    
    setTimeout(() => {
      isUpdating.value = false;
      emit('update', { oldValue, newValue });
    }, 300);
  }
});

// 事件处理
const handleClick = (event) => {
  if (props.clickable && !props.loading && !props.error) {
    emit('click', event);
  }
};
</script>

<style scoped>
.stat-card {
  cursor: default;
}

.stat-card.clickable {
  cursor: pointer;
}

.stat-card.clickable:hover {
  transform: translateY(-6px);
}

.stat-card.error .stat-icon {
  background: linear-gradient(135deg, var(--color-danger), #ff6b6b) !important;
}

.stat-card.loading .stat-icon {
  background: linear-gradient(135deg, #f0f0f0, #e0e0e0) !important;
}

.is-loading {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义渐变支持 */
.stat-card.custom .stat-icon {
  background: var(--custom-gradient, var(--gradient-primary));
}
</style>
