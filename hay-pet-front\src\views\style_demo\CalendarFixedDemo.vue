<template>
  <div class="calendar-fixed-demo">
    <div class="demo-header">
      <h1>🔧 修复后的日历视图</h1>
      <p>优化了今天日期的显示效果，修复了悬浮提示框的位置偏移问题</p>
    </div>

    <!-- 修复说明 -->
    <div class="fix-info">
      <h2>🎯 修复内容</h2>
      <div class="fix-grid">
        <div class="fix-item">
          <div class="fix-icon">📅</div>
          <h3>今天日期优化</h3>
          <p>移除了过于夸张的动画效果，采用更自然的渐变背景和圆形数字设计</p>
        </div>
        <div class="fix-item">
          <div class="fix-icon">💬</div>
          <h3>提示框位置修复</h3>
          <p>修复了悬浮提示框的位置偏移，增加了边界检测和智能定位</p>
        </div>
        <div class="fix-item">
          <div class="fix-icon">🎨</div>
          <h3>视觉效果改进</h3>
          <p>优化了提示框样式，增加了箭头指示器和更好的视觉层次</p>
        </div>
      </div>
    </div>

    <!-- 日历演示 -->
    <div class="demo-section">
      <h2>📅 修复后的日历</h2>
      <div class="demo-content">
        <CalendarViewPreset
          title="修复后的日历视图"
          :records="demoRecords"
          :record-types="recordTypes"
          :color-mapping="colorMapping"
          :label-mapping="labelMapping"
          :show-stats="true"
          @date-click="handleDateClick"
        />
      </div>
    </div>

    <!-- 对比说明 -->
    <div class="comparison-section">
      <h2>📊 修复前后对比</h2>
      <div class="comparison-grid">
        <div class="comparison-item">
          <h3>修复前</h3>
          <ul>
            <li>❌ 今天日期有过于夸张的动画边框</li>
            <li>❌ 悬浮提示框位置偏移</li>
            <li>❌ 缺少边界检测</li>
            <li>❌ 提示框样式简单</li>
          </ul>
        </div>
        <div class="comparison-item">
          <h3>修复后</h3>
          <ul>
            <li>✅ 今天日期采用自然的渐变设计</li>
            <li>✅ 提示框位置准确对齐</li>
            <li>✅ 智能边界检测和调整</li>
            <li>✅ 美观的箭头指示器</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 交互日志 -->
    <div class="event-log-section">
      <h2>📋 交互测试</h2>
      <p class="test-instruction">请悬浮在有记录的日期上测试提示框效果，点击日期查看交互日志</p>
      <div class="event-log">
        <div v-if="eventLogs.length === 0" class="no-events">
          暂无交互记录，请点击日历进行测试
        </div>
        <div
          v-for="(log, index) in eventLogs"
          :key="index"
          class="log-item"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CalendarViewPreset from '@/components/presets/CalendarViewPreset.vue'

// 交互日志
const eventLogs = ref([])

// 测试数据 - 重点测试今天和有记录的日期
const demoRecords = ref([
  // 今天的记录
  { id: 1, record_type: 'vaccination', date: '2025-06-20', description: '今天的疫苗接种' },
  { id: 2, record_type: 'checkup', date: '2025-06-20', description: '今天的体检' },
  
  // 其他日期的记录
  { id: 3, record_type: 'medication', date: '2025-06-18', description: '用药记录' },
  { id: 4, record_type: 'illness', date: '2025-06-15', description: '疾病记录' },
  { id: 5, record_type: 'deworming', date: '2025-06-12', description: '驱虫记录' },
  { id: 6, record_type: 'surgery', date: '2025-06-10', description: '手术记录' },
  { id: 7, record_type: 'checkup', date: '2025-06-08', description: '体检记录' },
  { id: 8, record_type: 'vaccination', date: '2025-06-05', description: '疫苗记录' },
  { id: 9, record_type: 'medication', date: '2025-06-03', description: '用药记录' },
  { id: 10, record_type: 'illness', date: '2025-06-01', description: '疾病记录' },
  
  // 多记录日期测试
  { id: 11, record_type: 'vaccination', date: '2025-06-25', description: '疫苗接种1' },
  { id: 12, record_type: 'checkup', date: '2025-06-25', description: '体检记录1' },
  { id: 13, record_type: 'medication', date: '2025-06-25', description: '用药记录1' },
  { id: 14, record_type: 'deworming', date: '2025-06-25', description: '驱虫记录1' },
  { id: 15, record_type: 'surgery', date: '2025-06-25', description: '手术记录1' },
  { id: 16, record_type: 'illness', date: '2025-06-25', description: '疾病记录1' }
])

// 记录类型配置
const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'deworming', label: '驱虫', color: '#E6A23C' },
  { value: 'checkup', label: '体检', color: '#409EFF' },
  { value: 'illness', label: '疾病', color: '#F56C6C' },
  { value: 'medication', label: '用药', color: '#909399' },
  { value: 'surgery', label: '手术', color: '#F56C6C' },
  { value: 'other', label: '其他', color: '#C0C4CC' }
])

// 颜色和标签映射
const colorMapping = ref({
  vaccination: 'vaccination',
  deworming: 'deworming',
  checkup: 'checkup',
  illness: 'illness',
  medication: 'medication',
  surgery: 'surgery',
  other: 'other'
})

const labelMapping = ref({
  vaccination: '疫苗接种',
  deworming: '驱虫',
  checkup: '体检',
  illness: '疾病',
  medication: '用药',
  surgery: '手术',
  other: '其他'
})

// 事件处理
const handleDateClick = (dateStr, records) => {
  const log = {
    time: new Date().toLocaleTimeString(),
    event: '点击日期',
    data: `${dateStr} (${records.length}条记录)`
  }
  eventLogs.value.unshift(log)
  
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10)
  }
}
</script>

<style scoped>
.calendar-fixed-demo {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 48px;
}

.demo-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #67C23A, #409EFF);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.demo-header p {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
}

/* 修复信息 */
.fix-info {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.fix-info h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 24px;
}

.fix-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.fix-item {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  border: 1px solid #e5e7eb;
}

.fix-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.fix-item h3 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
}

.fix-item p {
  color: #606266;
  line-height: 1.5;
  font-size: 14px;
}

/* 演示区域 */
.demo-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.demo-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 24px;
}

.demo-content {
  background: #fafbfc;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

/* 对比说明 */
.comparison-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.comparison-section h2 {
  color: #303133;
  margin-bottom: 24px;
  font-size: 24px;
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.comparison-item {
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.comparison-item:first-child {
  background: linear-gradient(135deg, #fef2f2, #ffffff);
  border-color: #fecaca;
}

.comparison-item:last-child {
  background: linear-gradient(135deg, #f0fdf4, #ffffff);
  border-color: #bbf7d0;
}

.comparison-item h3 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.comparison-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.comparison-item li {
  padding: 6px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 事件日志 */
.event-log-section {
  background: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.event-log-section h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 20px;
}

.test-instruction {
  color: #606266;
  margin-bottom: 16px;
  font-size: 14px;
  font-style: italic;
}

.event-log {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
}

.no-events {
  text-align: center;
  color: #909399;
  font-style: italic;
  padding: 24px;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f2f5;
  font-size: 14px;
  border-radius: 6px;
  margin-bottom: 4px;
  background: #ffffff;
}

.log-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.log-time {
  color: #909399;
  font-family: monospace;
  min-width: 80px;
  flex-shrink: 0;
  font-size: 12px;
}

.log-event {
  color: #67C23A;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.log-data {
  color: #606266;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .calendar-fixed-demo {
    padding: 16px;
  }

  .demo-header h1 {
    font-size: 24px;
  }

  .fix-grid,
  .comparison-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .log-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .log-time,
  .log-event {
    min-width: auto;
  }
}
</style>
