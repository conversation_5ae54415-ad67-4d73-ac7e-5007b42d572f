<template>
  <div class="home">
    <!-- 顶部工具栏 -->
    <div class="home-header">
      <div class="header-actions">
        <!-- 展示模式切换按钮 -->
        <div class="view-mode-toggle">
          <el-tooltip content="卡片视图" placement="bottom">
            <el-button 
              :type="viewMode === 'card' ? 'primary' : ''"
              :plain="viewMode !== 'card'"
              @click="setViewMode('card')"
              size="small"
              circle
            >
              <el-icon><Grid /></el-icon>
            </el-button>
          </el-tooltip>
          <el-tooltip content="简约视图" placement="bottom">
            <el-button 
              :type="viewMode === 'compact' ? 'primary' : ''"
              :plain="viewMode !== 'compact'"
              @click="setViewMode('compact')"
              size="small"
              circle
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>
    
    <!-- Dashboard 区域 -->
    <div class="dashboard-container" :class="`view-mode-${viewMode}`">
      <!-- 动态渲染可见的板块 -->
      <component 
        v-for="block in visibleBlocks" 
        :key="block.id"
        :is="getBlockComponent(block.id)"
        :block-config="block"
        :view-mode="viewMode"
        class="dashboard-block"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onBeforeUnmount } from 'vue';
import { Grid, List } from '@element-plus/icons-vue';
import { useDashboardStore } from '@/stores/dashboard';
import { usePetStore } from '@/stores/pet';
import PetInfoCard from '@/components/dashboard/PetInfoCard.vue';
import WeightTrendCard from '@/components/dashboard/WeightTrendCard.vue';
import ExpenseOverviewCard from '@/components/dashboard/ExpenseOverviewCard.vue';
import RemindersCard from '@/components/dashboard/RemindersCard.vue';
import HealthSummaryCard from '@/components/dashboard/HealthSummaryCard.vue';
import HealthTimelineCard from '@/components/dashboard/HealthTimelineCard.vue';
import ActivityTrackingCard from '@/components/dashboard/ActivityTrackingCard.vue';
import NutritionAnalysisCard from '@/components/dashboard/NutritionAnalysisCard.vue';
import PhotoGalleryCard from '@/components/dashboard/PhotoGalleryCard.vue';

const dashboardStore = useDashboardStore();
const petStore = usePetStore();

// 展示模式状态
const viewMode = ref<'card' | 'compact'>('compact');

// 计算属性
const visibleBlocks = computed(() => dashboardStore.visibleBlocks);

// 设置展示模式
const setViewMode = (mode: 'card' | 'compact') => {
  viewMode.value = mode;
  // 保存到本地存储
  localStorage.setItem('dashboard-view-mode', mode);
};

// 组件映射
const componentMap = {
  'pet-info': PetInfoCard,
  'weight-trend': WeightTrendCard,
  'expense-overview': ExpenseOverviewCard,
  'reminders': RemindersCard,
  'health-summary': HealthSummaryCard,
  'health-timeline': HealthTimelineCard,
  'activity-tracking': ActivityTrackingCard,
  'nutrition-analysis': NutritionAnalysisCard,
  'photo-gallery': PhotoGalleryCard
};

// 获取板块对应的组件
function getBlockComponent(blockId: string) {
  return componentMap[blockId as keyof typeof componentMap] || 'div';
}

// 处理宠物变更事件
const handlePetChanged = async () => {
  await petStore.fetchPets();
};

onMounted(async () => {
  dashboardStore.loadConfig();
  await petStore.fetchPets();
  
  // 加载保存的展示模式
  const savedViewMode = localStorage.getItem('dashboard-view-mode') as 'card' | 'compact';
  if (savedViewMode && ['card', 'compact'].includes(savedViewMode)) {
    viewMode.value = savedViewMode;
  }
  
  // 监听宠物变更事件
  window.addEventListener('petChanged', handlePetChanged, { passive: true });
  window.addEventListener('currentPetChanged', handlePetChanged, { passive: true });
});

onBeforeUnmount(() => {
  window.removeEventListener('petChanged', handlePetChanged);
  window.removeEventListener('currentPetChanged', handlePetChanged);
});
</script>

<style scoped>
.home {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 60px);
}

/* 顶部工具栏样式 */
.home-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 8px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.view-mode-toggle {
  display: flex;
  gap: 8px;
  padding: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-container {
  display: grid;
  gap: 24px;
  animation: fadeInUp 0.6s ease-out;
  transition: all 0.3s ease;
}

/* 卡片视图模式 */
.dashboard-container.view-mode-card {
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* 简约视图模式 */
.dashboard-container.view-mode-compact {
  grid-template-columns: 1fr;
  gap: 12px;
}

.dashboard-block {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

/* 卡片视图模式下的块样式 */
.view-mode-card .dashboard-block {
  border-radius: 16px;
}

.view-mode-card .dashboard-block:hover {
  transform: translateY(-8px);
}

/* 简约视图模式下的块样式 */
.view-mode-compact .dashboard-block {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.view-mode-compact .dashboard-block:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home {
    padding: 16px;
  }
  
  .home-header {
    margin-bottom: 16px;
  }
  
  .dashboard-container.view-mode-card {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .dashboard-container.view-mode-compact {
    gap: 8px;
  }
  
  .view-mode-toggle {
    padding: 2px;
    gap: 4px;
  }
}
</style>