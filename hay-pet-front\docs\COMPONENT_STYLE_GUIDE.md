# Hay!Pet 组件样式预设系统指南

## 📋 概述

本文档介绍 Hay!Pet 项目的标准化组件样式预设系统，旨在确保整个项目中UI组件的视觉一致性和可维护性。

## 🎯 设计目标

- **统一性**：确保所有标签组件使用相同的设计语言
- **可复用性**：避免跨文件复制粘贴样式代码
- **可维护性**：集中管理样式配置，便于后续调整
- **扩展性**：为其他组件类型的标准化奠定基础

## 📁 文件结构

```
src/
├── styles/
│   ├── design-tokens.css           # 设计令牌配置
│   ├── tag-presets.css             # 标签组件预设样式
│   ├── color-picker-presets.css    # 颜色选择器预设样式
│   ├── stats-card-presets.css      # 统计卡片预设样式
│   └── drag-sort-presets.css       # 拖拽排序预设样式
├── components/
│   └── common/
│       ├── StandardTag.vue         # 通用标签组件
│       ├── StandardColorPicker.vue # 通用颜色选择器组件
│       ├── StandardStatsCard.vue   # 通用统计卡片组件
│       └── StandardStatsGrid.vue   # 通用统计网格组件
├── views/
│   └── style_demo/                 # 样式演示系统
│       ├── StyleDemoIndex.vue      # 主展示页面
│       ├── StandardTagDemo.vue     # 标签组件演示
│       ├── ColorPickerDemo.vue     # 颜色选择器演示
│       ├── DesignTokensDemo.vue    # 设计令牌演示
│       └── ButtonPresetsDemo.vue   # 按钮预设演示
└── docs/
    └── COMPONENT_STYLE_GUIDE.md   # 本文档
```

## 🎨 设计令牌系统

### 颜色系统
基于现有项目的渐变色彩方案：

```css
/* 主色调 */
--color-primary: #409EFF;    /* 蓝色 */
--color-success: #67C23A;    /* 绿色 */
--color-warning: #E6A23C;    /* 橙色 */
--color-danger: #F56C6C;     /* 红色 */
--color-info: #909399;       /* 灰色 */
```

### 尺寸系统
```css
/* 标签尺寸 */
tag-xs: 60px × 28px    /* 超小 */
tag-sm: 70px × 32px    /* 小 */
tag-md: 80px × 40px    /* 中等（默认）*/
tag-lg: 100px × 48px   /* 大 */
tag-xl: 120px × 56px   /* 超大 */
```

### 动画系统
```css
/* 过渡时间 */
--duration-fast: 0.15s    /* 快速 */
--duration-base: 0.3s     /* 标准 */
--duration-slow: 0.5s     /* 缓慢 */

/* 缓动函数 */
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1)
```

## 🏷️ StandardTag 组件使用指南

### 基础用法

```vue
<template>
  <!-- 基础标签 -->
  <StandardTag text="基础标签" />
  
  <!-- 不同颜色变体 -->
  <StandardTag text="主色调" variant="primary" />
  <StandardTag text="成功" variant="success" />
  <StandardTag text="警告" variant="warning" />
  <StandardTag text="危险" variant="danger" />
  <StandardTag text="信息" variant="info" />
  
  <!-- 不同尺寸 -->
  <StandardTag text="超小" size="xs" />
  <StandardTag text="小" size="sm" />
  <StandardTag text="中等" size="md" />
  <StandardTag text="大" size="lg" />
  <StandardTag text="超大" size="xl" />
</template>
```

### 交互功能

```vue
<template>
  <!-- 激活状态 -->
  <StandardTag 
    text="激活标签" 
    :active="true" 
    variant="primary" 
  />
  
  <!-- 可编辑和删除 -->
  <StandardTag 
    text="可编辑标签" 
    :editable="true"
    :deletable="true"
    @edit="handleEdit"
    @delete="handleDelete"
  />
  
  <!-- 带图标 -->
  <StandardTag 
    text="带图标" 
    left-icon="Star"
    right-icon="ArrowRight"
  />
  
  <!-- 禁用状态 -->
  <StandardTag 
    text="禁用标签" 
    :disabled="true" 
  />
  
  <!-- 加载状态 -->
  <StandardTag 
    text="加载中" 
    :loading="true" 
  />
</template>
```

### 自定义样式

```vue
<template>
  <!-- 自定义颜色 -->
  <StandardTag 
    text="自定义颜色" 
    color="#ff6b6b"
    background-color="rgba(255, 107, 107, 0.1)"
    border-color="rgba(255, 107, 107, 0.3)"
  />
</template>
```

## 📝 Props 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | String | '' | 标签文本内容 |
| variant | String | 'primary' | 颜色变体：primary/success/warning/danger/info |
| size | String | 'md' | 尺寸：xs/sm/md/lg/xl |
| active | Boolean | false | 是否激活状态 |
| clickable | Boolean | true | 是否可点击 |
| disabled | Boolean | false | 是否禁用 |
| loading | Boolean | false | 是否加载中 |
| editable | Boolean | false | 是否可编辑 |
| deletable | Boolean | false | 是否可删除 |
| draggable | Boolean | false | 是否可拖拽 |
| leftIcon | String/Object | null | 左侧图标 |
| rightIcon | String/Object | null | 右侧图标 |
| tooltip | String | '' | 提示文本 |

## 🎭 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | event | 点击标签时触发 |
| edit | event | 点击编辑按钮时触发 |
| delete | event | 点击删除按钮时触发 |
| mouseenter | event | 鼠标进入时触发 |
| mouseleave | event | 鼠标离开时触发 |

## 🎨 样式预设类

### 直接使用CSS类

```html
<!-- 基础标签 -->
<div class="tag-base tag-primary tag-clickable">标签文本</div>

<!-- 组合类 -->
<div class="tag-primary-sm">小号主色调标签</div>
<div class="tag-success-md">中号成功标签</div>
<div class="tag-warning-lg">大号警告标签</div>
```

### 工具类

```html
<!-- 快速应用样式 -->
<div class="tag">默认标签</div>
<div class="tag-static">静态标签（不可点击）</div>
```

## 📱 响应式设计

标签组件自动适配不同屏幕尺寸：

- **桌面端**：完整尺寸和功能
- **平板端**：适当缩小尺寸
- **手机端**：进一步优化尺寸和间距

## 🔧 最佳实践

### 1. 选择合适的变体
```vue
<!-- ✅ 推荐：根据语义选择变体 -->
<StandardTag text="疫苗接种" variant="success" />
<StandardTag text="驱虫" variant="warning" />
<StandardTag text="过敏" variant="danger" />

<!-- ❌ 不推荐：随意选择颜色 -->
<StandardTag text="疫苗接种" variant="danger" />
```

### 2. 保持尺寸一致性
```vue
<!-- ✅ 推荐：同一区域使用相同尺寸 -->
<div class="tag-group">
  <StandardTag text="标签1" size="md" />
  <StandardTag text="标签2" size="md" />
  <StandardTag text="标签3" size="md" />
</div>
```

### 3. 合理使用交互功能
```vue
<!-- ✅ 推荐：根据实际需求启用功能 -->
<StandardTag 
  text="用户标签" 
  :editable="userCanEdit"
  :deletable="userCanDelete"
/>
```

## 🚀 迁移指南

### 从现有标签组件迁移

1. **替换基础标签**：
```vue
<!-- 旧代码 -->
<div class="type-tag clickable-tag">标签文本</div>

<!-- 新代码 -->
<StandardTag text="标签文本" />
```

2. **迁移样式配置**：
```vue
<!-- 旧代码 -->
<div 
  class="type-tag"
  :class="{ 'is-active': isActive }"
  :style="{ background: customColor }"
>
  标签文本
</div>

<!-- 新代码 -->
<StandardTag 
  text="标签文本"
  :active="isActive"
  :background-color="customColor"
/>
```

## 🎨 StandardColorPicker 颜色选择器组件

### 基础用法

```vue
<template>
  <StandardColorPicker
    v-model="selectedColor"
    title="选择颜色"
    @change="handleColorChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import StandardColorPicker from '@/components/common/StandardColorPicker.vue'

const selectedColor = ref('#409EFF')

const handleColorChange = (color) => {
  console.log('颜色已更改为:', color)
}
</script>
```

### 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | String | '#409EFF' | 当前选中的颜色值 |
| title | String | '颜色选择' | 选择器标题 |
| size | String | 'md' | 按钮尺寸：sm/md/lg/xl |
| columns | Number | 6 | 网格列数：4/5/6/8 |
| variant | String | 'standard' | 主题变体：standard/compact/minimal |
| showHeader | Boolean | true | 是否显示标题区域 |
| showAlpha | Boolean | false | 是否支持透明度 |
| required | Boolean | false | 是否显示必填标记 |
| disabled | Boolean | false | 是否禁用 |
| predefinedColors | Array | 默认色板 | 预设颜色数组 |

### 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | color: string | 颜色值变化时触发 |
| change | color: string | 颜色改变时触发 |
| select | color: string | 选择预设颜色时触发 |

### 组件方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| selectColor | color: string | 程序化选择颜色 |
| getCurrentColor | - | 获取当前颜色值 |
| resetColor | - | 重置为默认颜色 |

### 样式预设类

```css
/* 基础类 */
.color-picker-container    /* 颜色选择器容器 */
.color-selection-area      /* 颜色选择区域 */
.unified-color-grid        /* 颜色网格布局 */
.color-button              /* 颜色按钮基础样式 */
.selected-indicator        /* 选中状态指示器 */

/* 尺寸变体 */
.color-button-sm          /* 小号颜色按钮 */
.color-button-md          /* 中号颜色按钮（默认） */
.color-button-lg          /* 大号颜色按钮 */
.color-button-xl          /* 超大颜色按钮 */

/* 布局变体 */
.color-grid-4             /* 4列网格布局 */
.color-grid-5             /* 5列网格布局 */
.color-grid-6             /* 6列网格布局（默认） */
.color-grid-8             /* 8列网格布局 */

/* 主题变体 */
.color-picker-compact     /* 紧凑主题 */
.color-picker-minimal     /* 极简主题 */

/* 工具类 */
.color-picker             /* 快速应用颜色选择器 */
.color-grid               /* 快速应用颜色网格 */
.color-btn                /* 快速应用颜色按钮 */
```

## 📊 StandardStatsCard 统计卡片组件

### 基础用法

```vue
<template>
  <!-- 单个统计卡片 -->
  <StandardStatsCard
    label="总记录数"
    :value="42"
    date="全部记录"
    :icon="Document"
    variant="total-records"
  />

  <!-- 统计卡片网格 -->
  <StandardStatsGrid
    :stats-items="statsData"
    :clickable="true"
    @card-click="handleCardClick"
  />
</template>

<script setup>
import { Document, Calendar, Bell, Clock, TrendCharts } from '@element-plus/icons-vue'
import StandardStatsCard from '@/components/common/StandardStatsCard.vue'
import StandardStatsGrid from '@/components/common/StandardStatsGrid.vue'

const statsData = [
  {
    key: 'total',
    label: '总记录数',
    value: 42,
    date: '全部记录',
    icon: Document,
    variant: 'total-records'
  },
  {
    key: 'monthly',
    label: '本月记录',
    value: 8,
    date: '2024年6月',
    icon: Calendar,
    variant: 'month-records'
  }
]

const handleCardClick = ({ item, index }) => {
  console.log('点击了统计卡片:', item.label)
}
</script>
```

### 预设主题

```vue
<template>
  <!-- 预设主题 -->
  <StandardStatsCard variant="total-records" />
  <StandardStatsCard variant="month-records" />
  <StandardStatsCard variant="pending-reminders" />
  <StandardStatsCard variant="recent-activity" />
  <StandardStatsCard variant="activity-frequency" />

  <!-- 通用主题 -->
  <StandardStatsCard variant="primary" />
  <StandardStatsCard variant="success" />
  <StandardStatsCard variant="warning" />
  <StandardStatsCard variant="danger" />

  <!-- 自定义主题 -->
  <StandardStatsCard
    variant="custom"
    custom-gradient="linear-gradient(135deg, #ff6b6b, #feca57)"
  />
</template>
```

### 尺寸变体

```vue
<template>
  <!-- 紧凑尺寸 -->
  <StandardStatsCard size="compact" />

  <!-- 正常尺寸 -->
  <StandardStatsCard size="normal" />

  <!-- 大尺寸 -->
  <StandardStatsCard size="large" />
</template>
```

### 状态管理

```vue
<template>
  <!-- 加载状态 -->
  <StandardStatsCard :loading="true" />

  <!-- 错误状态 -->
  <StandardStatsCard :error="true" />

  <!-- 可点击 -->
  <StandardStatsCard
    :clickable="true"
    @click="handleClick"
  />
</template>
```

## 📅 CalendarViewPreset 日历视图预设组件

### 概述

`CalendarViewPreset` 是一个完整的日历视图组件，支持月视图、周视图、年视图，包含记录指示器、悬浮提示和响应式设计。该组件完全复刻了事件记录页面的日历功能，并提供了高度的可配置性。

### 基础用法

```vue
<template>
  <CalendarViewPreset
    title="宠物健康记录日历"
    :records="healthRecords"
    :record-types="recordTypes"
    :color-mapping="colorMapping"
    :label-mapping="labelMapping"
    @date-click="handleDateClick"
    @view-change="handleViewChange"
    @date-change="handleDateChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'
import CalendarViewPreset from '@/components/presets/CalendarViewPreset.vue'

const healthRecords = ref([
  {
    id: 1,
    record_type: 'vaccination',
    date: '2024-06-15',
    description: '狂犬病疫苗接种'
  },
  {
    id: 2,
    record_type: 'checkup',
    date: '2024-06-10',
    description: '常规体检'
  }
])

const recordTypes = ref([
  { value: 'vaccination', label: '疫苗接种', color: '#67C23A' },
  { value: 'checkup', label: '体检', color: '#409EFF' }
])

const colorMapping = reactive({
  vaccination: 'vaccination',
  checkup: 'checkup'
})

const labelMapping = reactive({
  vaccination: '疫苗接种',
  checkup: '体检'
})

const handleDateClick = (dateStr, records) => {
  console.log('点击日期:', dateStr, '记录:', records)
}

const handleViewChange = (newView) => {
  console.log('视图切换:', newView)
}

const handleDateChange = (newDate) => {
  console.log('日期变化:', newDate)
}
</script>
```

### 自定义字段映射

```vue
<template>
  <CalendarViewPreset
    title="事件记录日历"
    :records="eventRecords"
    record-id-field="event_id"
    record-type-field="event_type"
    record-date-field="event_date"
    record-desc-field="event_description"
    :color-mapping="eventColorMapping"
    :label-mapping="eventLabelMapping"
    initial-view="week"
    @date-click="handleEventDateClick"
  />
</template>

<script setup>
const eventRecords = ref([
  {
    event_id: 'evt_1',
    event_type: 'meeting',
    event_date: '2024-06-20',
    event_description: '团队会议'
  }
])

const eventColorMapping = reactive({
  meeting: 'checkup',
  training: 'vaccination'
})

const eventLabelMapping = reactive({
  meeting: '会议',
  training: '培训'
})
</script>
```

### Props 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | '日历视图' | 日历标题 |
| records | Array | [] | 记录数据数组 |
| recordIdField | String | 'id' | 记录ID字段名 |
| recordTypeField | String | 'record_type' | 记录类型字段名 |
| recordDateField | String | 'date' | 记录日期字段名 |
| recordDescField | String | 'description' | 记录描述字段名 |
| recordTypes | Array | [] | 记录类型配置数组 |
| colorMapping | Object | {} | 颜色映射配置 |
| labelMapping | Object | {} | 标签映射配置 |
| initialView | String | 'month' | 初始视图模式：month/week/year |
| initialDate | Date | new Date() | 初始日期 |

### 事件说明

| 事件名 | 参数 | 说明 |
|--------|------|------|
| date-click | (dateStr: string, records: Array) | 点击日期时触发 |
| view-change | (newView: string) | 日历视图切换时触发 |
| date-change | (newDate: Date) | 选中日期变化时触发 |
| month-change | (month: number) | 月份变化时触发 |
| year-change | (year: number) | 年份变化时触发 |

### 特性功能

#### 1. 三种视图模式
- **月视图**：显示整月日历，支持记录指示器和悬浮提示
- **周视图**：显示一周日历，适合查看详细的每日记录
- **年视图**：显示全年月份，显示每月记录统计

#### 2. 记录指示器
- 在日历单元格中显示彩色圆点，表示当日有记录
- 支持最多3个记录类型的圆点显示
- 超过3个记录时显示"+N"更多指示器

#### 3. 悬浮提示
- 鼠标悬浮在有记录的日期上时显示详细信息
- 显示记录类型标签和描述信息
- 支持最多5条记录的预览，超出显示"还有N条记录"

#### 4. 导航控制
- 支持上一月/下一月、上一周/下一周、上一年/下一年导航
- 提供"今天"按钮快速回到当前日期
- 年份和月份下拉选择器

#### 5. 响应式设计
- 自动适配桌面、平板、手机等不同设备
- 移动端优化布局和交互体验

### 样式定制

#### 记录类型样式映射

组件内置了以下记录类型的样式：

```css
.record-dot.vaccination { background-color: #67C23A; } /* 疫苗接种 - 绿色 */
.record-dot.deworming { background-color: #E6A23C; }   /* 驱虫 - 橙色 */
.record-dot.checkup { background-color: #409EFF; }     /* 体检 - 蓝色 */
.record-dot.illness { background-color: #F56C6C; }     /* 疾病 - 红色 */
.record-dot.medication { background-color: #909399; }  /* 用药 - 灰色 */
.record-dot.surgery { background-color: #F56C6C; }     /* 手术 - 红色 */
.record-dot.other { background-color: #C0C4CC; }       /* 其他 - 浅灰 */
```

#### 自定义样式

可以通过 `colorMapping` 属性自定义记录类型的样式映射：

```javascript
const colorMapping = reactive({
  meeting: 'checkup',      // 会议使用体检样式（蓝色）
  training: 'vaccination', // 培训使用疫苗样式（绿色）
  deadline: 'illness'      // 截止日期使用疾病样式（红色）
})
```

### 集成示例

#### 在事件记录页面中使用

```vue
<template>
  <div class="health-records-view">
    <!-- 日历面板 -->
    <CalendarViewPreset
      title="健康记录日历"
      :records="eventRecords"
      :record-types="recordTypes"
      :color-mapping="colorMapping"
      :label-mapping="labelMapping"
      @date-click="handleCalendarDateClick"
      @view-change="handleCalendarViewChange"
    />

    <!-- 其他内容 -->
  </div>
</template>

<script setup>
// 使用现有的事件记录数据和配置
const handleCalendarDateClick = (dateStr, records) => {
  // 处理日期点击，可以筛选显示当日记录
  selectedDateForFilter.value = dateStr
  isDateFiltered.value = true
}

const handleCalendarViewChange = (newView) => {
  // 处理视图切换
  console.log('日历视图切换到:', newView)
}
</script>
```

#### 在提醒页面中使用

```vue
<template>
  <div class="reminders-view">
    <!-- 提醒日历 -->
    <CalendarViewPreset
      title="提醒日历"
      :records="reminders"
      record-id-field="reminder_id"
      record-type-field="category"
      record-date-field="due_date"
      record-desc-field="content"
      :color-mapping="reminderColorMapping"
      :label-mapping="reminderLabelMapping"
      initial-view="month"
      @date-click="handleReminderDateClick"
    />
  </div>
</template>
```

### 最佳实践

#### 1. 数据结构规范
确保记录数据包含必要的字段：
```javascript
const records = [
  {
    id: 1,                    // 唯一标识
    record_type: 'vaccination', // 记录类型
    date: '2024-06-15',       // 日期（YYYY-MM-DD格式）
    description: '疫苗接种'    // 描述信息
  }
]
```

#### 2. 颜色映射配置
建议为不同的记录类型配置语义化的颜色：
```javascript
const colorMapping = {
  vaccination: 'vaccination', // 疫苗 - 绿色（健康）
  illness: 'illness',         // 疾病 - 红色（警告）
  checkup: 'checkup',         // 体检 - 蓝色（信息）
  medication: 'medication'    // 用药 - 灰色（中性）
}
```

#### 3. 事件处理
合理处理日历事件，提供良好的用户体验：
```javascript
const handleDateClick = (dateStr, records) => {
  if (records.length > 0) {
    // 有记录时的处理逻辑
    showRecordDetails(records)
  } else {
    // 无记录时的处理逻辑
    showAddRecordDialog(dateStr)
  }
}
```

## 🔮 未来扩展

该预设系统为后续其他组件类型的标准化奠定了基础：

- **表单组件预设**
- **卡片组件预设**
- **导航组件预设**
- **图表组件预设**

## 🎯 快速开始

### 1. 引入样式文件
确保在 `src/style.css` 中已引入设计令牌和预设样式：

```css
@import './styles/design-tokens.css';
@import './styles/tag-presets.css';
@import './styles/color-picker-presets.css';
@import './styles/stats-card-presets.css';
```

### 2. 使用预设组件
```vue
<template>
  <!-- 使用标准标签组件 -->
  <StandardTag text="示例标签" variant="primary" />

  <!-- 使用颜色选择器 -->
  <StandardColorPicker v-model="color" />

  <!-- 使用统计卡片 -->
  <StandardStatsCard label="总数" :value="100" />
</template>
```

## 🔄 拖拽排序组件

### 概述

拖拽排序组件提供了一套完整的拖拽排序解决方案，包括视觉反馈、数据库更新和错误处理机制。

### 基础用法

```vue
<template>
  <draggable
    v-model="items"
    v-bind="dragOptions"
    @start="onDragStart"
    @end="onDragEnd"
    item-key="id"
    class="draggable-container"
  >
    <template #item="{ element: item }">
      <StandardTag
        :text="item.label"
        :draggable="true"
        :class="{ 'is-dragging': isDragging }"
        @click="handleItemClick(item)"
      />
    </template>
  </draggable>
</template>

<script setup>
import { ref } from 'vue'
import draggable from 'vuedraggable'
import StandardTag from '@/components/common/StandardTag.vue'

const items = ref([
  { id: 1, label: '项目1', sort_order: 1 },
  { id: 2, label: '项目2', sort_order: 2 },
  { id: 3, label: '项目3', sort_order: 3 }
])

const isDragging = ref(false)

const dragOptions = {
  animation: 300,
  group: 'items',
  disabled: false,
  ghostClass: 'ghost'
}

const onDragStart = () => {
  isDragging.value = true
}

const onDragEnd = async (evt) => {
  isDragging.value = false

  if (evt.oldIndex === evt.newIndex) {
    return
  }

  try {
    // 更新排序
    const updates = items.value.map((item, index) => ({
      id: item.id,
      sort_order: index + 1
    }))

    // 批量更新数据库
    for (const update of updates) {
      await updateItemOrder(update.id, update.sort_order)
    }

    ElMessage.success('排序已保存')
  } catch (error) {
    console.error('保存排序失败:', error)
    ElMessage.error('保存排序失败')
    // 重新获取数据以恢复原始顺序
    await fetchItems()
  }
}
</script>
```

### 样式预设类

```css
/* 拖拽容器 */
.draggable-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

/* 可拖拽元素 */
.draggable-tag {
  cursor: move;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
}

/* 拖拽状态 */
.draggable-tag.is-dragging {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  cursor: grabbing;
}

/* 幽灵元素 */
.ghost {
  opacity: 0.5;
  background: linear-gradient(135deg, #c8ebfb 0%, #e3f2fd 100%) !important;
  border: 2px dashed #409EFF !important;
  transform: scale(0.95);
  color: #409EFF !important;
}

/* 选中状态 */
.sortable-chosen {
  opacity: 0.8;
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  z-index: 999;
}

/* 拖拽中 */
.sortable-drag {
  opacity: 0.8;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}
```

### 高级功能

#### 1. 网格布局拖拽

```vue
<template>
  <draggable
    v-model="gridItems"
    v-bind="dragOptions"
    class="draggable-grid"
    @start="onDragStart"
    @end="onDragEnd"
  >
    <template #item="{ element: item }">
      <div class="draggable-grid-item">
        <StandardStatsCard
          :label="item.label"
          :value="item.value"
          :variant="item.variant"
        />
      </div>
    </template>
  </draggable>
</template>

<style>
.draggable-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.draggable-grid-item.is-dragging {
  opacity: 0.8;
  transform: scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}
</style>
```

#### 2. 列表拖拽

```vue
<template>
  <draggable
    v-model="listItems"
    v-bind="dragOptions"
    tag="ul"
    class="sortable-list"
    @start="onDragStart"
    @end="onDragEnd"
  >
    <template #item="{ element: item }">
      <li class="sortable-list-item">
        <div class="drag-handle">
          <el-icon><Rank /></el-icon>
        </div>
        <span>{{ item.title }}</span>
      </li>
    </template>
  </draggable>
</template>

<style>
.sortable-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sortable-list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.sortable-list-item.is-dragging {
  opacity: 0.8;
  transform: rotate(2deg) scale(1.02);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.drag-handle {
  cursor: grab;
  color: #909399;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.drag-handle:hover {
  color: #409EFF;
  background: rgba(64, 158, 255, 0.1);
}

.drag-handle:active {
  cursor: grabbing;
}
</style>
```

### 移动端优化

```css
/* 移动端拖拽优化 */
@media (max-width: 768px) {
  .draggable-container {
    gap: 8px;
  }

  .draggable-tag {
    touch-action: none; /* 防止移动端滚动冲突 */
  }

  .draggable-tag.is-dragging {
    transform: rotate(3deg) scale(1.03);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  .ghost {
    transform: scale(0.98);
  }

  .sortable-chosen {
    transform: scale(1.03);
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.25);
  }
}
```

### 错误处理和回滚

```javascript
const onDragEnd = async (evt) => {
  isDragging.value = false

  if (evt.oldIndex === evt.newIndex) {
    return
  }

  // 保存原始顺序用于回滚
  const originalOrder = [...items.value]

  try {
    // 显示加载状态
    isUpdating.value = true

    // 更新排序
    const updates = items.value.map((item, index) => ({
      id: item.id,
      sort_order: index + 1
    }))

    // 批量更新数据库
    await Promise.all(
      updates.map(update =>
        updateItemOrder(update.id, update.sort_order)
      )
    )

    ElMessage.success('排序已保存')
  } catch (error) {
    console.error('保存排序失败:', error)
    ElMessage.error('保存排序失败，已恢复原始顺序')

    // 回滚到原始顺序
    items.value = originalOrder
  } finally {
    isUpdating.value = false
  }
}
```

### 最佳实践

#### 1. 性能优化
```javascript
// 使用防抖避免频繁更新
import { debounce } from 'lodash-es'

const debouncedUpdate = debounce(async (updates) => {
  await batchUpdateOrder(updates)
}, 300)
```

#### 2. 用户体验
```vue
<template>
  <!-- 添加拖拽提示 -->
  <div v-if="isDragging" class="drag-feedback show">
    正在调整顺序...
  </div>

  <!-- 加载状态 -->
  <div v-if="isUpdating" class="drag-loading">
    <!-- 拖拽容器内容 -->
  </div>
</template>
```

#### 3. 无障碍支持
```vue
<template>
  <draggable
    v-model="items"
    v-bind="dragOptions"
    role="list"
    aria-label="可拖拽排序的项目列表"
  >
    <template #item="{ element: item }">
      <div
        role="listitem"
        :aria-label="`项目 ${item.label}，位置 ${item.sort_order}`"
        tabindex="0"
        @keydown="handleKeyboardSort"
      >
        {{ item.label }}
      </div>
    </template>
  </draggable>
</template>
```

### 工具类

```css
/* 快速应用拖拽功能 */
.draggable           /* 基础拖拽容器 */
.draggable-item      /* 可拖拽项目 */
.drag-handle         /* 拖拽手柄 */
.drag-zone           /* 拖拽区域 */
.drag-feedback       /* 拖拽反馈提示 */
.drag-loading        /* 拖拽加载状态 */

/* 状态类 */
.is-dragging         /* 拖拽中状态 */
.drag-disabled       /* 禁用拖拽 */
.drag-over           /* 拖拽悬停 */
```

## 🎯 快速开始

### 1. 引入样式文件
确保在 `src/style.css` 中已引入设计令牌和预设样式：

```css
@import './styles/design-tokens.css';
@import './styles/tag-presets.css';
@import './styles/color-picker-presets.css';
@import './styles/stats-card-presets.css';
@import './styles/drag-sort-presets.css';
```

### 2. 安装依赖
```bash
npm install vuedraggable
```

### 3. 使用预设组件
```vue
<template>
  <!-- 标签组件 -->
  <StandardTag
    text="示例标签"
    variant="primary"
    @click="handleClick"
  />

  <!-- 颜色选择器组件 -->
  <StandardColorPicker
    v-model="selectedColor"
    title="选择颜色"
    @change="handleColorChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import StandardTag from '@/components/common/StandardTag.vue'
import StandardColorPicker from '@/components/common/StandardColorPicker.vue'

const selectedColor = ref('#409EFF')

const handleClick = () => {
  console.log('标签被点击')
}

const handleColorChange = (color) => {
  console.log('颜色已更改为:', color)
}
</script>
```

### 3. 查看完整示例
访问 `/style_demo` 路由查看所有组件样式的完整展示和使用指南。

#### 样式演示系统
- **主页面**: `/style_demo` - 预设样式系统概览
- **标签组件**: `/style_demo/standard-tag` - StandardTag 组件演示
- **颜色选择器**: `/style_demo/color-picker` - StandardColorPicker 组件演示
- **设计令牌**: `/style_demo/design-tokens` - 设计变量系统展示
- **按钮预设**: `/style_demo/button-presets` - 按钮样式预设展示

## 📞 支持与反馈

如有问题或建议，请：
1. 查看本文档的相关章节
2. 检查 `design-tokens.css` 中的变量定义
3. 参考 `StandardTag.vue` 的实现代码
4. 访问 `ComponentShowcaseView.vue` 查看使用示例
5. 提交 Issue 或 PR 进行改进

## 📈 版本历史

- **v1.2.0** - 颜色选择器组件系统
  - 新增 StandardColorPicker 组件
  - 新增颜色选择器预设样式系统
  - 完善样式演示系统 (`/style_demo`)
  - 优化选中状态动画效果
  - 支持多种尺寸、布局和主题变体

- **v1.1.0** - 添加按钮预设扩展
  - 扩展标签组件功能
  - 新增添加按钮样式预设
  - 支持多种动画效果和颜色变体
  - 完善工具类系统

- **v1.0.0** - 初始版本，包含基础标签组件预设系统
  - 基于 health-records 面板标签组件的设计模式
  - 支持5种颜色变体、5种尺寸、完整交互功能
  - 包含设计令牌系统和响应式设计
