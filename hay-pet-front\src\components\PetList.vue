<template>
  <div class="pet-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>我的宠物</span>
          <el-button type="primary" @click="$emit('refresh')" :icon="Refresh">
            刷新
          </el-button>
        </div>
      </template>
      
      <!-- 空状态 -->
      <el-empty v-if="pets.length === 0" description="还没有添加宠物，快去添加第一只吧！">
        <el-button type="primary" @click="$emit('add-pet')">添加宠物</el-button>
      </el-empty>
      
      <!-- 宠物列表 -->
      <div v-else>
        <!-- 表格视图 -->
        <el-table :data="pets" style="width: 100%" stripe>
          <el-table-column label="头像" width="80">
            <template #default="scope">
              <el-avatar
                :size="50"
                :src="scope.row.avatar_url"
                :icon="UserFilled"
                shape="square"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="name" label="姓名" width="120">
            <template #default="scope">
              <el-link @click="showPetDetail(scope.row)" type="primary">
                {{ scope.row.name }}
              </el-link>
            </template>
          </el-table-column>
          
          <el-table-column prop="species" label="品种" width="150" />
          
          <el-table-column prop="age" label="年龄" width="80">
            <template #default="scope">
              {{ scope.row.age }}岁
            </template>
          </el-table-column>
          
          <el-table-column prop="gender" label="性别" width="80">
            <template #default="scope">
              <el-tag :type="getGenderTagType(scope.row.gender)">
                {{ getGenderText(scope.row.gender) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="最新体重" width="100">
            <template #default="scope">
              <span v-if="scope.row.latest_weight">
                {{ scope.row.latest_weight }}kg
              </span>
              <span v-else class="no-data">未记录</span>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="添加时间" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="280">
            <template #default="scope">
              <el-button
                size="small"
                @click="showPetDetail(scope.row)"
                :icon="View"
              >
                详情
              </el-button>
              <el-button
                size="small"
                @click="editPet(scope.row)"
                :icon="Edit"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                @click="showPhotoGallery(scope.row)"
                :icon="Picture"
                type="success"
              >
                相册
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deletePet(scope.row)"
                :icon="Delete"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <!-- 宠物编辑对话框 -->
    <PetEditDialog
      v-model:visible="editDialogVisible"
      :pet="selectedPet"
      @pet-updated="handlePetUpdated"
    />
    
    <!-- 照片相册对话框 -->
    <PetPhotoGallery
      v-model:visible="photoGalleryVisible"
      :pet="selectedPet"
    />
    
    <!-- 宠物详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="selectedPet?.name + ' 的详细信息'"
      width="600px"
    >
      <div v-if="selectedPet" class="pet-detail">
        <div class="pet-info">
          <div class="avatar-section">
            <el-avatar
              :size="120"
              :src="selectedPet.avatar_url"
              :icon="UserFilled"
              shape="square"
            />
          </div>
          <div class="info-section">
            <p><strong>姓名：</strong>{{ selectedPet.name }}</p>
            <p><strong>品种：</strong>{{ selectedPet.species }}</p>
            <p><strong>年龄：</strong>{{ selectedPet.age }}岁</p>
            <p><strong>性别：</strong>{{ getGenderText(selectedPet.gender) }}</p>
            <p><strong>添加时间：</strong>{{ formatDate(selectedPet.created_at) }}</p>
            <p v-if="selectedPet.notes"><strong>备注：</strong>{{ selectedPet.notes }}</p>
          </div>
        </div>
        
        <!-- 体重记录 -->
        <div class="weight-section">
          <h4>体重记录</h4>
          <el-table :data="weightRecords" size="small">
            <el-table-column prop="record_date" label="日期" width="120" />
            <el-table-column prop="weight" label="体重(kg)" width="100" />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button
                  size="small"
                  type="danger"
                  @click="deleteWeight(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 添加体重记录 -->
          <div class="add-weight">
            <el-form :inline="true" style="margin-top: 15px;">
              <el-form-item label="日期">
                <el-date-picker
                  v-model="newWeight.date"
                  type="date"
                  placeholder="选择日期"
                  style="width: 150px;"
                />
              </el-form-item>
              <el-form-item label="体重">
                <el-input-number
                  v-model="newWeight.weight"
                  :min="0"
                  :max="200"
                  :precision="2"
                  placeholder="体重"
                  style="width: 120px;"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="addWeight">添加</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, inject } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, View, Edit, Delete, UserFilled, Picture } from '@element-plus/icons-vue'
import PetEditDialog from './PetEditDialog.vue'
import PetPhotoGallery from './PetPhotoGallery.vue'

const props = defineProps({
  pets: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['refresh', 'add-pet'])

const supabase = inject('supabase')
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const photoGalleryVisible = ref(false)
const selectedPet = ref(null)
const weightRecords = ref([])
const newWeight = ref({
  date: new Date(),
  weight: null
})

// 性别标签类型
const getGenderTagType = (gender) => {
  switch (gender) {
    case 'male': return 'primary'
    case 'female': return 'success'
    default: return 'info'
  }
}

// 性别文本
const getGenderText = (gender) => {
  switch (gender) {
    case 'male': return '公'
    case 'female': return '母'
    default: return '未知'
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 显示宠物详情
const showPetDetail = async (pet) => {
  selectedPet.value = pet
  detailDialogVisible.value = true
  await loadWeightRecords(pet.id)
}

// 加载体重记录
const loadWeightRecords = async (petId) => {
  const { data, error } = await supabase
    .from('weight_records')
    .select('*')
    .eq('pet_id', petId)
    .order('record_date', { ascending: false })
  
  if (error) {
    console.error('加载体重记录失败:', error)
    weightRecords.value = []
  } else {
    weightRecords.value = data || []
  }
}

// 添加体重记录
const addWeight = async () => {
  if (!newWeight.value.weight || !newWeight.value.date) {
    ElMessage.warning('请填写完整的体重信息')
    return
  }
  
  const weightData = {
    pet_id: selectedPet.value.id,
    weight: newWeight.value.weight,
    record_date: new Date(newWeight.value.date).toISOString().split('T')[0]
  }
  
  const { error } = await supabase
    .from('weight_records')
    .insert([weightData])
  
  if (error) {
    ElMessage.error(`添加体重记录失败: ${error.message}`)
  } else {
    ElMessage.success('体重记录添加成功')
    newWeight.value = { date: new Date(), weight: null }
    await loadWeightRecords(selectedPet.value.id)
    emit('refresh')
  }
}

// 删除体重记录
const deleteWeight = async (weight) => {
  try {
    await ElMessageBox.confirm('确定要删除这条体重记录吗？', '确认删除', {
      type: 'warning'
    })
    
    const { error } = await supabase
      .from('weight_records')
      .delete()
      .eq('id', weight.id)
    
    if (error) {
      ElMessage.error(`删除失败: ${error.message}`)
    } else {
      ElMessage.success('删除成功')
      await loadWeightRecords(selectedPet.value.id)
      emit('refresh')
    }
  } catch {
    // 用户取消删除
  }
}

// 编辑宠物
const editPet = (pet) => {
  selectedPet.value = pet
  editDialogVisible.value = true
}

// 处理宠物更新
const handlePetUpdated = () => {
  emit('refresh')
  editDialogVisible.value = false
}

// 显示照片相册
const showPhotoGallery = (pet) => {
  selectedPet.value = pet
  photoGalleryVisible.value = true
}

// 删除宠物
const deletePet = async (pet) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除宠物 "${pet.name}" 吗？这将同时删除所有相关记录。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消'
      }
    )
    
    // 先删除体重记录
    await supabase
      .from('weight_records')
      .delete()
      .eq('pet_id', pet.id)
    
    // 再删除宠物记录
    const { error } = await supabase
      .from('pets')
      .delete()
      .eq('id', pet.id)
    
    if (error) {
      ElMessage.error(`删除失败: ${error.message}`)
    } else {
      ElMessage.success('删除成功')
      emit('refresh')
    }
  } catch {
    // 用户取消删除
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

.pet-detail {
  padding: 10px;
}

.pet-info {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.avatar-section {
  flex-shrink: 0;
}

.info-section {
  flex: 1;
}

.info-section p {
  margin: 8px 0;
  line-height: 1.5;
}

.weight-section h4 {
  margin: 0 0 15px 0;
  color: #409eff;
}

.add-weight {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}
</style>