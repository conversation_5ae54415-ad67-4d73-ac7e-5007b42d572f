<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TypeManagementDialog 彻底重构</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .hero-section {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        .hero-title {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 16px;
        }
        .hero-subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .section-title {
            color: #409EFF;
            margin-bottom: 20px;
            border-bottom: 3px solid #409EFF;
            padding-bottom: 10px;
            font-size: 24px;
            font-weight: 600;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #409EFF;
        }
        .feature-card h4 {
            color: #409EFF;
            margin-top: 0;
            margin-bottom: 12px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .feature-list li {
            padding: 6px 0;
            position: relative;
            padding-left: 24px;
        }
        .feature-list li:before {
            content: "✨";
            position: absolute;
            left: 0;
            font-size: 14px;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            margin: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        .test-button.success {
            background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
            box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
        }
        .test-button.success:hover {
            box-shadow: 0 8px 20px rgba(103, 194, 58, 0.4);
        }
        .highlight-box {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #67C23A;
            margin: 20px 0;
        }
        .highlight-box h4 {
            color: #67C23A;
            margin-top: 0;
        }
        .color-preview {
            display: inline-flex;
            gap: 4px;
            margin: 10px 0;
        }
        .color-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e4e7ed;
        }
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #303133;
        }
        .old-version {
            color: #F56C6C;
        }
        .new-version {
            color: #67C23A;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <h1 class="hero-title">🎨 TypeManagementDialog 彻底重构</h1>
        <p class="hero-subtitle">现代化设计 · 完美交互 · 极致体验</p>
        <div>
            <button class="test-button" onclick="openExpenseTracking()">测试花费追踪</button>
            <button class="test-button success" onclick="openHealthRecords()">测试健康记录</button>
            <button class="test-button" onclick="openStyleDemo()">样式演示</button>
        </div>
    </div>

    <div class="test-container">
        <h2 class="section-title">🚀 重构亮点</h2>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🎨 全新视觉设计</h4>
                <ul class="feature-list">
                    <li>现代化卡片式布局</li>
                    <li>优雅的颜色预览区域</li>
                    <li>清晰的视觉层次</li>
                    <li>精致的圆角和阴影</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4>🎯 完美的颜色选择器</h4>
                <ul class="feature-list">
                    <li>24种精选颜色预设</li>
                    <li>6x4网格完美布局</li>
                    <li>36px圆形按钮</li>
                    <li>流畅的交互动画</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4>✨ 优雅的交互体验</h4>
                <ul class="feature-list">
                    <li>悬停缩放1.1倍</li>
                    <li>蓝色边框选中状态</li>
                    <li>白色勾选图标</li>
                    <li>立体阴影效果</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4>📱 响应式适配</h4>
                <ul class="feature-list">
                    <li>桌面端6列布局</li>
                    <li>移动端5列自适应</li>
                    <li>灵活的间距调整</li>
                    <li>完美的触摸体验</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="section-title">🎨 颜色预设展示</h2>
        
        <div class="highlight-box">
            <h4>24种精选颜色</h4>
            <p><strong>第一行 - 主要品牌色：</strong></p>
            <div class="color-preview">
                <div class="color-dot" style="background: #409EFF;" title="主蓝色"></div>
                <div class="color-dot" style="background: #67C23A;" title="成功绿"></div>
                <div class="color-dot" style="background: #E6A23C;" title="警告橙"></div>
                <div class="color-dot" style="background: #F56C6C;" title="危险红"></div>
                <div class="color-dot" style="background: #909399;" title="信息灰"></div>
                <div class="color-dot" style="background: #9C27B0;" title="优雅紫"></div>
            </div>
            
            <p><strong>第二行 - 活力色彩：</strong></p>
            <div class="color-preview">
                <div class="color-dot" style="background: #FF6B6B;" title="活力红"></div>
                <div class="color-dot" style="background: #4ECDC4;" title="薄荷绿"></div>
                <div class="color-dot" style="background: #45B7D1;" title="天空蓝"></div>
                <div class="color-dot" style="background: #96CEB4;" title="清新绿"></div>
                <div class="color-dot" style="background: #FFEAA7;" title="柠檬黄"></div>
                <div class="color-dot" style="background: #DDA0DD;" title="淡紫色"></div>
            </div>
            
            <p><strong>第三行 - 专业色调：</strong></p>
            <div class="color-preview">
                <div class="color-dot" style="background: #6C5CE7;" title="深紫色"></div>
                <div class="color-dot" style="background: #A29BFE;" title="浅紫色"></div>
                <div class="color-dot" style="background: #FD79A8;" title="粉红色"></div>
                <div class="color-dot" style="background: #FDCB6E;" title="金黄色"></div>
                <div class="color-dot" style="background: #E17055;" title="珊瑚色"></div>
                <div class="color-dot" style="background: #74B9FF;" title="亮蓝色"></div>
            </div>
            
            <p><strong>第四行 - 自然色系：</strong></p>
            <div class="color-preview">
                <div class="color-dot" style="background: #00B894;" title="翡翠绿"></div>
                <div class="color-dot" style="background: #00CEC9;" title="青绿色"></div>
                <div class="color-dot" style="background: #E84393;" title="玫红色"></div>
                <div class="color-dot" style="background: #FF7675;" title="浅红色"></div>
                <div class="color-dot" style="background: #81ECEC;" title="浅青色"></div>
                <div class="color-dot" style="background: #A29BFE;" title="浅紫色"></div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="section-title">📊 重构对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>特性</th>
                    <th class="old-version">重构前</th>
                    <th class="new-version">重构后</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>颜色数量</strong></td>
                    <td class="old-version">18种颜色</td>
                    <td class="new-version">24种精选颜色</td>
                </tr>
                <tr>
                    <td><strong>布局方式</strong></td>
                    <td class="old-version">6x3网格</td>
                    <td class="new-version">6x4网格</td>
                </tr>
                <tr>
                    <td><strong>按钮尺寸</strong></td>
                    <td class="old-version">32px圆形</td>
                    <td class="new-version">36px圆形</td>
                </tr>
                <tr>
                    <td><strong>选中指示</strong></td>
                    <td class="old-version">白色圆形+勾选</td>
                    <td class="new-version">白色勾选图标</td>
                </tr>
                <tr>
                    <td><strong>悬停效果</strong></td>
                    <td class="old-version">缩放1.15倍</td>
                    <td class="new-version">缩放1.1倍</td>
                </tr>
                <tr>
                    <td><strong>颜色预览</strong></td>
                    <td class="old-version">简单预览框</td>
                    <td class="new-version">信息卡片式预览</td>
                </tr>
                <tr>
                    <td><strong>响应式</strong></td>
                    <td class="old-version">移动端5列</td>
                    <td class="new-version">移动端5列优化</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        function openExpenseTracking() {
            window.open('http://localhost:5208/expense-tracking', '_blank');
        }

        function openHealthRecords() {
            window.open('http://localhost:5208/health-records', '_blank');
        }

        function openStyleDemo() {
            window.open('http://localhost:5208/style_demo', '_blank');
        }

        // 页面加载动画
        window.onload = function() {
            console.log('TypeManagementDialog 彻底重构完成！');
            
            // 添加加载动画
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        };
    </script>
</body>
</html>
