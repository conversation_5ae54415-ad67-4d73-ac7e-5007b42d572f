/* 日历视图预设样式 */

/* 记录类型样式预设 */
.record-dot-primary {
  background: #409EFF;
}

.record-dot-success {
  background: #67C23A;
}

.record-dot-warning {
  background: #E6A23C;
}

.record-dot-danger {
  background: #F56C6C;
}

.record-dot-info {
  background: #9C27B0;
}

.record-dot-default {
  background: #909399;
}

/* 事件记录类型样式 */
.record-dot-feeding {
  background: #67C23A;
}

.record-dot-medical {
  background: #F56C6C;
}

.record-dot-grooming {
  background: #409EFF;
}

.record-dot-exercise {
  background: #E6A23C;
}

.record-dot-training {
  background: #9C27B0;
}

.record-dot-other {
  background: #909399;
}

/* 提醒事项类型样式 */
.record-dot-health {
  background: #F56C6C;
}

.record-dot-food {
  background: #67C23A;
}

.record-dot-beauty {
  background: #409EFF;
}

.record-dot-play {
  background: #E6A23C;
}

.record-dot-social {
  background: #9C27B0;
}

.record-dot-work {
  background: #606266;
}

/* 日历主题变体 */
.calendar-theme-blue .calendar-header {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.calendar-theme-blue .calendar-cell.selected-date {
  background: linear-gradient(135deg, #2196F3, #64B5F6);
}

.calendar-theme-green .calendar-header {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

.calendar-theme-green .calendar-cell.selected-date {
  background: linear-gradient(135deg, #4CAF50, #81C784);
}

.calendar-theme-purple .calendar-header {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
}

.calendar-theme-purple .calendar-cell.selected-date {
  background: linear-gradient(135deg, #9C27B0, #BA68C8);
}

/* 日历尺寸变体 */
.calendar-size-compact .calendar-cell {
  height: 60px;
  padding: 4px;
}

.calendar-size-compact .date-number {
  font-size: 14px;
}

.calendar-size-compact .record-dot {
  width: 4px;
  height: 4px;
}

.calendar-size-large .calendar-cell {
  height: 100px;
  padding: 12px;
}

.calendar-size-large .date-number {
  font-size: 18px;
}

.calendar-size-large .record-dot {
  width: 8px;
  height: 8px;
}

/* 动画效果预设 */
.calendar-view-switching {
  opacity: 0.7;
  transform: scale(0.98);
  transition: all 0.2s ease;
}

.calendar-cell-hover-effect {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.calendar-cell-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 特殊状态样式 */
.calendar-cell-today {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  color: white;
}

.calendar-cell-weekend {
  background: #fafafa;
}

.calendar-cell-holiday {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.calendar-cell-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.calendar-cell-disabled:hover {
  transform: none;
  background: inherit;
}

/* 记录密度指示器 */
.record-density-low .record-indicators {
  opacity: 0.6;
}

.record-density-medium .record-indicators {
  opacity: 0.8;
}

.record-density-high .record-indicators {
  opacity: 1;
}

.record-density-high .calendar-cell {
  border: 2px solid #409EFF;
}

/* 工具提示增强样式 */
.tooltip-enhanced .records-tooltip {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.tooltip-enhanced .tooltip-header {
  background: linear-gradient(135deg, rgba(248, 249, 255, 0.9) 0%, rgba(240, 242, 255, 0.9) 100%);
}

/* 无障碍访问增强 */
.calendar-accessible .calendar-cell:focus {
  outline: 2px solid #409EFF;
  outline-offset: 2px;
}

.calendar-accessible .record-dot {
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 打印样式 */
@media print {
  .calendar-header {
    background: white !important;
    border-bottom: 2px solid #000;
  }
  
  .calendar-cell {
    border: 1px solid #000;
  }
  
  .record-dot {
    border: 1px solid #000;
    background: white !important;
  }
  
  .navigation-buttons,
  .date-selectors {
    display: none;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .calendar-dark-theme .calendar-header {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    color: white;
  }
  
  .calendar-dark-theme .calendar-cell {
    background: #2d3748;
    color: white;
    border-color: #4a5568;
  }
  
  .calendar-dark-theme .calendar-cell:hover {
    background: #4a5568;
  }
  
  .calendar-dark-theme .records-tooltip {
    background: #2d3748;
    color: white;
    border-color: #4a5568;
  }
  
  .calendar-dark-theme .tooltip-header {
    background: linear-gradient(135deg, #4a5568 0%, #718096 100%);
  }
}

/* 高对比度主题 */
.calendar-high-contrast .calendar-cell {
  border: 2px solid #000;
}

.calendar-high-contrast .record-dot {
  border: 2px solid #000;
  background: #000 !important;
}

.calendar-high-contrast .calendar-cell.selected-date {
  background: #000 !important;
  color: #fff !important;
}

/* 动画禁用（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .calendar-cell,
  .nav-btn,
  .today-btn,
  .records-tooltip {
    transition: none !important;
    animation: none !important;
  }
  
  .calendar-cell:hover {
    transform: none !important;
  }
}
