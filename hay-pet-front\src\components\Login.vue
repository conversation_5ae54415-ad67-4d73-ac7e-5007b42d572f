<template>
  <div class="login-container" v-motion="containerMotion">
    <div class="login-card" v-motion="cardMotion">
      <!-- 关闭按钮 -->
      <div class="close-button" @click="closeLogin" v-motion="{ initial: { opacity: 0 }, enter: { opacity: 1, transition: { delay: 400, duration: 300 } } }">
        <el-icon><Close /></el-icon>
      </div>
      
      <!-- 背景动画元素 -->
      <div class="animated-background">
        <div class="bg-circle circle-1" v-motion="{ initial: { opacity: 0 }, enter: { opacity: 0.3, transition: { delay: 100, duration: 500 } } }"></div>
        <div class="bg-circle circle-2" v-motion="{ initial: { opacity: 0 }, enter: { opacity: 0.3, transition: { delay: 200, duration: 500 } } }"></div>
        <div class="bg-circle circle-3" v-motion="{ initial: { opacity: 0 }, enter: { opacity: 0.3, transition: { delay: 300, duration: 500 } } }"></div>
      </div>
      
      <!-- 品牌标识区域 -->
      <div class="brand-section" v-motion="brandMotion">
        <div class="brand-icon" @mouseenter="animatePawIcon">
          <div class="paw-icon" ref="pawIconRef" v-motion="{ initial: { opacity: 0, scale: 0, rotate: -30 }, enter: { opacity: 1, scale: 1, rotate: 0, transition: { duration: 600, ease: 'elastic.out(1, 0.3)' } } }">🐾</div>
        </div>
        <h1 class="brand-title">Hay!Pet</h1>
        <p class="brand-subtitle">{{ isRegisterMode ? '加入我们，开始记录宠物生活' : '欢迎回来，继续您的宠物旅程' }}</p>
      </div>
      
      <!-- 表单区域 -->
      <div class="form-section" v-motion="formMotion">
        <h2 class="form-title">{{ isRegisterMode ? '创建账户' : '登录账户' }}</h2>
        
        <el-form 
          ref="formRef" 
          :model="form" 
          :rules="rules" 
          label-position="top"
          class="login-form"
        >
          <el-form-item prop="email">
            <div class="input-label">邮箱地址</div>
            <el-input
              v-model="form.email"
              type="email"
              placeholder="请输入您的邮箱"
              autocomplete="email"
              size="large"
              @focus="handleInputFocus"
            >
              <template #prefix>
                <el-icon><Message /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item prop="password">
            <div class="input-label">密码</div>
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入您的密码"
              autocomplete="current-password"
              show-password
              size="large"
              @focus="handleInputFocus"
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <!-- 记住我选项和邀请码输入框 -->
          <div class="form-options">
            <el-checkbox v-if="!isRegisterMode" v-model="rememberMe">记住我</el-checkbox>
            <!-- 邀请码输入框（仅在邀请制模式且注册时显示） -->
            <el-input
              v-if="registrationMode === 'invite' && isRegisterMode"
              v-model="form.inviteCode"
              placeholder="请输入邀请码"
              size="large"

              @focus="handleInputFocus"
              class="invite-code-input"
            >
              <template #prefix>
                <el-icon><Key /></el-icon>
              </template>
            </el-input>
          </div>
        </el-form>
      </div>
      
      <!-- 按钮区域 -->
      <div class="action-section" v-motion="buttonMotion">
        <el-button 
          v-if="!isRegisterMode"
          type="primary" 
          @click="signIn" 
          :loading="loading"
          size="large"
          class="action-button"
          ref="loginButtonRef"
          @mouseenter="animateButton"
        >
          <span>立即登录</span>
          <el-icon class="button-icon"><Right /></el-icon>
        </el-button>
        
        <el-button 
          v-else
          type="primary" 
          @click="signUp" 
          :loading="loading"
          size="large"
          class="action-button"
          ref="registerButtonRef"
          @mouseenter="animateButton"
        >
          <span>立即注册</span>
          <el-icon class="button-icon"><Right /></el-icon>
        </el-button>
        
        <!-- 切换模式按钮 -->
        <div class="switch-mode">
          <span v-if="!isRegisterMode">还没有账户？</span>
          <span v-else>已有账户？</span>
          <el-button 
            v-if="!isRegisterMode && registrationMode !== 'disabled'"
            link 
            @click="switchToRegister"
            class="switch-button"
          >
            立即注册
          </el-button>
          <el-button 
            v-else
            link 
            @click="switchToLogin"
            class="switch-button"
          >
            立即登录
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Message, Lock, Key, Right, Close } from '@element-plus/icons-vue'
import { useMotion } from '@vueuse/motion'
import { gsap } from 'gsap'
import 'animate.css'

const emit = defineEmits(['login-success', 'close'])

const supabase = inject('supabase')
const formRef = ref()
const loading = ref(false)
const isRegisterMode = ref(false)

// 动画相关引用
const pawIconRef = ref(null)
const loginButtonRef = ref(null)
const registerButtonRef = ref(null)

const form = ref({
  email: '',
  password: '',
  inviteCode: ''
})

// 记住我选项
const rememberMe = ref(false)

// 获取环境变量配置
const registrationMode = import.meta.env.VITE_REGISTRATION_MODE || 'open'
const configuredInviteCode = import.meta.env.VITE_INVITE_CODE || ''

const rules = computed(() => {
  const baseRules = {
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度至少6位', trigger: 'blur' }
    ]
  }
  
  // 只有在邀请制模式且为注册模式时才添加邀请码验证规则
  if (registrationMode === 'invite' && isRegisterMode.value) {
    baseRules.inviteCode = [
      { 
        required: true, 
        message: '请输入邀请码', 
        trigger: 'blur' 
      }
    ]
  }
  
  return baseRules
})

// 动画配置
const containerMotion = {
  initial: { opacity: 0 },
  enter: { 
    opacity: 1,
    transition: { duration: 300 }
  }
}

const cardMotion = {
  initial: { opacity: 0, scale: 0.9, y: 20 },
  enter: { 
    opacity: 1, 
    scale: 1,
    y: 0,
    transition: { 
      duration: 500,
      ease: 'back.out(1.7)'
    }
  }
}

const brandMotion = {
  initial: { opacity: 0, y: -20 },
  enter: { 
    opacity: 1, 
    y: 0,
    transition: { 
      delay: 200,
      duration: 400
    }
  }
}

const formMotion = {
  initial: { opacity: 0, y: 20 },
  enter: { 
    opacity: 1, 
    y: 0,
    transition: { 
      delay: 300,
      duration: 400
    }
  }
}

const buttonMotion = {
  initial: { opacity: 0, y: 20 },
  enter: { 
    opacity: 1, 
    y: 0,
    transition: { 
      delay: 400,
      duration: 400
    }
  }
}

// 爪子图标动画
const animatePawIcon = () => {
  if (pawIconRef.value) {
    gsap.to(pawIconRef.value, {
      rotation: 15,
      scale: 1.2,
      duration: 0.5,
      ease: 'elastic.out(1.2, 0.3)',
      onComplete: () => {
        gsap.to(pawIconRef.value, {
          rotation: 0,
          scale: 1,
          duration: 0.5,
          ease: 'elastic.out(1, 0.3)'
        })
      }
    })
  }
}

// 按钮悬停动画
const animateButton = (event) => {
  const button = event.currentTarget;
  if (button) {
    gsap.to(button, {
      scale: 1.05,
      duration: 0.3,
      ease: 'power2.out'
    })

    // 按钮恢复原状
    try {
      // 检查button是否为DOM元素且有addEventListener方法
      if (button instanceof Element && typeof button.addEventListener === 'function') {
        button.addEventListener('mouseleave', () => {
          if (button) {
            gsap.to(button, {
              scale: 1,
              duration: 0.3,
              ease: 'power2.out'
            })
          }
        }, { once: true })
      } else {
        // 如果不是DOM元素，使用gsap的onComplete回调
        setTimeout(() => {
          if (button) {
            gsap.to(button, {
              scale: 1,
              duration: 0.3,
              ease: 'power2.out'
            })
          }
        }, 2000) // 2秒后自动恢复，模拟鼠标离开效果
      }
    } catch (error) {
      console.warn('无法添加mouseleave事件监听器:', error)
    }
  }
}

// 输入框聚焦动画
const handleInputFocus = (event) => {
  const input = event.currentTarget;
  if (input) {
    gsap.to(input, {
      scale: 1.02,
      duration: 0.3,
      ease: 'power2.out'
    })

    input.addEventListener('blur', () => {
      gsap.to(input, {
        scale: 1,
        duration: 0.3,
        ease: 'power2.out'
      })
    }, { once: true })
  }
}

// 登录
const signIn = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  loading.value = true
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: form.value.email,
      password: form.value.password
    })
    
    if (error) {
      ElMessage.error(`登录失败: ${error.message}`)
    } else {
      ElMessage.success('登录成功')
      emit('login-success', data.user)
    }
  } catch (err) {
    ElMessage.error('登录过程中发生错误')
    console.error('登录错误:', err)
  } finally {
    loading.value = false
  }
}

// 切换到注册模式
const switchToRegister = () => {
  // 添加切换动画
  gsap.to('.login-form', {
    opacity: 0,
    y: -20,
    duration: 0.3,
    onComplete: () => {
      isRegisterMode.value = true
      // 清空邀请码
      form.value.inviteCode = ''
      
      // 显示注册表单
      setTimeout(() => {
        gsap.to('.login-form', {
          opacity: 1,
          y: 0,
          duration: 0.3
        })
      }, 50)
    }
  })
}

// 切换到登录模式
const switchToLogin = () => {
  // 添加切换动画
  gsap.to('.login-form', {
    opacity: 0,
    y: -20,
    duration: 0.3,
    onComplete: () => {
      isRegisterMode.value = false
      // 清空邀请码
      form.value.inviteCode = ''
      
      // 显示登录表单
      setTimeout(() => {
        gsap.to('.login-form', {
          opacity: 1,
          y: 0,
          duration: 0.3
        })
      }, 50)
    }
  })
}

// 注册
const signUp = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  // 邀请制模式下验证邀请码
  if (registrationMode === 'invite') {
    if (!form.value.inviteCode) {
      ElMessage.error('请输入邀请码')
      return
    }
    if (form.value.inviteCode !== configuredInviteCode) {
      ElMessage.error('邀请码错误')
      return
    }
  }
  
  loading.value = true
  
  try {
    const { data, error } = await supabase.auth.signUp({
      email: form.value.email,
      password: form.value.password
    })
    
    if (error) {
      ElMessage.error(`注册失败: ${error.message}`)
    } else {
      ElMessage.success('注册成功，请检查邮箱确认')
      if (data.user) {
        emit('login-success', data.user)
      }
    }
  } catch (err) {
    ElMessage.error('注册过程中发生错误')
    console.error('注册错误:', err)
  } finally {
    loading.value = false
  }
}

// 关闭登录面板
const closeLogin = () => {
  // 添加关闭动画
  gsap.to('.login-card', {
    opacity: 0,
    scale: 0.9,
    y: 20,
    duration: 0.3,
    onComplete: () => {
      emit('close')
    }
  })
}

// 组件挂载时初始化动画
onMounted(() => {
  // 初始化背景圆圈动画 - 简化动画效果
  const circles = document.querySelectorAll('.bg-circle');
  
  if (circles.length > 0) {
    // 先设置初始透明度为0
    gsap.set(circles, { opacity: 0 });
    
    // 整体淡入动画
    gsap.to(circles, {
      opacity: 0.3,
      duration: 0.6,
      stagger: 0.1,
      ease: 'power1.out'
    });
    
    // 圆圈轻微浮动动画
    circles.forEach((circle, index) => {
      gsap.to(circle, {
        x: Math.random() * 15 - 7,
        y: Math.random() * 15 - 7,
        duration: 4 + index,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: index * 0.1
      });
    });
  }
  
  // 表单动画
  const loginForm = document.querySelector('.login-form');
  if (loginForm) {
    gsap.from(loginForm, {
      y: 10,
      opacity: 0,
      duration: 0.4,
      delay: 0.2,
      ease: 'power2.out'
    });
  }
  
  // 按钮动画
  const actionSection = document.querySelector('.action-section');
  if (actionSection) {
    gsap.from(actionSection, {
      y: 10,
      opacity: 0,
      duration: 0.4,
      delay: 0.3,
      ease: 'power2.out'
    });
  }
})
</script>

<style scoped>
/* 登录容器样式 - 参考欢迎页设计 */
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, 
    #f8fafc 0%, 
    #e2e8f0 25%, 
    #cbd5e1 50%, 
    #94a3b8 75%, 
    #64748b 100%);
  background-size: 200% 200%;
  animation: gentleShift 20s ease infinite;
  z-index: 9999;
  overflow: hidden;
}

@keyframes gentleShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 登录卡片样式 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px 40px;
  max-width: 420px;
  width: 100%;
  text-align: center;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.6);
  position: relative;
  transform-style: preserve-3d;
  overflow: hidden;
}

/* 关闭按钮样式 */
.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.close-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.close-button:active {
  transform: scale(0.95);
}

.close-button .el-icon {
  font-size: 18px;
  color: #64748b;
}

/* 背景动画元素 */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.15), rgba(6, 182, 212, 0.15));
  opacity: 0;
  filter: blur(40px);
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -50px;
  left: -50px;
}

.circle-2 {
  width: 300px;
  height: 300px;
  bottom: -100px;
  right: -50px;
}

.circle-3 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
}

/* 品牌标识区域 */
.brand-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.brand-icon {
  margin-bottom: 16px;
  cursor: pointer;
  display: inline-block;
}

.paw-icon {
  font-size: 48px;
  display: inline-block;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
  transform-origin: center center;
  will-change: transform;
}

.brand-title {
  font-size: 36px;
  font-weight: 700;
  background: linear-gradient(135deg, #0ea5e9, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
  position: relative;
}

.brand-subtitle {
  color: #64748b;
  font-size: 16px;
  margin: 0;
  font-weight: 500;
  position: relative;
  line-height: 1.5;
}

/* 表单区域 */
.form-section {
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
  text-align: left;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 24px 0;
  letter-spacing: -0.3px;
  text-align: center;
}

.login-form {
  margin: 0;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 8px;
  display: block;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input) {
  --el-input-border-radius: 12px;
}

:deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  min-height: 48px;
}

:deep(.el-input__wrapper:hover) {
  border-color: #cbd5e1;
  background: #ffffff;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  background: #ffffff;
  border-color: #0ea5e9;
}

:deep(.el-input__inner) {
  font-size: 15px;
  color: #374151;
  font-weight: 400;
}

:deep(.el-input__prefix) {
  color: #9ca3af;
  margin-right: 12px;
}

:deep(.el-input__prefix .el-icon) {
  font-size: 18px;
}

/* 表单选项 */
.form-options {
    display: flex;
    align-items: center; /* 垂直居中对齐 */
    margin-top: 10px;
    margin-bottom: 20px;
    gap: 10px; /* 使用 gap 替代 margin-right，更现代的布局方式 */

    .el-checkbox {
      /* 保持默认样式，或根据需要调整 */
    }

    .invite-code-input {
      flex-grow: 1; /* 邀请码输入框占据剩余空间 */
      .el-input__wrapper {
        height: 40px; /* 保持与复选框等高，Element Plus large size 默认高度 */
      }
    }
  }

:deep(.el-checkbox__label) {
  font-size: 14px;
  color: #4b5563;
  font-weight: 500;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #0ea5e9;
  border-color: #0ea5e9;
}

/* 按钮区域 */
.action-section {
  position: relative;
  z-index: 1;
}

.action-button {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #0ea5e9, #06b6d4);
  border: none;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.action-button:hover {
  background: linear-gradient(135deg, #0284c7, #0891b2);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(14, 165, 233, 0.3);
}

.action-button:active {
  transform: translateY(0);
}

.button-icon {
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.action-button:hover .button-icon {
  transform: translateX(4px);
}

/* 切换模式 */
.switch-mode {
  text-align: center;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.switch-button {
  color: #0ea5e9;
  font-weight: 600;
  padding: 0;
  margin-left: 4px;
  text-decoration: none;
  transition: color 0.2s ease;
}

.switch-button:hover {
  color: #0284c7;
  text-decoration: underline;
}

:deep(.switch-button.el-button--text) {
  color: #0ea5e9;
}

:deep(.switch-button.el-button--text:hover) {
  color: #0284c7;
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card {
    padding: 32px 24px;
    border-radius: 20px;
    max-width: 100%;
  }
  
  .brand-title {
    font-size: 28px;
  }
  
  .form-title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .brand-subtitle {
    font-size: 14px;
  }
  
  .paw-icon {
    font-size: 40px;
  }
  
  :deep(.el-input__wrapper) {
    min-height: 44px;
    padding: 10px 14px;
  }
  
  .action-button {
    height: 44px;
    font-size: 15px;
  }
}

@media (max-width: 360px) {
  .login-card {
    padding: 24px 20px;
  }
  
  .brand-title {
    font-size: 24px;
  }
  
  .form-title {
    font-size: 20px;
  }
}
</style>