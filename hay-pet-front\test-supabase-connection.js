// 测试 Supabase 连接和分类功能的脚本
// 在浏览器控制台中运行此脚本来测试功能

console.log('开始测试 Supabase 连接...');

// 测试获取用户信息
async function testUserAuth() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    
    console.log('用户信息:', user);
    return user;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
}

// 测试创建分类表
async function testCreateCategoriesTable() {
  try {
    console.log('测试创建分类表...');
    
    // 检查表是否存在
    const { data, error } = await supabase
      .from('expense_categories')
      .select('count(*)')
      .limit(1);
    
    if (error) {
      console.error('分类表不存在或无法访问:', error);
      console.log('请在 Supabase SQL 编辑器中执行 database-migration-add-expense-categories.sql 脚本');
      return false;
    }
    
    console.log('分类表存在，可以正常访问');
    return true;
  } catch (error) {
    console.error('测试分类表失败:', error);
    return false;
  }
}

// 测试创建默认分类
async function testCreateDefaultCategories() {
  try {
    const user = await testUserAuth();
    if (!user) {
      console.log('用户未登录，无法创建分类');
      return false;
    }

    console.log('测试创建默认分类...');
    
    // 检查是否已有分类
    const { data: existingCategories, error: selectError } = await supabase
      .from('expense_categories')
      .select('*')
      .eq('user_id', user.id);
    
    if (selectError) throw selectError;
    
    if (existingCategories && existingCategories.length > 0) {
      console.log('已存在分类:', existingCategories);
      return true;
    }
    
    // 创建默认分类
    const defaultCategories = [
      { name: '食物', color: '#FF6B6B', order_index: 1 },
      { name: '医疗', color: '#4ECDC4', order_index: 2 },
      { name: '玩具', color: '#45B7D1', order_index: 3 },
      { name: '用品', color: '#96CEB4', order_index: 4 },
      { name: '美容', color: '#FFEAA7', order_index: 5 },
      { name: '其他', color: '#F7DC6F', order_index: 6 }
    ];

    const categoriesToInsert = defaultCategories.map(cat => ({
      ...cat,
      user_id: user.id,
      is_active: true
    }));

    const { data, error } = await supabase
      .from('expense_categories')
      .insert(categoriesToInsert)
      .select();

    if (error) throw error;
    
    console.log('默认分类创建成功:', data);
    return true;
  } catch (error) {
    console.error('创建默认分类失败:', error);
    return false;
  }
}

// 测试获取分类
async function testFetchCategories() {
  try {
    const user = await testUserAuth();
    if (!user) return false;

    console.log('测试获取分类...');
    
    const { data, error } = await supabase
      .from('expense_categories')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('order_index', { ascending: true });

    if (error) throw error;
    
    console.log('获取到的分类:', data);
    return data;
  } catch (error) {
    console.error('获取分类失败:', error);
    return null;
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('=== 开始 Supabase 功能测试 ===');
  
  const user = await testUserAuth();
  if (!user) {
    console.log('❌ 用户未登录，请先登录');
    return;
  }
  
  const tableExists = await testCreateCategoriesTable();
  if (!tableExists) {
    console.log('❌ 分类表不存在，请先创建数据库表');
    return;
  }
  
  const categoriesCreated = await testCreateDefaultCategories();
  if (!categoriesCreated) {
    console.log('❌ 创建默认分类失败');
    return;
  }
  
  const categories = await testFetchCategories();
  if (!categories) {
    console.log('❌ 获取分类失败');
    return;
  }
  
  console.log('✅ 所有测试通过！');
  console.log('=== 测试完成 ===');
}

// 导出测试函数供控制台使用
window.testSupabase = {
  runAllTests,
  testUserAuth,
  testCreateCategoriesTable,
  testCreateDefaultCategories,
  testFetchCategories
};

console.log('测试脚本已加载，请在控制台运行: testSupabase.runAllTests()');
